﻿/// <reference path="app.js" />
PCSMSApp.controller('deviceController', function ($scope, deviceServices, licenseServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    //====================================================================Declaration=================================================================================
    $scope.LicenseList = [];
    $scope.LicenseDetails = null;
    $scope.DeviceList = [];
    $scope.Device = {};



    //====================================================================Modal Operation=============================================================================
    $scope.cancelLicenseDetailsModal = function () {
        $('#LicenseDetailsModal').modal("hide");
        $timeout(function () {
            $scope.License = {};
        }, 200);
    }

    licenseServices.GetLicenseListByCompanyId($rootScope.CompanyId).then(function (response) {
        $scope.LicenseList = response.data;
        angular.forEach(response.data, function (value, key) {
            if (value.LicenseKey == null) {
                value.LicenseKey = "---------------------------------------";
            }
        });

    });

   
    $scope.SeeDeviceList = function (licenseId) {
        deviceServices.GetDeviceListByLicenseId(licenseId).then(function (response) {
            $scope.DeviceList = response.data;
            console.log($scope.DeviceList);
            

        });
        licenseServices.GetLicenseDetailsByLicenseId(licenseId).then(function (response) {
            $scope.LicenseDetails = response.data;
        });
    }



    //====================================================================DB Operation================================================================================
    
    //$scope.GetLicenseDetailsByLicenseId = function (Id) {
    //    deviceServices.GetLicenseDetailsByLicenseId(Id).then(function(response) {
    //            $scope.License = response.data;
    //            console.log($scope.License);
    //        })
    //        .then(function() {
    //            $('#LicenseDetailsModal').modal('show');
    //        });
    //}

    
    $scope.OpenDeviceNameChangeModal = function (deviceUniqueId, deviceName, e) {
        $('#DeviceDetailsModal').modal('show');
        $scope.Device.DeviceUniqueId = deviceUniqueId;
        $scope.Device.DeviceName = deviceName;
        
        e.stopPropagation();
    }

    $scope.UpdateDeviceName = function () {
        deviceServices.ChangeDeviceName($scope.Device.DeviceUniqueId, $scope.Device.DeviceName).then(function (response) {
                if (response.data.IsReport == "Ok") {
                    toastr.success(response.data.Message, "Success!");
                }
                else if (response.data.IsReport == "NotOk") {
                    toastr.error(response.data.Message, "Error!");
                }
            })
            .then(function () {
                $scope.cancelDeviceDetailsModal();
                $timeout(function () {
                    $scope.SeeDeviceList($scope.LicenseDetails.Id);
                }, 200)
                
            });
        
    }

    $scope.cancelDeviceDetailsModal = function () {
        $('#DeviceDetailsModal').modal('hide');
        $timeout(function () {
            $scope.Device = {};
        }, 200)
    }
});