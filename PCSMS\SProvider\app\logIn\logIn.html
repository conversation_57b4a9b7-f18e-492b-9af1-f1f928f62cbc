﻿<style>
    .page-body {
        padding-left: 10px;
        padding-right: 10px;
        padding-bottom: 50px;
    }

    .logo {
        max-width: 420px;
        margin: 20px auto 0px;
        margin-bottom: 40px;
    }

        .logo img {
            display: block;
            margin: auto;
            width: 50%;
            min-width: 140px;
        }

    .box {
        background: #ffffff;
        border-radius: 5px;
        padding: 15px;
        max-width: 420px;
        margin: 20px auto 0px;
    }
</style>


<div class="animated slideInDown">
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <!--LOGO-->
    <div class="logo">
        <img alt="logo" src="Assets_SProvider/images/admin-logo.png" style="border:1px solid #bfd6d1" />
    </div>
    <div class="box">
        <!--LOG IN FORM-->
        <div class="panel mb-none">
            <div class="panel-content bg-scale-0">
                <form name="logInForm" novalidate>
                    <div class="form-group mt-md">
                        <span class="input-with-icon">
                            <input id="Username" name="Username" class="form-control input-md" type="text" placeholder="Enter email"
                                   ng-model="LogInObj.Username"
                                   ng-change="ControlRememberMe()"
                                   ng-required="true"
                                   ng-minlength="11"
                                ng-maxlength="30"
                                ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'/>
                            <i class="fa fa-envelope"></i>
                        </span>
                        <div class="row custom-row">
                            <ul class="list-unstyled errormessage pull-left"
                                ng-show="logInForm.Username.$dirty && logInForm.Username.$invalid">
                                <li class="pull-left" ng-show="logInForm.Username.$error.pattern">Not a valid email address</li>
                                <li><span class="pull-left" ng-show="!logInForm.Username.$error.pattern && logInForm.Username.$error.minlength">Minimum length is 11</span></li>
                                <li><span class="pull-left" ng-show="!logInForm.Username.$error.pattern && logInForm.Username.$error.maxlength">Maximum length is 30</span></li>
                            </ul>
                        </div>
                    </div>
                    <div class="form-group">
                        <span class="input-with-icon">
                            <input id="Password" name="Password" class="form-control input-md" type="password" placeholder="Password"
                                   ng-model="LogInObj.Password"
                                   ng-change="ControlRememberMe()"
                                   ng-required="true"
                                   ng-minlength="6"
                                   ng-maxlength="50"
                                   ng-pattern="/([a-zA-Z0-9 ])\w+/" />
                            <i class="fa fa-key"></i>
                        </span>
                        <div class="row custom-row">
                            <ul class="list-unstyled errormessage pull-left" ng-show="logInForm.Password.$dirty && logInForm.Password.$invalid">
                                <li><span class="pull-left" ng-show="logInForm.Password.$error.required">*Required</span></li>
                                <li><span class="pull-left" ng-show="logInForm.Password.$error.pattern">Not a valid password</span></li>
                                <li><span class="pull-left" ng-show="!logInForm.Password.$error.pattern && logInForm.Password.$error.minlength">Minimum required length is 6</span></li>
                                <li><span class="pull-left" ng-show="!logInForm.Password.$error.pattern && logInForm.Password.$error.maxlength">Maximum required length is 50</span></li>
                            </ul>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-custom checkbox-primary">
                            <input type="checkbox" id="remember-me"
                                   ng-disabled="logInForm.$invalid"
                                   ng-true-value="1"
                                   ng-false-value="0"
                                   ng-model="Remember" />
                            <label class="check" for="remember-me">Remember me</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary btn-block"
                                ng-click="LogIn();"
                                ng-disabled="logInForm.$invalid">
                            Sign in
                        </button>
                    </div>
                    <div class="form-group text-center">
                        <!--<a ui-sref="forgotPassword">Forgot password?</a>-->
                        <hr />
                        <!--<span>Don't have an account?</span>
                        <a href="/index.html" class="btn btn-block mt-sm">Register</a>-->
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>