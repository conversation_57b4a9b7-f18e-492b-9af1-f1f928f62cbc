﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using PCSMS.Services.Services_Shared;

namespace PCSMS.Controllers
{
    [RoutePrefix("Api/TaskScheduler")]
    public class TaskSchedulerController : ApiController
    {
        private readonly ISubscriptionServices _subscriptionServices;
        public TaskSchedulerController()
        {
            _subscriptionServices = new SubscriptionServices();
        }




        [Route("TerminateSubscriptionByTaskScheduler")]
        [HttpGet]
        public IHttpActionResult TerminateSubscriptionByTaskScheduler()
        {
            return Ok(_subscriptionServices.TerminateSubscriptionByTaskScheduler().Data);
        }

    }
}
