﻿/// <reference path="../../../Assets_SProvider/angularjs/angular.js" />
/// <reference path="../../../app.js" />
PCSMSApp.factory("ProfileServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        SaveSP_Profile: function (param) {
            return $http({
                url: "/Api/SP_Profile/SaveSP_Profile",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        UpdateSP_Profile: function (param) {
            return $http({
                url: "/Api/SP_Profile/UpdateSP_Profile",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        //GetSP_ProfileList: function () {
        //    return $http({
        //        url: "/Api/SP_Profile/GetSP_ProfileList",
        //        method: "GET",
        //        headers: {
        //            "content-type": "application/json",
        //            "cache-control": "no-cache"
        //        },
        //        async: false
        //    });
        //},
        GetSP_ProfileDetails: function () {
            return $http({
                url: "/Api/SP_Profile/GetSP_ProfileDetails",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DeleteSP_Profile: function (id) {
            return $http({
                url: "/Api/SP_Profile/DeleteSP_Profile/" + id,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        /*Enternal API*/
        GetSP_BranchList: function () {
            return $http({
                url: "/Api/SP_Branch/GetSP_BranchList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetAccessTypeList: function () {
            return $http({
                url: "/api/AccessType/GetAccessTypeList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCompanyTypeList: function () {
            return $http({
                url: "/api/CompanyType/GetCompanyTypeList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetSP_DesignationList: function () {
            return $http({
                url: "/Api/SP_Designation/GetSP_DesignationList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        UploadSP_Logo: function (formdata) {
            return $http({
                url: "/Api/SP_Profile/UploadSP_Logo",
                method: "POST",
                data: formdata,
                headers: {
                    "content-type": undefined,
                    "cache-control": "no-cache"
                }
            });
        },
        UploadSP_PrivacyPolicy: function (formdata) {
            return $http({
                url: "/Api/SP_Profile/UploadSP_PrivacyPolicy",
                method: "POST",
                data: formdata,
                headers: {
                    "content-type": undefined,
                    "cache-control": "no-cache"
                }
            });
        },
    };
}]);