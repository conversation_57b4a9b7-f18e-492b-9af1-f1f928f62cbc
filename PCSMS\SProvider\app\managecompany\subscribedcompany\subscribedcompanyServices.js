﻿PCSMSApp.factory("subscribedcompanyServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetSubscribedCompanyList: function () {
            return $http({
                url: "/api/Subscription/GetSubscribedCompanyList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCompanyProfileDetails: function (companyId) {
            return $http({
                url: "/Api/CP_Profile/GetCompanyProfileDetails/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
    };
}]);