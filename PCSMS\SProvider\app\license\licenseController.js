﻿/// <reference path="app.js" />
PCSMSApp.controller('licenseController', function ($scope, licenseServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    //====================================================================Declaration=================================================================================
    $rootScope.LicenseList = [];
    $scope.License = {};
    $scope.isOpenFrom = false;





    //====================================================================Element Processing==========================================================================
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [DTColumnDefBuilder.newColumnDef(2).notSortable()
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
        .withOption('paging', true)
        .withOption('searching', true)
        .withOption('info', true)
        .withOption('order', [2, 'desc']);

   
    




    //====================================================================DB Operation================================================================================
    licenseServices.GetAllLicenseList().then(function (response) {
        $rootScope.LicenseList = response.data;

        angular.forEach(response.data, function (value, key) {
            if (value.RequestedOn != null) {
                var reqDateUtc = moment.utc(value.RequestedOn);
                value.RequestedOn = reqDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
            }
            if (value.ExpiryDate != null) {
                var expDateUtc = moment.utc(value.ExpiryDate);
                value.ExpiryDate = expDateUtc.local().format("YYYY-MM-DD").toString();
            }
        });
    });

    $scope.ApproveOrRejectLicense = function () {
        console.log($scope.License.Status);
        console.log($scope.License.SecondaryStatus);

        var txt = "";
        if ($scope.License.Status == "Requested") {
            var txt = "You are going to approve this license request";
        }
        else if ($scope.License.SecondaryStatus == "Renewal Requested") {
            var txt = "You are going to renew this license";
        }
        swal({
            title: "Are You Sure?",
            text: txt,
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn btn-success",
            confirmButtonText: "Yes",
            cancelButtonClass: "btn btn-default",
            cancelButtonText: "No",
            closeOnConfirm: true
        },
        function (isConfirm) {
            if (isConfirm) {
                if ($scope.ApproveOrRejectLicenseForm.$invalid == false) {
                    if ($scope.License.Status == "Requested") {
                        //console.log(moment($scope.License.ExpiryDate).format("YYYY-MM-DD HH:mm:ss.sss"));
                        licenseServices.ApproveLicense($scope.License.Id, $scope.License.DeviceLimit, moment($scope.License.ExpiryDate).format("YYYY-MM-DD HH:mm:ss.sss")).then(function (response) {
                            if (response.data.IsReport == "Ok") {
                                toastr.success(response.data.Message, "Success");
                            }
                            else if (response.data.IsReport == "NotOk") {
                                toastr.error(response.data.Message, "Error!");
                            }
                        })
                        .then(function () {
                            $('#ApproveOrRejectLicenseModal').modal('hide');
                        })
                        .then(function () {
                            $timeout(function () {
                                $state.reload();
                            }, 300)
                        })
                    }
                    else if ($scope.License.SecondaryStatus == "Renewal Requested") {
                        licenseServices.ApproveLicenseRenewal(moment($scope.License.ExpiryDate).format("YYYY-MM-DD HH:mm:ss.sss"), $scope.License.Id).then(function (response) {
                            if (response.data.IsReport == "Ok") {
                                toastr.success(response.data.Message, "Success");
                            }
                            else if (response.data.IsReport == "NotOk") {
                                toastr.error(response.data.Message, "Error!");
                            }
                        })
                        .then(function () {
                            $('#ApproveOrRejectLicenseModal').modal('hide');
                        })
                        .then(function () {
                            $timeout(function () {
                                $state.reload();
                            }, 300)
                        })
                    }
                    else {
                        if ($scope.License.Status == "Active") {
                            toastr.error("This is an Active License", "Error!")
                        }
                        else if ($scope.License.Status == "In-Active") {
                            toastr.error("This License is In-Active", "Error!")
                        }
                        else if ($scope.License.Status == "Expired") {
                            toastr.error("This License is already Expired", "Error!")
                        }
                    }
                }
                else {
                    toastr.error("This form contains invalid data. Can not be submitted", "Error!");
                }
            } else {
                $('#ApproveOrRejectLicenseModal').modal('hide');
                $timeout(function () {
                    $state.reload();
                }, 300)
            }
        });
    }

    $scope.GetLicenseDetailsByLicenseId = function (Id) {
        licenseServices.GetLicenseDetailsByLicenseId(Id).then(function (response) {
            $scope.License = response.data;
                if ($scope.License.ActivatedOn != null) {
                    var actDateUtc = moment.utc($scope.License.ActivatedOn);
                    $scope.License.ActivatedOn = actDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
                }
                if ($scope.License.RequestedOn != null) {
                    var reqDateUtc = moment.utc($scope.License.RequestedOn);
                    $scope.License.RequestedOn = reqDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
                }
                if ($scope.License.ExpiryDate != null) {
                    var expDateUtc = moment.utc($scope.License.ExpiryDate);
                    $scope.License.ExpiryDate = expDateUtc.local().format("YYYY-MM-DD").toString();
                }
        })
        .then(function () {
            $('#LicenseDetailsModal').modal('show');
        })
    };

    

    //====================================================================Modal Operation=============================================================================
    $scope.openApproveOrRejectLicenseModal = function (Id, Status, SecondaryStatus, ExpiryDate, LCode) {
        //console.log(Status);
        //console.log(SecondaryStatus);
        //console.log(ExpiryDate);
        $scope.License.Status = Status;
        $scope.License.SecondaryStatus = SecondaryStatus;
        $scope.License.Id = Id;
        $scope.License.LCode = LCode;
        $('#ApproveOrRejectLicenseModal').modal('show');

        //Imagine, expiry date of the license is 20 , this will disable 19th in the calendar.
        //So,I need to add one more day because i want disable up to 20th
        console.log(ExpiryDate);
       if (ExpiryDate == null) {
           var date = new Date();
           $scope.DisableUpTo = date.setDate(date.getDate() + 1);
       } else {
           var expDate = new Date(ExpiryDate);
           $scope.DisableUpTo = expDate.setDate(expDate.getDate() + 1);
       }

    };

    $scope.cancelApproveOrRejectLicenseModal = function () {
        $('#ApproveOrRejectLicenseModal').modal('hide');
        $timeout(function () {
            $scope.License = {};
            $scope.ApproveOrRejectLicenseForm.$setPristine();
            $scope.ApproveOrRejectLicenseForm.$setUntouched();
        }, 200)
    };

    $scope.cancelLicenseDetailsModal = function () {
        $('#LicenseDetailsModal').modal("hide");
        $timeout(function () {
            $scope.License = {};
        }, 200);
    };

    //===================================================================Validation==============================
    $scope.IsDateTimeSelected = function () {
        if ($scope.License.ExpiryDate == null) {
            return false;
        } else {
            return true;
        }
    }



});