﻿PCSMSApp.factory("onlyregisteredcompanyServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetRegisteredCompanyList: function () {
            return $http({
                url: "/api/Subscription/GetRegisteredCompanyList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCompanyProfileDetails: function (companyId) {
            return $http({
                url: "/Api/CP_Profile/GetCompanyProfileDetails/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
    };
}]);