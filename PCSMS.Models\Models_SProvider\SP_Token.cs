﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using PCSMS.Models.Common;

namespace PCSMS.Models.Models_SProvider
{
    public class SP_Token : Entity<int>
    {

        [ForeignKey("UserId")]
        public virtual SP_User SP_User { get; set; }
        public int? UserId { get; set; }

        public string AuthToken { get; set; }
        public DateTime? IssuedOn { get; set; }
        public DateTime? ExpiresOn { get; set; }

        
    }
}
