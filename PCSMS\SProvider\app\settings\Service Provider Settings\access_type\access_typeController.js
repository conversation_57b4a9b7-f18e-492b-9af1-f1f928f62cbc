﻿/// <reference path="../../../Assets_SProvider/angularjs/angular.js" />
/// <reference path="../../../app.js" />
PCSMSApp.controller('access_typeController', function ($scope, $rootScope, AccessTypeServices, blockUI, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    $rootScope.settingsOpen = true;

    //Retrieving data from db:
    $scope.AccessTypeList = AccessTypeServices.GetAccessTypeList().then(function (response) {
        $scope.AccessTypeList = response.data;
    });

    //Datatable
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [
                             DTColumnDefBuilder.newColumnDef(1).withOption('width', '1%').notSortable()
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
                            .withOption('paging', true)
                            .withOption('searching', true)
                            .withOption('info', true);

    //Creating an empty object to be sent:
    $scope.AccessType = {};



    //Opening a modal:
    $scope.openAccessTypeModal = function (id) {

        if (id != null) {
            //blockUI.start();
            //blockUI.message("Loading Access Type Details ...");
            AccessTypeServices.GetAccessTypeDetails(id).then(function (response) {
                $scope.AccessType = response.data;
                $scope.TempAccessTypeName = response.data.AccessTypeName;
            })
            .then(function () {
                //blockUI.stop();
                $('#AccessTypeModal').modal('show');
            });
        }
    };


    //Saving or updating:
    $scope.SaveOrUpdateAccessType = function () {
        if ($scope.AccessTypeForm.$invalid == false && $scope.LiveValidation($scope.AccessType.AccessTypeName) == true) {
            if ($scope.AccessType.Id == null) {
                //blockUI.start();
                //blockUI.message("Saving Access Type ...");
                AccessTypeServices.SaveAccessType($scope.AccessType).then(function (response) {
                    $('#AccessTypeModal').modal('hide');
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "AccessTypeNameExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                })
                .then(function () {
                    //Clear the models
                    $scope.cancelAccessTypeListModal();
                })
                .then(function () {
                    
                    $timeout(function () {
                        //Reload the List
                        //blockUI.message("Loading Access Type List ...");
                        AccessTypeServices.GetAccessTypeList().then(function (response) {
                            $scope.AccessTypeList = response.data;
                        });
                    }, 100)

                });
            } else {
                //blockUI.start();
                //blockUI.message("Updating Access Type ...");
                AccessTypeServices.UpdateAccessType($scope.AccessType).then(function (response) {
                    $('#AccessTypeModal').modal('hide');
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "AccessTypeNameExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                })
                .then(function () {
                    //Clear the models
                    $scope.cancelAccessTypeListModal();
                })
                .then(function () {

                    $timeout(function () {
                        //Reload the List
                        //blockUI.message("Loading Access Type List ...");
                        AccessTypeServices.GetAccessTypeList().then(function (response) {
                            $scope.AccessTypeList = response.data;
                        });
                    }, 100)

                });
            }
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }
    };

    //Deleting:
    //$scope.deleteAccessTypeAlert = function (AccessTypeId, AccessTypeName) {
    //    swal({
    //        title: "Are you sure?",
    //        text: "You are going to delete the access type " + AccessTypeName,
    //        type: "warning",
    //        showCancelButton: true,
    //        confirmButtonClass: "btn-danger",
    //        confirmButtonText: "Yes, delete it!",
    //        closeOnConfirm: true
    //    },
    //    function () {
    //        console.log(AccessTypeId + ' ' + AccessTypeName);
    //        AccessTypeServices.DeleteAccessType(AccessTypeId).then(function (response) {
    //            console.log(response.data);
    //            if (response.data.IsReport === "Ok") {
    //                toastr.success(response.data.Message, 'Successful');
    //            }
    //            else if (response.data.IsReport === "NotOk") {
    //                toastr.error(response.data.Message, 'Failed');
    //            }
    //            $state.reload();
    //        });

    //    });
    //};

    $scope.cancelAccessTypeListModal = function () {
        $('#AccessTypeModal').modal('hide');
        $timeout(function () {
            //$scope.AccessType = {};
            //$scope.TempAccessTypeName = null;
            //blockUI.stop();
             $state.reload();
        }, 300);        
        //$scope.AccessTypeForm.$setPristine();
        //$scope.AccessTypeForm.$setUntouched();
    };

    //Live Validation
    $scope.LiveValidation = function (x) {        
        var valid = false;
        if ($scope.AccessTypeList.length > 0) {
            for (var i = 0; i < $scope.AccessTypeList.length; i++) {                
                if ($scope.AccessTypeList[i].AccessTypeName == x) {                    
                    if ($scope.AccessTypeList[i].AccessTypeName == $scope.TempAccessTypeName) {
                        valid = true;
                    }
                    else {
                        valid = false;
                        $scope.LiveValidationError = "Access Type already exists";
                        break;
                    }
                }
                else {
                    valid = true;
                }
            }
        }
        else if ($scope.AccessTypeList.length == 0) {
            valid = true;
        }
        return valid;
    };
})