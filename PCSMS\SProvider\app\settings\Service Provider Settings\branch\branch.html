﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!-- main content -->

<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-building" aria-hidden="true"></i><a ui-sref="branch">Branch</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-header">
                        <button type="button" class="btn btn-default btn-o pull-right btn-addon btn-o-addon" id="btnAddBranch" data-toggle="modal" data-target="#BranchModal">
                            <i class="fa fa-plus" aria-hidden="true"></i>
                            Add Branch
                        </button>
                    </div>
                    <div class="panel-content">
                        <div class="table-responsive">
                            <!-- DataTable -->
                            <table class="data-table table table-striped nowrap table-bordered table-condensed" id="basic-table" datatable="ng" dt-options="vm.dtOptions" dt-instance="vm.dtInstance" dt-column-defs="vm.dtColumnDefs" cellspacing="0">
                                <thead>
                                    <tr class="animated fadeIn">
                                        <th>Branch</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="animated fadeIn" ng-repeat="x in BranchList">
                                        <td>{{x.BranchName}}</td>
                                        <td>
                                            <button class="btn btn-default btn-Line-Height btn-datatables" tooltip-placement="top" uib-tooltip="Edit" ng-click="openBranchModal(x.Id)" style="border:1px solid #02715a; background-color:#ffffff"><img src="Assets_SProvider/images/datatables/edit.png" width="20" /></button>
                                            <!--<button class="btn btn-default btn-Line-Height btn-datatables" ng-click="deleteBranchAlert(x.Id, x.BranchName)" style="border-color: #abaaaa"><img src="Assets_SProvider/img/datatables/delete.png" width="20" /></button>-->
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>

<form novalidate name="BranchForm">
    <div class="modal fade" id="BranchModal" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog  modal-dialog-at">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="custom-close" data-dismiss="modal" id="Branch-btn-top-close" ng-click="cancelBranchListModal()" tooltip-placement="left" uib-tooltip="Close">&times;</button>
                    <h4 ng-if="Branch.Id === undefined">Create New Branch</h4>
                    <h4 ng-if="Branch.Id >=1">Update Branch</h4>
                    <div class="form-sep">
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-10 col-md-offset-2">
                            <div class="col-md-3">
                                <label class="control-label label-for-textbox-center-direction">Branch</label>
                            </div>
                            <div class="col-md-9">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Enter Branch" name="BranchName"
                                           ng-required="true"
                                           ng-model="Branch.BranchName"
                                           ng-change="LiveValidation(Branch.BranchName)"
                                           ng-minlength="4"
                                           ng-maxlength="50"
                                           ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" />
                                    <div class="row custom-row">
                                        <ul class="list-unstyled text-danger pull-left" ng-show="LiveValidation(Branch.BranchName)==false">
                                            <li><span class="pull-left" ng-show="LiveValidation(Branch.BranchName)==false">{{LiveValidationError}}</span></li>
                                        </ul>
                                        <ul class="list-unstyled text-danger pull-left" ng-show="LiveValidation(Designation.BranchName)==false || BranchForm.BranchName.$dirty && BranchForm.BranchName.$invalid">
                                            <li><span class="pull-left" ng-show="LiveValidation(Designation.BranchName)==false">{{LiveValidationError}}</span></li>
                                            <li><span class="pull-left" ng-show="BranchForm.BranchName.$error.required">*Required</span></li>
                                            <li><span class="pull-left" ng-show="BranchForm.BranchName.$error.pattern">Start with capital letter, don't use special character.</span></li>
                                            <li><span class="pull-left" ng-show="!BranchForm.BranchName.$error.pattern && BranchForm.BranchName.$error.minlength">Minimum length is 4</span></li>
                                            <li><span class="pull-left" ng-show="!BranchForm.BranchName.$error.pattern && BranchForm.BranchName.$error.maxlength">Maximum length is 50</span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="col-md-5 col-md-offset-7">
                        <div class="btn-group btn-group-justified" role="group" aria-label="group button" ng-if="Branch.Id === undefined">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-default pull-right" ng-click="cancelBranchListModal()" role="button">Close</button>
                            </div>
                            <div class="btn-group" role="group">
                                <button ng-disabled="BranchForm.$invalid || LiveValidation(Branch.BranchName)==false"
                                        type="button"
                                        ng-click="SaveOrUpdateBranch()" class="btn btn-sm btn-primary">
                                    Save &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                </button>
                            </div>
                        </div>
                        <div class="btn-group btn-group-justified" role="group" aria-label="group button" ng-if="Branch.Id >=1">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-default pull-right" ng-click="cancelBranchListModal()" role="button">Close</button>
                            </div>
                            <div class="btn-group" role="group">
                                <button ng-disabled="BranchForm.$invalid || LiveValidation(Branch.BranchName)==false"
                                        type="button"
                                        ng-click="SaveOrUpdateBranch()" class="btn btn-sm btn-primary">
                                    Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

