﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PCSMS.Common;
using PCSMS.Models;

namespace PCSMS.Services.Services_Shared
{
    public static class GenericServices
    {
        private static readonly PCSMSDbContext _context;

        static GenericServices()
        {
            _context = new PCSMSDbContext();
        }
        
        

        public static string CreateRandomNumberWithLetter()
        {
            var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ";
            var numbers = "0123456789";
            var stringChars = new char[12];
            var random = new Random();

            for (int i = 0; i < 3; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }
            for (int i = 2; i < 7; i++)
            {
                stringChars[i] = numbers[random.Next(numbers.Length)];
            }
            for (int i = 7; i < 12; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }

            string newlyGenratedUsername = new String(stringChars);
            return newlyGenratedUsername;
        }
        public static string CreateRandomNumber1()
        {
            var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
            var stringChars = new char[4];
            var random = new Random();

            for (int i = 0; i < stringChars.Length; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }

            var finalString = new String(stringChars);
            return finalString;
        }
        public static string CreateRandomNumber2()
        {
            var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var stringChars = new char[4];
            var random = new Random();

            for (int i = 0; i < stringChars.Length; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }

            var finalString = new String(stringChars);
            return finalString;
        }
        public static string CreateRandomNumber3()
        {
            var chars = "123456789123456789";
            var stringChars = new char[4];
            var random = new Random();

            for (int i = 0; i < stringChars.Length; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }

            var finalString = new String(stringChars);
            return finalString;
        }
        public static string CreateRandomNumber4()
        {
            var chars = "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            var stringChars = new char[4];
            var random = new Random();

            for (int i = 0; i < stringChars.Length; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }

            var finalString = new String(stringChars);
            return finalString;
        }
        public static string CreateRandomNumber5()
        {
            var chars = "ABC2356PLOI123456GTY";
            var stringChars = new char[5];
            var random = new Random();

            for (int i = 0; i < stringChars.Length; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }

            var finalString = new String(stringChars);
            return finalString;
        }

        public static string CreateRandomNumberWith8Characters()
        {
            var chars = "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            var stringChars = new char[8];
            var random = new Random();

            for (int i = 0; i < stringChars.Length; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }

            var finalString = new String(stringChars);
            return finalString;
        }

        public static string GenerateLCode(int companyId)
        {
            //var lastLCodeOfaCompany = _context.CP_License.Where(x => x.CompanyId == companyId).OrderByDescending(x => x.LCode).Select(x => x.LCode).FirstOrDefault();
            var lastLCodeOfaCompany = _context.CP_License.Where(x => x.CompanyId == companyId).OrderByDescending(x => x.Id).Select(x => x.LCode).FirstOrDefault();
            var LCode = "";
            if (lastLCodeOfaCompany != null)
            {
                int lengthOfCompanyId = Convert.ToString(companyId).Length;
                int num = 7 + lengthOfCompanyId;
                string onlyLastPartofLCode = lastLCodeOfaCompany.Substring(num);
                int onlyLastPart = Int32.Parse(onlyLastPartofLCode);
                int newId = onlyLastPart + 1;
                LCode = "L#" + companyId + "00000" + newId;
            }
            else
            {
                LCode = "L#" + companyId + "00000" + 1;
            }
            return LCode;
        }

        public static string GenerateCompanyCode()
        {
            var lastCompanyCode = _context.CP_Profile.OrderByDescending(x => x.Id).Select(x => x.CompanyCode).FirstOrDefault();
            var companyCode = "";
            if (lastCompanyCode != null)
            {
                string onlyLastPartofPackageCode = lastCompanyCode.Substring(12);
                int onlyLastPart = Int32.Parse(onlyLastPartofPackageCode);
                int newId = onlyLastPart + 1;
                companyCode = "CM-" + CreateRandomNumberWith8Characters() + "-" + newId;
            }
            else
            {
                companyCode = "CM-" + CreateRandomNumberWith8Characters() + "-" + 1;
            }
            return companyCode;
        }
    }
}

    
