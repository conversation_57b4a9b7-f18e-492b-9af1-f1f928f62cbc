﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PCSMS.Models.Common;
using PCSMS.Models.Models_Shared;

namespace PCSMS.Models.Models_Company
{
    public class CP_Profile : Entity<int>
    {

        //Sign Up:
        [MaxLength(70)]
        public string CompanyName { get; set; }
        [MaxLength(70)]
        public string CompanyEmail { get; set; }
        [MaxLength(70)]
        public string CompanyPassword { get; set; }

        [MaxLength(70)]
        public string CompanyCode { get; set; }



        //Company Info :
        [ForeignKey("CompanyTypeId")]
        public virtual CompanyType CompanyType { get; set; }
        public int? CompanyTypeId { get; set; }
        [MaxLength(50)]
        public string CompanyPhone { get; set; }
        [MaxLength(50)]
        public string CompanyMobile { get; set; }
        [MaxLength(70)]
        public string CompanyWebsite { get; set; }
        [MaxLength(250)]
        public string CompanyAddress { get; set; }
        [MaxLength(250)]
        public string CompanyBillingAddress { get; set; }


        //Company Logo:
        [MaxLength(150)]
        public string CompanyLogo { get; set; }


        ////Location :
        public double? CompanyLat { get; set; }
        public double? CompanyLong { get; set; }
        //[ForeignKey("CountryId")]
        //public virtual Country Country { get; set; }
        //public int? CountryId { get; set; }
        //[ForeignKey("DivisionId")]
        //public virtual Division Division { get; set; }
        //public int? DivisionId { get; set; }
        //[ForeignKey("ZoneId")]
        //public virtual Zone Zone { get; set; }
        //public int? ZoneId { get; set; }
        //[ForeignKey("AreaId")]
        //public virtual Area Area { get; set; }
        //public int? AreaId { get; set; }
        


        //Contact Person Info :
        [MaxLength(50)]
        public string ContactPerson { get; set; }
        [MaxLength(50)]
        public string ContactPersonPhone { get; set; }
        [MaxLength(50)]
        public string ContactPersonMobile { get; set; }
        [MaxLength(50)]
        public string ContactPersonEmail { get; set; }
        [MaxLength(50)]
        public string ContactPersonDesignation { get; set; }

        //Miscellaneous: All Dates
        public DateTime? RegistrationDate { get; set; }

        

    }
}
