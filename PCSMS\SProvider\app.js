﻿var PCSMSApp = angular.module("PCSMSApp", ['ui.router', 'ngMessages', 'datatables', 'toastr', 'ngSanitize', 'ngFileUpload', '720kb.tooltips', '720kb.datepicker', 'ngAnimate', 'ui.bootstrap', 'ui.bootstrap.datetimepicker',"ngCookies", "base64", "ngFileUpload", "blockUI"]);


PCSMSApp.service('authInterceptor', function ($q, $window, $rootScope, $timeout, $injector, $cookies) {
    var service = this;


    service.request = function (response) {
        $rootScope.response = true;
        return $q.resolve(response);
    };
    service.response = function (response) {
        //console.log(response);
        $rootScope.response = false;
        return $q.resolve(response);
    };
    service.responseError = function (response) {
        $rootScope.spUsernameOrPasswordNotMatched = false;

        function goToLoginPageAndClearLocalStorage() {
            $window.location.href = "/SProvider/#/logIn";
            $cookies.remove('SProvider_Token_Codeaura_Demo', { path: '/' });
            $cookies.remove('SProvider_Details_Codeaura_Demo', { path: '/' });
        }

        var toastr = $injector.get('toastr');
        var $state = $injector.get('$state');


        //If token value expires or wrong token provided:
        if (response.status === 401) {
            goToLoginPageAndClearLocalStorage();
            $rootScope.spUnauthorizedRequestFound = true;
        }
        else if (response.status === 500) {
            //goToLoginPageAndClearLocalStorage();
            toastr.error("Internal Server Error! Please try again !", {
                timeOut: 3000
            });
            $rootScope.spInternalServerErrorFound = true;
        }

        else if (response.status === 403) {
            $state.reload();
            $rootScope.spUsernameOrPasswordNotMatched = true;
        }
        return $q.reject(response);
    };

});


PCSMSApp.config(function ($stateProvider, $urlRouterProvider, $httpProvider, toastrConfig, blockUIConfig) {

    $httpProvider.interceptors.push('authInterceptor', function ($q, $cookies, $rootScope) {
        return {
            'request': function (config) {
                var SProvider_Token_Codeaura_Demo;
                var SProvider_Token_Codeaura_DemoFromCookies = $cookies.get('SProvider_Token_Codeaura_Demo');
                if (SProvider_Token_Codeaura_DemoFromCookies) {
                    SProvider_Token_Codeaura_Demo = JSON.parse(SProvider_Token_Codeaura_DemoFromCookies);
                } else {
                    SProvider_Token_Codeaura_Demo = null;
                }

                config.headers['Token'] = SProvider_Token_Codeaura_Demo;
                return config;
            }
        };
    });

    angular.extend(toastrConfig, {
        autoDismiss: false,
        containerId: 'toast-container',
        maxOpened: 0,
        newestOnTop: true,
        positionClass: 'toast-top-right',
        preventDuplicates: false,
        preventOpenDuplicates: false,
        target: 'body',
        progressBar: true,
        allowHtml: true
    });

    blockUIConfig.message = 'Please Wait ...';

    $urlRouterProvider.otherwise('/logIn');
    $stateProvider

        //-------------------------------------------------------------LOGIN-----------------------------------------------------
        .state('logIn', {
            url: '/logIn',
            templateUrl: 'app/logIn/logIn.html',
            controller: 'logInController'

        })
        //-------------------------------------------------------------HOME-----------------------------------------------------
        .state('home', {
            url: '/home',
            templateUrl: 'app/home/<USER>',
            controller: 'homeController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })

        //-------------------------------------------------------------PROFILE-----------------------------------------------------
        .state('profile', {
            url: '/profile',
            templateUrl: 'app/profile/profile.html',
            controller: 'profileController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })

        //-------------------------------------------------------------SETTINGS----------------------------------------------------
        .state("branch", {
            url: "/branch",
            templateUrl: "app/settings/Service Provider Settings/branch/branch.html",
            controller: "branchController",
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        .state("designation", {
            url: "/designation",
            templateUrl: "app/settings/Service Provider Settings/designation/designation.html",
            controller: "designationController",
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        .state("user", {
            url: "/user",
            templateUrl: "app/settings/Service Provider Settings/user/user.html",
            controller: "userController",
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        .state("userprofile", {
            url: "/userprofile",
            templateUrl: "app/settings/Service Provider Settings/user_profile/user profile.html",
            controller: "user_profileController",
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        .state("accesstype", {
            url: "/accesstype",
            templateUrl: "app/settings/Service Provider Settings/access_type/access type.html",
            controller: "access_typeController",
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        .state("companytype", {
            url: "/companytype",
            templateUrl: "app/settings/Service Provider Settings/company_type/company type.html",
            controller: "company_typeController",
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })


        //-------------------------------------------------------------MANAGE COMPANY----------------------------------------------
        .state("onlyregisteredcompany", {
            url: "/onlyregisteredcompany",
            templateUrl: "app/managecompany/onlyregisteredcompany/onlyregisteredcompany.html",
            controller: "onlyregisteredcompanyController",
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        .state("subscribedcompany", {
            url: "/subscribedcompany",
            templateUrl: "app/managecompany/subscribedcompany/subscribedcompany.html",
            controller: "subscribedcompanyController",
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })

        //-------------------------------------------------------------LICENSE-----------------------------------------------------
        .state('license', {
            url: '/license',
            templateUrl: 'app/license/license.html',
            controller: 'licenseController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
    

        //-------------------------------------------------------------DATABASE-----------------------------------------------------
        .state('database', {
            url: '/database',
            templateUrl: 'app/database/database.html',
            controller: 'databaseController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })

});

function checkAuthentication($q, customServices, $state, $timeout, $window, toastr, $rootScope, $cookies) {
    if (customServices.isLoggedIn()) {
        // Resolve the promise successfully
        return $q.when();
    }
    else {
        $timeout(function () {
            if ($rootScope.UserDetails) {
                toastr.error("Session expired, Please login again !", {
                    timeOut: 2000
                });
                $window.location.href = "/SProvider/#/logIn";
                $cookies.remove('SProvider_Details_Codeaura_Demo', { path: '/' });
            } else {
                toastr.error("You seem to be unauthorized, Please login !", {
                    timeOut: 2000
                });
                $window.location.href = "/SProvider/#/logIn";
            }


        });
        // Reject the authentication promise to prevent the state from loading
        return $q.reject();
    }
}


PCSMSApp.run(function ($http, $rootScope, $window, $location, $state, toastr, $timeout, $q, appServices, $cookies, customServices) {
    //========On $stateChangeStart Event========
    $rootScope.$on('$stateChangeStart', function (event, toState, toParams, fromState, fromParams) {
        //Getting Token and Other objects :
        $rootScope.SProvider_Token_Codeaura_DemoFromCookies = $cookies.get('SProvider_Token_Codeaura_Demo');
        if ($rootScope.SProvider_Token_Codeaura_DemoFromCookies) {
            $rootScope.SProvider_Token_Codeaura_Demo = JSON.parse($rootScope.SProvider_Token_Codeaura_DemoFromCookies);
        }

        $rootScope.SProvider_Details_Codeaura_DemoFromCookies = $cookies.get('SProvider_Details_Codeaura_Demo');
        if ($rootScope.SProvider_Details_Codeaura_DemoFromCookies) {
            $rootScope.SProvider_Details_Codeaura_Demo = JSON.parse($rootScope.SProvider_Details_Codeaura_DemoFromCookies);
            $rootScope.UserId = $rootScope.SProvider_Details_Codeaura_Demo.Id;
            $rootScope.UserFirstName = $rootScope.SProvider_Details_Codeaura_Demo.FirstName;
            $rootScope.UserLastName = $rootScope.SProvider_Details_Codeaura_Demo.LastName;
            if ($rootScope.SProvider_Details_Codeaura_Demo.FirstName != null || $rootScope.SProvider_Details_Codeaura_Demo.LastName != null) {
                $rootScope.UserFullName = $rootScope.SProvider_Details_Codeaura_Demo.FirstName + ' ' + $rootScope.SProvider_Details_Codeaura_Demo.LastName;
            }
            else {
                $rootScope.UserFullName = "Super User";
            }
            $rootScope.UserAccessTypeName = $rootScope.SProvider_Details_Codeaura_Demo.AccessTypeName;
            $rootScope.UserAccessTypeId = $rootScope.SProvider_Details_Codeaura_Demo.AccessTypeId;
            if ($rootScope.SProvider_Details_Codeaura_Demo.Photo != null) {
                $rootScope.UserPhoto = '../SProvider_Images/User_Images/' + $rootScope.SProvider_Details_Codeaura_Demo.Photo;
            }
            else {
                if ($rootScope.SProvider_Details_Codeaura_Demo.Gender == null) {
                    $rootScope.UserPhoto = '../SProvider_Images/Default_Images/Image-not-found.png';
                }
                else if ($rootScope.SProvider_Details_Codeaura_Demo.Gender != null) {
                    if ($rootScope.SProvider_Details_Codeaura_Demo.Gender == "Male") {
                        $rootScope.UserPhoto = '../SProvider_Images/Default_Images/male.png';
                    }
                    else if ($rootScope.SProvider_Details_Codeaura_Demo.Gender == "Female") {
                        $rootScope.UserPhoto = '../SProvider_Images/Default_Images/female.png';
                    }
                }
            }
        }

        //Assume, user is logged-in and closes his browser and now he reopens the browser
        //three phrases are found:
        //1. he starts his journey from unknown state
        //2. our app will commence journey from logIn state
        //3. he could be logged in

        var userIsLoggedIn = customServices.isLoggedIn();

        var unKnownState = "^";
        if (fromState.url === unKnownState && toState.url === "/logIn" && userIsLoggedIn) {
            $window.location.href = "/SProvider/#/home";

        }


            //If user is logged-in and wants to go to logIn page again, To restrict him:
        else if (toState.url === "/logIn" && userIsLoggedIn) {
            event.preventDefault();
            toastr.error("You are already logged in, hence you can not go to login page !", {
                timeOut: 3000
            });
            $window.location.href = "/SProvider/#" + fromState.url;
        }

        //Updating cookies :
            if ($rootScope.SProvider_Token_Codeaura_DemoFromCookies) {
                var date = new Date();
                var expireTime = date.getTime() + 10800000; // 3 hours
                date.setTime(expireTime);
                $cookies.put('SProvider_Token_Codeaura_Demo', $cookies.get('SProvider_Token_Codeaura_Demo'), { 'expires': date, 'path': '/' });
                $cookies.put('SProvider_Details_Codeaura_Demo', $cookies.get('SProvider_Details_Codeaura_Demo'), { 'expires': date, 'path': '/' });
            }
    });


    //========On $stateChangeSuccess Event========
    $rootScope.$on("$stateChangeSuccess", function (event, toState, toParams, fromState, fromParams) {
        $rootScope.HTMLCollapseStatus = $cookies.get('HTMLCollapseStatus');
        if ($rootScope.HTMLCollapseStatus == null || $rootScope.HTMLCollapseStatus == undefined) {
            $rootScope.HTMLCollapseStatus = "fixed left-sidebar-top";
        }
        $rootScope.settingsOpen = false;
        $rootScope.managecompanyOpen = false;
    });
});





