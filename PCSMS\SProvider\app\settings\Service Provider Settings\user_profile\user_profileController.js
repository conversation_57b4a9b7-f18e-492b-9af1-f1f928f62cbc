﻿/// <reference path="../../../app.js" />
PCSMSApp.controller('user_profileController', function ($scope, $rootScope, UserProfileServices, blockUI, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {

    //Funtion used/called later:
    function updateCookieWithSProvider_BANKDEMO_UserDetails(name, value) {
        var extendedTime = new Date();
        var universalTime = new Date(extendedTime.getTime() + extendedTime.getTimezoneOffset() * 60000);
        var expireTime = universalTime.getTime() + ********; // 3 hours
        //var expireTime = universalTime.getTime() + 10000; // 10 seconds
        extendedTime.setTime(expireTime);
        document.cookie = name + '=' + value + '; Path=/; Expires=' + extendedTime + ';';
    };

    var currentDate = new Date();
    $scope.MaxDate = currentDate.setDate(currentDate.getDate() - 1);
    $scope.MinDate = currentDate.setYear(currentDate.getFullYear() - 90);
    $scope.User = {};
    $scope.User.Id = $rootScope.UserId;
    $scope.HasImage = false;
    $scope.ChangedImage = false;

    //Retrieving data from db:
    $scope.UserList = UserProfileServices.GetSP_UserList().then(function (response) {
        $scope.UserList = response.data;
        return response.data;
    });
    UserProfileServices.GetSP_BranchList().then(function (response) {
        $scope.BranchList = response.data;
        return response.data;
    });
    UserProfileServices.GetAccessTypeList().then(function (response) {
        $scope.AccessTypeList = response.data;
        return response.data;
    });
    UserProfileServices.GetSP_DesignationList().then(function (response) {
        $scope.DesignationList = response.data;
        //console.log("----List of User----");
        //console.log($scope.DesignationList);
        return response.data;
    });
    UserProfileServices.GetSP_UserDetails($scope.User.Id).then(function (response) {
        $scope.User = response.data;
        $scope.TempEmail = response.data.Email;
        if ($scope.User.Photo != null) {
            $('#imageHolder').prepend('<img id="imageTag" class="animated fadeIn" src="" />');
            $('#imageTag').attr("src", "../SProvider_Images/User_Images/" + $scope.User.Photo);
            $scope.src = "../SProvider_Images/User_Images/" + $scope.User.Photo;
            $scope.HasImage = true;
            $scope.ChangedImage = false;
            //imageName = response.data.CompanyLogo ;
        }
        else if ($scope.User.Photo == null) {
            $scope.src = "../SProvider_Images/Default_Images/Image-not-found.png";
            $scope.HasImage = false;
        }
    })

    
    //Update Personal Info
    $scope.UpdateUserFormPersonalInfo = function () {
        if ($scope.UserFormPersonalInfo.$invalid == false && $scope.LiveValidation($scope.User.Email) == true) {
            //console.log("===Outside===");
            //console.log($scope.User);
            //$scope.User.Password = "123456";
            //Should Be Changed
            //blockUI.start();
            //blockUI.message("Updating Personal Info ...");
            UserProfileServices.UpdateSP_UserPersonalInfo($scope.User).then(function (response) {
                //console.log(response.data);
                if (response.data.IsReport === "Ok") {
                    toastr.success(response.data.Message, 'Successful');
                }
                else if (response.data.IsReport === "NotOk") {
                    toastr.error(response.data.Message, 'Failed');
                }
                else if (response.data.IsReport === "UserExists") {
                    toastr.warning(response.data.Message, 'Already Exist');
                }
            })
            .then(function () {
                //blockUI.start();
                $timeout(function () {
                    $state.reload();
                    //blockUI.stop();
                })

            });
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }
    };

    //Update Login Credentials
    $scope.UpdateUserFormCredentials = function () {
        if ($scope.UserFormCredentials.$invalid == false && $scope.LiveValidationPassword($scope.User.ExistingPassword, $scope.User.NewPassword) == true) {
            //console.log("===Outside===");
            //console.log($scope.User);
            //$scope.User.Password = "123456";
            //Should Be Changed
            //blockUI.start();
            //blockUI.message("Updating Credentials ...");
            UserProfileServices.ChangeSP_UserPassword($rootScope.UserId, $scope.User.ExistingPassword, $scope.User.NewPassword).then(function (response) {
                //console.log(response.data);
                if (response.data.IsReport === "Ok") {
                    toastr.success(response.data.Message, 'Successful');
                }
                else if (response.data.IsReport === "NotOk") {
                    toastr.error(response.data.Message, 'Failed');
                }
                else if (response.data.IsReport === "ExistingAndNewPasswordSame") {
                    toastr.warning(response.data.Message, 'Already Exist');
                }
                else if (response.data.IsReport === "ExistingPasswordDoesNotMatch") {
                    toastr.error(response.data.Message, 'Error!');
                }
            })
            .then(function () {
                //blockUI.start();
                $timeout(function () {
                    $state.reload();
                    //blockUI.stop();
                })

            });
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }
    }

    //Update Profile Picture
    $scope.UpdateUserFormProfilePicture = function () {
        if ($scope.HasImage == true && $scope.ChangedImage == true) {
            //blockUI.start();
            //blockUI.message("Updating Profile Picture ...");
            UserProfileServices.UploadSP_UserPhoto($rootScope.UserId, formdata).then(function (response) {
                if (response.data.IsReport == "NotOk") {
                    toastr.error(response.data.Message, "Error!");
                }
                else if (response.data.IsReport == "Ok") {
                    toastr.success(response.data.Message, "Success!");

                    UserProfileServices.GetSP_UserDetails($rootScope.UserId).then(function (response) {
                        if (response.data) {
                            //updating Cookie With CompanyPckgPrivileges
                            updateCookieWithSProvider_BANKDEMO_UserDetails('SProvider_BANKDEMO_UserDetails', angular.toJson(response.data));
                            ////console.log(angular.toJson(response.data));
                        }
                    });
                }
            })
            .then(function () {
                //blockUI.start();
                $timeout(function () {
                    $state.reload();
                    //blockUI.stop();
                })

            });
        }
        else {
            if ($scope.HasImage == false) {
                toastr.error("There is no image to be uploaded", 'Error!');
            }
            else if ($scope.ChangedImage == false) {
                toastr.error("Please choose a new image to upload", 'Error!');
            }
        }
    };

    //Update Administrative Info
    $scope.UpdateUserFormAdministrativeInfo = function () {
        $scope.LoggedInUserObj = {
            LoggedInUserId: $rootScope.UserId,
        }
        UserProfileServices.UpdateSP_UserAdministrativeInfo($scope).then(function (response) {
            $('#UserModal').modal('hide');
            //console.log(response.data);
            if (response.data.IsReport === "Ok") {
                toastr.success(response.data.Message, 'Successful');
            }
            else if (response.data.IsReport === "NotOk") {
                toastr.error(response.data.Message, 'Failed');
            }
            else if (response.data.IsReport === "UserExists") {
                toastr.warning(response.data.Message, 'Already Exist');
            }
        })
        .then(function () {
            $state.reload();
        })
    }

    //Live Validation
    $scope.LiveValidation = function (x) {
        var valid = false;
        if ($scope.UserList.length > 0) {
            for (var i = 0; i < $scope.UserList.length; i++) {
                if ($scope.UserList[i].Email == x) {
                    //console.log($scope.UserList[i].Email);
                    if ($scope.UserList[i].Email == $scope.TempEmail) {
                        valid = true;
                    }
                    else {
                        valid = false;
                        $scope.LiveValidationError = "Username/email already exists";
                        break;
                    }
                }
                else {
                    valid = true;
                }
            }
        }
        else if ($scope.UserList.length == 0) {
            valid = true;
        }
        return valid;
    };

    $scope.LiveValidationPassword = function (oldPassword, newPassword) {
        if (oldPassword != undefined && newPassword != undefined) {
            var valid = false;
            if (oldPassword != newPassword) {
                valid = true;
            }
            else if (oldPassword == newPassword) {
                valid = false;
                $scope.LiveValidationPasswordError = "Existing password and new password are same. Try a new one.";
            }
        }
        else {
            valid = true;
        }
        return valid;
    }


    //Profile Picture Upload
    var formdata = new FormData();
    $scope.getTheFiles = function ($files) {
        $timeout(function () {
            formdata.append(0, $files[0]);
        })
        .then(function () {
            $scope.ChangedImage = true;
            $scope.HasImage = true;
        })
    };


    //Only For Administrative Info
    $scope.FilterAccessType = function (model) {
        return function (model) {
            if (model.Id < $rootScope.UserAccessTypeId) {
                return false;
            }
            return true;
        };
    } //ng-options filter


})