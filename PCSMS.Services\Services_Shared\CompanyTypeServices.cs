﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web.Mvc;
using PCSMS.Common;
using PCSMS.Models;
using PCSMS.Models.Models_Shared;

namespace PCSMS.Services.Services_Shared
{
    public class CompanyTypeServices: ICompanyTypeServices
    {
        private readonly PCSMSDbContext _context;
        private readonly IEntityService<CompanyType> _services;
        public CompanyTypeServices()
        {
            _context = new PCSMSDbContext();
            _services = new EntityService<CompanyType>(_context);
        }

        //Private Services :
        private bool CompanyTypeExist(CompanyType obj)
        {
            var result = _context.CompanyType.FirstOrDefault(x => x.CompanyTypeName == obj.CompanyTypeName && x.Id != obj.Id);

            if (result == null)
            {
                return false;
            }
            return true;
        }


        //Public Services :
        public JsonResult SaveCompanyType(CompanyType obj)
        {
            string message;
            try
            {
                if (_services.DoesExist(x => x.CompanyTypeName == obj.CompanyTypeName))
                {
                    Generator.IsReport = "CompanyTypeNameExists";
                    message = "This Company-Type already exists !";
                }
                else
                {
                    _services.Save(obj);
                    _services.SaveChanges();

                    Generator.IsReport = "Ok";
                    message = "Company-Type created successfully !";
                }
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult UpdateCompanyType(CompanyType obj)
        {
            List<object> avoidProperties = new List<object>();
            string message;

            if (CompanyTypeExist(obj))
            {
                Generator.IsReport = "CompanyTypeNameExists";
                message = "Company-Type : " + "\"" + obj.CompanyTypeName + "\"" + " already exists !";

            }
            else
            {
                try
                {
                    _services.Update(obj, avoidProperties);
                    _services.SaveChanges();
                    Generator.IsReport = "Ok";
                    message = "Company-Type updated successfully !";
                }
                catch (Exception ex)
                {
                    message = ex.Message;
                    Generator.IsReport = "NotOk";
                }
            }

            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult DeleteCompanyType(int companyTypeId)
        {
            string message;
            try
            {
                _context.Database.ExecuteSqlCommand($"Delete [CompanyType] WHERE Id = {companyTypeId}");
                _context.SaveChanges();
                Generator.IsReport = "Ok";
                message = "Company-Type deleted successfully !";
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult GetCompanyTypeList()
        {
            return new JsonResult
            {
                Data = _context.CompanyType.Select(x => new
                {
                    x.Id,
                    x.CompanyTypeName
                }).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult GetCompanyTypeDetails(Expression<Func<CompanyType, bool>> expression)
        {
            return new JsonResult
            {
                Data = _context.CompanyType.Where(expression)
                .Select(x => new
                {
                    x.Id,
                    x.CompanyTypeName
                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
    }

    public interface ICompanyTypeServices
    {
        JsonResult DeleteCompanyType(int companyTypeId);
        JsonResult GetCompanyTypeDetails(Expression<Func<CompanyType, bool>> expression);
        JsonResult GetCompanyTypeList();
        JsonResult SaveCompanyType(CompanyType obj);
        JsonResult UpdateCompanyType(CompanyType obj);
    }
}
