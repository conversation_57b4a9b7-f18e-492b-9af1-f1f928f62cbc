﻿using System.Linq;
using System.Web.Http;
using Newtonsoft.Json.Linq;
using PCSMS.Filters;
using PCSMS.Models.Models_Company;
using PCSMS.Models.Models_SProvider;
using PCSMS.Models.Models_Temp;
using PCSMS.Services.Services_Company;
using PCSMS.Services.Services_SProvider;

namespace PCSMS.Controllers
{
    [AuthorizationRequired]
    [RoutePrefix("Api/CP_User")]
    public class CP_UserController : ApiController
    {
        private readonly ICP_UserServices _cpUserServices;
        public CP_UserController()
        {
            _cpUserServices = new CP_UserServices();
        }

        //Company Dashboard methods

        [Route("InviteNewUser")]
        [HttpPost]
        public IHttpActionResult SaveCP_User(JObject jObject)
        {
            dynamic jsonData = jObject;

            //userObj
            JObject userObjJson = jsonData.UserObj;
            var userObj = userObjJson.ToObject<CP_User>();
            


            return Ok(_cpUserServices.InviteNewUser(userObj).Data);
        }

        [Route("UpdateCP_UserProfile")]
        [HttpPost]
        public IHttpActionResult UpdateCP_UserProfile(JObject jObject)
        {
            dynamic jsonData = jObject;

            //userObj
            JObject userObjJson = jsonData.UserObj;
            var userObj = userObjJson.ToObject<CP_User>();



            return Ok(_cpUserServices.UpdateCP_UserProfile(userObj).Data);
        }

        [Route("DeleteCP_User/{userId:int}/{companyId:int}")]
        [HttpPost]
        public IHttpActionResult DeleteCP_User(int userId, int companyId)
        {
            return Ok(_cpUserServices.DeleteCP_User(userId, companyId).Data);
        }

        [Route("GetCP_UserListByCompanyId/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetCP_UserListByCompanyId(int companyId)
        {
            return Ok(_cpUserServices.GetCP_UserListByCompanyId(companyId).Data);
        }

        [Route("GetCP_UserDetails/{id:int}")]
        [HttpGet]
        public IHttpActionResult GetCP_UserDetails(int id)
        {
            return Ok(_cpUserServices.GetCP_UserDetails(id).Data);
        }


        //home services:

        [Route("GetLoggedInUserList/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetLoggedInUserList(int companyId)
        {
            return Ok(_cpUserServices.GetLoggedInUserList(companyId).Data);
        }

        [Route("GetCountOfActiveAndBlockedUsersByCompanyId/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetCountOfActiveAndBlockedUsersByCompanyId(int companyId)
        {
            return Ok(_cpUserServices.GetCountOfActiveAndBlockedUsersByCompanyId(companyId).Data);
        }

        [Route("GetCountOfRegisteredDevicesByCompanyId/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetCountOfRegisteredDevicesByCompanyId(int companyId)
        {
            return Ok(_cpUserServices.GetCountOfRegisteredDevicesByCompanyId(companyId).Data);
        }

        [Route("GetCountOfLicensesByCompanyId/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetCountOfLicensesByCompanyId(int companyId)
        {
            return Ok(_cpUserServices.GetCountOfLicensesByCompanyId(companyId).Data);
        }

    }
}
