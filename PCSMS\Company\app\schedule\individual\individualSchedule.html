﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!-- CONTENT -->
<!-- ========================================================= -->
<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-calendar-check-o" aria-hidden="true"></i><a ui-sref="device">Individual Schedule Settings</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-content">
                        <div class="panel-header">
                            <!--<button type="button" class="btn btn-default btn-o pull-right btn-addon btn-o-addon" ng-click="Requestlicense()">
                                <i class="fa fa-key" aria-hidden="true"></i>
                                Request For New License
                            </button>-->
                        </div>
                        <div class="panel-content">
                            <div class="table-responsive">
                                <!-- DataTable -->
                                <table class="data-table table table-striped nowrap table-bordered table-condensed" id="basic-table" datatable="ng" dt-options="vm.dtOptions" dt-instance="vm.dtInstance" dt-column-defs="vm.dtColumnDefs" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Device Name</th>
                                            <th>Device Id</th>
                                            <th>Settings</th>
                                            <th class="custom-datatabel-action-th">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="x in NonGroupDeviceList">
                                            <td>{{x.DeviceName}}</td>
                                            <td>{{x.DeviceUniqueId}}</td>
                                            <td>{{x.Settings}}</td>
                                            <td>
                                                <button style="border: 1px solid #02715a; background-color: #ffffff"
                                                        type="button" tooltip-placement="top"
                                                        uib-tooltip="View Details" class="btn btn-default"
                                                        ng-click="GetDeviceDetailsForScheduleUpdate(x.Id);">
                                                    <img src="Assets_Company/images/datatables/edit.png" width="20" />
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>
<div class="modal fade" id="DeviceDetailsModal" tabindex="-1" role="dialog" aria-labelledby="modal-label" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header" style="height:65px">
                <button type="button" class="custom-close" ng-click="cancelDeviceDetailsModal()" style="margin-top:0px;" aria-label="Close" tooltip-placement="left" uib-tooltip="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" style="margin-top:1.6%">Individual Device Schedule Settings</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-primary panel-bordered">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-2">
                                <div class="img-responsive">
                                    <img src="Assets_Company/images/datatables/cms.png" style="width: 100%" />
                                </div>
                            </div>
                            <div class="col-lg-offset-3 col-lg-7">
                                <div class="table-responsive">
                                    <table class="table  table-bordered ">
                                        <tbody>
                                            <tr>
                                                <th width="30%">Device Name</th>
                                                <td><label class="control-label">{{Schedule.DeviceName}}</label></td>
                                            </tr>
                                            <tr>
                                                <th width="30%">Device Id</th>
                                                <td><label class="control-label">{{Schedule.DeviceUniqueId}}</label></td>
                                            </tr>
                                            <tr>
                                                <td colspan="2">
                                                    <img src="Assets_Company/images/datatables/settings.png" class="pull-left" style="width: 5%"/>
                                                    <label class="control-label" style="margin-left: 10px;">{{Schedule.Settings}} on this pc</label>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <form name="IndividualScheduleForm" class="form-horizontal" novalidate>
                            <div class="row">
                                <div class="col-md-12">
                                    <!--Mandatory Fields Related to Capturing (01)-->
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="panel panel-primary panel-bordered">
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <!--CapturingStartTime-->
                                                        <div class="col-lg-3">
                                                            <div class="form-group-sm">
                                                                <label class="control-label">Capture Start Time<span class="required">*</span></label>
                                                                <div uib-timepicker="" ng-model="Schedule.StartTime" max="Schedule.EndTime" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ng-required="true" class="ng-pristine ng-untouched ng-valid ng-isolate-scope ng-not-empty ng-valid-time ng-valid-required" required="required">
                                                                    <table class="uib-timepicker">
                                                                        <tbody>
                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                <td class="uib-increment hours"><button class="btn-link" ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}" ng-disabled="noIncrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                <td>&nbsp;</td>
                                                                                <td class="uib-increment minutes"><button class="btn-link" ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}" ng-disabled="noIncrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                <td ng-show="showSeconds" class="uib-increment seconds ng-hide"><button class="btn-link" ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}" ng-disabled="noIncrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                <td ng-show="showMeridian" class=""></td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="form-group uib-time hours" ng-class="{'has-error': invalidHours}">
                                                                                    <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-hours ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementHours()" ng-blur="blur()">
                                                                                </td>
                                                                                <td class="uib-separator" style="font-weight:bold">:</td>
                                                                                <td class="form-group uib-time minutes" ng-class="{'has-error': invalidMinutes}">
                                                                                    <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-minutes ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementMinutes()" ng-blur="blur()">
                                                                                </td>
                                                                                <td ng-show="showSeconds" class="uib-separator ng-hide" style="font-weight:bold">:</td>
                                                                                <td class="form-group uib-time seconds ng-hide" ng-class="{'has-error': invalidSeconds}" ng-show="showSeconds">
                                                                                    <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-seconds ng-not-empty ng-valid-maxlength" ng-readonly="readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementSeconds()" ng-blur="blur()">
                                                                                </td>
                                                                                <td ng-show="showMeridian" class="uib-time am-pm"><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-date text-center ng-binding disabled" style="height:30px; padding-top:4px; padding-bottom:4px" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="0" disabled="disabled">AM</button></td>
                                                                            </tr>
                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                <td class="uib-decrement hours" style="padding-top:3px"><button class="btn-link" ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}" ng-disabled="noDecrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                <td>&nbsp;</td>
                                                                                <td class="uib-decrement minutes" style="padding-top:3px"><button class="btn-link" ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}" ng-disabled="noDecrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                <td ng-show="showSeconds" class="uib-decrement seconds ng-hide" style="padding-top:3px"><button class="btn-link" ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}" ng-disabled="noDecrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                <td ng-show="showMeridian" class=""></td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                            <br>
                                                        </div>
                                                        <!--CapturingEndTime-->
                                                        <div class="col-lg-3 separator">
                                                            <div class="form-group-sm">
                                                                <label class="control-label">Capture End Time<span class="required">*</span></label>
                                                                <div uib-timepicker="" ng-model="Schedule.EndTime" min="Schedule.StartTime" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ng-required="true" class="ng-pristine ng-untouched ng-valid ng-isolate-scope ng-not-empty ng-valid-time ng-valid-required" required="required">
                                                                    <table class="uib-timepicker">
                                                                        <tbody>
                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                <td class="uib-increment hours"><button class="btn-link" ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}" ng-disabled="noIncrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                <td>&nbsp;</td>
                                                                                <td class="uib-increment minutes"><button class="btn-link" ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}" ng-disabled="noIncrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                <td ng-show="showSeconds" class="uib-increment seconds ng-hide"><button class="btn-link" ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}" ng-disabled="noIncrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                <td ng-show="showMeridian" class=""></td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="form-group uib-time hours" ng-class="{'has-error': invalidHours}">
                                                                                    <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-hours ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementHours()" ng-blur="blur()">
                                                                                </td>
                                                                                <td class="uib-separator" style="font-weight:bold">:</td>
                                                                                <td class="form-group uib-time minutes" ng-class="{'has-error': invalidMinutes}">
                                                                                    <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-minutes ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementMinutes()" ng-blur="blur()">
                                                                                </td>
                                                                                <td ng-show="showSeconds" class="uib-separator ng-hide" style="font-weight:bold">:</td>
                                                                                <td class="form-group uib-time seconds ng-hide" ng-class="{'has-error': invalidSeconds}" ng-show="showSeconds">
                                                                                    <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-seconds ng-not-empty ng-valid-maxlength" ng-readonly="readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementSeconds()" ng-blur="blur()">
                                                                                </td>
                                                                                <td ng-show="showMeridian" class="uib-time am-pm"><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-date text-center ng-binding disabled" style="height:30px; padding-top:4px; padding-bottom:4px" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="0" disabled="disabled">PM</button></td>
                                                                            </tr>
                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                <td class="uib-decrement hours" style="padding-top:3px"><button class="btn-link" ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}" ng-disabled="noDecrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                <td>&nbsp;</td>
                                                                                <td class="uib-decrement minutes" style="padding-top:3px"><button class="btn-link" ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}" ng-disabled="noDecrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                <td ng-show="showSeconds" class="uib-decrement seconds ng-hide" style="padding-top:3px"><button class="btn-link" ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}" ng-disabled="noDecrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                <td ng-show="showMeridian" class=""></td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                            <br>
                                                        </div>
                                                        <div class="col-lg-6">
                                                            <!--Capturing Interval-->
                                                            <div class="form-group-sm">
                                                                <div class="row">
                                                                    <div class="col-lg-5">
                                                                        <label class="control-label">Capture Interval<span class="required">*</span></label>
                                                                    </div>
                                                                    <div class="col-lg-7">
                                                                        <select class="form-control" name="Timing"
                                                                                ng-model="Schedule.Interval"
                                                                                ng-required="true"
                                                                                ng-options="Timing.Id as Timing.Timing for Timing in TimingList">
                                                                            <option value="">Select</option>
                                                                            <option ng-repeat="e in TimingList" ng-selected="Schedule.Interval==e.Id" value="{{e.Id}}">{{e.Timing}}</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <br>
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="panel panel-primary panel-bordered">
                                                <div class="panel-body">
                                                    <!--ng-WeekDays-->
                                                    <div class="form-group-sm">
                                                        <div class="row">
                                                            <div class="col-lg-3"><label class="control-label" style="margin-top:16px">Day (s) of the week<span class="required">*</span></label></div>
                                                            <div class="col-lg-9">
                                                                <div class="col-sm-4">
                                                                    <div class="checkbox">
                                                                        <label>
                                                                            <input type="checkbox" value="Saturday" ng-model="Schedule.Sat" ng-true-value="'Y'" ng-false-value="'N'">
                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                            Saturday
                                                                        </label>
                                                                    </div>
                                                                    <div class="checkbox">
                                                                        <label>
                                                                            <input type="checkbox" value="" ng-model="Schedule.Sun" ng-true-value="'Y'" ng-false-value="'N'">
                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                            Sunday
                                                                        </label>
                                                                    </div>
                                                                    <div class="checkbox">
                                                                        <label>
                                                                            <input type="checkbox" value="" ng-model="Schedule.Mon" ng-true-value="'Y'" ng-false-value="'N'">
                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                            Monday
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-4">
                                                                    <div class="checkbox">
                                                                        <label>
                                                                            <input type="checkbox" value="" ng-model="Schedule.Tues" ng-true-value="'Y'" ng-false-value="'N'">
                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                            Tuesday
                                                                        </label>
                                                                    </div>
                                                                    <div class="checkbox">
                                                                        <label>
                                                                            <input type="checkbox" value="" ng-model="Schedule.Wed" ng-true-value="'Y'" ng-false-value="'N'">
                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                            Wednesday
                                                                        </label>
                                                                    </div>
                                                                    <div class="checkbox">
                                                                        <label>
                                                                            <input type="checkbox" value="" ng-model="Schedule.Thurs" ng-true-value="'Y'" ng-false-value="'N'">
                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                            Thursday
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-sm-4">
                                                                    <div class="checkbox">
                                                                        <label>
                                                                            <input type="checkbox" value="" ng-model="Schedule.Fri" ng-true-value="'Y'" ng-false-value="'N'">
                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                            Friday
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button"
                        class="btn  btn-default btn-wide pull-right btn-left-border-radius"
                        ng-click="cancelDeviceDetailsModal()"
                        role="button">
                    Close &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-close fa-1x"></i>
                </button>
                <button type="button"
                        class="btn btn-wide btn-primary pull-right btn-right-border-radius"
                        ng-disabled="IndividualScheduleForm.$invalid || Loading==true || IsAtLeastOneDaySelected()==false"
                        ng-click="UpdateIndividualSchedule()">
                    <span ng-if="Loading==false">
                        Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                    </span>
                    <i ng-if="Loading==true" class="fa fa-spinner fa-spin"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .checkbox label:after,
    .radio label:after {
        content: '';
        display: table;
        clear: both;
    }

    .checkbox .cr,
    .radio .cr {
        position: relative;
        display: inline-block;
        border: 1px solid #a9a9a9;
        border-radius: .25em;
        width: 1.3em;
        height: 1.3em;
        float: left;
        margin-right: .5em;
    }

    .radio .cr {
        border-radius: 50%;
    }

        .checkbox .cr .cr-icon,
        .radio .cr .cr-icon {
            position: absolute;
            font-size: .8em;
            line-height: 0;
            top: 50%;
            left: 20%;
        }

        .radio .cr .cr-icon {
            margin-left: 0.04em;
        }

    .checkbox label input[type="checkbox"],
    .radio label input[type="radio"] {
        display: none;
    }

        .checkbox label input[type="checkbox"] + .cr > .cr-icon,
        .radio label input[type="radio"] + .cr > .cr-icon {
            transform: scale(3) rotateZ(-20deg);
            opacity: 0;
            transition: all .3s ease-in;
        }

        .checkbox label input[type="checkbox"]:checked + .cr > .cr-icon,
        .radio label input[type="radio"]:checked + .cr > .cr-icon {
            transform: scale(1) rotateZ(0deg);
            opacity: 1;
        }

        .checkbox label input[type="checkbox"]:disabled + .cr,
        .radio label input[type="radio"]:disabled + .cr {
            opacity: .5;
        }
</style>