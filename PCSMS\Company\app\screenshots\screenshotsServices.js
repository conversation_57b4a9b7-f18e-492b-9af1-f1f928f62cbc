﻿PCSMSApp.factory("screenshotsServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetScreenshotsListByCompanyId: function(companyId) {
            return $http({
                url: "/api/Device/GetScreenshotsListByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetDeviceListByCompanyId: function(companyId) {
            return $http({
                url: "/api/Device/GetDeviceListByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetLicenseListByDeviceId: function(deviceId) {
            return $http({
                url: "/api/Device/GetLicenseListByDeviceId/" + deviceId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetScreenshotsListByDeviceUniqueId: function(deviceId) {
            return $http({
                url: "/api/Device/GetScreenshotsListByDeviceUniqueId/" + deviceId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetScreenshotsListForSpecificSessionByDeviceId: function (deviceId, session) {
            return $http({
                url: "/api/Device/GetScreenshotsListForSpecificSessionByDeviceId/" + deviceId + "/" + session,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetScreenshotsListByCustomRange: function(deviceId, from, to) {
            return $http({
                url: "/api/Device/GetScreenshotsListByCustomRange/" + deviceId + "/" + from + "/" + to,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DeleteScreenshot: function(id) {
            return $http({
                url: "/api/Device/DeleteScreenshot/" + id,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DeleteScreenshotPermanently: function(id) {
            return $http({
                url: "/api/Device/DeleteScreenshotPermanently/" + id,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DownloadAllScreenShots: function(item) {
            return $http({
                url: "/api/Device/DownloadAllScreenShots",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: item,
                async: false
            });
        },

        GetScreenshotsListByPrevButton: function (deviceId, companyId, lastId) {
            return $http({
                url: "/api/Device/GetScreenshotsListByPrevButton/" + deviceId + "/" + companyId + "/" + lastId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },

        GetScreenshotsListByNewButton: function (deviceId, companyId, lastId) {
            return $http({
                url: "/api/Device/GetScreenshotsListByNewButton/" + deviceId+ "/" + companyId  + "/" + lastId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },

    }
}]);