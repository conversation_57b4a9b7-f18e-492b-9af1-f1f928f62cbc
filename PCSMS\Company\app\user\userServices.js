﻿PCSMSApp.factory("userServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetCP_UserListByCompanyId: function (companyId) {
            return $http({
                url: "/Api/CP_User/GetCP_UserListByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCP_UserDetails: function (userId) {
            return $http({
                url: "/Api/CP_User/GetCP_UserDetails/" + userId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        InviteNewUser: function ($scope) {
            return $http({
                url: "/Api/CP_User/InviteNewUser",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: {
                    UserObj: $scope.User
                },
                async: false
            });
        },
        UpdateCP_UserProfile: function ($scope) {
            return $http({
                url: "/Api/CP_User/UpdateCP_UserProfile",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: {
                    UserObj: $scope.User
                },
                async: false
            });
        },
        DeleteCP_User: function (userId, companyId) {
            return $http({
                url: "/Api/CP_User/DeleteCP_User/" + userId + "/" + companyId,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
    };
}]);