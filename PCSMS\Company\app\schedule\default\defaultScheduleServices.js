﻿PCSMSApp.factory("defaultScheduleServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        UpdateDeviceDefaultScheduleByCompany: function ($scope) {
            return $http({
                url: "/api/Device/UpdateDeviceDefaultScheduleByCompany/",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: {
                    scheduleObj: $scope.Schedule
                },
                async: false
            });
        },
        CreateDeviceDefaultScheduleByCompany: function ($scope) {
            return $http({
                url: "/api/Device/CreateDeviceDefaultScheduleByCompany/",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: {
                    scheduleObj: $scope.Schedule
                },
                async: false
            });
        },
        GetDeviceDefaultScheduleByCompanyId: function (companyId) {
            return $http({
                url: "/Api/Device/GetDeviceDefaultScheduleByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        
    };
}]);