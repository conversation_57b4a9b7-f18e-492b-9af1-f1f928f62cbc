﻿/// <reference path="../../../Assets_SProvider/angularjs/angular.js" />
/// <reference path="../../../app.js" />
PCSMSApp.factory("UserServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        SaveSP_User: function ($scope) {
            return $http({
                url: "/Api/SP_User/SaveSP_User",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: {
                    UserObj: $scope.User,
                    LoggedInUserObj: $scope.LoggedInUserObj
                },
                async: false
            });
        },
        UpdateSP_UserAdministrativeInfo: function ($scope) {
            return $http({
                url: "/Api/SP_User/UpdateSP_UserAdministrativeInfo",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: {
                    UserObj: $scope.User,
                    LoggedInUserObj: $scope.LoggedInUserObj
                },
                async: false
            });
        },
        GetSP_UserList: function () {
            return $http({
                url: "/Api/SP_User/GetSP_UserList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetSP_UserDetails: function (id) {
            return $http({
                url: "/Api/SP_User/GetSP_UserDetails/" + id,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DeleteSP_User: function (id) {
            return $http({
                url: "/Api/SP_User/DeleteSP_User/" + id,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        /*Enternal API*/
        GetSP_BranchList: function () {
            return $http({
                url: "/Api/SP_Branch/GetSP_BranchList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetAccessTypeList: function () {
            return $http({
                url: "/api/AccessType/GetAccessTypeList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCompanyTypeList: function () {
            return $http({
                url: "/api/CompanyType/GetCompanyTypeList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetSP_DesignationList: function () {
            return $http({
                url: "/Api/SP_Designation/GetSP_DesignationList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        }
    };
}]);