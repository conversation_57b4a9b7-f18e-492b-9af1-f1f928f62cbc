﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Microsoft.AspNet.SignalR;
using Microsoft.AspNet.SignalR.Hubs;

namespace PCSMS.Hubs
{
    [HubName("notificationHub")]
    public class NotificationHub : Hub
    {
        //For destop app:
        public void NotifyServerThatLicenseBecomesExpiredOrInActiveOrActiveOrRenewed(int licenseId)
        {
            Clients.All.onLicenseStatusTransition(licenseId);
        }
        public void NotifyServerThatCompanyModifiedSchedule(int deviceId)
        {
            Clients.All.onScheduleChange(deviceId);
        }



        //For browser [company]:
        public void NotifyServerThatCompanyRequestedLicense()
        {
            Clients.All.onLicenseRequested();
        }
        public void NotifyServerThatCompanyRenewalRequestedForLicense(string licenseCode)
        {
            Clients.All.onLicenseRenewalRequested(licenseCode);
        }
        public void NotifyCompanyThatLicenseExpired(int companyId, string licenseCode)
        {
            Clients.All.onLicenseExpiration(companyId, licenseCode); 
        }



        //For browser [sprovider]:
        public void NotifyServerThatSPoviderApprovedLicense(int companyId, string licenseCode)
        {
            Clients.All.onLicenseApproval(companyId, licenseCode);
        }
        public void NotifyServerThatCompanyRenewedLicense(int companyId, string licenseCode)
        {
            Clients.All.onLicenseRenewal(companyId, licenseCode);
        }
        public void NotifySproviderThatCompanyLicenseExpired( string companyName, string licenseCode)
        {
            Clients.All.broadcastSProdiverThatCompanyLicenseExpired(companyName, licenseCode); 
        }
    }
}