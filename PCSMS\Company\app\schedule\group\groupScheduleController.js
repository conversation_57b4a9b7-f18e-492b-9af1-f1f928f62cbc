﻿/// <reference path="app.js" />
PCSMSApp.controller('groupScheduleController', function ($scope, licenseServices, deviceServices, groupScheduleServices, defaultScheduleServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    $scope.ScheduleMenuOpen = true;

    $scope.SelectedDeviceArray = [];
    $scope.GroupList = [];
    $scope.GroupCreatableDeviceList = [];
    $scope.Group = {};
    $scope.GroupMembers = [];
    $scope.Device = {};
    $scope.hstep = 1;
    $scope.mstep = 1;
    $scope.Schedule = {};
    $scope.ismeridian = true;
    $scope.Schedule.Sat = "N";
    $scope.Schedule.Sun = "N";
    $scope.Schedule.Mon = "N";
    $scope.Schedule.Tues = "N";
    $scope.Schedule.Wed = "N";
    $scope.Schedule.Thurs = "N";
    $scope.Schedule.Fri = "N";
    $scope.Loading = false;//For Capturing and identyfying loader;
    $scope.Schedule.StartTime = moment("09:00:00", "HH:mm:ss")._d;
    $scope.Schedule.EndTime = moment("22:00:00", "HH:mm:ss")._d;
    $scope.TimingList = [
        { "Timing": 1, "Id": 1 },
        { "Timing": 5, "Id": 5 },
        { "Timing": 10, "Id": 10 },
        { "Timing": 15, "Id": 15 },
        { "Timing": 20, "Id": 20 },
        { "Timing": 30, "Id": 30 },
        { "Timing": 60, "Id": 60 },
        { "Timing": "Random", "Id": "Random" },
    ]

    $scope.IsAtLeastOneDaySelected = function () {
        if ($scope.Schedule.Sat == "N" &&
            $scope.Schedule.Sun == "N" &&
            $scope.Schedule.Mon == "N" &&
            $scope.Schedule.Tues == "N" &&
            $scope.Schedule.Wed == "N" &&
            $scope.Schedule.Thurs == "N" &&
            $scope.Schedule.Fri == "N") {
            return false;
        }
        return true;
    }

    licenseServices.GetLicenseListByCompanyId($rootScope.CompanyId).then(function (response) {
        $scope.LicenseList = response.data;
        angular.forEach(response.data, function (value, key) {
            if (value.LicenseKey == null) {
                value.LicenseKey = "---------------------------------------";
            }
        });
        //console.log($scope.LicenseList);

    });

    groupScheduleServices.GetGroupListByCompanyId($rootScope.CompanyId).then(function (response) {
        $scope.ListOfGroups = response.data;
        angular.forEach(response.data, function (value, key) {
            if (value.CreatedOn != null) {
                var creDateUtc = moment.utc(value.CreatedOn); 
                value.CreatedOn = creDateUtc.local().format("DD-MM-YYYY hh:mm:ss a").toString();
            }
            if (value.UpdatedOn != null) {
                var modDateUtc = moment.utc(value.UpdatedOn);
                value.UpdatedOn = modDateUtc.local().format("DD-MM-YYYY hh:mm:ss a").toString();
            }
            else {
                value.UpdatedOn = "Not updated yet";
            }
        });
    });


    $scope.SeeDeviceList = function (licenseId) {
        deviceServices.GetDeviceListByLicenseId(licenseId).then(function (response) {
            $scope.DeviceListByLicense = response.data;


            //Checking if GroupName is available:
            $scope.GroupNameAvailable = false;
            var keepGoing = true;
            angular.forEach(response.data, function (value, key) {
                //console.log(value.GroupName);
                if (keepGoing) {
                    if (value.GroupName != null) {
                        $scope.GroupNameAvailable = true;
                        keepGoing = false;
                    }
                }
            });
        });
        //When user clicks on Reg. Devices the following needs to be cleared
        $scope.SelectedDeviceArray = [];
    }


    //---------Push Device One after another---------


    $scope.PushToSelectedDeviceArray = function (id, DeviceName, DeviceUniqueId, event) {
        if (id != null || id != undefined) {
            if ($scope.SelectedDeviceArray.length == 0) {
                $scope.SelectedDeviceArray.push({ Id: id, DeviceName: DeviceName, DeviceUniqueId: DeviceUniqueId });
            }
            else {
                for (var i = 0; i < $scope.SelectedDeviceArray.length; i++) {
                    if ($scope.SelectedDeviceArray[i].Id != id) {
                        var index = $scope.SelectedDeviceArray.findIndex(x => x.Id == id);
                        if (index == -1) {
                            $scope.SelectedDeviceArray.push({ Id: id, DeviceName: DeviceName, DeviceUniqueId: DeviceUniqueId });
                            //console.log($scope.SelectedDeviceArray);
                            return;
                        }
                        else {
                            //console.log($scope.SelectedDeviceArray);
                        }
                    }
                    else if ($scope.SelectedDeviceArray[i].Id == id) {
                        var index = $scope.SelectedDeviceArray.findIndex(x => x.Id == id);
                        if (index > -1) {
                            $scope.SelectedDeviceArray.splice(index, 1);
                            var thisCheckbox = $('#' + event.target.id);
                            thisCheckbox.prop('checked', false);
                            $scope.SelectAllDevice = "NAll";
                        }
                        return;
                    }
                }
            }
        }


    }
    
    //Group Creation
    $scope.CreateGroupAndSetSchedule = function () {
        $scope.Schedule.StartTime = moment($scope.Schedule.StartTime).format("HH:mm:ss");
        $scope.Schedule.EndTime = moment($scope.Schedule.EndTime).format("HH:mm:ss");
        $scope.Schedule.CompanyId = $rootScope.CompanyId;
        if ($scope.Schedule.Interval == "Random") {
            $scope.Schedule.Interval = null;
            $scope.Schedule.IsRandom = "Y";

        } else {
            $scope.Schedule.IsRandom = "N";
        }


        groupScheduleServices.AddGroupScheduleSetting($scope).then(function (response) {
            if (response.data.IsReport == "Ok") {
                toastr.success(response.data.Message, "Success!");
            }
            else if (response.data.IsReport == "NotOk") {
                toastr.error(response.data.Message, "Error!");
            }
            $('#ScheduleModal').modal("hide");
        })
            .then(function () {
                $scope.cancelScheduleModal();
            });
    };

    //Edit Group
    $scope.GetGroupDetails = function (groupId) {


        var licenseId;
        //first thing needed:
        groupScheduleServices.GetGroupDetailsByGroupId(groupId).then(function (response) {
            $scope.Schedule = response.data;
            $scope.Schedule.StartTime = moment($scope.Schedule.StartTime, "HH:mm:ss")._d;
            $scope.Schedule.EndTime = moment($scope.Schedule.EndTime, "HH:mm:ss")._d;
            if ($scope.Schedule.IsRandom == "Y") {
                $scope.Schedule.Interval = "Random";
            }

            $scope.GroupedDeviceList = response.data.DeviceDetails;
            //console.log(response.data.DeviceDetails);
            angular.forEach(response.data.DeviceDetails, function (value, key) {
                $scope.SelectedDeviceArray.push({
                    Id: value.Id,
                    DeviceName: value.DeviceName,
                    DeviceUniqueId: value.DeviceUniqueId,
                });
            });


            
            ////second thing needed:
            licenseId = $scope.Schedule.LicenseId;

            $scope.GroupableDeviceList = [];
            groupScheduleServices.GetNonGroupedDeviceListByLicenseId(licenseId).then(function (response) {
                
                //$scope.GroupedDeviceList indicates those devices which are already grouped
                //response.data indicates those devices which are not grouped yet
                //We are pushing these two category devices into GroupableDevices array
                angular.forEach($scope.GroupedDeviceList, function (value, key) {
                    $scope.GroupableDeviceList.push({
                        Id: value.Id,
                        DeviceName: value.DeviceName,
                        DeviceUniqueId: value.DeviceUniqueId,
                        Settings: value.Settings,
                    });
                });
                
                angular.forEach(response.data, function (value, key) {
                    $scope.GroupableDeviceList.push({
                        Id: value.Id,
                        DeviceName: value.DeviceName,
                        DeviceUniqueId: value.DeviceUniqueId,
                        Settings: value.Settings,
                    });
                });
            });


            

            
        });


    }
    $scope.UpdateGroup = function () {
        $scope.Schedule.StartTime = moment($scope.Schedule.StartTime).format("HH:mm:ss");
        $scope.Schedule.EndTime = moment($scope.Schedule.EndTime).format("HH:mm:ss");
        $scope.Schedule.CompanyId = $rootScope.CompanyId;

        if ($scope.Schedule.Interval == "Random") {
            $scope.Schedule.Interval = null;
            $scope.Schedule.IsRandom = "Y";

        } else {
            $scope.Schedule.IsRandom = "N";
        }




        groupScheduleServices.UpdateGroupScheduleSetting($scope).then(function (response) {
            if (response.data.IsReport == "Ok") {
                toastr.success(response.data.Message, "Success!");
            }
            else if (response.data.IsReport == "NotOk") {
                toastr.error(response.data.Message, "Error!");
            }
            $('#ScheduleEditModal').modal("hide");
        })
            .then(function () {
                $scope.cancelScheduleEditModal();
            });
    };

    //Delete Group
    $scope.DeleteThisGroup = function (groupId) {
        swal({
            title: "Are You Sure?",
            text: "You are going to delete this group",
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn btn-success",
            confirmButtonText: "Yes",
            cancelButtonClass: "btn btn-default",
            cancelButtonText: "No",
            closeOnConfirm: true
        },
        function (isConfirm) {
            if (isConfirm) {
                groupScheduleServices.DeleteGroup(groupId).then(function (response) {
                    if (response.data.IsReport == "Ok") {
                        toastr.success(response.data.Message, "Success");
                    }
                    else if (response.data.IsReport == "NotOk") {
                        toastr.error(response.data.Message, "Error!");
                    }
                })
                .then(function () {
                    $state.reload();
                })
            } else {
                $state.reload();
            }
        });
    };


    //Additional Methods:
    
    $scope.FirstSegmentValid = false;
    $scope.FirstSegmentValid = function () {
        if ($scope.SelectedDeviceArray.length > 1 && !$scope.GroupForm.$invalid)
        { return true; }
        else return false;
    }





    //====================================================================Element Processing==========================================================================
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [DTColumnDefBuilder.newColumnDef(3).notSortable() //note : this three to adjust for avoiding error
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
        .withOption('paging', true)
        .withOption('searching', true)
        .withOption('info', true)
        .withOption('order', [1, 'desc']); //note : this one to adjust for avoiding error


    //========================CLEAR MODAL==============


    $scope.cancelScheduleModal = function () {
        $('#ScheduleModal').modal('hide');
        $timeout(function () {
            $state.reload();
        }, 300);
    };
    $scope.cancelScheduleEditModal = function () {
        $('#ScheduleEditModal').modal('hide');
        $timeout(function () {
            $state.reload();
        }, 300);
    };


    //---------------------------------------------------Multi-Step Form-------------------------------------------------
    $scope.Step = 1;
    $scope.StepUp = function () {
        if ($scope.Step == 1) {
            $scope.SwitchTab2();
        }

    }
    $scope.StepBack = function () {
        if ($scope.Step == 2) {
            $scope.SwitchTab1();
        }
        else if ($scope.Step == 1) {
            toastr.error('This is first step. You can not go back', 'Error!');
        }
    }

    $scope.SwitchTab1 = function () {
        //$("li.active").prev().addClass('validated');
        $scope.Step = 1;
        $("li").prevAll().addClass('validated');
        $("li").nextAll().removeClass('validated');

    }
    $scope.SwitchTab2 = function () {
        if ($scope.FirstSegmentValid() == false) {
            toastr.error('Minimum two devices should be selected and a valid group-name is required.', 'Error!')
        }
        else {
            $scope.Step = 2;
            $("li").prevAll().addClass('validated');
        }


    }


});