﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity.Core.Common.CommandTrees;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using Microsoft.AspNet.SignalR;
using System.Web;
using System.Web.Hosting;
using System.Web.Mvc;
using System.Web.UI.WebControls.WebParts;
using ICSharpCode.SharpZipLib.Zip;
using PCSMS.Common;
using PCSMS.Models;
using PCSMS.Models.Models_Company;
using PCSMS.Services.Services_Shared;

namespace PCSMS.Services.Services_Company
{
    public class CP_DeviceServices : ICP_DeviceServices
    {
        private readonly PCSMSDbContext _context;
        private readonly IEntityService<CP_Device> _deviceServices;
        private readonly IEntityService<CP_Device_License> _deviceLicenseServices;
        private readonly IEntityService<CP_License> _licenseServices;
        private readonly IEntityService<CP_License_Period> _licensePeriodServices;
        private readonly IEntityService<CP_ScreenCapture> _screenCatureServices;
        private readonly IEntityService<CP_Device_Schedule> _scheduleServices;
        private readonly IEntityService<CP_Device_Group> _groupServices;
        private readonly IHubContext _notificationHubContext;
        public CP_DeviceServices()
        {
            _context = new PCSMSDbContext();
            _deviceServices = new EntityService<CP_Device>(_context);
            _deviceLicenseServices = new EntityService<CP_Device_License>(_context);
            _licenseServices = new EntityService<CP_License>(_context);
            _licensePeriodServices = new EntityService<CP_License_Period>(_context);
            _screenCatureServices = new EntityService<CP_ScreenCapture>(_context);
            _scheduleServices = new EntityService<CP_Device_Schedule>(_context);
            _groupServices = new EntityService<CP_Device_Group>(_context);
            _notificationHubContext = GlobalHost.ConnectionManager.GetHubContext("notificationHub");
        }


        //Private Services :
        private bool GroupExistInSameCompany(CP_Device_Group obj)
        {
            var result = _context.CP_Device_Group.FirstOrDefault(x => x.GroupName == obj.GroupName && x.CompanyId == obj.CompanyId);

            if (result == null)
            {
                return false;
            }
            return true;
        }

        private bool GroupExistInSameCompanyWithoutOwnName(CP_Device_Group obj)
        {
            var result = _context.CP_Device_Group.FirstOrDefault(x => x.GroupName == obj.GroupName && x.CompanyId == obj.CompanyId && x.Id != obj.Id);

            if (result == null)
            {
                return false;
            }
            return true;
        }
        private int GenerateRandomKeyLicense(int companyId)
        {
            int deviceLimitForTrialLicense = 10;

            //saving licenseobj in CP_License Table
            CP_License licenseObj = new CP_License
            {
                CompanyId = companyId,
                LicenseType = "Trial",
                RequestedOn = DateTime.UtcNow,
                ActivatedOn = DateTime.UtcNow,
                LCode = GenericServices.GenerateLCode(companyId),
                LicenseKey = GenericServices.CreateRandomNumber1() + "-" + GenericServices.CreateRandomNumber2() + "-" + GenericServices.CreateRandomNumber3() + "-" + GenericServices.CreateRandomNumber4() + "-" + GenericServices.CreateRandomNumber5(),  //later i will check if license key does not get matched with other license key
                DeviceLimit = deviceLimitForTrialLicense,
                Status = "Active"
            };

            _licenseServices.Save(licenseObj);
            _licenseServices.SaveChanges();

            return licenseObj.Id;
        }
        private void SaveLicensePeriod(int licenseId)
        {

            var expiryDateOfATrialLicense = DateTime.UtcNow.AddDays(12).Date.AddHours(18).AddMinutes(00).AddSeconds(00);


            CP_License_Period cpl = new CP_License_Period();
            cpl.LicenseId = licenseId;
            cpl.ExpiryDate = expiryDateOfATrialLicense;
            cpl.Status = "Current";

            _licensePeriodServices.Save(cpl);
            _licensePeriodServices.SaveChanges();

        }
        private int SaveDeviceScheduleForCompany(int companyId)
        {
            //add a row in CP_Device_Schedule table a 
            var deviceSchedule = new CP_Device_Schedule
            {
                CompanyId = companyId,
                Interval = 1,
                IsRandom = "N",
                StartTime = new TimeSpan(07, 00, 00),
                EndTime = new TimeSpan(23, 00, 00),
                Mon = "Y",
                Tues = "Y",
                Wed = "Y",
                Thurs = "Y",
                Fri = "Y",
                Sat = "N",
                Sun = "N",
            };

            _scheduleServices.Save(deviceSchedule);
            _scheduleServices.SaveChanges();

            return deviceSchedule.Id;
        }
        private int SaveDevice(int companyId, string deviceName, string deviceUniqueId, int defaultScheduleId)
        {
            var device = new CP_Device
            {
                CompanyId = companyId,
                DeviceName = deviceName,
                DeviceUniqueId = deviceUniqueId,
                Status = "Registered",
                DefaultScheduleId = defaultScheduleId,
            };
            _deviceServices.Save(device);
            _deviceServices.SaveChanges();

            return device.Id;
        }
        private void SaveDeviceLicense(int deviceId, int licenseId)
        {

            var deviceLicense = new CP_Device_License
            {
                DeviceId = deviceId,
                LicenseId = licenseId,
                Status = "Active",
            };

            _deviceLicenseServices.Save(deviceLicense);
            _deviceLicenseServices.SaveChanges();
        }
        private int DeviceRegisteredSoFarUnderALicense(int licenseId)
        {
            return _context.CP_Device_License.Count(x => x.LicenseId == licenseId);
        }
        private string[] RegisterDeviceAndActivate(int companyId, string deviceName, string deviceUniqueId, int licenseId, int deviceLimit)
        {
            var message = "";
            //Now, we have to check whether the device name is unique or not
            bool deviceNameMatches = _context.CP_Device.Any(x => x.DeviceName == deviceName && x.DeviceUniqueId != deviceUniqueId);

            //Now, we also have to chcek if user wants to register any device multiple times.
            bool deviceUniqueIdMatches = _context.CP_Device.Any(x => x.DeviceUniqueId == deviceUniqueId && x.CompanyId == companyId);

            //Getting device limit
            int deviceRegisteredSoFar = DeviceRegisteredSoFarUnderALicense(licenseId);

            if (deviceRegisteredSoFar >= deviceLimit)
            {
                message = "You are allowed to register " + deviceLimit + " PC / devices only under your license.";
                Generator.IsReport = "NotOk";
            }
            else if (deviceNameMatches)
            {
                message = "Another PC or Device has same name, Please choose different name.";
                Generator.IsReport = "NotOk";
            }
            else if (deviceUniqueIdMatches)
            {
                message = "This PC / device already has an activated license key.";
                Generator.IsReport = "NotOk";
            }
            else
            {
                //now saving or registering a device under a license key
                var defaultScheduleObj = _context.CP_Device_Schedule.Where(x => x.CompanyId == companyId).Select(x => x).FirstOrDefault();
                if (defaultScheduleObj == null)
                {

                    //1. Saving device schedule for a company
                    int deviceScheduleId = SaveDeviceScheduleForCompany(companyId);


                    //2. Saving device now
                    int deviceId = SaveDevice(companyId, deviceName, deviceUniqueId, deviceScheduleId);


                    //3. Saving device_license
                    SaveDeviceLicense(deviceId, licenseId);


                    //4. We have to update the deviceName if new different deviceName is received 
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device] SET DeviceName = '{deviceName}' WHERE DeviceUniqueId = '{deviceUniqueId}'");

                    message = "License activated successfully !";
                    Generator.IsReport = "Ok";
                }
                else
                {
                    //1. Saving device now
                    int deviceId = SaveDevice(companyId, deviceName, deviceUniqueId, defaultScheduleObj.Id);


                    //2. Saving device_license
                    SaveDeviceLicense(deviceId, licenseId);


                    //3. We have to update the deviceName if new different deviceName is received 
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device] SET DeviceName = '{deviceName}' WHERE DeviceUniqueId = '{deviceUniqueId}'");


                    message = "License activated successfully !";
                    Generator.IsReport = "Ok";
                }

            }

            return new string[] { message, Generator.IsReport };
        }
        private int CreateTrialLicense(int companyId)
        {
            var result = 0;
            try
            {
                //1. Saving License
                int licenseId = GenerateRandomKeyLicense(companyId);


                //2. Saving License Period
                SaveLicensePeriod(licenseId);


                //3. Saving License Schedule 
                var defaultScheduleObj = _context.CP_Device_Schedule.Where(x => x.CompanyId == companyId).Select(x => x).FirstOrDefault();
                if (defaultScheduleObj == null)
                {
                    SaveDeviceScheduleForCompany(companyId);
                }



                result = licenseId;
            }
            catch (Exception)
            {
                result = 0;
            }

            return result;
        }
        private int GetMemberDeviceCount(int groupId, int companyId)
        {
            return _context.CP_Device.Count(x => x.GroupId == groupId && x.CompanyId == companyId);
        }
        public bool ChangeBase64StringIntoImageAndSave(string imgStr, string imageName)
        {
            String path = HttpContext.Current.Server.MapPath("~/Company_Images/Screenshots"); //Path

            //set the image path
            string imgPath = Path.Combine(path, imageName);

            byte[] imageBytes = Convert.FromBase64String(imgStr);

            File.WriteAllBytes(imgPath, imageBytes);

            return true;
        }


        //Main services:
        public JsonResult GetDeviceDefaultScheduleByCompanyId(int companyId)
        {
            return new JsonResult
            {
                Data = _context.CP_Device_Schedule.Where(x => x.CompanyId == companyId).Select(x => new
                {
                    x.Id,
                    x.Interval,
                    x.IsRandom,
                    x.CompanyId,
                    x.StartTime,
                    x.EndTime,
                    x.Mon,
                    x.Tues,
                    x.Wed,
                    x.Thurs,
                    x.Fri,
                    x.Sat,
                    x.Sun
                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult IsCompanyCodeValid(string companyCode)
        {
            var companyObj = _context.CP_Profile.Where(x => x.CompanyCode == companyCode)
                  .Select(x => new
                  {
                      x.Id,
                  }).FirstOrDefault();
            var result = companyObj != null;

            return new JsonResult
            {
                Data = result,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetDeviceListByCompanyId(int companyId)
        {
            return new JsonResult
            {
                Data = _context.CP_Device.Where(x => x.CompanyId == companyId)
                    .Select(d => new
                    {
                        d.Id,
                        d.DeviceName,
                        d.DeviceUniqueId,
                    }).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult GetLicenseListByDeviceId(int deviceId)
        {
            var result = _context.CP_Device_License.Where(x => x.DeviceId == deviceId)
                .Join(_context.CP_License,
                    x => x.LicenseId,
                    y => y.Id,
                    (x, y) => new { DeviceLicense = x, License = y })
                .Select(x => new
                {
                    x.License.Id,
                    x.License.LCode,
                    x.License.LicenseKey
                }).ToList();



            return new JsonResult
            {
                Data = result,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetDeviceListByLicenseId(int licenseId)
        {

            return new JsonResult
            {
                Data = _context.CP_Device_License.Where(d => d.LicenseId == licenseId)
                    .Join(_context.CP_Device,
                        d => d.DeviceId,
                        e => e.Id,
                        (d, e) => new { DeviceLicense = d, Device = e })
                    .GroupJoin(_context.CP_Device_Schedule,
                d => d.Device.DefaultScheduleId,
                e => e.Id,
                (d, e) => new { d.DeviceLicense, d.Device, DefaultSchedule = e })
                    .SelectMany(d => d.DefaultSchedule.DefaultIfEmpty(),
                (d, e) => new { d.DeviceLicense, d.Device, DefaultSchedule = e })
                .GroupJoin(_context.CP_Device_Group,
                d => d.Device.GroupScheduleId,
                e => e.Id,
                (d, e) => new { d.DeviceLicense, d.Device, d.DefaultSchedule, GroupSchedule = e })
                    .SelectMany(d => d.GroupSchedule.DefaultIfEmpty(),
                (d, e) => new { d.DeviceLicense, d.Device, d.DefaultSchedule, GroupSchedule = e })
                .Select(d => new
                {
                    d.Device.Id,
                    d.Device.DeviceName,
                    d.Device.DeviceUniqueId,
                    Settings = d.Device.GroupScheduleId != null ? "Group settings applied" : d.Device.DefaultScheduleId != null ? "Default settings applied" : "Individual settings applied",

                    Interval = d.Device.GroupScheduleId != null ? d.GroupSchedule.Interval : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Interval : d.Device.Interval,
                    IsRandom = d.Device.GroupScheduleId != null ? d.GroupSchedule.IsRandom : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.IsRandom : d.Device.IsRandom,
                    StartTime = d.Device.GroupScheduleId != null ? d.GroupSchedule.StartTime : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.StartTime : d.Device.StartTime,
                    EndTime = d.Device.GroupScheduleId != null ? d.GroupSchedule.EndTime : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.EndTime : d.Device.EndTime,
                    Sat = d.Device.GroupScheduleId != null ? d.GroupSchedule.Sat : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Sat : d.Device.Sat,
                    Sun = d.Device.GroupScheduleId != null ? d.GroupSchedule.Sun : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Sun : d.Device.Sun,
                    Mon = d.Device.GroupScheduleId != null ? d.GroupSchedule.Mon : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Mon : d.Device.Mon,
                    Tues = d.Device.GroupScheduleId != null ? d.GroupSchedule.Tues : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Tues : d.Device.Tues,
                    Wed = d.Device.GroupScheduleId != null ? d.GroupSchedule.Wed : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Wed : d.Device.Wed,
                    Thurs = d.Device.GroupScheduleId != null ? d.GroupSchedule.Thurs : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Thurs : d.Device.Thurs,
                    Fri = d.Device.GroupScheduleId != null ? d.GroupSchedule.Fri : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Fri : d.Device.Fri,
                    d.GroupSchedule.GroupName,


                }).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetDeviceDetails(int deviceId)
        {
            return new JsonResult
            {
                Data = _context.CP_Device.Where(x => x.Id == deviceId)
                            .GroupJoin(_context.CP_Device_Schedule,
                                d => d.DefaultScheduleId,
                                e => e.Id,
                                (d, e) => new { Device = d, DefaultSchedule = e })
                            .SelectMany(d => d.DefaultSchedule.DefaultIfEmpty(),
                                (d, e) => new { d.Device, DefaultSchedule = e })
                            .GroupJoin(_context.CP_Device_Group,
                                d => d.Device.GroupScheduleId,
                                e => e.Id,
                                (d, e) => new { d.Device, d.DefaultSchedule, GroupSchedule = e })
                            .SelectMany(d => d.GroupSchedule.DefaultIfEmpty(),
                                (d, e) => new { d.Device, d.DefaultSchedule, GroupSchedule = e })
                            .GroupJoin(_context.CP_Device_License,
                                d => d.Device.Id,
                                e => e.DeviceId,
                                (d, e) => new { d.Device, d.DefaultSchedule, d.GroupSchedule, Device_License = e })
                            .SelectMany(d => d.Device_License.DefaultIfEmpty(),
                                (d, e) => new { d.Device, d.DefaultSchedule, d.GroupSchedule, Device_License = e })
                            .GroupJoin(_context.CP_License,
                                d => d.Device_License.LicenseId,
                                e => e.Id,
                                (d, e) => new { d.Device, d.DefaultSchedule, d.GroupSchedule, d.Device_License, License = e })
                            .SelectMany(d => d.License.DefaultIfEmpty(),
                                (d, e) => new { d.Device, d.DefaultSchedule, d.GroupSchedule, d.Device_License, License = e })
                            .Select(d => new
                            {
                                d.Device.Id,
                                d.Device.DeviceName,
                                d.Device.DeviceUniqueId,
                                d.License.LCode,
                                d.License.LicenseKey,
                                d.License.LicenseType,
                                d.License.Status,
                                d.License.SecondaryStatus,

                                Settings = d.Device.GroupScheduleId != null ? "Group settings applied" : d.Device.DefaultScheduleId != null ? "Default settings applied" : "Individual settings applied",

                                Interval = d.Device.GroupScheduleId != null ? d.GroupSchedule.Interval : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Interval : d.Device.Interval,
                                IsRandom = d.Device.GroupScheduleId != null ? d.GroupSchedule.IsRandom : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.IsRandom : d.Device.IsRandom,
                                StartTime = d.Device.GroupScheduleId != null ? d.GroupSchedule.StartTime : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.StartTime : d.Device.StartTime,
                                EndTime = d.Device.GroupScheduleId != null ? d.GroupSchedule.EndTime : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.EndTime : d.Device.EndTime,
                                Sat = d.Device.GroupScheduleId != null ? d.GroupSchedule.Sat : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Sat : d.Device.Sat,
                                Sun = d.Device.GroupScheduleId != null ? d.GroupSchedule.Sun : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Sun : d.Device.Sun,
                                Mon = d.Device.GroupScheduleId != null ? d.GroupSchedule.Mon : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Mon : d.Device.Mon,
                                Tues = d.Device.GroupScheduleId != null ? d.GroupSchedule.Tues : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Tues : d.Device.Tues,
                                Wed = d.Device.GroupScheduleId != null ? d.GroupSchedule.Wed : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Wed : d.Device.Wed,
                                Thurs = d.Device.GroupScheduleId != null ? d.GroupSchedule.Thurs : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Thurs : d.Device.Thurs,
                                Fri = d.Device.GroupScheduleId != null ? d.GroupSchedule.Fri : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Fri : d.Device.Fri,


                            }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetDeviceAndLicenseDetails(int deviceId)
        {
            #region deviceObj
            var deviceObj = _context.CP_Device.Where(x => x.Id == deviceId)
                .GroupJoin(_context.CP_Device_Schedule,
                    d => d.DefaultScheduleId,
                    e => e.Id,
                    (d, e) => new { Device = d, DefaultSchedule = e })
                .SelectMany(d => d.DefaultSchedule.DefaultIfEmpty(),
                    (d, e) => new { d.Device, DefaultSchedule = e })
                .GroupJoin(_context.CP_Device_Group,
                    d => d.Device.GroupScheduleId,
                    e => e.Id,
                    (d, e) => new { d.Device, d.DefaultSchedule, GroupSchedule = e })
                .SelectMany(d => d.GroupSchedule.DefaultIfEmpty(),
                    (d, e) => new { d.Device, d.DefaultSchedule, GroupSchedule = e })
                .Select(d => new
                {
                    d.Device.Id,
                    d.Device.DeviceName,


                    Settings = d.Device.GroupScheduleId != null ? "Group settings applied" : d.Device.DefaultScheduleId != null ? "Default settings applied" : "Individual settings applied",

                    Interval = d.Device.GroupScheduleId != null ? d.GroupSchedule.Interval : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Interval : d.Device.Interval,
                    IsRandom = d.Device.GroupScheduleId != null ? d.GroupSchedule.IsRandom : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.IsRandom : d.Device.IsRandom,
                    StartTime = d.Device.GroupScheduleId != null ? d.GroupSchedule.StartTime : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.StartTime : d.Device.StartTime,
                    EndTime = d.Device.GroupScheduleId != null ? d.GroupSchedule.EndTime : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.EndTime : d.Device.EndTime,
                    Sat = d.Device.GroupScheduleId != null ? d.GroupSchedule.Sat : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Sat : d.Device.Sat,
                    Sun = d.Device.GroupScheduleId != null ? d.GroupSchedule.Sun : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Sun : d.Device.Sun,
                    Mon = d.Device.GroupScheduleId != null ? d.GroupSchedule.Mon : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Mon : d.Device.Mon,
                    Tues = d.Device.GroupScheduleId != null ? d.GroupSchedule.Tues : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Tues : d.Device.Tues,
                    Wed = d.Device.GroupScheduleId != null ? d.GroupSchedule.Wed : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Wed : d.Device.Wed,
                    Thurs = d.Device.GroupScheduleId != null ? d.GroupSchedule.Thurs : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Thurs : d.Device.Thurs,
                    Fri = d.Device.GroupScheduleId != null ? d.GroupSchedule.Fri : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Fri : d.Device.Fri,

                }).FirstOrDefault();
            #endregion

            #region Getting licenseId through deviceId
            var licenseId = _context.CP_Device_License.Where(x => x.DeviceId == deviceObj.Id).OrderByDescending(x => x.Id).Select(x => x.LicenseId).FirstOrDefault();
            #endregion

            #region licenseObj:

            var licenseObj = _context.CP_License.Where(x => x.Id == licenseId)
                .Join(_context.CP_License_Period.Where(x => x.Status == "Current"),
                    x => x.Id,
                    y => y.LicenseId,
                    (x, y) => new { License = x, CP_License_Period = y })
                .Select(x => new
                {
                    x.License.Id,
                    x.License.LicenseKey,
                    x.License.Status,
                    x.License.SecondaryStatus,
                    x.License.ActivatedOn,
                    x.CP_License_Period.ExpiryDate,
                }).FirstOrDefault();
            #endregion

            return new JsonResult
            {
                Data = new
                {
                    Content = new
                    {
                        deviceObj,
                        licenseObj
                    }
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }



        //Group creation/ deletion related
        public JsonResult GetNonGroupedDeviceListByCompanyId(int companyId)
        {
            return new JsonResult
            {
                Data = _context.CP_Device.Where(x => x.CompanyId == companyId && x.GroupId == null && x.GroupScheduleId == null)
                    .GroupJoin(_context.CP_Device_Schedule,
                        d => d.DefaultScheduleId,
                        e => e.Id,
                        (d, e) => new { Device = d, DefaultSchedule = e })
                    .SelectMany(d => d.DefaultSchedule.DefaultIfEmpty(),
                        (d, e) => new { d.Device, DefaultSchedule = e })
                    .GroupJoin(_context.CP_Device_Group,
                        d => d.Device.GroupScheduleId,
                        e => e.Id,
                        (d, e) => new { d.Device, d.DefaultSchedule, GroupSchedule = e })
                    .SelectMany(d => d.GroupSchedule.DefaultIfEmpty(),
                        (d, e) => new { d.Device, d.DefaultSchedule, GroupSchedule = e })
                    .Select(d => new
                    {
                        d.Device.Id,
                        d.Device.DeviceName,
                        d.Device.DeviceUniqueId,

                        Settings = d.Device.GroupScheduleId != null ? "Group settings applied" : d.Device.DefaultScheduleId != null ? "Default settings applied" : "Individual settings applied",

                    }).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetNonGroupedDeviceListByLicenseId(int licenseId)
        {
            return new JsonResult
            {
                Data = _context.CP_Device_License.Where(d => d.LicenseId == licenseId && d.Status == "Active")
                        .Join(_context.CP_License,
                        d => d.LicenseId,
                        e => e.Id,
                        (d, e) => new { Device_License = d, License = e })
                        .Join(_context.CP_Device.Where(d => d.GroupId == null && d.GroupScheduleId == null),
                        d => d.Device_License.DeviceId,
                        e => e.Id,
                        (d, e) => new { d.Device_License, d.License, Device = e })
                    .Select(d => new
                    {
                        d.Device.Id,
                        d.Device.DeviceName,
                        d.Device.DeviceUniqueId,

                        Settings = d.Device.GroupScheduleId != null ? "Group settings applied" : d.Device.DefaultScheduleId != null ? "Default settings applied" : "Individual settings applied",

                    }).OrderBy(d => d.Id).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetGroupListByCompanyId(int companyId)
        {
            return new JsonResult
            {
                Data = _context.CP_Device_Group.Where(d => d.CompanyId == companyId).ToList()
                            //.GroupJoin(_context.CP_Device_License,
                            //    d => d.Id,
                            //    e => e.DeviceId,
                            //    (d, e) => new { Device_Group =d, Device_License = e })
                            //.SelectMany(d => d.Device_License.DefaultIfEmpty(),
                            //    (d, e) => new { d.Device_Group, Device_License = e })
                            //.GroupJoin(_context.CP_License.Where(x=> x.Status == "Active"),
                            //    d => d.Device_License.LicenseId,
                            //    e => e.Id,
                            //    (d, e) => new { d.Device_Group, d.Device_License, License = e })
                            //.SelectMany(d => d.License.DefaultIfEmpty(),
                            //    (d, e) => new { d.Device_Group, d.Device_License, License = e })
                            .Select(d => new
                            {
                                d.Id,
                                d.CompanyId,
                                d.GroupName,
                                d.StartTime,
                                d.EndTime,
                                d.Interval,
                                d.IsRandom,
                                d.Sat,
                                d.Sun,
                                d.Mon,
                                d.Tues,
                                d.Wed,
                                d.Thurs,
                                d.Fri,
                                d.CreatedOn,
                                d.UpdatedOn,
                                MemberDevices = GetMemberDeviceCount(d.Id, (int)d.CompanyId),

                                //A Group member pcs can have multiple license such as trial and chrageable

                            }).OrderByDescending(d => d.CreatedOn).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetGroupDetailsByGroupId(int groupId)
        {
            //I have to get licenseId of this group, To achieve that i am getting deviceId first and then by deviceId I will retrieve licenseId:
            int deviceId = _context.CP_Device_Group
                .Join(_context.CP_Device,
                    x => x.Id,
                    y => y.GroupId,
                    (x, y) => new { DeviceGroup = x, Device = y })
                .Select(x => x.Device.Id).FirstOrDefault();

            //now getting licenseId by device Id (However, a device can have two types of licenses, we will pick up active one which is current chargeable one)

            var licenseId = _context.CP_Device_License.Where(x => x.DeviceId == deviceId && x.Status == "Active")
                .Join(_context.CP_License.Where(x => x.Status == "Active"),
                    x => x.LicenseId,
                    y => y.Id,
                    (x, y) => new { DeviceLicense = x, License = y })
                .Select(x => x.License.Id).FirstOrDefault();
            return new JsonResult
            {
                Data = _context.CP_Device_Group.Where(x => x.Id == groupId)
                    .Select(x => new
                    {
                        x.Id,
                        x.GroupName,
                        x.CreatedOn,
                        x.UpdatedOn,
                        x.StartTime,
                        x.EndTime,
                        x.Interval,
                        x.IsRandom,
                        x.Sat,
                        x.Sun,
                        x.Mon,
                        x.Tues,
                        x.Wed,
                        x.Thurs,
                        x.Fri,
                        LicenseId = licenseId,




                        DeviceDetails = _context.CP_Device.Where(d => d.GroupId == x.Id)
                            .GroupJoin(_context.CP_Device_Schedule,
                                d => d.DefaultScheduleId,
                                e => e.Id,
                                (d, e) => new { Device = d, DefaultSchedule = e })
                            .SelectMany(d => d.DefaultSchedule.DefaultIfEmpty(),
                                (d, e) => new { d.Device, DefaultSchedule = e })
                            .GroupJoin(_context.CP_Device_Group,
                                d => d.Device.GroupScheduleId,
                                e => e.Id,
                                (d, e) => new { d.Device, d.DefaultSchedule, GroupSchedule = e })
                            .SelectMany(d => d.GroupSchedule.DefaultIfEmpty(),
                                (d, e) => new { d.Device, d.DefaultSchedule, GroupSchedule = e })
                            .Select(d => new
                            {
                                d.Device.Id,
                                d.Device.DeviceName,
                                d.Device.DeviceUniqueId,



                                Settings = d.Device.GroupScheduleId != null ? "Group settings applied" : d.Device.DefaultScheduleId != null ? "Default settings applied" : "Individual settings applied",

                                //Interval = d.Device.GroupScheduleId != null ? d.GroupSchedule.Interval : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Interval : d.Device.Interval,
                                //IsRandom = d.Device.GroupScheduleId != null ? d.GroupSchedule.IsRandom : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.IsRandom : d.Device.IsRandom,
                                //StartTime = d.Device.GroupScheduleId != null ? d.GroupSchedule.StartTime : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.StartTime : d.Device.StartTime,
                                //EndTime = d.Device.GroupScheduleId != null ? d.GroupSchedule.EndTime : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.EndTime : d.Device.EndTime,
                                //Sat = d.Device.GroupScheduleId != null ? d.GroupSchedule.Sat : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Sat : d.Device.Sat,
                                //Sun = d.Device.GroupScheduleId != null ? d.GroupSchedule.Sun : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Sun : d.Device.Sun,
                                //Mon = d.Device.GroupScheduleId != null ? d.GroupSchedule.Mon : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Mon : d.Device.Mon,
                                //Tues = d.Device.GroupScheduleId != null ? d.GroupSchedule.Tues : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Tues : d.Device.Tues,
                                //Wed = d.Device.GroupScheduleId != null ? d.GroupSchedule.Wed : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Wed : d.Device.Wed,
                                //Thurs = d.Device.GroupScheduleId != null ? d.GroupSchedule.Thurs : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Thurs : d.Device.Thurs,
                                //Fri = d.Device.GroupScheduleId != null ? d.GroupSchedule.Fri : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Fri : d.Device.Fri,


                            }).ToList(),

                    }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult DeleteGroup(int groupId)
        {
            string message;
            try
            {
                var companyId = _context.CP_Device_Group.Where(x => x.Id == groupId).Select(x => x.CompanyId).FirstOrDefault();
                int defaultScheduleId = _context.CP_Device_Schedule.Where(x => x.CompanyId == companyId).Select(x => x.Id).FirstOrDefault();
                _context.Database.ExecuteSqlCommand($"Delete [CP_Device_Group] WHERE Id = {groupId}");
                _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device] SET GroupId = NULL, GroupScheduleId = NULL, DefaultScheduleId = {defaultScheduleId}, Interval = NULL, IsRandom = NULL, StartTime =  NULL, EndTime =  NULL, Sat = NULL, Sun = NULL, Mon  = NULL, Tues  = NULL, Wed  = NULL, Thurs  = NULL, Fri  = NULL  WHERE GroupId = {groupId}");
                Generator.IsReport = "Ok";
                message = "Group deleted successfully !";
            }
            catch (Exception ex)
            {
                Generator.IsReport = "NotOk";
                message = ex.Message;
            }

            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }



        //Schedule creation, update or change:
        public JsonResult ChangeDeviceName(string deviceUniqueId, string deviceName)
        {
            string message = "";

            bool deviceNameMatchesExceptOwnName = _context.CP_Device.Any(x => x.DeviceName == deviceName && x.DeviceUniqueId != deviceUniqueId);

            try
            {
                if (deviceNameMatchesExceptOwnName)
                {
                    Generator.IsReport = "NotOk";
                    message = "Another PC or device has same name ! Try different one.";
                }
                else
                {
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device] SET  DeviceName = '{deviceName}' WHERE DeviceUniqueId = '{deviceUniqueId}'");
                    Generator.IsReport = "Ok";
                    message = "Device name changed successfully !";
                }


            }
            catch (Exception ex)
            {
                var error = ex.Message;
                Generator.IsReport = "NotOk";
                message = "Something went wrong ! Please contact system administrator.";
            }
            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult CreateDeviceDefaultScheduleByCompany(CP_Device_Schedule scheduleObj)
        {
            if (scheduleObj.Interval == null)
            {
                scheduleObj.IsRandom = "Y";

            }
            else
            {
                scheduleObj.IsRandom = "N";
            }

            string message;
            try
            {
                if (scheduleObj.CompanyId == null)
                {
                    Generator.IsReport = "NotOk";
                    message = "Company id missing !";
                }
                else
                {
                    _scheduleServices.Save(scheduleObj);
                    _scheduleServices.SaveChanges();

                    Generator.IsReport = "Ok";
                    message = "Default-schedule created successfully !";
                }


            }
            catch (Exception ex)
            {
                message = ex.Message;
                Generator.IsReport = "NotOk";
                message = "Something went wrong, contact system administrator !";
            }

            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                }
            };

        }
        public JsonResult UpdateDeviceDefaultScheduleByCompany(CP_Device_Schedule scheduleObj)
        {
            string message = "";
            if (scheduleObj.Id > 0)
            {
                try
                {
                    if (scheduleObj.Interval == null)
                    {
                        scheduleObj.IsRandom = "Y";
                        _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device_Schedule] SET Interval = NULL, IsRandom = '{scheduleObj.IsRandom}', StartTime = '{scheduleObj.StartTime}', EndTime = '{scheduleObj.EndTime}', Sat = '{scheduleObj.Sat}', Sun = '{scheduleObj.Sun}' , Mon = '{scheduleObj.Mon}' , Tues = '{scheduleObj.Tues}', Wed = '{scheduleObj.Wed}', Thurs = '{scheduleObj.Thurs}', Fri = '{scheduleObj.Fri}' WHERE Id = {scheduleObj.Id}");
                    }
                    else
                    {
                        scheduleObj.IsRandom = "N";
                        _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device_Schedule] SET Interval = {scheduleObj.Interval}, IsRandom = '{scheduleObj.IsRandom}', StartTime = '{scheduleObj.StartTime}', EndTime = '{scheduleObj.EndTime}', Sat = '{scheduleObj.Sat}', Sun = '{scheduleObj.Sun}' , Mon = '{scheduleObj.Mon}' , Tues = '{scheduleObj.Tues}', Wed = '{scheduleObj.Wed}', Thurs = '{scheduleObj.Thurs}', Fri = '{scheduleObj.Fri}' WHERE Id = {scheduleObj.Id}");
                    }


                    Generator.IsReport = "Ok";
                    message = "Default-schedule updated successfully !";

                    //after that, now it is time to broadcast that company has changed default schedule
                    //to achieve that we need to get all the device ids who contain default-schedule settings
                    int[] devicesHaveDefaultSchedule = _context.CP_Device.Where(x => x.DefaultScheduleId == scheduleObj.Id).Select(x => x.Id).ToArray();
                    foreach (var deviceId in devicesHaveDefaultSchedule)
                    {
                        _notificationHubContext.Clients.All.onScheduleChange(deviceId);
                    }

                }
                catch (Exception ex)
                {
                    var error = ex.Message;
                    Generator.IsReport = "NotOk";
                    message = "Something went wrong ! Please contact system administrator.";
                }
            }
            else
            {
                Generator.IsReport = "NotOk";
                message = "Invalid Schedule Id ! Please contact system administrator.";
            }



            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult AddGroupScheduleSetting(CP_Device_Group groupObj, IEnumerable<CP_Device> deviceObjs)
        {
            var message = "";
            bool isDeviceIdExistsInDeviceTable = false;
            bool isDeviceAlreadyInGroup = false;


            //LOGIC: The device ids i am getting should be checked whether they are already in a group or not; 
            //if in any of the ids is engaged in group , then we wil refuse to "addGroupScheduleSetting"

            IList<int> deviceIdsArray = new List<int>();
            foreach (var item in deviceObjs)
            {
                deviceIdsArray.Add(item.Id);
            }
            IList<int> deviceDistinctIdsArrays = deviceIdsArray.Select(x => x).Distinct().ToList();

            for (int i = 0; i < deviceDistinctIdsArrays.Count; i++)
            {
                int id = deviceDistinctIdsArrays[i];
                isDeviceIdExistsInDeviceTable = _context.CP_Device.Any(x => x.Id == id);
                if (!isDeviceIdExistsInDeviceTable)
                {
                    break;

                }
            }

            for (int i = 0; i < deviceDistinctIdsArrays.Count; i++)
            {
                int id = deviceDistinctIdsArrays[i];
                isDeviceAlreadyInGroup = _context.CP_Device.Any(x => x.Id == id && x.GroupScheduleId > 0);
                if (isDeviceAlreadyInGroup)
                {
                    break;

                }
            }


            if (deviceDistinctIdsArrays.Count() == 1)
            {
                Generator.IsReport = "NotOk";
                message = "Group should contain at least two devices / PCs !";
            }
            else if (!isDeviceIdExistsInDeviceTable)
            {
                Generator.IsReport = "NotOk";
                message = "The group you are trying to create consists of unknown devices (NOT registered devices under any license) !";
            }
            else if (isDeviceAlreadyInGroup)
            {
                Generator.IsReport = "NotOk";
                message = "One / more devices that you have chosen to create a group are already in some group! Please choose only those devices that are not in a group.";
            }
            else if (GroupExistInSameCompany(groupObj))
            {
                Generator.IsReport = "NotOk";
                message = "Group name exists ! Please choose another name.";
            }
            else
            {

                //saving group first
                groupObj.CreatedOn = DateTime.UtcNow;
                _groupServices.Save(groupObj);
                _groupServices.SaveChanges();


                //then group details
                foreach (var item in deviceObjs)
                {
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device] SET GroupId = {groupObj.Id}, GroupScheduleId = {groupObj.Id}  WHERE Id = {item.Id}");
                    Generator.IsReport = "Ok";
                    message = "Group schedule setting applied to group '" + groupObj.GroupName + "' ! ";

                    //after that, now it is time to broadcast that company has created group-schedule
                    _notificationHubContext.Clients.All.onScheduleChange(item.Id);
                }

            }

            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UpdateGroupScheduleSetting(CP_Device_Group groupObj, IEnumerable<CP_Device> groupedDeviceObjs)
        {
            string message;
            var isGroupIdValid = _context.CP_Device_Group.Any(x => x.Id == groupObj.Id);
            bool isDeviceIdExistsInDeviceTable = false;
            bool isDeviceIdGroupable = false;


            //Getting only Ids in custom arrays and sorting them distinct for later usage.
            int[] groupedDeviceIds = groupedDeviceObjs.Select(item => item.Id).ToArray();
            IList<int> groupedDeviceDistinctIds = groupedDeviceIds.Select(x => x).Distinct().ToArray();

            //Ids user removed from groups
            int[] removedDeviceIds = _context.CP_Device.Where(x => !groupedDeviceDistinctIds.Contains(x.Id) && x.GroupId == groupObj.Id).Select(x => x.Id).ToArray();

            #region //Checking if user desireable groupids actually exists in DB or not
            foreach (var groupedDeviceId in groupedDeviceDistinctIds)
            {
                isDeviceIdExistsInDeviceTable = _context.CP_Device.Any(x => x.Id == groupedDeviceId);
                if (!isDeviceIdExistsInDeviceTable)
                {
                    break;

                }
            }
            #endregion

            #region //Checking if user desireable groupids actually groupable or not (Means: the ids we are getting can be engaged with another group, thus we have to cross check)
            foreach (var groupedDeviceId in groupedDeviceDistinctIds)
            {
                isDeviceIdGroupable = _context.CP_Device.Any(x => x.Id == groupedDeviceId && (x.GroupId == groupObj.Id || x.GroupId == null));
                if (!isDeviceIdGroupable)
                {
                    break;

                }
            }
            #endregion


            if (isGroupIdValid)
            {
                if (GroupExistInSameCompanyWithoutOwnName(groupObj))
                {
                    Generator.IsReport = "NotOk";
                    message = "The name you chosen for group matches with other group ! Please choose another name.";
                }
                else
                {

                    if (groupedDeviceDistinctIds.Count > 1)
                    {
                        if (isDeviceIdExistsInDeviceTable)
                        {
                            if (isDeviceIdGroupable)
                            {
                                #region If all ok
                                var updatedOn = DateTime.UtcNow;

                                if (groupObj.Interval == null)
                                {
                                    groupObj.IsRandom = "Y";
                                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device_Group] SET GroupName = '{groupObj.GroupName}', UpdatedOn = '{updatedOn}',  Interval = NULL,  IsRandom = '{groupObj.IsRandom}', StartTime = '{groupObj.StartTime}', EndTime = '{groupObj.EndTime}', Sat = '{groupObj.Sat}', Sun = '{groupObj.Sun}' , Mon = '{groupObj.Mon}' , Tues = '{groupObj.Tues}', Wed = '{groupObj.Wed}', Thurs = '{groupObj.Thurs}', Fri = '{groupObj.Fri}' WHERE Id = {groupObj.Id}");
                                }
                                else
                                {
                                    groupObj.IsRandom = "N";
                                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device_Group] SET GroupName = '{groupObj.GroupName}', UpdatedOn = '{updatedOn}',  Interval = {groupObj.Interval}, IsRandom = '{groupObj.IsRandom}', StartTime = '{groupObj.StartTime}', EndTime = '{groupObj.EndTime}', Sat = '{groupObj.Sat}', Sun = '{groupObj.Sun}' , Mon = '{groupObj.Mon}' , Tues = '{groupObj.Tues}', Wed = '{groupObj.Wed}', Thurs = '{groupObj.Thurs}', Fri = '{groupObj.Fri}' WHERE Id = {groupObj.Id}");
                                }

                                foreach (var groupedDeviceId in groupedDeviceDistinctIds)
                                {
                                    _context.Database.ExecuteSqlCommand($"Update [CP_Device] SET GroupId = {groupObj.Id}, GroupScheduleId = {groupObj.Id} WHERE Id = {groupedDeviceId}");

                                    //after that, now it is time to broadcast that company has updated group-schedule
                                    _notificationHubContext.Clients.All.onScheduleChange(groupedDeviceId);
                                }

                                var companyId = _context.CP_Device_Group.Where(x => x.Id == groupObj.Id).Select(x => x.CompanyId).FirstOrDefault();
                                int defaultScheduleId = _context.CP_Device_Schedule.Where(x => x.CompanyId == companyId).Select(x => x.Id).FirstOrDefault();
                                foreach (var removedDeviceId in removedDeviceIds)
                                {
                                    _context.Database.ExecuteSqlCommand($"Update [CP_Device] SET GroupId = NULL, GroupScheduleId = NULL, DefaultScheduleId = {defaultScheduleId}, Interval = NULL, StartTime =  NULL, EndTime =  NULL, Sat = NULL, Sun = NULL, Mon  = NULL, Tues  = NULL, Wed  = NULL, Thurs  = NULL, Fri  = NULL  WHERE Id = {removedDeviceId}");

                                    //after that, now it is time to broadcast that default settings get applied to each 'removedDeviceId' 
                                    _notificationHubContext.Clients.All.onScheduleChange(removedDeviceId);
                                }

                                Generator.IsReport = "Ok";
                                message = "Group settings updated successfully !";
                                #endregion
                            }
                            else
                            {
                                Generator.IsReport = "NotOk";
                                message = "One or more devices / Pcs that you chosen to update group are already engaged with another group ! Update not possible.";
                            }
                        }
                        else
                        {
                            Generator.IsReport = "NotOk";
                            message = "One or more devices / Pcs that you chosen to update group does not exists !";
                        }
                    }
                    else
                    {
                        Generator.IsReport = "NotOk";
                        message = "Group should contain at least two devices / PCs !";
                    }
                }


            }
            else
            {
                Generator.IsReport = "NotOk";
                message = "Update is not possible because the provide group does not exists at all !";
            }
            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UpdateSingularDeviceScheduling(CP_Device deviceObj)
        {
            var message = "";
            //first i have to check, if the device is engaged with any group-settings; what i meant is , i need to check if group-settings 
            //are applied on this requested device. if applied then i am not letting any update

            var existingDeviceObj = _context.CP_Device.Where(x => x.Id == deviceObj.Id).Select(x => new
            {
                x.GroupScheduleId,

            }).FirstOrDefault();

            if (existingDeviceObj == null)
            {
                Generator.IsReport = "NotOk";
                message = "Invalid deviceId !";
            }
            else if (existingDeviceObj.GroupScheduleId != null)
            {
                //It means, device has group settings applied on it, so can not update settings, first company has to remove this device from Group and then re-apply
                Generator.IsReport = "NotOk";
                message = "Group setting is applied on this device ! Remove this device from group and try again !";
            }
            else if (existingDeviceObj.GroupScheduleId == null)
            {
                //It means, device has default settings, then we can go ahead 


                if (deviceObj.Interval == null)
                {
                    deviceObj.IsRandom = "Y";
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device] SET DefaultScheduleId = NULL, Interval = NULL , IsRandom = '{deviceObj.IsRandom}', StartTime = '{deviceObj.StartTime}', EndTime = '{deviceObj.EndTime}', Sat = '{deviceObj.Sat}', Sun = '{deviceObj.Sun}' , Mon = '{deviceObj.Mon}' , Tues = '{deviceObj.Tues}', Wed = '{deviceObj.Wed}', Thurs = '{deviceObj.Thurs}', Fri = '{deviceObj.Fri}' WHERE Id = {deviceObj.Id}");
                }
                else
                {
                    deviceObj.IsRandom = "N";
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device] SET DefaultScheduleId = NULL, Interval = {deviceObj.Interval}, IsRandom = '{deviceObj.IsRandom}', StartTime = '{deviceObj.StartTime}', EndTime = '{deviceObj.EndTime}', Sat = '{deviceObj.Sat}', Sun = '{deviceObj.Sun}' , Mon = '{deviceObj.Mon}' , Tues = '{deviceObj.Tues}', Wed = '{deviceObj.Wed}', Thurs = '{deviceObj.Thurs}', Fri = '{deviceObj.Fri}' WHERE Id = {deviceObj.Id}");
                }


                Generator.IsReport = "Ok";
                message = "Device schedule setting updated successfully !";

                //after that, now it is time to broadcast that company has changed a device to individual-schedule
                _notificationHubContext.Clients.All.onScheduleChange(deviceObj.Id);
            }
            else
            {
                Generator.IsReport = "NotOk";
                message = "Something went wrong !";
            }
            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }


        //License activation:
        public JsonResult ActivateFreeTrial(string deviceName, string deviceUniqueId, string companyCode)
        {

            //step 1: First check korte hobe companycode er against j company ache ta valid kina?
            //step 2: Then check korte hobe ai company aage kono somoy Trial version use koreche kina (how : status == "In-Active / Expired")
            //step 3: If satisfied above, then create a license automatically that should have an active status with expiry timespan 
            //step 4" If Trial version currently challasse, then limit thakle shudhu device/PC guolo register kore dite hobe with some 
            //validation (i.e. device name unique holo kina ba ek-e device a ekadhikbar license activate korar attempt neya hocche kina aisob)
            var message = "";




            var companyObj = _context.CP_Profile.Where(x => x.CompanyCode == companyCode)
                 .Select(x => new
                 {
                     x.Id,
                 }).FirstOrDefault();

            if (companyObj != null)
            {
                var licenseStatus = _context.CP_License.Where(x => x.CompanyId == companyObj.Id && x.LicenseType == "Trial").Select(x => x.Status).FirstOrDefault();
                var activeChargeableLicenseFound = _context.CP_License.Any(x => x.CompanyId == companyObj.Id && x.LicenseType == "Chargeable" && x.Status == "Active");

                #region licenseStatus == "Active"
                if (licenseStatus == "Active")
                {
                    //we DON'T have to create a license 
                    int licenseId = _context.CP_License.Where(x => x.CompanyId == companyObj.Id && x.LicenseType == "Trial" && x.Status == "Active").Select(x => x.Id).FirstOrDefault();
                    int deviceLimit = _context.CP_License.Where(x => x.Id == licenseId).Select(x => x.DeviceLimit).FirstOrDefault();
                    string[] r = RegisterDeviceAndActivate(companyObj.Id, deviceName, deviceUniqueId, licenseId, deviceLimit);
                    message = r[0];
                    Generator.IsReport = r[1];

                    if (Generator.IsReport == "Ok")
                    {
                        var licenseDetails = _context.CP_License.Where(x => x.Id == licenseId)
                            .Join(_context.CP_License_Period.Where(x => x.Status == "Current"),
                                x => x.Id,
                                y => y.LicenseId,
                                (x, y) => new { License = x, CP_License_Period = y })
                            .Select(x => new
                            {
                                x.License.Id,
                                x.License.LicenseKey,
                                x.License.Status,
                                x.License.SecondaryStatus,
                                x.License.ActivatedOn,
                                x.CP_License_Period.ExpiryDate,
                            }).FirstOrDefault();

                        return new JsonResult
                        {
                            Data = new
                            {
                                Message = message,
                                Generator.IsReport,
                                Content = licenseDetails
                            },
                            JsonRequestBehavior = JsonRequestBehavior.AllowGet,
                        };
                    }
                }
                #endregion

                #region licenseStatus == "In-Active" || licenseStatus == "Expired"
                else if (licenseStatus == "In-Active" || licenseStatus == "Expired")
                {
                    message = "Your company has already enjoyed trial version of this software !";
                    Generator.IsReport = "NotOk";
                }
                #endregion

                #region licenseStatus Empty && activeChargeableLicenseFound
                else if (string.IsNullOrEmpty(licenseStatus) && activeChargeableLicenseFound)
                {
                    message = "You are not eligible for Trial License anymore ! Your company is currently using one/ more pro license (s). Use one of those license key (s).";
                    Generator.IsReport = "NotOk";
                }
                #endregion

                #region licenseStatus Empty && no active chargeable license found
                else if (string.IsNullOrEmpty(licenseStatus) && activeChargeableLicenseFound == false)
                {
                    #region //In that case, we have to create a license first

                    int licenseId = CreateTrialLicense(companyObj.Id);

                    #endregion

                    #region  //And then register device

                    if (licenseId > 0)
                    {
                        int deviceLimit = _context.CP_License.Where(x => x.Id == licenseId).Select(x => x.DeviceLimit).FirstOrDefault();
                        string[] r = RegisterDeviceAndActivate(companyObj.Id, deviceName, deviceUniqueId, licenseId, deviceLimit);
                        message = r[0];
                        Generator.IsReport = r[1];

                        if (Generator.IsReport == "Ok")
                        {
                            var licenseDetails = _context.CP_License.Where(x => x.Id == licenseId)
                                .Join(_context.CP_License_Period.Where(x => x.Status == "Current"),
                                    x => x.Id,
                                    y => y.LicenseId,
                                    (x, y) => new { License = x, CP_License_Period = y })
                                .Select(x => new
                                {
                                    x.License.Id,
                                    x.License.LicenseKey,
                                    x.License.Status,
                                    x.License.SecondaryStatus,
                                    x.License.ActivatedOn,
                                    x.CP_License_Period.ExpiryDate,
                                }).FirstOrDefault();

                            return new JsonResult
                            {
                                Data = new
                                {
                                    Message = message,
                                    Generator.IsReport,
                                    Content = licenseDetails
                                },
                                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
                            };
                        }

                    }
                    else
                    {
                        message = "Something went wrong ! Contact system administrator.";
                        Generator.IsReport = "NotOk";
                    }
                    #endregion
                }
                #endregion
            }
            else
            {
                message = "Your company-code is not valid !";
                Generator.IsReport = "NotOk";
            }

            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport,
                    Content = (string)null
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
            };

        }
        public JsonResult ActivateLicenseKey(string deviceName, string deviceUniqueId, string companyCode, string licenseKey)
        {
            //licensekey valid?
            //companyCode diye company khuje ber korte hobe
            //licenseKey diye company khuje ber korte hobe
            //check korte hobe companyCode-er company ebong licenseKey-er company same kina?
            //then khujte hobe ai requested-device registered kina ? (by checking in CP_Device_License Table)
            ///////Jodi registered hoy kintu status = "Active" hoy tar maane hocche device currently using a valid 
            ///////license, in that case: requested license-key active kora somvob na
            ///////R jodi status = "In-Active" hoy tar maane aager license er meyad shesh, so requested license activate kora jabe
            //R jodi requested device registered-e na thake tahole prothome device register kore then license-key activate korte hobe


            object details = new object();
            var message = "";

            //First getting companyId from companyCode:
            #region companyObj
            var companyObj = _context.CP_Profile.Where(x => x.CompanyCode == companyCode)
                 .Select(x => new
                 {
                     x.Id,
                 }).FirstOrDefault();

            #endregion

            //Then getting licenseObj by provided licenseKey:
            #region licenseObj
            var licenseObj = _context.CP_License.Where(x => x.LicenseKey == licenseKey && x.CompanyId == companyObj.Id && x.LicenseType != "Trial")
                 .Select(x => new
                 {
                     x.Id,
                     x.Status,
                     x.SecondaryStatus,
                     x.CompanyId,
                     x.DeviceLimit,
                     x.LicenseType
                 }).FirstOrDefault();
            #endregion






            if (companyObj != null)
            {
                if (licenseObj != null)
                {
                    //Getting deviceObj by provided deviceUniqueId and companyId (this defines if device is already registered or not)

                    #region deviceObj
                    var deviceObj = _context.CP_Device_License
                        .Join(_context.CP_Device.Where(x => x.DeviceUniqueId == deviceUniqueId && x.CompanyId == companyObj.Id),
                            x => x.DeviceId,
                            y => y.Id,
                            (x, y) => new { DeviceLicense = x, Device = y })
                        .Select(x => new
                        {
                            x.DeviceLicense.Id,
                            x.DeviceLicense.DeviceId,
                            x.DeviceLicense.Status

                        }).OrderByDescending(x => x.Id).FirstOrDefault();
                    #endregion

                    #region if deviceObj is null
                    if (deviceObj == null)
                    {
                        //Since the requested device is NOT registered, then we have to register device first and activate afterwards
                        string[] r = RegisterDeviceAndActivate(companyObj.Id, deviceName, deviceUniqueId, licenseObj.Id, licenseObj.DeviceLimit);
                        message = r[0];
                        Generator.IsReport = r[1];
                        if (Generator.IsReport == "Ok")
                        {
                            var content = details = _context.CP_License.Where(x => x.Id == licenseObj.Id)
                                .Join(_context.CP_License_Period.Where(x => x.Status == "Current"),
                                    x => x.Id,
                                    y => y.LicenseId,
                                    (x, y) => new { License = x, CP_License_Period = y })
                                .Select(x => new
                                {
                                    x.License.Id,
                                    x.License.LicenseKey,
                                    x.License.Status,
                                    x.License.SecondaryStatus,
                                    x.License.ActivatedOn,
                                    x.CP_License_Period.ExpiryDate,
                                }).FirstOrDefault();

                            return new JsonResult
                            {
                                Data = new
                                {
                                    Message = message,
                                    Generator.IsReport,
                                    Content = content
                                },
                                JsonRequestBehavior = JsonRequestBehavior.AllowGet
                            };
                        }


                    }
                    #endregion

                    #region if deviceObj.Status == "Active"
                    else if (deviceObj.Status == "Active")
                    {
                        //if deviceObj.Status is "Active", it means device is under a valid license key and it is currently being used, so another activation is not possible
                        message = "Currently, you have an activated license key ! Simply login with user credentials.";
                        Generator.IsReport = "NotOk";



                        //Wrting logs:

                        string myPath = "";
                        myPath = HostingEnvironment.MapPath("~/Logs/logs.txt");


                        List<string> lines = new List<string>();
                        var Written_At = DateTime.UtcNow.ToString();
                        lines.Add("Device Name           : " + deviceName);
                        lines.Add("Device Unique Id      : " + deviceUniqueId);
                        lines.Add("Company Code          : " + companyCode);
                        lines.Add("Company Id            : " + companyObj.Id);
                        lines.Add("License Key           : " + licenseKey);
                        lines.Add("Device  Id            : " + deviceObj.DeviceId);
                        lines.Add("Device Status         : " + deviceObj.Status + "         //active means already registered ");
                        lines.Add("Written_At (UTC Time) : " + Written_At);
                        lines.Add("===============================================================================\n");


                        if (!File.Exists(myPath))
                        {
                            File.Create(myPath).Dispose();

                            File.WriteAllLines(myPath, lines);

                        }
                        else if (File.Exists(myPath))
                        {
                            File.AppendAllLines(myPath, lines);
                        }

                    }
                    #endregion

                    #region deviceObj.Status == "In-Active"
                    else if (deviceObj.Status == "In-Active")
                    {
                        //Since the device registration is NOT needed, just we have to activate license key for this requested device
                        try
                        {
                            bool deviceNameMatchesExcepetOwnName = _context.CP_Device.Any(x => x.DeviceName == deviceName && x.DeviceUniqueId != deviceUniqueId);
                            //Getting device limit
                            int deviceRegisteredSoFar = DeviceRegisteredSoFarUnderALicense(licenseObj.Id);
                            if (deviceNameMatchesExcepetOwnName)
                            {

                                message = "Another PC or Device has same name, Please choose different name for your PC / device.";
                                Generator.IsReport = "NotOk";

                            }
                            else if (deviceRegisteredSoFar >= licenseObj.DeviceLimit)
                            {
                                message = "You are allowed to register " + licenseObj.DeviceLimit + " PC / devices only under your license.";
                                Generator.IsReport = "NotOk";
                            }
                            else
                            {
                                //However, we have to update the deviceName if new different deviceName is received 
                                _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device] SET DeviceName = '{deviceName}' WHERE DeviceUniqueId = '{deviceUniqueId}'");

                                //Since the device registration is NOT needed, just have to activate license key for this requested device
                                var deviceLicense = new CP_Device_License
                                {
                                    DeviceId = deviceObj.DeviceId,
                                    LicenseId = licenseObj.Id,
                                    Status = "Active",
                                };

                                _deviceLicenseServices.Save(deviceLicense);
                                _deviceLicenseServices.SaveChanges();



                                message = "License activated successfully !";
                                Generator.IsReport = "Ok";


                                var content = _context.CP_License.Where(x => x.Id == licenseObj.Id)
                                    .Join(_context.CP_License_Period.Where(x => x.Status == "Current"),
                                        x => x.Id,
                                        y => y.LicenseId,
                                        (x, y) => new { License = x, CP_License_Period = y })
                                    .Select(x => new
                                    {
                                        x.License.Id,
                                        x.License.LicenseKey,
                                        x.License.Status,
                                        x.License.SecondaryStatus,
                                        x.License.ActivatedOn,
                                        x.CP_License_Period.ExpiryDate,
                                    }).FirstOrDefault();


                                return new JsonResult
                                {
                                    Data = new
                                    {
                                        Message = message,
                                        Generator.IsReport,
                                        Content = content
                                    },
                                    JsonRequestBehavior = JsonRequestBehavior.AllowGet
                                };
                            }

                        }
                        catch
                        {
                            message = "Something went wrong ! Please contact system administrator.";
                            Generator.IsReport = "NotOk";
                        }
                    }
                    #endregion

                    //if (companyObj.Id != licenseObj.CompanyId)
                    //{
                    //    //if companyObj.Id != licenseObj.CompanyId, that means user is using either different company's code or different company's license
                    //    message = "Either company-code is invalid or license key !";
                    //    Generator.IsReport = "NotOk";
                    //}
                    //else if (licenseObj.LicenseType == "Trial")
                    //{
                    //    //if LicenseType is "Trial", then it cannot be activated either
                    //    message = "Invalid license key ! Please use valid license key !";
                    //    Generator.IsReport = "NotOk";
                    //}
                    //else if (licenseObj.Status != "Active")
                    //{
                    //    //if licenseObj.Status is NOT "Active", it means license key is in "Requested" status which is not possible to acitivate
                    //    message = "Invalid license key !";
                    //    Generator.IsReport = "NotOk";
                    //}
                    //else
                    //{
                    //    //Getting deviceObj by provided deviceUniqueId and companyId (this defines if device is already registered or not)

                    //    #region deviceObj
                    //    var deviceObj = _context.CP_Device_License
                    //        .Join(_context.CP_Device.Where(x => x.DeviceUniqueId == deviceUniqueId && x.CompanyId == companyObj.Id),
                    //            x => x.DeviceId,
                    //            y => y.Id,
                    //            (x, y) => new { DeviceLicense = x, Device = y })
                    //        .Select(x => new
                    //        {
                    //            x.DeviceLicense.Id,
                    //            x.DeviceLicense.DeviceId,
                    //            x.DeviceLicense.Status

                    //        }).OrderByDescending(x => x.Id).FirstOrDefault();
                    //    #endregion

                    //    #region if deviceObj is null
                    //    if (deviceObj == null)
                    //    {
                    //        //Since the requested device is NOT registered, then we have to register device first and activate afterwards
                    //        string[] r = RegisterDeviceAndActivate(companyObj.Id, deviceName, deviceUniqueId, licenseObj.Id, licenseObj.DeviceLimit);
                    //        message = r[0];
                    //        Generator.IsReport = r[1];
                    //        if (Generator.IsReport == "Ok")
                    //        {
                    //            var content = details = _context.CP_License.Where(x => x.Id == licenseObj.Id)
                    //                .Join(_context.CP_License_Period.Where(x => x.Status == "Current"),
                    //                    x => x.Id,
                    //                    y => y.LicenseId,
                    //                    (x, y) => new { License = x, CP_License_Period = y })
                    //                .Select(x => new
                    //                {
                    //                    x.License.Id,
                    //                    x.License.LicenseKey,
                    //                    x.License.Status,
                    //                    x.License.SecondaryStatus,
                    //                    x.License.ActivatedOn,
                    //                    x.CP_License_Period.ExpiryDate,
                    //                }).FirstOrDefault();

                    //            return new JsonResult
                    //            {
                    //                Data = new
                    //                {
                    //                    Message = message,
                    //                    Generator.IsReport,
                    //                    Content = content
                    //                },
                    //                JsonRequestBehavior = JsonRequestBehavior.AllowGet
                    //            };
                    //        }


                    //    }
                    //    #endregion

                    //    #region if deviceObj.Status == "Active"
                    //    else if (deviceObj.Status == "Active")
                    //    {
                    //        //if deviceObj.Status is "Active", it means device is under a valid license key and it is currently being used, so another activation is not possible
                    //        message = "Currently, you have an activated license key ! Simply login with user credentials.";
                    //        Generator.IsReport = "NotOk";
                    //    }
                    //    #endregion

                    //    #region deviceObj.Status == "In-Active"
                    //    else if (deviceObj.Status == "In-Active")
                    //    {
                    //        //Since the device registration is NOT needed, just we have to activate license key for this requested device
                    //        try
                    //        {
                    //            bool deviceNameMatchesExcepetOwnName = _context.CP_Device.Any(x => x.DeviceName == deviceName && x.DeviceUniqueId != deviceUniqueId);
                    //            //Getting device limit
                    //            int deviceRegisteredSoFar = DeviceRegisteredSoFarUnderALicense(licenseObj.Id);
                    //            if (deviceNameMatchesExcepetOwnName)
                    //            {

                    //                message = "Another PC or Device has same name, Please choose different name for your PC / device.";
                    //                Generator.IsReport = "NotOk";

                    //            }
                    //            else if (deviceRegisteredSoFar >= licenseObj.DeviceLimit)
                    //            {
                    //                message = "You are allowed to register " + licenseObj.DeviceLimit + " PC / devices only under your license.";
                    //                Generator.IsReport = "NotOk";
                    //            }
                    //            else
                    //            {
                    //                //However, we have to update the deviceName if new different deviceName is received 
                    //                _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device] SET DeviceName = '{deviceName}' WHERE DeviceUniqueId = '{deviceUniqueId}'");

                    //                //Since the device registration is NOT needed, just have to activate license key for this requested device
                    //                var deviceLicense = new CP_Device_License
                    //                {
                    //                    DeviceId = deviceObj.DeviceId,
                    //                    LicenseId = licenseObj.Id,
                    //                    Status = "Active",
                    //                };

                    //                _deviceLicenseServices.Save(deviceLicense);
                    //                _deviceLicenseServices.SaveChanges();



                    //                message = "License activated successfully !";
                    //                Generator.IsReport = "Ok";


                    //                var content = _context.CP_License.Where(x => x.Id == licenseObj.Id)
                    //                    .Join(_context.CP_License_Period.Where(x => x.Status == "Current"),
                    //                        x => x.Id,
                    //                        y => y.LicenseId,
                    //                        (x, y) => new { License = x, CP_License_Period = y })
                    //                    .Select(x => new
                    //                    {
                    //                        x.License.Id,
                    //                        x.License.LicenseKey,
                    //                        x.License.Status,
                    //                        x.License.SecondaryStatus,
                    //                        x.License.ActivatedOn,
                    //                        x.CP_License_Period.ExpiryDate,
                    //                    }).FirstOrDefault();


                    //                return new JsonResult
                    //                {
                    //                    Data = new
                    //                    {
                    //                        Message = message,
                    //                        Generator.IsReport,
                    //                        Content = content
                    //                    },
                    //                    JsonRequestBehavior = JsonRequestBehavior.AllowGet
                    //                };
                    //            }

                    //        }
                    //        catch
                    //        {
                    //            message = "Something went wrong ! Please contact system administrator.";
                    //            Generator.IsReport = "NotOk";
                    //        }
                    //    }
                    //    #endregion
                    //}
                }
                else
                {
                    //if licenseObj is null, that means LICENSE is completely garbage
                    message = "Invalid license key !";
                    Generator.IsReport = "NotOk";
                }
            }
            else
            {
                //if companyObj is null, that means COMPANY-CODE is completely garbage
                message = "Invalid company-code !";
                Generator.IsReport = "NotOk";
            }


            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport,
                    Content = (string)null
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }



        //Screenshots related
        public JsonResult SaveScreenCapture(IEnumerable<CP_ScreenCapture> screenCaptureObjs)
        {
            var message = "";

            foreach (var item in screenCaptureObjs)
            {
                try
                {
                    var companyId = _context.CP_User.Where(x => x.Id == item.UserId).Select(x => x.CompanyId).FirstOrDefault();
                    int deviceId = _context.CP_Device.Where(x => x.DeviceUniqueId == item.DeviceUniqueId && x.CompanyId == companyId).Select(x => x.Id).FirstOrDefault();

                    //We are getting screenshot by base64 from devices / pcs; I am converting it to image and 
                    //saving it to a folder, and saving imagename in Screenshot column (which is nothing but random strings with
                    //'unique column primary Id' and 'companyId' since only company name can not make it unique name)

                    item.DeviceId = deviceId;
                    item.CompanyId = companyId;
                    //item.ScreenShot = imageName;
                    item.EntriedOn = DateTime.UtcNow;
                    _screenCatureServices.Save(item);
                    _screenCatureServices.SaveChanges();


                    int primaryKey = item.Id;
                    var imageName = primaryKey + "_" + companyId + "_" + GenericServices.CreateRandomNumber1() + GenericServices.CreateRandomNumber2() + ".jpg";
                    ChangeBase64StringIntoImageAndSave(item.ScreenShot, imageName);
                    _context.Database.ExecuteSqlCommand($"Update [CP_ScreenCapture] SET ScreenShot = '{imageName}' WHERE Id = {item.Id}");

                    Generator.IsReport = "Ok";
                    message = "Screenshot(s) saved successfully !";

                }
                catch (Exception ex)
                {
                    var error = ex.Message;
                    Generator.IsReport = "NotOk";
                    message = "Something went wrong ! Please contact system administrator.";
                }
            }


            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetScreenshotsListByDeviceUniqueId(string deviceUniqueId)
        {
            return new JsonResult
            {
                //We will be retrieving only 5 photos for the first time

                Data = _context.CP_ScreenCapture.Where(x => x.DeviceUniqueId == deviceUniqueId)
                    .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                    .Join(_context.CP_User,
                        x => x.SCapture.UserId,
                        y => y.Id,
                        (x, y) => new { x.SCapture, x.Device, User = y })
                    .Select(x => new
                    {
                        x.SCapture.Id,
                        x.SCapture.CapturedOn,
                        x.SCapture.EntriedOn,
                        x.SCapture.Status,
                        x.SCapture.MouseClick,
                        x.SCapture.KeyStroke,
                        x.SCapture.ScreenShot,

                        x.User.FirstName,
                        x.User.LastName,
                        x.User.Designation,
                        x.User.Photo,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                        x.Device.DeviceUniqueId,
                        x.Device.DeviceName,

                        Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    }).OrderByDescending(x => x.Id).Take(5).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetScreenshotsListByUserId(int userId)
        {

            return new JsonResult
            {

                Data = _context.CP_ScreenCapture.Where(x => x.UserId == userId)
                    .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                    .Join(_context.CP_User,
                        x => x.SCapture.UserId,
                        y => y.Id,
                        (x, y) => new { x.SCapture, x.Device, User = y })
                    .Select(x => new
                    {
                        x.SCapture.Id,
                        x.SCapture.CapturedOn,
                        x.SCapture.EntriedOn,
                        x.SCapture.Status,
                        x.SCapture.MouseClick,
                        x.SCapture.KeyStroke,
                        x.SCapture.ScreenShot,

                        x.User.FirstName,
                        x.User.LastName,
                        x.User.Designation,
                        x.User.Photo,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                        x.Device.DeviceUniqueId,
                        x.Device.DeviceName,

                        Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    }).OrderByDescending(x => x.Id).Take(5).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetFiveScreenshotsListByDeviceId(int deviceId)
        {
            return new JsonResult
            {
                //We will be retrieving only 5 photos for the first time

                Data = _context.CP_ScreenCapture.Where(x => x.DeviceId == deviceId)
                    .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                    .Join(_context.CP_User,
                        x => x.SCapture.UserId,
                        y => y.Id,
                        (x, y) => new { x.SCapture, x.Device, User = y })
                    .Select(x => new
                    {
                        x.SCapture.Id,
                        x.SCapture.CapturedOn,
                        x.SCapture.EntriedOn,
                        x.SCapture.Status,
                        x.SCapture.MouseClick,
                        x.SCapture.KeyStroke,
                        x.SCapture.ScreenShot,

                        x.User.FirstName,
                        x.User.LastName,
                        x.User.Designation,
                        x.User.Photo,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                        x.Device.DeviceUniqueId,
                        x.Device.DeviceName,

                        Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    }).OrderByDescending(x => x.Id).Take(5).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetMoreScreenshotsListByDeviceLastRetrievalId(int lastId)
        {

            return new JsonResult
            {

                Data = _context.CP_ScreenCapture.Where(x => x.Id == lastId)
                    .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                    .Join(_context.CP_User,
                        x => x.SCapture.UserId,
                        y => y.Id,
                        (x, y) => new { x.SCapture, x.Device, User = y })
                    .Select(x => new
                    {
                        x.SCapture.Id,
                        x.SCapture.CapturedOn,
                        x.SCapture.EntriedOn,
                        x.SCapture.Status,
                        x.SCapture.MouseClick,
                        x.SCapture.KeyStroke,
                        x.SCapture.ScreenShot,

                        x.User.FirstName,
                        x.User.LastName,
                        x.User.Designation,
                        x.User.Photo,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                        x.Device.DeviceUniqueId,
                        x.Device.DeviceName,

                        Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    }).OrderByDescending(x => x.Id).Take(5).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        
        public JsonResult GetScreenshotsListByCustomRangeByDeviceId(int deviceId, DateTime dateFrom, DateTime dateTo)
        {

            dateFrom = dateFrom.ToUniversalTime();
            dateTo = dateTo.ToUniversalTime();

            return new JsonResult
            {
                //We will be retrieving only 5 photos for the first time

                Data = _context.CP_ScreenCapture.Where(x => x.DeviceId == deviceId && (x.CapturedOn >= dateFrom && x.CapturedOn <= dateTo))
                    .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                    .Join(_context.CP_User,
                        x => x.SCapture.UserId,
                        y => y.Id,
                        (x, y) => new { x.SCapture, x.Device, User = y })
                    .Select(x => new
                    {
                        x.SCapture.Id,
                        x.SCapture.CapturedOn,
                        x.SCapture.EntriedOn,
                        x.SCapture.Status,
                        x.SCapture.MouseClick,
                        x.SCapture.KeyStroke,
                        x.SCapture.ScreenShot,

                        x.User.FirstName,
                        x.User.LastName,
                        x.User.Designation,
                        x.User.Photo,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                        x.Device.DeviceUniqueId,
                        x.Device.DeviceName,

                        Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    }).OrderByDescending(x => x.Id).Take(6).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetScreenshotsListByCustomRangeByUserId(int userId, DateTime dateFrom, DateTime dateTo)
        {

            dateFrom = dateFrom.ToUniversalTime();
            dateTo = dateTo.ToUniversalTime();

            return new JsonResult
            {
                //We will be retrieving only 5 photos for the first time

                Data = _context.CP_ScreenCapture.Where(x => x.DeviceId == userId && (x.CapturedOn >= dateFrom && x.CapturedOn <= dateTo))
                    .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                    .Join(_context.CP_User,
                        x => x.SCapture.UserId,
                        y => y.Id,
                        (x, y) => new { x.SCapture, x.Device, User = y })
                    .Select(x => new
                    {
                        x.SCapture.Id,
                        x.SCapture.CapturedOn,
                        x.SCapture.EntriedOn,
                        x.SCapture.Status,
                        x.SCapture.MouseClick,
                        x.SCapture.KeyStroke,
                        x.SCapture.ScreenShot,

                        x.User.FirstName,
                        x.User.LastName,
                        x.User.Designation,
                        x.User.Photo,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                        x.Device.DeviceUniqueId,
                        x.Device.DeviceName,

                        Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    }).OrderByDescending(x => x.Id).Take(6).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetScreenshotsListByCustomRangeByUserId(int userId, DateTime dateFrom, DateTime dateTo, int lastId)
        {

            dateFrom = dateFrom.ToUniversalTime();
            dateTo = dateTo.ToUniversalTime();

            return new JsonResult
            {
                //We will be retrieving only 5 photos for the first time

                Data = _context.CP_ScreenCapture.Where(x => x.UserId == userId && (x.CapturedOn >= dateFrom && x.CapturedOn <= dateTo) && x.Id < lastId)
                    .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                    .Join(_context.CP_User,
                        x => x.SCapture.UserId,
                        y => y.Id,
                        (x, y) => new { x.SCapture, x.Device, User = y })
                    .Select(x => new
                    {
                        x.SCapture.Id,
                        x.SCapture.CapturedOn,
                        x.SCapture.EntriedOn,
                        x.SCapture.Status,
                        x.SCapture.MouseClick,
                        x.SCapture.KeyStroke,
                        x.SCapture.ScreenShot,

                        x.User.FirstName,
                        x.User.LastName,
                        x.User.Designation,
                        x.User.Photo,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                        x.Device.DeviceUniqueId,
                        x.Device.DeviceName,

                        Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    }).OrderByDescending(x => x.Id).Take(6).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult GetScreenshotsListForSpecificSessionByDeviceId(int deviceId, string session)
        {
            DateTime dateFrom = new DateTime();//<-----I need to work here
            DateTime dateTo = new DateTime();

            if (session == "Today")
            {
                dateFrom = DateTime.Today.ToUniversalTime();
                dateTo = DateTime.Today.ToUniversalTime().AddDays(1).AddTicks(-1);
            }
            else if (session == "Yesterday")
            {
                dateFrom = DateTime.Today.ToUniversalTime().AddDays(-1);
                dateTo = DateTime.Today.ToUniversalTime().AddDays(-1).AddDays(1).AddTicks(-1);
            }
            else if (session == "OneWeek")
            {
                dateFrom = DateTime.Today.ToUniversalTime().AddDays(-6);
                dateTo = DateTime.Today.ToUniversalTime().AddDays(1).AddTicks(-1);
            }

            var result = _context.CP_ScreenCapture.Where(x => x.DeviceId == deviceId && (x.CapturedOn >= dateFrom && x.CapturedOn <= dateTo))
                .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                .Join(_context.CP_User,
                    x => x.SCapture.UserId,
                    y => y.Id,
                    (x, y) => new { x.SCapture, x.Device, User = y })
                .Select(x => new
                {
                    x.SCapture.Id,
                    x.SCapture.CapturedOn,
                    x.SCapture.EntriedOn,
                    x.SCapture.Status,
                    x.SCapture.MouseClick,
                    x.SCapture.KeyStroke,
                    x.SCapture.ScreenShot,

                    x.User.FirstName,
                    x.User.LastName,
                    x.User.Designation,
                    x.User.Photo,
                    x.User.Gender,
                    UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                    x.Device.DeviceUniqueId,
                    x.Device.DeviceName,

                    Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    SearchSession = "Search Result for " + session,
                }).OrderByDescending(x => x.Id).Take(6).ToList();


            return new JsonResult
            {
                Data = result,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetScreenshotsListForSpecificSessionByUserId(int userId, string session)
        {
            DateTime dateFrom = new DateTime();//<-----I need to work here
            DateTime dateTo = new DateTime();

            if (session == "Today")
            {
                dateFrom = DateTime.Today.ToUniversalTime();
                dateTo = DateTime.Today.ToUniversalTime().AddDays(1).AddTicks(-1);
            }
            else if (session == "Yesterday")
            {
                dateFrom = DateTime.Today.ToUniversalTime().AddDays(-1);
                dateTo = DateTime.Today.ToUniversalTime().AddDays(-1).AddDays(1).AddTicks(-1);
            }
            else if (session == "OneWeek")
            {
                dateFrom = DateTime.Today.ToUniversalTime().AddDays(-6);
                dateTo = DateTime.Today.ToUniversalTime().AddDays(1).AddTicks(-1);
            }

            var result = _context.CP_ScreenCapture.Where(x => x.UserId == userId && (x.CapturedOn >= dateFrom && x.CapturedOn <= dateTo))
                .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                .Join(_context.CP_User,
                    x => x.SCapture.UserId,
                    y => y.Id,
                    (x, y) => new { x.SCapture, x.Device, User = y })
                .Select(x => new
                {
                    x.SCapture.Id,
                    x.SCapture.CapturedOn,
                    x.SCapture.EntriedOn,
                    x.SCapture.Status,
                    x.SCapture.MouseClick,
                    x.SCapture.KeyStroke,
                    x.SCapture.ScreenShot,

                    x.User.FirstName,
                    x.User.LastName,
                    x.User.Designation,
                    x.User.Photo,
                    x.User.Gender,
                    UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                    x.Device.DeviceUniqueId,
                    x.Device.DeviceName,

                    Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    SearchSession = "Search Result for " + session,
                }).OrderByDescending(x => x.Id).Take(6).ToList();


            return new JsonResult
            {
                Data = result,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetScreenshotsListForSpecificSessionByUserId(int userId, string session, int lastId)
        {
            DateTime dateFrom = new DateTime();//<-----I need to work here
            DateTime dateTo = new DateTime();

            if (session == "Today")
            {
                dateFrom = DateTime.Today.ToUniversalTime();
                dateTo = DateTime.Today.ToUniversalTime().AddDays(1).AddTicks(-1);
            }
            else if (session == "Yesterday")
            {
                dateFrom = DateTime.Today.ToUniversalTime().AddDays(-1);
                dateTo = DateTime.Today.ToUniversalTime().AddDays(-1).AddDays(1).AddTicks(-1);
            }
            else if (session == "OneWeek")
            {
                dateFrom = DateTime.Today.ToUniversalTime().AddDays(-6);
                dateTo = DateTime.Today.ToUniversalTime().AddDays(1).AddTicks(-1);
            }

            var result = _context.CP_ScreenCapture.Where(x => x.UserId == userId && (x.CapturedOn >= dateFrom && x.CapturedOn <= dateTo) && x.Id < lastId)
                .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                .Join(_context.CP_User,
                    x => x.SCapture.UserId,
                    y => y.Id,
                    (x, y) => new { x.SCapture, x.Device, User = y })
                .Select(x => new
                {
                    x.SCapture.Id,
                    x.SCapture.CapturedOn,
                    x.SCapture.EntriedOn,
                    x.SCapture.Status,
                    x.SCapture.MouseClick,
                    x.SCapture.KeyStroke,
                    x.SCapture.ScreenShot,

                    x.User.FirstName,
                    x.User.LastName,
                    x.User.Designation,
                    x.User.Photo,
                    x.User.Gender,
                    UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                    x.Device.DeviceUniqueId,
                    x.Device.DeviceName,

                    Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    SearchSession = "Search Result for " + session,
                }).OrderByDescending(x => x.Id).Take(6).ToList();


            return new JsonResult
            {
                Data = result,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }


        public JsonResult GetScreenshotsListByLicenseId(int licenseId)
        {

            //Now we are getting deviceList under this given license 

            int?[] deviceIds = _context.CP_Device_License.Where(x => x.LicenseId == licenseId).Select(x => x.DeviceId).Distinct().ToArray();

            var result = _context.CP_Device.Where(a => deviceIds.Contains(a.Id)).Select(a => new
            {
                a.Id,
                a.DeviceUniqueId,
                a.DeviceName,


                Screenshots = _context.CP_ScreenCapture.Where(x => x.DeviceId == a.Id)
                    .Join(_context.CP_Device,
                        x => x.DeviceId,
                        y => y.Id,
                        (x, y) => new { SCapture = x, Device = y })
                    .Join(_context.CP_User,
                        x => x.SCapture.UserId,
                        y => y.Id,
                        (x, y) => new { x.SCapture, x.Device, User = y })
                    .Select(x => new
                    {
                        x.SCapture.Id,
                        x.SCapture.CapturedOn,
                        x.SCapture.EntriedOn,
                        x.SCapture.Status,
                        x.SCapture.MouseClick,
                        x.SCapture.KeyStroke,
                        x.SCapture.ScreenShot,

                        x.User.FirstName,
                        x.User.LastName,
                        x.User.Designation,
                        x.User.Photo,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                        x.Device.DeviceUniqueId,
                        x.Device.DeviceName,

                        Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    }).OrderByDescending(x => x.Id).Take(5).ToList(),


            }).ToList();

            return new JsonResult
            {
                //We will be retrieving only 5 photos for the first time

                Data = result,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetScreenshotsListByCompanyId(int companyId)
        {

            //Now we are getting deviceList under this given companyId 

            int[] deviceIds = _context.CP_Device.Where(x => x.CompanyId == companyId).Select(x => x.Id).Distinct().ToArray();

            var result = _context.CP_Device.Where(a => deviceIds.Contains(a.Id)).Select(a => new
            {
                a.Id,
                a.DeviceUniqueId,
                a.DeviceName,
                LastUser = _context.CP_ScreenCapture.Where(x => x.DeviceId == a.Id)
                                    .Join(_context.CP_User,
                                        x => x.UserId,
                                        y => y.Id,
                                        (x, y) => new { SC = x, User = y }).Select(x => x.User.FirstName + " " + x.User.LastName).FirstOrDefault(),
                //LastUserFullName = GetLastlyUsedDeviceUserFullName(a.Id),

                Screenshots = _context.CP_ScreenCapture.Where(x => x.DeviceId == a.Id)
                    .Join(_context.CP_Device,
                        x => x.DeviceId,
                        y => y.Id,
                        (x, y) => new { SCapture = x, Device = y })
                    .Join(_context.CP_User,
                        x => x.SCapture.UserId,
                        y => y.Id,
                        (x, y) => new { x.SCapture, x.Device, User = y })
                    .Select(x => new
                    {
                        x.SCapture.Id,
                        x.SCapture.CapturedOn,
                        x.SCapture.EntriedOn,
                        x.SCapture.Status,
                        x.SCapture.MouseClick,
                        x.SCapture.KeyStroke,
                        x.SCapture.ScreenShot,

                        x.User.FirstName,
                        x.User.LastName,
                        x.User.Designation,
                        x.User.Photo,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                        x.Device.DeviceUniqueId,
                        x.Device.DeviceName,

                        Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    }).OrderByDescending(x => x.Id).Take(5).ToList(),

            }).ToList();

            return new JsonResult
            {
                //We will be retrieving only 5 photos for the first time

                Data = result,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        
        public JsonResult GetScreenshotsListByPrevButton(string deviceUniqueId, int companyId, int lastId)
        {
            var res = _context.CP_ScreenCapture.Where(x => x.DeviceUniqueId == deviceUniqueId && x.CompanyId == companyId && x.Id < lastId).Select(x => x.Id).Count();

            if (res > 0)
            {
                return new JsonResult
                {

                    Data = _context.CP_ScreenCapture.Where(x => x.DeviceUniqueId == deviceUniqueId && x.CompanyId == companyId && x.Id < lastId)
                     .Join(_context.CP_Device,
                     x => x.DeviceId,
                     y => y.Id,
                     (x, y) => new { SCapture = x, Device = y })
                     .Join(_context.CP_User,
                         x => x.SCapture.UserId,
                         y => y.Id,
                         (x, y) => new { x.SCapture, x.Device, User = y })
                     .Select(x => new
                     {
                         x.SCapture.Id,
                         x.SCapture.CapturedOn,
                         x.SCapture.EntriedOn,
                         x.SCapture.Status,
                         x.SCapture.MouseClick,
                         x.SCapture.KeyStroke,
                         x.SCapture.ScreenShot,

                         x.User.FirstName,
                         x.User.LastName,
                         x.User.Designation,
                         x.User.Photo,
                         x.User.Gender,
                         UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                         x.Device.DeviceUniqueId,
                         x.Device.DeviceName,

                         Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                     }).OrderByDescending(x => x.Id).Take(5).ToList(),
                    JsonRequestBehavior = JsonRequestBehavior.AllowGet
                };
            }
            return new JsonResult
            {
                Data = null
            };

        }
        public JsonResult GetScreenshotsListByNewButton(string deviceUniqueId, int companyId, int lastId)
        {
            var res = _context.CP_ScreenCapture.Where(x => x.DeviceUniqueId == deviceUniqueId && x.CompanyId == companyId && x.Id > lastId).Select(x => x.Id).Count();

            if (res > 0)
            {
                return new JsonResult
                {

                    Data = _context.CP_ScreenCapture.Where(x => x.DeviceUniqueId == deviceUniqueId && x.CompanyId == companyId && x.Id > lastId)
                    .Join(_context.CP_Device,
                    x => x.DeviceId,
                    y => y.Id,
                    (x, y) => new { SCapture = x, Device = y })
                    .Join(_context.CP_User,
                        x => x.SCapture.UserId,
                        y => y.Id,
                        (x, y) => new { x.SCapture, x.Device, User = y })
                    .Select(x => new
                    {
                        x.SCapture.Id,
                        x.SCapture.CapturedOn,
                        x.SCapture.EntriedOn,
                        x.SCapture.Status,
                        x.SCapture.MouseClick,
                        x.SCapture.KeyStroke,
                        x.SCapture.ScreenShot,

                        x.User.FirstName,
                        x.User.LastName,
                        x.User.Designation,
                        x.User.Photo,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male") ? "/Company_Images/Default_Images/male.png" : (x.User.Photo == null && x.User.Gender == "Female") ? "/Company_Images/Default_Images/female.jpg" : "/Company_Images/User_Images/" + x.User.Photo,
                        x.Device.DeviceUniqueId,
                        x.Device.DeviceName,

                        Url = x.SCapture.Status == null ? "/Company_Images/Screenshots/" + x.SCapture.ScreenShot : "/Company_Images/Screenshots/no-image.png",
                    }).Take(5).ToList().OrderByDescending(x => x.Id).ToList(),
                    JsonRequestBehavior = JsonRequestBehavior.AllowGet
                };
            }
            return new JsonResult
            {
                Data = null
            };

        }


        public JsonResult DeleteScreenshot(int id)
        {
            //In this method I am actually not deleting screenshot; I am just updating the status as 'Deleted'

            var message = "";
            bool isIdExist = _context.CP_ScreenCapture.Any(x => x.Id == id);
            if (isIdExist)
            {
                var status = "Deleted";
                _context.Database.ExecuteSqlCommand($"Update [CP_ScreenCapture] SET Status = '{status}' WHERE Id = {id}");
                Generator.IsReport = "Ok";
                message = "Screenshot deleted successfully !";
            }
            else
            {
                Generator.IsReport = "NotOk";
                message = "Invalid Id !";
            }
            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult DeleteScreenshotPermanently(int id)
        {
            //In this method I am actually deleting screenshots by its id
            var message = "";
            bool isIdExist = _context.CP_ScreenCapture.Any(x => x.Id == id);
            if (isIdExist)
            {
                _context.Database.ExecuteSqlCommand($"Delete [CP_ScreenCapture] WHERE Id = {id}");
                Generator.IsReport = "Ok";
                message = "Screenshot deleted successfully !";
            }
            else
            {
                Generator.IsReport = "NotOk";
                message = "Invalid Id !";
            }
            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult DeleteSingularCompanyScreenshotsFromFolderByDateRange(int companyId, DateTime dateUpTo)
        {
            dateUpTo = dateUpTo.Date.AddHours(23).AddMinutes(59).AddSeconds(59);

            var message = "";
            dateUpTo = dateUpTo.ToUniversalTime();

            try
            {
                int[] primaryIdsOfScreenshots = _context.CP_ScreenCapture.Where(x => x.CompanyId == companyId && x.CapturedOn <= dateUpTo).Select(x => x.Id).ToArray();

                if (primaryIdsOfScreenshots.Length > 0)
                {
                    #region Step 01: deleting image from folder by primaryKey and CompanyId combination
                    var myPath = System.Web.Hosting.HostingEnvironment.MapPath("~/Company_Images/Screenshots/");

                    foreach (var primaryId in primaryIdsOfScreenshots)
                    {
                        string deleteImageWithThis = primaryId + "_" + companyId + "_";
                        string[] imageNames = Directory.GetFiles(myPath);

                        foreach (string imageName in imageNames)
                        {
                            if (imageName.Contains(deleteImageWithThis))
                            {
                                File.Delete(imageName);
                            }
                        }
                    }
                    #endregion

                    #region Step 2: deleting row from db permanently
                    foreach (var primaryId in primaryIdsOfScreenshots)
                    {
                        _context.Database.ExecuteSqlCommand($"Delete [CP_ScreenCapture] WHERE Id = {primaryId}");
                    }
                    #endregion

                    Generator.IsReport = "Ok";
                    message = primaryIdsOfScreenshots.Length + " screenshot (s) deleted permanently !";
                }
                else
                {
                    Generator.IsReport = "NoImage";
                    message = "There is no image / screenshot found for deletion !";
                }


            }
            catch (Exception ex)
            {
                Generator.IsReport = "NotOk";
                message = ex.Message;
            }


            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult DeleteAllCompanyScreenshotsFromFolderByDateRange(DateTime dateUpTo)
        {
            dateUpTo = dateUpTo.Date.AddHours(23).AddMinutes(59).AddSeconds(59);

            var message = "";
            dateUpTo = dateUpTo.ToUniversalTime();

            try
            {
                int[] primaryIdsOfScreenshots = _context.CP_ScreenCapture.Where(x => x.CapturedOn <= dateUpTo).Select(x => x.Id).ToArray();

                if (primaryIdsOfScreenshots.Length > 0)
                {
                    #region Step 01: deleting image from folder by primaryKey and CompanyId combination
                    var myPath = System.Web.Hosting.HostingEnvironment.MapPath("~/Company_Images/Screenshots/");

                    foreach (var primaryId in primaryIdsOfScreenshots)
                    {
                        string deleteImageWithThis = primaryId + "_";
                        string[] imageNames = Directory.GetFiles(myPath);

                        foreach (string imageName in imageNames)
                        {
                            if (imageName.Contains(deleteImageWithThis))
                            {
                                File.Delete(imageName);
                            }
                        }
                    }
                    #endregion

                    #region Step 2: deleting row from db permanently
                    foreach (var primaryId in primaryIdsOfScreenshots)
                    {
                        _context.Database.ExecuteSqlCommand($"Delete [CP_ScreenCapture] WHERE Id = {primaryId}");
                    }
                    #endregion

                    Generator.IsReport = "Ok";
                    message = primaryIdsOfScreenshots.Length + " screenshot (s) deleted permanently for all companies !";
                }
                else
                {
                    Generator.IsReport = "NoImage";
                    message = "There is no image / screenshot found for deletion !";
                }


            }
            catch (Exception ex)
            {
                Generator.IsReport = "NotOk";
                message = ex.Message;
            }


            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }


        public JsonResult DownloadAllScreenShots(IEnumerable<CP_ScreenCapture> objs)
        {
            var message = "";
            try
            {
                var myPath = HostingEnvironment.MapPath("~/Company_Images/Screenshots/");
                List<string> filePaths = new List<string>();
                foreach (var item in objs)
                {
                    filePaths.Add(myPath + item.ScreenShot);
                }

                HttpContext.Current.Response.AddHeader("Content-Disposition",
                    "attachment; filename=" + "compressedFileName" + ".zip");
                HttpContext.Current.Response.ContentType = "application/zip";

                using (var zipStream = new ZipOutputStream(HttpContext.Current.Response.OutputStream))
                {
                    foreach (string filePath in filePaths)
                    {
                        byte[] fileBytes = File.ReadAllBytes(filePath);

                        var fileEntry = new ZipEntry(Path.GetFileName(filePath))
                        {
                            Size = fileBytes.Length
                        };

                        zipStream.PutNextEntry(fileEntry);
                        zipStream.Write(fileBytes, 0, fileBytes.Length);
                    }

                    zipStream.Flush();
                    zipStream.Close();
                    Generator.IsReport = "Ok";
                    message = "File zipped successfully !";
                }
            }
            catch (Exception ex)
            {
                Generator.IsReport = "NotOk";
                message = ex.Message;
            }

            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message,
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

    }

    public interface ICP_DeviceServices
    {

        JsonResult GetDeviceDefaultScheduleByCompanyId(int companyId);
        JsonResult UpdateDeviceDefaultScheduleByCompany(CP_Device_Schedule scheduleObj);
        JsonResult IsCompanyCodeValid(string companyCode);


        JsonResult GetDeviceListByCompanyId(int companyId);
        JsonResult GetLicenseListByDeviceId(int deviceId);
        JsonResult GetDeviceListByLicenseId(int licenseId);
        JsonResult GetDeviceDetails(int deviceId);
        JsonResult GetDeviceAndLicenseDetails(int deviceId);
        JsonResult GetNonGroupedDeviceListByCompanyId(int companyId);
        JsonResult GetNonGroupedDeviceListByLicenseId(int licenseId);
        JsonResult GetGroupListByCompanyId(int companyId);
        JsonResult GetGroupDetailsByGroupId(int groupId);
        JsonResult ChangeDeviceName(string deviceUniqueId, string deviceName);
        JsonResult CreateDeviceDefaultScheduleByCompany(CP_Device_Schedule scheduleObj);
        JsonResult UpdateSingularDeviceScheduling(CP_Device deviceObj);
        JsonResult AddGroupScheduleSetting(CP_Device_Group groupObj, IEnumerable<CP_Device> deviceObjs);
        JsonResult UpdateGroupScheduleSetting(CP_Device_Group groupObj, IEnumerable<CP_Device> groupedDeviceObjs);
        JsonResult DeleteGroup(int groupId);




        JsonResult ActivateFreeTrial(string deviceName, string deviceUniqueId, string companyCode);
        JsonResult ActivateLicenseKey(string deviceName, string deviceUniqueId, string companyCode, string licenseKey);


        JsonResult SaveScreenCapture(IEnumerable<CP_ScreenCapture> screenCaptureObjs);
        JsonResult GetScreenshotsListByDeviceUniqueId(string deviceUniqueId);
        JsonResult GetScreenshotsListByUserId(int userId);
        JsonResult GetFiveScreenshotsListByDeviceId(int deviceId);
        JsonResult GetMoreScreenshotsListByDeviceLastRetrievalId(int lastId);
        JsonResult GetScreenshotsListForSpecificSessionByDeviceId(int deviceId, string session);
        JsonResult GetScreenshotsListByCustomRangeByDeviceId(int deviceId, DateTime dateFrom, DateTime dateTo);
        JsonResult GetScreenshotsListByCustomRangeByUserId(int userId, DateTime dateFrom, DateTime dateTo);
        JsonResult GetScreenshotsListByCustomRangeByUserId(int userId, DateTime dateFrom, DateTime dateTo, int lastId);
        JsonResult GetScreenshotsListForSpecificSessionByUserId(int userId, string session);
        JsonResult GetScreenshotsListForSpecificSessionByUserId(int userId, string session, int lastId);
        JsonResult GetScreenshotsListByLicenseId(int licenseId);
        JsonResult GetScreenshotsListByCompanyId(int licenseId);
        JsonResult GetScreenshotsListByPrevButton(string deviceUniqueId, int companyId, int lastId);
        JsonResult GetScreenshotsListByNewButton(string deviceUniqueId, int companyId, int lastId);

        JsonResult DeleteScreenshot(int id);
        JsonResult DeleteScreenshotPermanently(int id);
        JsonResult DeleteSingularCompanyScreenshotsFromFolderByDateRange(int companyId, DateTime dateUpTo);
        JsonResult DeleteAllCompanyScreenshotsFromFolderByDateRange(DateTime dateUpTo);


        JsonResult DownloadAllScreenShots(IEnumerable<CP_ScreenCapture> objs);
    }


}
