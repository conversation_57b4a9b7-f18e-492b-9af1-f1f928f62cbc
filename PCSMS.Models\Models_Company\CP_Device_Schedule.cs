﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PCSMS.Models.Common;

namespace PCSMS.Models.Models_Company
{
    public class CP_Device_Schedule : Entity<int>
    {
        [ForeignKey("CompanyId")]
        public virtual CP_Profile CP_Profile { get; set; }
        public int? CompanyId { get; set; }

        public int? Interval { get; set; }
        [MaxLength(5)]
        public string IsRandom { get; set; }
        public TimeSpan? StartTime { get; set; }
        public TimeSpan? EndTime { get; set; }
        [MaxLength(5)]
        public string Mon { get; set; }
        [MaxLength(5)]
        public string Tues { get; set; }
        [MaxLength(5)]
        public string Wed { get; set; }
        [MaxLength(5)]
        public string Thurs { get; set; }
        [MaxLength(5)]
        public string Fri { get; set; }
        [MaxLength(5)]
        public string Sat { get; set; }
        [MaxLength(5)]
        public string Sun { get; set; }

    }
}
