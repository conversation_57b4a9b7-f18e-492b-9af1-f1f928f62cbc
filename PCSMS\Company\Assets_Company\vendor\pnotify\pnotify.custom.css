/* PNotify modules included in this custom build file:
buttons
callbacks
confirm
desktop
history
mobile
nonblock
*/
/*
Author  : <PERSON>
Version : 3.0.0
Link    : http://sciactive.com/pnotify/
*/
/* -- Notice */
.ui-pnotify {
    top: 36px;
    right: 36px;
    position: absolute;
    height: auto;
    z-index: 2;
}
body > .ui-pnotify {
    /* Notices in the body context should be fixed to the viewport. */
    position: fixed;
    /* Ensures notices are above everything */
    z-index: 100040;
}
.ui-pnotify-modal-overlay {
    background-color: rgba(0, 0, 0, .4);
    top: 0;
    left: 0;
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: 1;
}
body > .ui-pnotify-modal-overlay {
    position: fixed;
    z-index: 100039;
}
.ui-pnotify.ui-pnotify-in {
    display: block !important;
}
.ui-pnotify.ui-pnotify-move {
    transition: left .5s ease, top .5s ease, right .5s ease, bottom .5s ease;
}
.ui-pnotify.ui-pnotify-fade-slow {
    transition: opacity .6s linear;
    opacity: 0;
}
.ui-pnotify.ui-pnotify-fade-slow.ui-pnotify.ui-pnotify-move {
    transition: opacity .6s linear, left .5s ease, top .5s ease, right .5s ease, bottom .5s ease;
}
.ui-pnotify.ui-pnotify-fade-normal {
    transition: opacity .4s linear;
    opacity: 0;
}
.ui-pnotify.ui-pnotify-fade-normal.ui-pnotify.ui-pnotify-move {
    transition: opacity .4s linear, left .5s ease, top .5s ease, right .5s ease, bottom .5s ease;
}
.ui-pnotify.ui-pnotify-fade-fast {
    transition: opacity .2s linear;
    opacity: 0;
}
.ui-pnotify.ui-pnotify-fade-fast.ui-pnotify.ui-pnotify-move {
    transition: opacity .2s linear, left .5s ease, top .5s ease, right .5s ease, bottom .5s ease;
}
.ui-pnotify.ui-pnotify-fade-in {
    opacity: 1;
}
.ui-pnotify .ui-pnotify-shadow {
    -webkit-box-shadow: 0px 6px 28px 0px rgba(0,0,0,0.1);
    -moz-box-shadow: 0px 6px 28px 0px rgba(0,0,0,0.1);
    box-shadow: 0px 6px 28px 0px rgba(0,0,0,0.1);
}
.ui-pnotify-container {
    background-position: 0 0;
    padding: .8em;
    height: 100%;
    margin: 0;
}
.ui-pnotify-container:after {
   content: " "; /* Older browser do not support empty content */
   visibility: hidden;
   display: block;
   height: 0;
   clear: both;
}
.ui-pnotify-container.ui-pnotify-sharp {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}
.ui-pnotify-title {
    display: block;
    margin-bottom: .4em;
    margin-top: 0;
}
.ui-pnotify-text {
    display: block;
}
.ui-pnotify-icon, .ui-pnotify-icon span {
    display: block;
    float: left;
    margin-right: .2em;
}
/* Alternate stack initial positioning. */
.ui-pnotify.stack-topleft, .ui-pnotify.stack-bottomleft {
    left: 25px;
    right: auto;
}
.ui-pnotify.stack-bottomright, .ui-pnotify.stack-bottomleft {
    bottom: 25px;
    top: auto;
}
.ui-pnotify.stack-modal {
    left: 50%;
    right: auto;
    margin-left: -150px;
}
.ui-pnotify-closer, .ui-pnotify-sticker {
    float: right;
    margin-left: .2em;
}
/* -- Pulldown */
.ui-pnotify-history-container {
    position: absolute;
    top: 0;
    right: 18px;
    width: 70px;
    border-top: none;
    padding: 0;
    -webkit-border-top-left-radius: 0;
    -moz-border-top-left-radius: 0;
    border-top-left-radius: 0;
    -webkit-border-top-right-radius: 0;
    -moz-border-top-right-radius: 0;
    border-top-right-radius: 0;
    /* Ensures history container is above notices. */
    z-index: 10000;
}
.ui-pnotify-history-container.ui-pnotify-history-fixed {
    position: fixed;
}
.ui-pnotify-history-container .ui-pnotify-history-header {
    padding: 2px;
    text-align: center;
}
.ui-pnotify-history-container button {
    cursor: pointer;
    display: block;
    width: 100%;
}
.ui-pnotify-history-container .ui-pnotify-history-pulldown {
    display: block;
    margin: 0 auto;
}
.ui-pnotify-container {
    position: relative;
    left: 0;
}
@media (max-width: 480px) {
    /* -- Notice */
    .ui-pnotify-mobile-able.ui-pnotify {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        width: auto !important;
        font-size: 1.2em;
        -webkit-font-smoothing: antialiased;
        -moz-font-smoothing: antialiased;
        -ms-font-smoothing: antialiased;
        font-smoothing: antialiased;
    }
    .ui-pnotify-mobile-able.ui-pnotify .ui-pnotify-shadow {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
        border-bottom-width: 5px;
    }
    .ui-pnotify-mobile-able .ui-pnotify-container {
        -webkit-border-radius: 0;
        -moz-border-radius: 0;
        border-radius: 0;
    }
    /* Alternate stack initial positioning. */
    .ui-pnotify-mobile-able.ui-pnotify.stack-topleft, .ui-pnotify-mobile-able.ui-pnotify.stack-bottomleft {
        left: 0;
        right: 0;
    }
    .ui-pnotify-mobile-able.ui-pnotify.stack-bottomright, .ui-pnotify-mobile-able.ui-pnotify.stack-bottomleft {
        left: 0;
        right: 0;
        bottom: 0;
        top: auto;
    }
    .ui-pnotify-mobile-able.ui-pnotify.stack-bottomright .ui-pnotify-shadow, .ui-pnotify-mobile-able.ui-pnotify.stack-bottomleft .ui-pnotify-shadow {
        border-top-width: 5px;
        border-bottom-width: 1px;
    }

}
.ui-pnotify.ui-pnotify-nonblock-fade {
    /*transition: opacity .3s ease;*/
    opacity: .2;
}
.ui-pnotify.ui-pnotify-nonblock-hide {
    display: none !important;
}