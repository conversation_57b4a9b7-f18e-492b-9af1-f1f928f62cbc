﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Net.Mail;
using System.Text.RegularExpressions;
using System.Web.Mvc;
using Microsoft.AspNet.SignalR;
using PCSMS.Common;
using PCSMS.Models;
using PCSMS.Models.Models_Shared;
using PCSMS.Models.Models_SProvider;
using PCSMS.Models.Models_Temp;
using PCSMS.Services.Services_Shared;

namespace PCSMS.Services.Services_SProvider
{
    public class SP_UserServices: ISP_UserServices
    {
        private readonly PCSMSDbContext _context;
        private readonly IEntityService<SP_User> _services;
        private readonly IEntityService<SP_Profile> _spProfile;
        public SP_UserServices()
        {
            _context = new PCSMSDbContext();
            _services = new EntityService<SP_User>(_context);
            _spProfile = new EntityService<SP_Profile>(_context);
        }



        //Additional Services :
        private bool UsernameExist(SP_User obj)
        {
            var result = _context.SP_User.FirstOrDefault(x => x.Email == obj.Email && x.Id != obj.Id);

            if (result == null)
            {
                return false;
            }
            return true;
        }
        private bool ExistingPasswordMatches(int userId, string extPassword)
        {
            var result = _context.SP_User.FirstOrDefault(x => x.Password == extPassword && x.Id == userId);

            if (result == null)
            {
                return false;
            }
            return true;
        }
        private string SendMailToNewUserWithLoginCredentials(string empName, string email, string password)
        {
            var message = "";

            string senderEmailAddress = "<EMAIL>";
            string senderDetails = "PCSMS - User Login Credentials";

            MailMessage mail = new MailMessage();
            mail.From = new MailAddress(senderEmailAddress, senderDetails);
            mail.To.Add(new MailAddress(email));

            mail.Subject = "Credentials to log into 'PCSMS Service Provider' Dashboard.";
            string Body = "Hello, " + empName + " !<br/><br/>" +
                          "You have been registered for 'PCSMS Service Provider' Dashboard.<br/><br/><br/> " +
                          "Here are your login credentials :<br/><br/> " +
                          "Username / email : " + email + " <br/> " +
                          "Password : " + password + " <br/><br/> " +
                          "NOTE: You may want to change your credentials after first login. <br/> <br/><br/>" +
                          "Thank you <br/> "+
                          "PCSMS (PC Screen Monitoring System) TEAM <br/> ";

            mail.Body = Body;
            mail.IsBodyHtml = true;
            mail.BodyEncoding = System.Text.Encoding.UTF8;
            mail.SubjectEncoding = System.Text.Encoding.Default;

            SmtpClient client = new SmtpClient();
            client.Host = "smtp.gmail.com";
            client.Port = 587;
            client.EnableSsl = true;
            client.Timeout = 10000;
            client.UseDefaultCredentials = false;
            client.Credentials = new NetworkCredential("<EMAIL>", "Infinity@207");

            try
            {
                client.Send(mail);
                message = "Mail sending Successful !";
                Generator.IsReport = "Ok";
            }
            catch (Exception ex)
            {
                message = ex.Message;
                Generator.IsReport = "NotOk";
            }

            return Generator.IsReport;
        }


        //Main Services :
        public JsonResult SaveSP_User(SP_User obj, LoggedInUser loggedInUserObj)
        {


            string message="Something went wrong!";
            try
            {

                var loggedInUserAccessType = _context.SP_User.Where(x => x.Id == loggedInUserObj.LoggedInUserId)
                        .GroupJoin(_context.AccessType,
                            x => x.AccessTypeId,
                            y => y.Id,
                            (x, y) => new { SP_User = x, AccessType = y })
                        .SelectMany(x => x.AccessType.DefaultIfEmpty(),
                            (x, y) => new { x.SP_User, AccessType = y })
                        .Select(x => x.AccessType.AccessTypeName).FirstOrDefault();

                if (loggedInUserAccessType == "Super Admin" || loggedInUserAccessType == "Admin")
                {

                    if (_services.DoesExist(x => x.Email == obj.Email))
                    {
                        Generator.IsReport = "UsernameExists";
                        message = "The username / email is already taken !";
                    }
                    else
                    {

                        // Profile Id is gotten from backend and assigned to obj.SP_ProfileId below:
                        int profileId = _context.SP_Profile.Select(x => x.Id).FirstOrDefault();



                        if (profileId > 0)
                        {
                            obj.SP_ProfileId = profileId;

                            obj.Password = "123456";
                            _services.Save(obj);
                            _services.SaveChanges();

                            #region step 04 : Sending mail to user with his/her credentials:

                            var empName = obj.FirstName + " " + obj.LastName;
                            string r = SendMailToNewUserWithLoginCredentials(empName, obj.Email, obj.Password);
                            #endregion

                            if (r == "Ok")
                            {
                                Generator.IsReport = "Ok";
                                message = "User has been created successfully ! Shortly, " + obj.FirstName + " " + obj.LastName + " will receive an e-mail with login credentials.";
                            }
                            else if (r == "NotOk")
                            {
                                Generator.IsReport = "Ok";
                                message = "User has been created successfully ! However, e-mail sending to your user failed !";
                            }
                        }
                        else
                        {
                            Generator.IsReport = "NotOk";
                            message = "You have to create 'Company-Profile' first !";
                        }


                    }

                }
                else
                {
                    Generator.IsReport = "NotOk";
                    message = "You do not have permission to create user.";
                }


            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UpdateSP_UserAdministrativeInfo(SP_User obj, LoggedInUser loggedInUserObj)
        {
            string message;

            try
            {

                var loggedInUserAccessType = _context.SP_User.Where(x => x.Id == loggedInUserObj.LoggedInUserId)
                    .GroupJoin(_context.AccessType,
                        x => x.AccessTypeId,
                        y => y.Id,
                        (x, y) => new { SP_User = x, AccessType = y })
                    .SelectMany(x => x.AccessType.DefaultIfEmpty(),
                        (x, y) => new { x.SP_User, AccessType = y })
                    .Select(x => x.AccessType.AccessTypeName).FirstOrDefault();

                var editableUserAccessType = _context.SP_User.Where(x => x.Id == obj.Id)
                    .GroupJoin(_context.AccessType,
                        x => x.AccessTypeId,
                        y => y.Id,
                        (x, y) => new { SP_User = x, AccessType = y })
                    .SelectMany(x => x.AccessType.DefaultIfEmpty(),
                        (x, y) => new { x.SP_User, AccessType = y })
                    .Select(x => x.AccessType.AccessTypeName).FirstOrDefault();

                if (loggedInUserAccessType == "Super Admin" || loggedInUserAccessType == "Admin")
                {
                    if (UsernameExist(obj))
                    {
                        Generator.IsReport = "UsernameExists";
                        message = "The username/email : " + "\"" + obj.Email + "\"" + " is already taken, could not update the user !";

                    }
                    else
                    {

                        // Profile Id is gotten from backend and assigned to obj.SP_ProfileId below:
                        int profileId = _context.SP_Profile.Select(x => x.Id).FirstOrDefault();



                        if (profileId > 0)
                        {
                            //keeping super-admin/admin 'isActive' status always yes/1:
                            if ((editableUserAccessType =="Super Admin") || (loggedInUserAccessType == "Admin" && editableUserAccessType == "Admin"))
                            {
                                obj.IsActive = "1";
                            }
                            _context.Database.ExecuteSqlCommand($"UPDATE [SP_User] SET SP_ProfileId = {profileId}, SP_BranchId = {obj.SP_BranchId}, AccessTypeId = {obj.AccessTypeId}, SP_DesignationId = {obj.SP_DesignationId}, IsActive = '{obj.IsActive}'  WHERE Id = {obj.Id}");
                            _context.SaveChanges();

                            Generator.IsReport = "Ok";
                            message = "User updated successfully !";


                            
                        }
                        else
                        {
                            Generator.IsReport = "NotOk";
                            message = "You have to create 'Company-Profile' first !";
                        }


                    }
                }
                else
                {
                    Generator.IsReport = "NotOk";
                    message = "You do not have permission to update user.";
                }


            }
            catch (Exception ex)
            {
                message = ex.Message;
                Generator.IsReport = "NotOk";
            }

            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UpdateSP_UserPersonalInfo(SP_User obj)
        {
            string message;

            try
            {


                if (UsernameExist(obj))
                {
                    Generator.IsReport = "UsernameExists";
                    message = "The username/email : " + "\"" + obj.Email + "\"" + " is already taken, could not update the user !";

                }
                else
                {
                    _context.Database.ExecuteSqlCommand($"UPDATE [SP_User] SET FirstName = '{obj.FirstName}', LastName = '{obj.LastName}', Address = '{obj.Address}', BirthDate = '{obj.BirthDate}', Gender = '{obj.Gender}', Mobile = '{obj.Mobile}', Email = '{obj.Email}'  WHERE Id = {obj.Id}");
                    _context.SaveChanges();

                    Generator.IsReport = "Ok";
                    message = "Your personal profile updated successfully !";

                }


            }
            catch (Exception ex)
            {
                message = ex.Message;
                Generator.IsReport = "NotOk";
            }

            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult ChangeSP_UserPassword(int userId, string extPassword, string newPassword)
        {
            string message;
            try
            {
                if (extPassword == newPassword)
                {
                    Generator.IsReport = "ExistingAndNewPasswordSame";
                    message = "Apparently, your existing password and new password are same, Password NOT updated !";
                }
                else if (ExistingPasswordMatches(userId, extPassword))
                {
                    //Update:
                    _context.Database.ExecuteSqlCommand($"UPDATE [SP_User] SET Password ='{newPassword}' WHERE Id = {userId}");
                    _context.SaveChanges();
                    Generator.IsReport = "Ok";
                    message = "Password has been updated successfully !";
                }
                else
                {
                    Generator.IsReport = "ExistingPasswordDoesNotMatch";
                    message = "Your existing password is incorrect, Password NOT updated !";
                }
            }
            catch (Exception ex)
            {

                Generator.IsReport = "NotOk";
                message = ex.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UploadSP_UserPhoto(int userId)
        {
            //Add System.Web to Services References

            string message = "";
            string content = "";
            int iUploadedCnt = 0;


            string myPath = "";
            myPath = System.Web.Hosting.HostingEnvironment.MapPath("~/SProvider_Images/User_Images/");

            var obj = _context.SP_User.Where(x => x.Id == userId).Select(x => new
            {
                x.Photo,
                x.FirstName
            }).FirstOrDefault();

            var getOldImageName = obj.Photo;
            string trimmedFirstName = Regex.Replace(obj.FirstName, " ", "");

            string randNum = GenericServices.CreateRandomNumberWithLetter();


            System.Web.HttpFileCollection httpFileCollection = System.Web.HttpContext.Current.Request.Files;
            if (httpFileCollection.Count > 0)
            {

                try
                {
                    for (int iCnt = 0; iCnt <= httpFileCollection.Count - 1; iCnt++)
                    {
                        System.Web.HttpPostedFile hpf = httpFileCollection[iCnt];

                        var imaginaryNameWithoutExtension = trimmedFirstName + "_" + userId + "_" + randNum; ;
                        var extension = Path.GetExtension(hpf.FileName);
                        var newImageName = imaginaryNameWithoutExtension + extension;

                        if (extension != ".pdf")
                        {
                            if (getOldImageName != null)
                            {
                                File.Delete(myPath + getOldImageName);
                            }

                            //From mobile sometimes no extension given, so we add up an extension :
                            if (extension.Length < 1)
                            {
                                newImageName = newImageName + ".jpg";
                            }


                            //now moving new image to folder:
                            hpf.SaveAs(myPath + newImageName);
                            _context.Database.ExecuteSqlCommand($"UPDATE [SP_User] SET Photo = '{newImageName}' WHERE Id = {userId}");
                            _context.SaveChanges();


                            iUploadedCnt = iUploadedCnt + 1;


                            content = newImageName;
                            Generator.IsReport = "Ok";
                            message = "Your profile picture uploaded successfully";

                        }
                        else
                        {
                            content = null;
                            message = "This is not an image type.";
                            Generator.IsReport = "NotOk";
                        }

                    }
                }
                catch (Exception ex)
                {
                    content = null;
                    message = ex.Message;
                    Generator.IsReport = "NotOk";
                }
            }
            else
            {
                content = null;
                Generator.IsReport = "NotOk";
                message = "There is no image to be saved";
            }

            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport,
                    Content = content,
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetSP_UserList()
        {
            return new JsonResult
            {
                Data = _context.SP_User
                .GroupJoin(_context.SP_Profile,
                x => x.SP_ProfileId,
                y => y.Id,
                (x, y) => new { User = x, Profile = y })
                .SelectMany(x => x.Profile.DefaultIfEmpty(),
                (x, y) => new { x.User, Profile = y })
                .GroupJoin(_context.SP_Branch,
                x => x.User.SP_BranchId,
                y => y.Id,
                (x, y) => new { x.User, x.Profile, Branch = y })
                .SelectMany(x => x.Branch.DefaultIfEmpty(),
                (x, y) => new { x.User, x.Profile, Branch = y })
                .GroupJoin(_context.AccessType,
                x => x.User.AccessTypeId,
                y => y.Id,
                (x, y) => new { x.User, x.Profile, x.Branch, AccessType = y })
                .SelectMany(x => x.AccessType.DefaultIfEmpty(),
                (x, y) => new { x.User, x.Profile, x.Branch, AccessType = y })
                .GroupJoin(_context.SP_Designation,
                x => x.User.SP_DesignationId,
                y => y.Id,
                (x, y) => new { x.User, x.Profile, x.Branch, x.AccessType, Designation = y })
                .SelectMany(x => x.Designation.DefaultIfEmpty(),
                (x, y) => new { x.User, x.Profile, x.Branch, x.AccessType, Designation = y })
                .Select(x => new
                {
                    x.User.Id,
                    x.User.Username,
                    x.User.FirstName,
                    x.User.LastName,
                    x.User.Address,
                    x.User.BirthDate,
                    x.User.Gender,
                    x.User.Mobile,
                    x.User.Email,
                    x.User.Photo,
                    x.User.IsActive,
                    x.User.SP_ProfileId,
                    x.User.SP_BranchId,
                    x.User.AccessTypeId,
                    x.User.SP_DesignationId,
                    //===============
                    x.Profile.OrganizationName,
                    x.Branch.BranchName,
                    x.AccessType.AccessTypeName,
                    x.Designation.DesignationName

                }).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetSP_UserDetails(int userId)
        {
            return new JsonResult
            {
                Data = _context.SP_User.Where(x=> x.Id == userId)
                .GroupJoin(_context.SP_Profile,
                x => x.SP_ProfileId,
                y => y.Id,
                (x, y) => new { User = x, Profile = y })
                .SelectMany(x=> x.Profile.DefaultIfEmpty(),
                (x, y) => new { x.User, Profile = y })
                .GroupJoin(_context.SP_Branch,
                x => x.User.SP_BranchId,
                y => y.Id,
                (x, y) => new { x.User, x.Profile, Branch = y })
                .SelectMany(x=> x.Branch.DefaultIfEmpty(),
                (x,y)=> new { x.User, x.Profile, Branch = y })
                .GroupJoin(_context.AccessType,
                x => x.User.AccessTypeId,
                y => y.Id,
                (x, y) => new { x.User, x.Profile, x.Branch, AccessType = y })
                .SelectMany(x=> x.AccessType.DefaultIfEmpty(),
                (x,y)=> new { x.User, x.Profile, x.Branch, AccessType = y })
                .GroupJoin(_context.SP_Designation,
                x => x.User.SP_DesignationId,
                y => y.Id,
                (x, y) => new { x.User, x.Profile, x.Branch, x.AccessType, Designation = y })
                .SelectMany(x=> x.Designation.DefaultIfEmpty(),
                (x,y)=> new { x.User, x.Profile, x.Branch, x.AccessType, Designation = y })
                .Select(x => new
                {
                    x.User.Id,
                    x.User.Username,
                    x.User.FirstName,
                    x.User.LastName,
                    x.User.Address,
                    x.User.BirthDate,
                    x.User.Gender,
                    x.User.Mobile,
                    x.User.Email,
                    x.User.Photo,
                    x.User.IsActive,
                    x.User.SP_ProfileId,
                    x.User.SP_BranchId,
                    x.User.AccessTypeId,
                    x.User.SP_DesignationId,
                    //===============
                    x.Profile.OrganizationName,
                    x.Branch.BranchName,
                    x.AccessType.AccessTypeName,
                    x.Designation.DesignationName

                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult DeleteSP_User(long userId)
        {
            string message;
            try
            {
                _context.Database.ExecuteSqlCommand($"Delete [SP_User] WHERE Id = {userId}");
                _context.SaveChanges();
                Generator.IsReport = "Ok";
                message = "User deleted successfully !";
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetCountOfSP_User()
        {
            return new JsonResult
            {
                Data = _context.SP_User.Select(x=> x.Id).Count(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
            };
        }
        public JsonResult GetCountOfActiveAndBlockedSP_Users()
        {
            var CountOfActiveUsers = _context.SP_User.Count(x => x.IsActive == "1");
            var CountOfBlockedUsers = _context.SP_User.Count(x => x.IsActive != "1");
            return new JsonResult
            {
                Data = new
                {
                    CountOfActiveUsers,
                    CountOfBlockedUsers
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
            };
        }

        //For login purpose :
        public int SP_Authenticate(string username, string password)
        {
            var TPUser = _context.SP_User.FirstOrDefault(u => u.Email == username && u.Password == password);
            if (TPUser != null && TPUser.Id > 0)
            {
                return TPUser.Id;
            }
            return 0;
        }


        
    }

    public interface ISP_UserServices
    {
        JsonResult SaveSP_User(SP_User obj, LoggedInUser loggedInUserObj);
        JsonResult UpdateSP_UserAdministrativeInfo(SP_User obj, LoggedInUser loggedInUserObj);
        JsonResult UpdateSP_UserPersonalInfo(SP_User obj);
        JsonResult UploadSP_UserPhoto(int userId);
        JsonResult ChangeSP_UserPassword(int userId, string extPassword, string newPassword);
        JsonResult GetSP_UserList();
        JsonResult GetSP_UserDetails(int userId);
        JsonResult DeleteSP_User(long userId);
        int SP_Authenticate(string username, string password);
        JsonResult GetCountOfSP_User();
        JsonResult GetCountOfActiveAndBlockedSP_Users();
    }
}
