﻿PCSMSApp.factory("licenseServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetAllLicenseList: function () {
            return $http({
                url: "/api/Subscription/GetAllLicenseList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetLicenseDetailsByLicenseId: function (licenseId) {
            return $http({
                url: "/api/Subscription/GetLicenseDetailsByLicenseId/" + licenseId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        ApproveLicense: function (licenseId, deviceLimit, expiryDate) {
            return $http({
                url: "/api/Subscription/ApproveLicense/" + licenseId + "/" + deviceLimit + "/" + expiryDate,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        ApproveLicenseRenewal: function (expiryDate, licenseId) {
            return $http({
                url: "/api/Subscription/ApproveLicenseRenewal/" + expiryDate + "/" + licenseId,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
    };
}]);