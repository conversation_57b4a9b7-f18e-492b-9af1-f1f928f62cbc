﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using Newtonsoft.Json.Linq;
using PCSMS.Filters;
using PCSMS.Models.Models_Company;
using PCSMS.Services.Services_Company;

namespace PCSMS.Controllers
{
    //[AuthorizationRequired]
    [RoutePrefix("api/Device")]
    public class CP_DeviceController : ApiController
    {
        private readonly ICP_DeviceServices _deviceServices;
        private readonly ICP_UserServices _cpUserServices;
        public CP_DeviceController()
        {
            _deviceServices = new CP_DeviceServices();
            _cpUserServices = new CP_UserServices();
        }

        


        [Route("GetDeviceDefaultScheduleByCompanyId/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetDeviceDefaultScheduleByCompanyId(int companyId)
        {
            return Ok(_deviceServices.GetDeviceDefaultScheduleByCompanyId(companyId).Data);
        }



        [Route("CreateDeviceDefaultScheduleByCompany")]
        [HttpPost]
        public IHttpActionResult CreateDeviceDefaultScheduleByCompany(JObject jObject)
        {
            dynamic jsonData = jObject;

            //userObj
            JObject scheduleObjObjJson = jsonData.scheduleObj;
            var scheduleObj = scheduleObjObjJson.ToObject<CP_Device_Schedule>();



            return Ok(_deviceServices.CreateDeviceDefaultScheduleByCompany(scheduleObj).Data);
        }

        [Route("UpdateDeviceDefaultScheduleByCompany")]
        [HttpPost]
        public IHttpActionResult UpdateDeviceDefaultScheduleByCompany(JObject jObject)
        {
            dynamic jsonData = jObject;

            //userObj
            JObject scheduleObjObjJson = jsonData.scheduleObj;
            var scheduleObj = scheduleObjObjJson.ToObject<CP_Device_Schedule>();



            return Ok(_deviceServices.UpdateDeviceDefaultScheduleByCompany(scheduleObj).Data);
        }
        

        [Route("IsCompanyCodeValid/{companyCode}")]
        [HttpGet]
        public IHttpActionResult IsCompanyCodeValid(string companyCode)
        {
            return Ok(_deviceServices.IsCompanyCodeValid(companyCode).Data);
        }





        //=====================================================================
        [Route("ChangeDeviceName/{deviceUniqueId}/{deviceName}")]
        [HttpPost]
        public IHttpActionResult ChangeDeviceName(string deviceUniqueId, string deviceName)
        {
            return Ok(_deviceServices.ChangeDeviceName(deviceUniqueId, deviceName).Data);
        }


        [Route("GetDeviceListByCompanyId/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetDeviceListByCompanyId(int companyId)
        {
            return Ok(_deviceServices.GetDeviceListByCompanyId(companyId).Data);
        }
        

        [Route("GetLicenseListByDeviceId/{deviceId:int}")]
        [HttpGet]
        public IHttpActionResult GetLicenseListByDeviceId(int deviceId)
        {
            return Ok(_deviceServices.GetLicenseListByDeviceId(deviceId).Data);
        }



        [Route("GetDeviceListByLicenseId/{licenseId:int}")]
        [HttpGet]
        public IHttpActionResult GetDeviceListByLicenseId(int licenseId)
        {
            return Ok(_deviceServices.GetDeviceListByLicenseId(licenseId).Data);
        }

        [Route("GetDeviceDetails/{deviceId:int}")]
        [HttpGet]
        public IHttpActionResult GetDeviceDetails(int deviceId)
        {
            return Ok(_deviceServices.GetDeviceDetails(deviceId).Data);
        }

        [Route("GetDeviceAndLicenseDetails/{deviceId:int}")]
        [HttpGet]
        public IHttpActionResult GetDeviceAndLicenseDetails(int deviceId)
        {
            return Ok(_deviceServices.GetDeviceAndLicenseDetails(deviceId).Data);
        }



        [Route("GetNonGroupedDeviceListByCompanyId/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetNonGroupedDeviceListByCompanyId(int companyId)
        {
            return Ok(_deviceServices.GetNonGroupedDeviceListByCompanyId(companyId).Data);
        }


        [Route("GetNonGroupedDeviceListByLicenseId/{licenseId:int}")]
        [HttpGet]
        public IHttpActionResult GetNonGroupedDeviceListByLicenseId(int licenseId)
        {
            return Ok(_deviceServices.GetNonGroupedDeviceListByLicenseId(licenseId).Data);
        }


        [Route("GetGroupListByCompanyId/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetGroupListByCompanyId(int companyId)
        {
            return Ok(_deviceServices.GetGroupListByCompanyId(companyId).Data);
        }
        
        
        [Route("GetGroupDetailsByGroupId/{groupId:int}")]
        [HttpGet]
        public IHttpActionResult GetGroupDetailsByGroupId(int groupId)
        {
            return Ok(_deviceServices.GetGroupDetailsByGroupId(groupId).Data);
        }

        
        [Route("AddGroupScheduleSetting")]
        [HttpPost]
        public IHttpActionResult AddGroupScheduleSetting(JObject objData)
        {

            dynamic jsonData = objData;

            //groupObj Object
            JObject groupObjJson = jsonData.groupObj;
            var groupObj = groupObjJson.ToObject<CP_Device_Group>();

            //deviceObjs Array
            JArray deviceObjsJson = jsonData.deviceObjs;
            var deviceObjs = deviceObjsJson.Select(item => item.ToObject<CP_Device>()).ToList();


            return Ok(_deviceServices.AddGroupScheduleSetting(groupObj, deviceObjs).Data);
        }

        
        //[Route("UpdateGroupScheduleSetting")]
        //[HttpPost]
        //public IHttpActionResult UpdateGroupScheduleSetting(JObject objData)
        //{

        //    dynamic jsonData = objData;

        //    //groupObj Object
        //    JObject groupObjJson = jsonData.groupObj;
        //    var groupObj = groupObjJson.ToObject<CP_Device_Group>();

        //    //groupedDeviceObjs Array
        //    JArray groupedDeviceObjsJson = jsonData.groupedDeviceObjs;
        //    var groupedDeviceObjs = groupedDeviceObjsJson.Select(item => item.ToObject<CP_Device>()).ToList();

        //    //removedDeviceObjs Array
        //    JArray removedDeviceObjsJson = jsonData.removedDeviceObjs;
        //    var removedDeviceObjs = removedDeviceObjsJson.Select(item => item.ToObject<CP_Device>()).ToList();


        //    return Ok(_deviceServices.UpdateGroupScheduleSetting(groupObj, groupedDeviceObjs, removedDeviceObjs).Data);
        //}

        [Route("UpdateGroupScheduleSetting")]
        [HttpPost]
        public IHttpActionResult UpdateGroupScheduleSetting(JObject objData)
        {

            dynamic jsonData = objData;

            //groupObj Object
            JObject groupObjJson = jsonData.groupObj;
            var groupObj = groupObjJson.ToObject<CP_Device_Group>();

            //groupedDeviceObjs Array
            JArray groupedDeviceObjsJson = jsonData.groupedDeviceObjs;
            var groupedDeviceObjs = groupedDeviceObjsJson.Select(item => item.ToObject<CP_Device>()).ToList();


            return Ok(_deviceServices.UpdateGroupScheduleSetting(groupObj, groupedDeviceObjs).Data);
        }


        [Route("UpdateSingularDeviceScheduling")]
        [HttpPost]
        public IHttpActionResult UpdateSingularDeviceScheduling(JObject objData)
        {

            dynamic jsonData = objData;

            //groupObj Object
            JObject groupObjJson = jsonData.deviceObj;
            var groupObj = groupObjJson.ToObject<CP_Device>();


            return Ok(_deviceServices.UpdateSingularDeviceScheduling(groupObj).Data);
        }

        [Route("DeleteGroup/{groupId:int}")]
        [HttpPost]
        public IHttpActionResult DeleteGroup(int groupId)
        {
            return Ok(_deviceServices.DeleteGroup(groupId).Data);
        }

        //===============================================





        [Route("ActivateFreeTrial/{deviceName}/{deviceUniqueId}/{companyCode}")]
        [HttpPost]
        public IHttpActionResult ActivateFreeTrial(string deviceName, string deviceUniqueId, string companyCode)
        {
            return Ok(_deviceServices.ActivateFreeTrial(deviceName, deviceUniqueId, companyCode).Data);
        }


        [Route("ActivateLicenseKey/{deviceName}/{deviceUniqueId}/{companyCode}/{licenseKey}")]
        [HttpPost]
        public IHttpActionResult ActivateLicenseKey(string deviceName, string deviceUniqueId, string companyCode, string licenseKey)
        {
            return Ok(_deviceServices.ActivateLicenseKey(deviceName, deviceUniqueId, companyCode, licenseKey).Data);
        }


        //================================================================================

        

        [Route("SaveScreenCapture")]
        [HttpPost]
        public IHttpActionResult SaveScreenCapture(IEnumerable<CP_ScreenCapture> screenCaptureObjs)
        {
            return Ok(_deviceServices.SaveScreenCapture(screenCaptureObjs).Data);
        }


        [Route("GetScreenshotsListByDeviceUniqueId/{deviceUniqueId}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListByDeviceUniqueId(string deviceUniqueId)
        {
            return Ok(_deviceServices.GetScreenshotsListByDeviceUniqueId(deviceUniqueId).Data);
        }


        [Route("GetScreenshotsListByUserId/{userId:int}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListByUserId(int userId)
        {
            return Ok(_deviceServices.GetScreenshotsListByUserId(userId).Data);
        }

        [Route("GetFiveScreenshotsListByDeviceId/{deviceId:int}")]
        [HttpGet]
        public IHttpActionResult GetFiveScreenshotsListByDeviceId(int deviceId)
        {
            return Ok(_deviceServices.GetFiveScreenshotsListByDeviceId(deviceId).Data);
        }

        [Route("GetMoreScreenshotsListByDeviceLastRetrievalId/{lastId:int}")]
        [HttpGet]
        public IHttpActionResult GetMoreScreenshotsListByDeviceLastRetrievalId(int lastId)
        {
            return Ok(_deviceServices.GetMoreScreenshotsListByDeviceLastRetrievalId(lastId).Data);
        }




        [Route("GetScreenshotsListByCustomRangeByDeviceId/{deviceId:int}/{dateFrom:datetime}/{dateTo:datetime}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListByCustomRangeByDeviceId(int deviceId, DateTime dateFrom, DateTime dateTo)
        {
            return Ok(_deviceServices.GetScreenshotsListByCustomRangeByDeviceId(deviceId, dateFrom, dateTo).Data);
        }

        [Route("GetScreenshotsListByCustomRangeByUserId/{userId:int}/{dateFrom:datetime}/{dateTo:datetime}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListByCustomRangeByUserId(int userId, DateTime dateFrom, DateTime dateTo)
        {
            return Ok(_deviceServices.GetScreenshotsListByCustomRangeByUserId(userId, dateFrom, dateTo).Data);
        }


        [Route("GetScreenshotsListByCustomRangeByUserId/{userId:int}/{dateFrom:datetime}/{dateTo:datetime}/{lastId:int}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListByCustomRangeByUserId(int userId, DateTime dateFrom, DateTime dateTo, int lastId)
        {
            return Ok(_deviceServices.GetScreenshotsListByCustomRangeByUserId(userId, dateFrom, dateTo, lastId).Data);
        }




        [Route("GetScreenshotsListForSpecificSessionByUserId/{userId:int}/{session}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListForSpecificSessionByUserId(int userId, string session)
        {
            return Ok(_deviceServices.GetScreenshotsListForSpecificSessionByUserId(userId, session).Data);
        }

        [Route("GetScreenshotsListForSpecificSessionByDeviceId/{deviceId:int}/{session}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListForSpecificSessionByDeviceId(int deviceId, string session)
        {
            return Ok(_deviceServices.GetScreenshotsListForSpecificSessionByDeviceId(deviceId, session).Data);
        }

        [Route("GetScreenshotsListForSpecificSessionByUserId/{userId:int}/{session}/{lastId:int}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListForSpecificSessionByUserId(int userId, string session, int lastId)
        {
            return Ok(_deviceServices.GetScreenshotsListForSpecificSessionByUserId(userId, session, lastId).Data);
        }

        

        [Route("GetScreenshotsListByLicenseId/{licenseId:int}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListByLicenseId(int licenseId)
        {
            return Ok(_deviceServices.GetScreenshotsListByLicenseId(licenseId).Data);
        }

        [Route("GetScreenshotsListByCompanyId/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListByCompanyId(int companyId)
        {
            return Ok(_deviceServices.GetScreenshotsListByCompanyId(companyId).Data);
        }


        [Route("DeleteScreenshot/{id:int}")]
        [HttpPost]
        public IHttpActionResult DeleteScreenshot(int id)
        {
            return Ok(_deviceServices.DeleteScreenshot(id).Data);
        }



        [Route("DeleteScreenshotPermanently/{id:int}")]
        [HttpPost]
        public IHttpActionResult DeleteScreenshotPermanently(int id)
        {
            return Ok(_deviceServices.DeleteScreenshotPermanently(id).Data);
        }




        [Route("DeleteSingularCompanyScreenshotsFromFolderByDateRange/{companyId:int}/{dateUpTo:datetime}")]
        [HttpPost]
        public IHttpActionResult DeleteSingularCompanyScreenshotsFromFolderByDateRange(int companyId, DateTime dateUpTo)
        {
            return Ok(_deviceServices.DeleteSingularCompanyScreenshotsFromFolderByDateRange(companyId, dateUpTo).Data);
        }




        [Route("DeleteAllCompanyScreenshotsFromFolderByDateRange/{dateUpTo:datetime}")]
        [HttpPost]
        public IHttpActionResult DeleteAllCompanyScreenshotsFromFolderByDateRange(DateTime dateUpTo)
        {
            return Ok(_deviceServices.DeleteAllCompanyScreenshotsFromFolderByDateRange(dateUpTo).Data);
        }




        [Route("DownloadAllScreenShots")]
        [HttpPost]
        public IHttpActionResult DownloadAllScreenShots(IEnumerable<CP_ScreenCapture> objs)
        {
            return Ok(_deviceServices.DownloadAllScreenShots(objs).Data);
        }
        



        //FOR DEVICE USER

        [Route("LogIn/{email}/{password}/{companyCode}/{deviceUniqueId}")]
        [HttpPost]
        public IHttpActionResult LogIn(string email, string password, string companyCode, string deviceUniqueId)
        {
            return Ok(_cpUserServices.LogIn(email, password, companyCode, deviceUniqueId).Data);
        }

        [Route("LogOut/{userId:int}")]
        [HttpPost]
        public IHttpActionResult LogOut(int userId)
        {
            return Ok(_cpUserServices.LogOut(userId).Data);
        }

        [Route("UpdateCP_UserProfile")]
        [HttpPost]
        public IHttpActionResult UpdateCP_UserProfile(JObject jObject)
        {
            dynamic jsonData = jObject;

            //userObj
            JObject userObjJson = jsonData.UserObj;
            var userObj = userObjJson.ToObject<CP_User>();



            return Ok(_cpUserServices.UpdateCP_UserProfile(userObj).Data);
        }


        [Route("ChangeCP_UserPassword/{userId:int}/{extPassword}/{newPassword}")]
        [HttpPost]
        public IHttpActionResult ChangeCP_UserPassword(int userId, string extPassword, string newPassword)
        {
            return Ok(_cpUserServices.ChangeCP_UserPassword(userId, extPassword, newPassword).Data);
        }


        [Route("UploadCP_UserPhoto/{userId:int}")]
        [HttpPost]
        public IHttpActionResult UploadCP_UserPhoto(int userId)
        {
            return Ok(_cpUserServices.UploadCP_UserPhoto(userId).Data);
        }

        [Route("ForgotPassword/{email}/{companyCode}")]
        [HttpPost]
        public IHttpActionResult ForgotPassword(string email, string companyCode)
        {
            return Ok(_cpUserServices.ForgotPassword(email, companyCode).Data);
        }

        [Route("GetScreenshotsListByPrevButton/{deviceUniqueId}/{companyId:int}/{lastId:int}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListByPrevButton(string deviceUniqueId, int companyId, int lastId)
        {
            return Ok(_deviceServices.GetScreenshotsListByPrevButton(deviceUniqueId, companyId, lastId).Data);
        }

        [Route("GetScreenshotsListByNewButton/{deviceUniqueId}/{companyId:int}/{lastId:int}")]
        [HttpGet]
        public IHttpActionResult GetScreenshotsListByNewButton(string deviceUniqueId, int companyId, int lastId)
        {
            return Ok(_deviceServices.GetScreenshotsListByNewButton(deviceUniqueId, companyId, lastId).Data);
        }


    }
}
