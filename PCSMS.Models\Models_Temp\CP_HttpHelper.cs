﻿using System;
using System.Linq;
using System.Net.Http;
using JWT;
using Newtonsoft.Json;
using PCSMS.Models.Models_Anonymous;

namespace PCSMS.Models.Models_Temp
{
    public class CP_HttpHelper
    {
        //This class is created to decode the token (brought from Client side).
        public CP_PayLoad GetCustomToken(HttpRequestMessage httpRequest)
        {
            CP_PayLoad token = null;
            try
            {
                if (httpRequest.Headers.Contains("Token"))
                {
                    //Decode token
                    string tokenKey = JsonWebToken.Decode(httpRequest.Headers.GetValues("Token").First(), EncryptionHelper.GetPrivateKey(), true);
                    //Users
                    token = JsonConvert.DeserializeObject<CP_PayLoad>(tokenKey);
                }
            }
            catch (Exception )
            {
                return null;
            }
            return token;
        }
    }
}