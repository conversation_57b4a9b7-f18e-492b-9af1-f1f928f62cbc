﻿.menu-title {
    font-size: 18Px;
    color: #33ddba;
    padding-left:35px;
}

.menu-profileImage {
    border: 3px solid beige;
    border-radius: 15px;
    width: 57px;
    margin-left: 3%;
    margin-top: 10px;
    margin-bottom: 10px;
}

.col-sm-offset-2 {
    margin-left: 20.666667%;
}

.panel .panel-header, .panel .panel-footer {
    padding: 10px 12px;
    height: 55px;
}

@media (min-width: 768px) {
}

.modal-dialog {
    margin: 3.7% auto; /*Re Edited By <PERSON><PERSON>b*/
}

/*input picture*/

.fileinput {
    margin-bottom: 9px;
    display: inline-block;
    margin-left: 44%;
}

.row.vdivide [class*='col-']:not(:last-child):after {
    background: #e0e0e0;
    width: 1px;
    content: "";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    min-height: 70px;
}

.errormessage {
    color: red;
}

.checkbox-custom.checkbox-inline {
    padding-left: 45%;
}
/*CSS Added By <PERSON><PERSON><PERSON>*/

/*<PERSON><PERSON> or Separator for inline div*/
.separator {
    border-right: 2px;
    border-right-color: #ececec;
    border-right-style: inset;
}
/*Margin or Separator for inline div*/
/*Icon for button addon*/
.btn-addon i {
    position: relative;
    float: left;
    width: 34px;
    height: 34px;
    margin: -8px -12px;
    margin-right: 12px;
    line-height: 34px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 2px 0 0 2px;
}

    .btn-addon i.pull-right {
        margin-right: -12px;
        margin-left: 12px;
        border-radius: 0 2px 2px 0;
    }

.btn-addon.btn-sm i {
    width: 30px;
    height: 30px;
    margin: -6px -10px;
    margin-right: 10px;
    line-height: 30px;
}

    .btn-addon.btn-sm i.pull-right {
        margin-right: -10px;
        margin-left: 10px;
    }

.btn-addon.btn-lg i {
    width: 45px;
    height: 45px;
    margin: -11px -16px;
    margin-right: 16px;
    line-height: 45px;
}

    .btn-addon.btn-lg i.pull-right {
        margin-right: -16px;
        margin-left: 16px;
    }

.btn-addon.btn-default i {
    background-color: #189279;
    /*border-right: 1px solid #dee5e7;*/
    color: white;
    margin-top: -8px;
}

.btn-addon.btn-default.cancel i {
    background-color: #d2322d;
    /*border-right: 1px solid #dee5e7;*/
    color: white;
    border-color: #d2322d;
}
/*Icon For Button Addon*/
/*Custom Close Button*/
.custom-close {
    float: right;
    font-size: 40px;
    font-weight: bold;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    filter: alpha(opacity=20);
    opacity: .2;
}

    .custom-close:hover,
    .custom-close:focus {
        color: #000;
        text-decoration: none;
        cursor: pointer;
        filter: alpha(opacity=50);
        opacity: .5;
    }

button.custom-close {
    -webkit-appearance: none;
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
}

.modal-header .custom-close {
    margin-top: 5px;
    margin-right: 10px;
}
/*Custom Close Button*/
/*Custom Row*/
.custom-row {
    margin-left: 0px;
}
/*Custom row*/
/*Custom DataTable Action th*/
.custom-datatabel-action-th {
    width: 10.5%;
}
/*Custom DataTabel Action Row*/
/*Custom Button for addon o*/
.btn-o-addon {
    border: 1px solid #189279;
}
/*Custom Button for addon o*/
/*Custom custom-form-control*/
.custom-custom-form-control {
    display: block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
    -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}


.custom-form-control[disabled],
.custom-form-control[readonly],
fieldset[disabled] .custom-form-control {
    background-color: #fff;
    opacity: 1;
    cursor: default;
}
/*Custom Form Control*/

/*Text on Image ==Banner*/

/* Container holding the image and the text */
.banner-container {
    position: relative;
    text-align: center;
    color: white;
}

/* Bottom left text */
.bottom-left-text-on-banner {
    position: absolute;
    bottom: 8px;
    left: 16px;
}

/* Top left text */
.top-left-text-on-banner {
    position: absolute;
    top: 8px;
    left: 16px;
}

/* Top right text */
.top-right-text-on-banner {
    position: absolute;
    top: 8px;
    right: 16px;
}

/* Bottom right text */
.bottom-right-text-on-banner {
    position: absolute;
    bottom: 8px;
    right: 16px;
}

/* Centered text */
.centered-text-on-banner {
    position: absolute;
    top: 47%;
    left: 223%;
    font-size: 17px;
    font-style: italic;
    transform: translate(-50%, -50%);
}
/*Text on Image == Banner*/

/*Set Badge on middle position where in Data Table*/
.badge-middle-in-data-table {
    margin-top: 8px;
}
/*Set Badge on middle position where in Data Table*/

/*Row for modal-footer*/
.row-modal {
    margin-left: 0px;
    margin-right: 0px;
}
/*Row for modal-footer*/

/*Plain border-radius for left and right buitton*/
.btn-left-border-radius {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
}

.btn-right-border-radius {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
/*Plain border-radius for left and right buitton*/

/*badge for Payment Method*/
.badge-bkash {
    background-color: #e43566;
}

    .badge-bkash:hover {
        background-color: #cc2453;
    }

.badge-bank-deposit {
    background-color: #00803E;
}

    .badge-bank-deposit:hover {
        background-color: #016330;
    }

.badge-cash {
    background-color: #35a3e4;
}

    .badge-cash:hover {
        background-color: #3b8ab9;
    }
/*badge for  Payment Method*/
/*ion icon custom color*/
.dashboard-color {
    color: #33ddba;
}

.settings-color {
    color: #7c7cff;
}

.company-color {
    color: #bc9742;
}

.bill-color {
    color: #00cfe4 /*#42e7f8*/;
}

.report-color {
    color: #b18cff;
}

.support-color {
    color: #e56d6d;
}

.payment-color {
    color: #6ca10c;
}

.tracking-color {
    color: #f44839;
}

.task-color {
    color: #bc42ac;
}

.myaccount-color {
    color: #bfe200;
}

.employee-color {
    color: #00b0ff;
}

.order-color {
    color: #ffed00;
}

.my-disable {
    pointer-events: none;
    color: #c4c1c1;
}

.custom-hr {
    margin-top: 5px;
    margin-bottom: 10px;
    border: 0;
    border-top: 1px solid #b5b3b3;
    width: 100%;
    margin-left: 0%;
}

.custom-hr2 {
    margin-top: 5px;
    margin-bottom: 10px;
    border: 0;
    border-top: 1px dashed #c4c1c1;
    width: 100%;
    margin-left: 0%;
}

/*Kendo-Awesome Style*/
.dropdown-header {
    border-width: 0 0 1px 0;
    text-transform: uppercase;
}

    .dropdown-header > span {
        display: inline-block;
        padding: 10px;
    }

        .dropdown-header > span:first-child {
            width: 50px;
        }

.k-list-container > .k-footer {
    padding: 10px;
}

#customers-list .k-item {
    line-height: 1em;
    min-width: 300px;
}

/* Material Theme padding adjustment*/

.k-material .k-item,
.k-material .k-item.k-state-hover,
.k-materialblack .k-item,
.k-materialblack .k-item.k-state-hover {
    padding-left: 5px;
    border-left: 0;
}

.k-item > span {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    vertical-align: top;
    margin: 20px 10px 10px 5px;
}

    .k-item > span:first-child {
        -moz-box-shadow: inset 0 0 30px rgba(0,0,0,.3);
        -webkit-box-shadow: inset 0 0 30px rgba(0,0,0,.3);
        box-shadow: inset 0 0 30px rgba(0,0,0,.3);
        margin: 10px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-size: 100%;
        background-repeat: no-repeat;
    }

.k-h3 {
    font-size: 1.2em;
    font-weight: normal;
    margin: 0 0 1px 0;
    padding: 0;
}

.k-p {
    margin: 0;
    padding: 0;
    font-size: .8em;
}
/*Kendo Aresome Style*/
/*ion icon custom color*/

/*FIELDSET*/

fieldset {
    border: 1px solid #ddd !important;
    margin: 0;
    padding: 10px 10px 0px;
    position: relative;
    border-radius: 4px;
    padding-left: 10px !important;
}

legend {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 0px;
    width: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px 5px 0px 5px;
    background-color: #ffffff;
}

/*FIELDSET*/


.msgSeen {
    color: #332a2a;
    font-weight: bold;
}

.msgUnseen {
    color: #2d2b2b;
}

.message-menu-icon {
    padding-left: 10px;
    border: 1px solid #189279;
    border-radius: 10px;
}

/*Modal Scroll Stuck Issue Solve CSS*/
.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
}
/*Modal Scroll Stuck Issue Solve CSS*/

/*Webkit Scrollbar*/
.webkitscroller {
    overflow: auto;
    padding-right: 5px;
}

    .webkitscroller::-webkit-scrollbar {
        background-color: #f1f1f1;
        border-radius: 50px;
        width: 5px;
        height: 5px;
    }

    .webkitscroller::-webkit-scrollbar-thumb {
        background-color: #189279;
        border-radius: 50px;
    }

        .webkitscroller::-webkit-scrollbar-thumb:hover {
            background-color: #126f5c;
            cursor: grab;
        }
/*Webkit Scrollbar*/

/*Webkit Scrollbar*/
.webkitscroller-transparent {
    overflow: auto;
    padding-right: 5px;
}

    .webkitscroller-transparent::-webkit-scrollbar {
        background-color: #e6e6e6;
        border-radius: 50px;
        width: 5px;
        height:5px;
    }

    .webkitscroller-transparent::-webkit-scrollbar-thumb {
        background-color: #adadad;
        border-radius: 50px;
    }

        .webkitscroller-transparent::-webkit-scrollbar-thumb:hover {
            background-color: #5d5d5d;
            cursor:grab;
        }
/*Webkit Scrollbar*/


/*File drag-over css*/
.jumbotron-dragover {
    box-shadow: 0px 0px 20px 0px #319278;
    border-radius: 20px;
    transition:all 1s cubic-bezier(0.44, -0.35, 0.54, 1.35) !important;
}
/*File drag-over css*/

/*Box Hover Animation*/
.block {
    -webkit-animation: scaledown 0.5s linear;
    -moz-animation: scaledown 0.5s linear;
    animation: scaledown 0.5s linear;
    transform-origin: 50% 50%;
    animation-fill-mode: forwards;
}

    .block:hover {
        z-index: 100;
        -webkit-animation: scale 0.5s linear;
        -moz-animation: scale 0.5s linear;
        animation: scale 0.5s linear;
        transform-origin: 50% 50%;
        animation-fill-mode: forwards;
    }



.block2s {
    -webkit-animation: scaledown 0.2s linear;
    -moz-animation: scaledown 0.2s linear;
    animation: scaledown 0.2s linear;
    transform-origin: 50% 50%;
    animation-fill-mode: forwards;
}

    .block2s:hover {
        z-index: 100;
        -webkit-animation: scale 0.2s linear;
        -moz-animation: scale 0.2s linear;
        animation: scale 0.2s linear;
        transform-origin: 50% 50%;
        animation-fill-mode: forwards;
    }

@keyframes scale {
    0% {
        transform: scale(1.0);
    }

    100% {
        transform: scale(1.1);
        -webkit-box-shadow: 10px 10px 60px 10px rgba(0,0,0,0.0);
        -moz-box-shadow: 10px 10px 60px 10px rgba(0,0,0,0.0);
        box-shadow: 10px 10px 60px 10px rgba(0,0,0,0.0);
    }
}

@keyframes scaledown {
    0% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1.0);
    }
}


.block2sForLabel {
    -webkit-animation: scaledownForLabel 0.2s linear;
    -moz-animation: scaledownForLabel 0.2s linear;
    animation: scaledownForLabel 0.2s linear;
    transform-origin: 50% 50%;
    animation-fill-mode: forwards;
}

    .block2sForLabel:hover {
        z-index: 100;
        -webkit-animation: scaleForLabel 0.2s linear;
        -moz-animation: scaleForLabel 0.2s linear;
        animation: scaleForLabel 0.2s linear;
        transform-origin: 50% 50%;
        animation-fill-mode: forwards;
    }

@keyframes scaleForLabel {
    0% {
        transform: scale(1.0);
    }

    100% {
        transform: scale(1.03);
        -webkit-box-shadow: 10px 10px 60px 10px rgba(0,0,0,0.0);
        -moz-box-shadow: 10px 10px 60px 10px rgba(0,0,0,0.0);
        box-shadow: 10px 10px 60px 10px rgba(0,0,0,0.0);
    }
}

@keyframes scaledownForLabel {
    0% {
        transform: scale(1.03);
    }

    100% {
        transform: scale(1.0);
    }
}
/*Box Hover Animation*/


.checkbox label:after,
    .radio label:after {
        content: '';
        display: table;
        clear: both;
    }

    .checkbox .cr,
    .radio .cr {
        position: relative;
        display: inline-block;
        border: 1px solid #a9a9a9;
        border-radius: .25em;
        width: 1.3em;
        height: 1.3em;
        float: left;
        margin-right: .5em;
    }

    .radio .cr {
        border-radius: 50%;
    }

    .checkbox .cr .cr-icon,
    .radio .cr .cr-icon {
        position: absolute;
        font-size: .8em;
        line-height: 0;
        top: 50%;
        left: 20%;
    }

    .radio .cr .cr-icon {
        margin-left: 0.04em;
    }

    .checkbox label input[type="checkbox"],
    .radio label input[type="radio"] {
        display: none;
    }

    .checkbox label input[type="checkbox"] + .cr > .cr-icon,
    .radio label input[type="radio"] + .cr > .cr-icon {
        transform: scale(3) rotateZ(-20deg);
        opacity: 0;
        transition: all .3s ease-in;
    }

    .checkbox label input[type="checkbox"]:checked + .cr > .cr-icon,
    .radio label input[type="radio"]:checked + .cr > .cr-icon {
        transform: scale(1) rotateZ(0deg);
        opacity: 1;
    }

    .checkbox label input[type="checkbox"]:disabled + .cr,
    .radio label input[type="radio"]:disabled + .cr {
        opacity: .5;
    }







/*Adding CSS For PCSMS*/
.label-for-textbox-center-direction {
    padding-top: 7px;
}

