﻿/// <reference path="../../../app.js" />
PCSMSApp.controller('userController', function ($scope, $rootScope, UserServices, $q, toastr, $compile, blockUI, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    $scope.pro = "User (s)";
    $rootScope.settingsOpen = true;
    var currentDate = new Date();
    $scope.MaxDate = currentDate.setDate(currentDate.getDate() - 1);
    $scope.MinDate = currentDate.setYear(currentDate.getFullYear() - 90);
    $scope.src = null;

    //Retrieving data from db:
    $scope.BranchList = UserServices.GetSP_BranchList().then(function (response) {
        $scope.BranchList = response.data;
        return response.data;
    });
    $scope.AccessTypeList = UserServices.GetAccessTypeList().then(function (response) {
        $scope.AccessTypeList = response.data;
        return response.data;
    });
    //$scope.CompanyTypeList = CompanyTypeServices.GetCompanyTypeList().then(function (response) {
    //    $scope.CompanyTypeList = response.data;
    //    return response.data;
    //});
    $scope.UserList = UserServices.GetSP_UserList().then(function (response) {
        $scope.UserList = response.data;
        return response.data;
    });
    $scope.DesignationList = UserServices.GetSP_DesignationList().then(function (response) {
        $scope.DesignationList = response.data;
        ////console.log("----List of User----");
        ////console.log($scope.DesignationList);
        return response.data;
    });

    //Datatable
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [
        DTColumnDefBuilder.newColumnDef(7).withOption('width', '5%').notSortable(),
        DTColumnDefBuilder.newColumnDef(1).notSortable(),
        DTColumnDefBuilder.newColumnDef(3).notSortable(),
        DTColumnDefBuilder.newColumnDef(4).notSortable(),
        DTColumnDefBuilder.newColumnDef(5).notSortable(),
        DTColumnDefBuilder.newColumnDef(6).notSortable()
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
        .withOption('paging', true)
        .withOption('searching', true)
        .withOption('info', true);

    

    //Creating an empty object to be sent:

    $scope.FilterAccessType = function (model) {
        return function (model) {

            if (model.Id < $rootScope.UserAccessTypeId) {
                return false;
            }
            return true;
        };
    } //ng-options filter

    $scope.User = {};



    //Opening a modal:
    $scope.openUserModal = function (id) {

        if (id != null) {
            //blockUI.start();
            //blockUI.message("Loading User Details ...");
            UserServices.GetSP_UserDetails(id).then(function (response) {
                $scope.User = response.data;
                $scope.TempEmail = response.data.Email;
                if ($scope.User.Photo != null) {
                    $('#imageHolder').prepend('<img id="imageTag" src="" />');
                    $('#imageTag').attr("src", "../SProvider_Images/User_Images/" + $scope.User.Photo);
                    $scope.src = "/SProvider_Images/User_Images/" + $scope.User.Photo;
                    $scope.HasImage = true;
                    //imageName = response.data.CompanyLogo ;
                }
                else if ($scope.User.Photo == null) {
                    $scope.src = "/SProvider_Images/Default_Images/Image-not-found.png";
                }
            })
                .then(function () {
                    //blockUI.stop();
                    $('#UserModal').modal('show');
                });
        }
    };

    $scope.openUserModalInfo = function (id) {
        if (id != null) {
            //blockUI.start();
            //blockUI.message("Loading User Details ...");
            UserServices.GetSP_UserDetails(id).then(function (response) {
                $scope.User = response.data;
                $scope.TempEmail = response.data.Email;
                if ($scope.User.Photo != null) {
                    $('#imageHolderInfo').prepend('<img id="imageTagInfo" src="" />');
                    $('#imageTagInfo').attr("src", "../SProvider_Images/User_Images/" + $scope.User.Photo);
                    $scope.src = "/SProvider_Images/User_Images/" + $scope.User.Photo;
                    $scope.HasImage = true;
                    //imageName = response.data.CompanyLogo ;
                }
                else if ($scope.User.Photo == null) {
                    $scope.src = "/SProvider_Images/Default_Images/Image-not-found.png";
                }
            })
                .then(function () {
                    //blockUI.stop();
                    $('#UserModalInfo').modal('show');
                });
        }
    };


    //Saving or updating:
    $scope.SaveOrUpdateUser = function () {

        if ($scope.UserForm.$invalid == false && $scope.LiveValidation($scope.User.Email) == true) {
            ////console.log("===Outside===");
            ////console.log($scope.User);
            $scope.LoggedInUserObj = {
                LoggedInUserId: $rootScope.UserId,
            }
            if ($scope.User.Id == null) {
                //Save User
                ////console.log("===Inside===");
                ////console.log($scope.User);

                //blockUI.start();
                //blockUI.message("Saving User ...");
                UserServices.SaveSP_User($scope).then(function (response) {
                    $('#UserModal').modal('hide');
                    ////console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "UsernameExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                })
                    .then(function () {
                        //Reload The BranchList
                        //blockUI.message("Loading Branch List ...");
                        UserServices.GetSP_BranchList().then(function (response) {
                            $scope.BranchList = response.data;
                            return response.data;
                        })
                            .then(function () {
                                //Then The AccessTypeList
                                //blockUI.message("Loading Access Type List ...");
                                UserServices.GetAccessTypeList().then(function (response) {
                                    $scope.AccessTypeList = response.data;
                                    return response.data;
                                })
                                    .then(function () {
                                        //Then the Designation List
                                        //blockUI.message("Loading Designation List ...");
                                        UserServices.GetSP_DesignationList().then(function (response) {
                                            $scope.DesignationList = response.data;
                                            ////console.log("----List of User----");
                                            ////console.log($scope.DesignationList);
                                            return response.data;
                                        })
                                            .then(function () {
                                                //Then The UserList
                                                //blockUI.message("Loading User List ...");
                                                UserServices.GetSP_UserList().then(function (response) {
                                                    $scope.UserList = response.data;
                                                    return response.data;
                                                })
                                            })
                                            .then(function () {
                                                $scope.cancelUserListModal();
                                            })
                                    })
                            })
                    });
            } else {
                //Update User
                //$scope.User.Password = "123456";
                //Should Be Changed
                //blockUI.start();
                //blockUI.message("Updating User ...");
                UserServices.UpdateSP_UserAdministrativeInfo($scope).then(function (response) {
                    $('#UserModal').modal('hide');
                    //console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "UserExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                })
                    .then(function () {
                        //Reload The BranchList
                        //blockUI.message("Loading Branch List ...");
                        UserServices.GetSP_BranchList().then(function (response) {
                            $scope.BranchList = response.data;
                            return response.data;
                        })
                            .then(function () {
                                //Then The AccessTypeList
                                //blockUI.message("Loading Access Type List ...");
                                UserServices.GetAccessTypeList().then(function (response) {
                                    $scope.AccessTypeList = response.data;
                                    return response.data;
                                })
                                    .then(function () {
                                        //Then the Designation List
                                        //blockUI.message("Loading Designation List ...");
                                        UserServices.GetSP_DesignationList().then(function (response) {
                                            $scope.DesignationList = response.data;
                                            //console.log("----List of User----");
                                            //console.log($scope.DesignationList);
                                            return response.data;
                                        })
                                            .then(function () {
                                                //Then The UserList
                                                //blockUI.message("Loading User List ...");
                                                UserServices.GetSP_UserList().then(function (response) {
                                                    $scope.UserList = response.data;
                                                    return response.data;
                                                })
                                            })
                                            .then(function () {
                                                $scope.cancelUserListModal();
                                            })
                                    })
                            })
                    });
            }
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }
    };

    //Deleting:
    //$scope.deleteUserAlert = function (user) {
    //    var gText1 = "his";
    //    var gText2 = "him";
    //    if (user.Gender == "Female") {
    //        gText1 = "her";
    //        gText2 = "her";
    //    }
    //    swal({
    //            title: "Are you sure?",
    //            text: "You are going to delete '" + user.FirstName + " " + user.LastName + "' and all " + gText1 + " associated data.",
    //            type: "warning",
    //            showCancelButton: true,
    //            confirmButtonClass: "btn-danger",
    //            confirmButtonText: "Yes, delete " + gText2,
    //            closeOnConfirm: true
    //        },
    //        function () {
    //            //console.log(UserId + ' ' + UserName);
    //            UserServices.DeleteCP_User(id).then(function (response) {
    //                //console.log(response.data);
    //                if (response.data.IsReport === "Ok") {
    //                    toastr.success(response.data.Message, 'Successful');
    //                }
    //                else if (response.data.IsReport === "NotOk") {
    //                    toastr.error(response.data.Message, 'Failed');
    //                }
    //                $state.reload();
    //            });

    //        });
    //};

    $scope.cancelUserListModal = function () {
        $('#UserModal').modal('hide');
       
        $timeout(function () {
            $scope.User = null;
            $scope.TempEmail = null;
            $scope.src = null;
        }, 300);
        $scope.UserForm.$setPristine();
        $scope.UserForm.$setUntouched();
    };

    $scope.cancelUserListModalInfo = function () {
        $('#UserModalInfo').modal('hide');
        
        $timeout(function () {
            $scope.User = {};
            $scope.TempEmail = null;
            $scope.src = null;
        }, 300);
    };

    //Live Validation
    $scope.LiveValidation = function (x) {
        var valid = false;
        if ($scope.UserList.length > 0) {
            for (var i = 0; i < $scope.UserList.length; i++) {
                if ($scope.UserList[i].Email == x) {
                    if ($scope.UserList[i].Email == $scope.TempEmail) {
                        valid = true;
                    }
                    else {
                        valid = false;
                        $scope.LiveValidationError = "This email is already taken by another user";
                        break;
                    }
                }
                else {
                    valid = true;
                }
            }
        }
        else if ($scope.UserList.length == 0) {
            valid = true;
        }
        return valid;
    };
})