﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!-- main content -->
<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-home" aria-hidden="true"></i><a ui-sref="home">Dashboard</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-content">
                        <!--mine starts here-->
                        <div class="row" ng-show="IsInCreateMode==true || IsInDisplayMode==true || isInEditMode==true">
                            <div class="col-md-12">
                                <!--CREATE-->
                                <div class="col-md-12" ng-show="IsInCreateMode==true">
                                    <div class="panel panel-default" style="padding:27px 38px 38px 38px; min-height:752px">
                                        <input id="tab1" type="radio" name="tabs" checked>
                                        <label for="tab1">Create Your Company Profile</label>

                                        <section id="content1">
                                            <form name="ProfileCreateForm">
                                                <div style="padding-top:50px; overflow: auto">
                                                    <table class="table table-bordered">
                                                        <tbody>
                                                            <tr>
                                                                <th style="line-height:2.7">Company Name<span class="text-danger">*</span></th>
                                                                <td>
                                                                    <input type="text" name="OrganizationName" class="form-control" placeholder="Enter Company Name"
                                                                           ng-required="true"
                                                                           ng-model="Profile.OrganizationName"
                                                                           ng-pattern="/^[a-zA-Z0-9\.\-_ ]*$/"
                                                                           ng-minlength="5"
                                                                           ng-maxlength="30" />
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileCreateForm.OrganizationName.$dirty && ProfileCreateForm.OrganizationName.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileCreateForm.OrganizationName.$error.required">*Required</span></li>
                                                                            <li><span class="pull-left" ng-show="ProfileCreateForm.OrganizationName.$error.pattern">Not a valid Company Name</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationName.$error.pattern && ProfileCreateForm.OrganizationName.$error.minlength">Minimum length is 5</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationName.$error.pattern && ProfileCreateForm.OrganizationName.$error.maxlength">Maximum length is 30</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                                <td rowspan="3" colspan="2" class="text-center">
                                                                    <div class="col-md-6 pull-right">
                                                                        <div style="float:right">
                                                                            <div class="fileinput fileinput-new" data-provides="fileinput" style="margin-left:0%; width:160px; min-width:100%; max-width:160%">
                                                                                <div id="imageHolder" class="fileinput-preview thumbnail" data-trigger="fileinput" style="min-width: 160px; max-width:100%; height: 114px;"></div>
                                                                                <div>
                                                                                    <span class="btn btn-default btn-file btn-block">
                                                                                        <span class="fileinput-new">Select Company Logo</span>
                                                                                        <span class="fileinput-exists">Change Logo</span>
                                                                                        <input type="file" id="fileCompanyLogo" ng-files="getTheFiles($files)" name="fileCompanyLogo" bind-file ng-model="theFile">
                                                                                    </span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Company Type<span class="text-danger">*</span></th>
                                                                <td>
                                                                    <select class="form-control" name="CompanyTypeId"
                                                                            ng-model="Profile.CompanyTypeId"
                                                                            ng-required="true"
                                                                            ng-class="{'parsley-error':ProfileCreateForm.CompanyTypeId.$touched && ProfileCreateForm.CompanyTypeId.$invalid}"
                                                                            ng-options="CompanyType.Id as CompanyType.CompanyTypeName for CompanyType in CompanyTypeList">
                                                                        <option value="">Select</option>
                                                                    </select>
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileCreateForm.CompanyTypeId.$dirty && ProfileCreateForm.CompanyTypeId.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileCreateForm.CompanyTypeId.$error.required">*Required</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Phone No.</th>
                                                                <td>
                                                                    <input type="text" name="OrganizationPhone" class="form-control" placeholder="(e.g. 02xxxxx)"
                                                                           ng-model="Profile.OrganizationPhone"
                                                                           ng-minlength="5"
                                                                           ng-maxlength="20" />
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileCreateForm.OrganizationPhone.$dirty && ProfileCreateForm.OrganizationPhone.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileCreateForm.OrganizationPhone.$error.pattern">Not a valid Phone Number</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationPhone.$error.pattern && ProfileCreateForm.OrganizationPhone.$error.minlength">Minimum length is 5</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationPhone.$error.pattern && ProfileCreateForm.OrganizationPhone.$error.maxlength">Maximum length is 20</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Mobile No.</th>
                                                                <td>
                                                                    <input type="text" name="OrganizationMobile" class="form-control" placeholder="(e.g. 01xxxxxxxxx)"
                                                                           ng-model="Profile.OrganizationMobile"
                                                                           ng-minlength="8"
                                                                           ng-maxlength="20" />
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileCreateForm.OrganizationMobile.$dirty && ProfileCreateForm.OrganizationMobile.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileCreateForm.OrganizationMobile.$error.pattern">Not a valid Mobile Number</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationMobile.$error.pattern && ProfileCreateForm.OrganizationMobile.$error.minlength">Minimum length is 8</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationMobile.$error.pattern && ProfileCreateForm.OrganizationMobile.$error.maxlength">Maximum length is 20</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                                <th style="line-height:2.7">Search Location</th>
                                                                <td>
                                                                    <input type="text" name="SearchLocation" id="SearchLocation" class="form-control" placeholder="Seacrh Location" ng-model="Profile.Search" />
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Website</th>
                                                                <td>
                                                                    <input type="text" name="OrganizationWebsite" class="form-control" placeholder="www.example.com"
                                                                           ng-model="Profile.OrganizationWebsite"
                                                                           ng-minlength="8"
                                                                           ng-maxlength="40"
                                                                           ng-pattern="/^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?(([www]){3}|[^w]{1}[a-zA-Z0-9]{1,64})([\.]{1}[a-zA-Z0-9-]{1,253})+\.[a-z]{2,6}(:[0-9]{1,5})?(\/.*)?$/" />
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileCreateForm.OrganizationWebsite.$dirty && ProfileCreateForm.OrganizationWebsite.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileCreateForm.OrganizationWebsite.$error.pattern">Not a valid Website</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationWebsite.$error.pattern && ProfileCreateForm.OrganizationWebsite.$error.minlength">Minimum length is 8</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationWebsite.$error.pattern && ProfileCreateForm.OrganizationWebsite.$error.maxlength">Maximum length is 40</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                                <td rowspan="3" colspan="2" class="text-center" style="width:39%">
                                                                    <div class="text-center" style="border-style:solid; border-width:1px; border-color:#ddd #eee #eee #ddd">
                                                                        <div ng-init="LoadMap()" style="height: 242px;position: relative;overflow: hidden;margin-left:  8.5%;width: 345px;margin:auto" id="map-canvasC">
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">E-mail</th>
                                                                <td>
                                                                    <input type="text" name="OrganizationEmail" class="form-control" placeholder="<EMAIL>"
                                                                           ng-maxlength="30"
                                                                           ng-minlength="11"
                                                                           ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'
                                                                           ng-model="Profile.OrganizationEmail" />
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileCreateForm.OrganizationEmail.$dirty && ProfileCreateForm.OrganizationEmail.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileCreateForm.OrganizationEmail.$error.pattern">Not a valid Email</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationEmail.$error.pattern && ProfileCreateForm.OrganizationEmail.$error.minlength">Minimum length is 11</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationEmail.$error.pattern && ProfileCreateForm.OrganizationEmail.$error.maxlength">Maximum length is 30</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Address</th>
                                                                <td>
                                                                    <textarea class="form-control no-resize" placeholder="Company address" style="min-height:140px; resize:none" name="OrganizationAddress"
                                                                              ng-minlength="6"
                                                                              ng-maxlength="100"
                                                                              ng-model="Profile.OrganizationAddress"></textarea>
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left"
                                                                            ng-show="ProfileCreateForm.OrganizationAddress.$dirty && ProfileCreateForm.OrganizationAddress.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileCreateForm.OrganizationAddress.$error.pattern">Not a valid Organization Name</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationAddress.$error.pattern && ProfileCreateForm.OrganizationAddress.$error.minlength">Minimum length is 6</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileCreateForm.OrganizationAddress.$error.pattern && ProfileCreateForm.OrganizationAddress.$error.maxlength">Maximum length is 100</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="4" align="right">
                                                                    <button type="button" class="btn btn-default btn-addon"
                                                                            ng-disabled="ProfileCreateForm.$invalid || EnableEdit==false"
                                                                            ng-click="SaveOrUpdateProfile()">
                                                                        <i class="glyphicon glyphicon-ok" style="margin-top:-8px"></i>
                                                                        <span>Create Profile</span>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </form>
                                        </section>
                                    </div>
                                </div>


                                <!--DISPLAY-->
                                <div class="col-md-12" ng-show="IsInDisplayMode==true">
                                    <div class="panel panel-default" style="padding:27px 38px 38px 38px; min-height:752px">
                                        <input id="tab2" type="radio" name="tabs" checked>
                                        <label for="tab2">Your Company Info</label>

                                        <section id="content2">
                                            <form>
                                                <div style="padding-top:50px; overflow: auto">
                                                    <table class="table table-bordered">
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="3" align="right">
                                                                    <button type="button" class="btn btn-default btn-addon" ng-click="SwitchToEditMode()" ng-disabled="EnableEdit==false">
                                                                        <i class="glyphicon glyphicon-edit" style="margin-top:-8px"></i>
                                                                        <span>Edit Profile</span>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th>Company Name</th>
                                                                <td>
                                                                    {{Profile.OrganizationName}}
                                                                    <div style="float:right; pointer-events:none">
                                                                        <div class="fileinput fileinput-new" data-provides="fileinput" style="margin-left:0%; width:160px; min-width:100%; max-width:160%">
                                                                            <div id="imageHolderView" class="fileinput-preview thumbnail" data-trigger="fileinput" style="min-width: 160px; max-width:100%; height: 114px;"></div>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td rowspan="8" class="text-center" style="width:53%">

                                                                    <div class="text-center" style="border-style:solid; border-width:1px; border-color:#ddd #eee #eee #ddd">
                                                                        <div ng-init="LoadMap()" style="height: 351px;position: relative;overflow: hidden;width: 480px; margin:auto" id="map-canvasD">
                                                                        </div>
                                                                    </div>

                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th>Company Type</th>
                                                                <td>
                                                                    {{Profile.CompanyTypeName}}
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th>Phone No.</th>
                                                                <td>{{Profile.OrganizationPhone}}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>Mobile No.</th>
                                                                <td>{{Profile.OrganizationMobile}}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>Website</th>
                                                                <td>{{Profile.OrganizationWebsite}}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>E-mail</th>
                                                                <td>{{Profile.OrganizationEmail}}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>Address</th>
                                                                <td>{{Profile.OrganizationAddress}}</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </form>
                                        </section>
                                    </div>
                                </div>


                                <!--EDIT-->
                                <div class="col-md-12" ng-show="isInEditMode==true">
                                    <div class="panel panel-default" style="padding:27px 38px 38px 38px; min-height:752px">
                                        <input id="tab3" type="radio" name="tabs">
                                        <label for="tab3">Edit Your Company Info</label>

                                        <section id="content3">
                                            <form name="ProfileEditForm">
                                                <div style="padding-top:50px; overflow: auto">
                                                    <table class="table table-bordered">
                                                        <tbody>
                                                            <tr>
                                                                <td colspan="4" align="right">
                                                                    <button type="button" class="btn btn-default cancel btn-addon" ng-click="SwitchToDisplayMode()" ng-disabled="EnableEdit==false">
                                                                        <i class="glyphicon glyphicon-remove" style="margin-top:-8px"></i>
                                                                        <span>Cancel Editing</span>
                                                                    </button>
                                                                    <button type="button" class="btn btn-default btn-addon"
                                                                            ng-disabled="ProfileEditForm.$invalid || EnableEdit==false"
                                                                            ng-click="SaveOrUpdateProfile()">
                                                                        <i class="glyphicon glyphicon-ok" style="margin-top:-8px"></i>
                                                                        <span>Update Profile</span>
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Company Name<span class="text-danger">*</span></th>
                                                                <td>
                                                                    <input type="text" name="OrganizationName" class="form-control" placeholder="Enter Company Name"
                                                                           ng-required="true"
                                                                           ng-model="Profile.OrganizationName"
                                                                           ng-pattern="/^[a-zA-Z0-9\.\-_ ]*$/"
                                                                           ng-minlength="5"
                                                                           ng-maxlength="30" />
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileEditForm.OrganizationName.$dirty && ProfileEditForm.OrganizationName.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileEditForm.OrganizationName.$error.required">*Required</span></li>
                                                                            <li><span class="pull-left" ng-show="ProfileEditForm.OrganizationName.$error.pattern">Not a valid Company Name</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationName.$error.pattern && ProfileEditForm.OrganizationName.$error.minlength">Minimum length is 5</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationName.$error.pattern && ProfileEditForm.OrganizationName.$error.maxlength">Maximum length is 30</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                                <td rowspan="3" colspan="2" class="text-center">
                                                                    <div class="col-md-12 pull-right">
                                                                        <div style="float:right">
                                                                            <div class="fileinput fileinput-new" data-provides="fileinput" style="margin-left:0%; width:160px; min-width:100%; max-width:160%">
                                                                                <div id="imageHolderEdit" class="fileinput-preview thumbnail" data-trigger="fileinput" style="min-width: 160px; max-width:100%; height: 114px;"></div>
                                                                                <div>
                                                                                    <span class="btn btn-default btn-file btn-block">
                                                                                        <span class="fileinput-new">Select Company Logo</span>
                                                                                        <span class="fileinput-exists">Change Logo</span>
                                                                                        <input type="file" id="fileCompanyLogo" ng-files="getTheFiles($files)" tabindex="-1" name="fileCompanyLogo" bind-file ng-model="theFile">
                                                                                    </span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Company Type<span class="text-danger">*</span></th>
                                                                <td>
                                                                    <select class="form-control" name="CompanyTypeId"
                                                                            ng-model="Profile.CompanyTypeId"
                                                                            ng-required="true"
                                                                            ng-options="CompanyType.Id as CompanyType.CompanyTypeName for CompanyType in CompanyTypeList">
                                                                        <option value="">Select</option>
                                                                    </select>
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileEditForm.CompanyTypeId.$dirty && ProfileEditForm.CompanyTypeId.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileEditForm.CompanyTypeId.$error.required">*Required</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Phone No.</th>
                                                                <td>
                                                                    <input type="text" name="OrganizationPhone" class="form-control" placeholder="(e.g. 02xxxxx)"
                                                                           ng-model="Profile.OrganizationPhone"
                                                                           ng-minlength="5"
                                                                           ng-maxlength="20" />
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileEditForm.OrganizationPhone.$dirty && ProfileEditForm.OrganizationPhone.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileEditForm.OrganizationPhone.$error.pattern">Not a valid Phone Number</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationPhone.$error.pattern && ProfileEditForm.OrganizationPhone.$error.minlength">Minimum length is 5</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationPhone.$error.pattern && ProfileEditForm.OrganizationPhone.$error.maxlength">Maximum length is 20</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Mobile No.</th>
                                                                <td>
                                                                    <input type="text" name="OrganizationMobile" class="form-control" placeholder="(e.g. 01xxxxxxxxx)"
                                                                           ng-model="Profile.OrganizationMobile"
                                                                           ng-minlength="11"
                                                                           ng-maxlength="20" />
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileEditForm.OrganizationMobile.$dirty && ProfileEditForm.OrganizationMobile.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileEditForm.OrganizationMobile.$error.pattern">Not a valid Mobile Number</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationMobile.$error.pattern && ProfileEditForm.OrganizationMobile.$error.minlength">Minimum length is 11</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationMobile.$error.pattern && ProfileEditForm.OrganizationMobile.$error.maxlength">Maximum length is 20</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                                <td style="line-height:2.7"><input type="text" class="form-control" placeholder="Seacrh Location" name="SearchLocation" id="SearchLocationEdit" /></td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Website</th>
                                                                <td>
                                                                    <input type="text" name="OrganizationWebsite" class="form-control" placeholder="www.example.com"
                                                                           ng-model="Profile.OrganizationWebsite"
                                                                           ng-minlength="8"
                                                                           ng-maxlength="40"
                                                                           ng-pattern="/^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?(([www]){3}|[^w]{1}[a-zA-Z0-9]{1,64})([\.]{1}[a-zA-Z0-9-]{1,253})+\.[a-z]{2,6}(:[0-9]{1,5})?(\/.*)?$/" />
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileEditForm.OrganizationWebsite.$dirty && ProfileEditForm.OrganizationWebsite.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileEditForm.OrganizationWebsite.$error.pattern">Not a valid Website</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationWebsite.$error.pattern && ProfileEditForm.OrganizationWebsite.$error.minlength">Minimum length is 8</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationWebsite.$error.pattern && ProfileEditForm.OrganizationWebsite.$error.maxlength">Maximum length is 40</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                                <td rowspan="8" class="text-center">
                                                                    <div class="text-center" style="border-style:solid; border-width:1px; border-color:#ddd #eee #eee #ddd">
                                                                        <div ng-init="LoadMap()" style="height: 351px;position: relative;overflow: hidden;width: 100%;" id="map-canvasE">
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">E-mail</th>
                                                                <td>
                                                                    <input type="text" name="OrganizationEmail" class="form-control" placeholder="<EMAIL>"
                                                                           ng-maxlength="30"
                                                                           ng-minlength="11"
                                                                           ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'
                                                                           ng-model="Profile.OrganizationEmail" />
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left" ng-show="ProfileEditForm.OrganizationEmail.$dirty && ProfileEditForm.OrganizationEmail.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileEditForm.OrganizationEmail.$error.pattern">Not a valid Email</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationEmail.$error.pattern && ProfileEditForm.OrganizationEmail.$error.minlength">Minimum length is 11</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationEmail.$error.pattern && ProfileEditForm.OrganizationEmail.$error.maxlength">Maximum length is 30</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <th style="line-height:2.7">Address</th>
                                                                <td>
                                                                    <textarea class="form-control no-resize" placeholder="Company address" style="min-height:140px;" name="OrganizationAddress"
                                                                              ng-minlength="6"
                                                                              ng-maxlength="200"
                                                                              ng-model="Profile.OrganizationAddress"></textarea>
                                                                    <div class="row custom-row">
                                                                        <ul class="list-unstyled text-danger pull-left"
                                                                            ng-show="ProfileEditForm.OrganizationAddress.$dirty && ProfileEditForm.OrganizationAddress.$invalid">
                                                                            <li><span class="pull-left" ng-show="ProfileEditForm.OrganizationAddress.$error.pattern">Not a valid Organization Name</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationAddress.$error.pattern && ProfileEditForm.OrganizationAddress.$error.minlength">Minimum length is 6</span></li>
                                                                            <li><span class="pull-left" ng-show="!ProfileEditForm.OrganizationAddress.$error.pattern && ProfileEditForm.OrganizationAddress.$error.maxlength">Maximum length is 200</span></li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </form>
                                        </section>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>






<style>
    *, *:before, *:after {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    html, body {
        height: 100vh;
    }

    body {
        /*font: 14px/1 'Open Sans', sans-serif;*/
        color: #555;
        background: #eee;
    }

    /*h1 {
        padding: 50px 0;
        font-weight: 400;
        text-align: center;
    }*/

    p {
        margin: 0 0 20px;
        line-height: 1.5;
    }

    main {
        min-width: 320px;
        max-width: 1084px;
        min-height: 662px;
        padding: 50px;
        margin: 0 auto;
        background: #fff;
    }

    section {
        display: none;
        padding: 20px 0 0;
        border-top: 1px solid #ddd;
    }

    input {
        display: none;
    }

    label {
        display: inline-block;
        margin: 0 0 -1px;
        padding: 15px 25px;
        font-weight: 600;
        text-align: center;
        color: #bbb;
        border: 1px solid transparent;
        font-size: 14px;
    }

        label:before {
            font-family: fontawesome;
            font-weight: normal;
            margin-right: 10px;
        }

        label[for*='1']:before {
            content: "\f036";
        }

        label[for*='2']:before {
            content: "\f039";
        }

        label[for*='3']:before {
            content: '\f040';
        }

        label[for*='4']:before {
            content: '\f1a9';
        }

        label[for*='5']:before {
            content: '\f1a9';
        }

        label:hover {
            color: #888;
            cursor: pointer;
        }

    input:checked + label {
        color: #555;
        border: 1px solid #ddd;
        border-top: 2px solid #64b92a;
        border-bottom: 1px solid #fff;
    }

    #tab1:checked ~ #content1,
    #tab2:checked ~ #content2,
    #tab3:checked ~ #content3,
    #tab4:checked ~ #content4 #tab5:checked ~ #content5 {
        display: block;
    }

    @media screen and (max-width: 650px) {
        label {
            font-size: 0;
        }

            label:before {
                margin: 0;
                font-size: 18px;
            }
    }

    @media screen and (max-width: 400px) {
        label {
            padding: 15px;
        }
    }

    .horizontal-line-1 {
        background-color: #fff;
        border-top: 1px dashed #e8e8e8;
    }
    /*Badge CSS*/
    .badge {
        padding: 4px 8px;
        margin: 0 2px;
    }

    .x-darker-1 {
        background: #10ae32;
        border-color: #10ae32;
        color: #ffffff;
    }
</style>
