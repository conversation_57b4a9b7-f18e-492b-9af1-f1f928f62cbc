﻿/// <reference path="../../../Assets_SProvider/angularjs/angular.js" />
/// <reference path="../../../app.js" />
PCSMSApp.controller('company_typeController', function ($scope, $rootScope, CompanyTypeServices, blockUI, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    $rootScope.settingsOpen = true;


    //Retrieving data from db:
    $scope.CompanyTypeList = CompanyTypeServices.GetCompanyTypeList().then(function (response) {
        $scope.CompanyTypeList = response.data;
        return response.data;
    });

    //Datatable
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [
                             DTColumnDefBuilder.newColumnDef(1).withOption('width', '1%').notSortable()
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
                            .withOption('paging', true)
                            .withOption('searching', true)
                            .withOption('info', true);

    

    //Creating an empty object to be sent:
    $scope.CompanyType = {};



    //Opening a modal:
    $scope.openCompanyTypeModal = function (id) {

        if (id != null) {
            //blockUI.start();
            //blockUI.message("Loading Company Type Details ...");
            CompanyTypeServices.GetCompanyTypeDetails(id).then(function (response) {
                $scope.CompanyType = response.data;
                $scope.TempCompanyTypeName = response.data.CompanyTypeName;
            })
            .then(function () {
                //blockUI.stop();
                $('#CompanyTypeModal').modal('show');
            });
        }
    };


    //Saving or updating:
    $scope.SaveOrUpdateCompanyType = function () {
        if ($scope.CompanyTypeForm.$invalid == false && $scope.LiveValidation($scope.CompanyType.CompanyTypeName) == true) {
            //console.log("===Outside===");
            //console.log($scope.CompanyType);
            if ($scope.CompanyType.Id == null) {
                //console.log("===Inside===");
                //console.log($scope.CompanyType);
                //blockUI.start();
                //blockUI.message("Saving Company Type ...");
                CompanyTypeServices.SaveCompanyType($scope.CompanyType).then(function (response) {
                    $('#CompanyTypeModal').modal('hide');
                    //console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "CompanyTypeNameExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                })
                .then(function () {
                    //Clear the models
                    $scope.cancelCompanyTypeListModal();
                    $scope.CompanyTypeForm.$setPristine();
                    $scope.CompanyTypeForm.$setUntouched();
                })
                .then(function () {
                    $timeout(function () {
                        //Reload the List
                        //blockUI.message("Loading Company Type List ...");
                        CompanyTypeServices.GetCompanyTypeList().then(function (response) {
                            $scope.CompanyTypeList = response.data;
                            return response.data;
                        });
                    }, 100)
                });
            } else {
                //blockUI.start();
                //blockUI.message("Updating Company Type ...");
                CompanyTypeServices.UpdateCompanyType($scope.CompanyType).then(function (response) {
                    $('#CompanyTypeModal').modal('hide');
                    //console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "CompanyTypeNameExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                })
                .then(function () {
                    //Clear the models
                    $scope.cancelCompanyTypeListModal();
                    $scope.CompanyTypeForm.$setPristine();
                    $scope.CompanyTypeForm.$setUntouched();

                })
                .then(function () {
                    $timeout(function () {
                        //Reload the List
                        //blockUI.message("Loading Company Type List ...");
                        CompanyTypeServices.GetCompanyTypeList().then(function (response) {
                            $scope.CompanyTypeList = response.data;
                            return response.data;
                        });
                    }, 100)
                });
            }
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }
    };

    //Deleting:
    $scope.deleteCompanyTypeAlert = function (CompanyTypeId, CompanyTypeName) {
        swal({
            title: "Are you sure?",
            text: "You are going to delete the Company Type " + CompanyTypeName,
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn-danger",
            confirmButtonText: "Yes, delete it!",
            closeOnConfirm: true
        },
        function () {
            //console.log(CompanyTypeId + ' ' + CompanyTypeName);
            //blockUI.start();
            //blockUI.message("Deleting Company Type ...");
            CompanyTypeServices.DeleteCompanyType(CompanyTypeId).then(function (response) {
                //console.log(response.data);
                if (response.data.IsReport === "Ok") {
                    toastr.success(response.data.Message, 'Successful');
                }
                else if (response.data.IsReport === "NotOk") {
                    toastr.error(response.data.Message, 'Failed');
                }
            })
            .then(function () {
                //Reload the List
                //blockUI.message("Loading Company Type List ...");
                CompanyTypeServices.GetCompanyTypeList().then(function (response) {
                    $scope.CompanyTypeList = response.data;
                    return response.data;
                })
                .then(function () {
                    //blockUI.stop();
                })
            });
        });
    };

    $scope.cancelCompanyTypeListModal = function () {
        $('#CompanyTypeModal').modal('hide');
        $timeout(function () {
            //$scope.CompanyType = {};
            //$scope.TempCompanyTypeName = null;
            //blockUI.stop();
            $state.reload();
        }, 300);
        ////console.log($scope.CompanyType);
    };

    //Live Validation
    $scope.LiveValidation = function (x) {        
        var valid = false;
        if ($scope.CompanyTypeList.length > 0) {
            for (var i = 0; i < $scope.CompanyTypeList.length; i++) {
                if ($scope.CompanyTypeList[i].CompanyTypeName == x) {
                    if ($scope.CompanyTypeList[i].CompanyTypeName == $scope.TempCompanyTypeName) {
                        valid = true;
                    }
                    else {
                        valid = false;
                        $scope.LiveValidationError = "Company Type already exists";
                        break;
                    }
                }
                else {
                    valid = true;
                }
            }
        }
        else if ($scope.CompanyTypeList.length == 0) {
            valid = true;
        }
        return valid;
    };
})