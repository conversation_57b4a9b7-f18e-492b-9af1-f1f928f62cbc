﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PCSMS.Models.Common;

namespace PCSMS.Models.Models_Company
{
    public class CP_User_LSession : Entity<int>
    {
        [ForeignKey("UserId")]
        public virtual CP_User CP_User { get; set; }
        public int? UserId { get; set; }

        public string DeviceUniqueId { get; set; }

        [ForeignKey("DeviceId")]
        public virtual CP_Device CP_Device { get; set; }
        public int? DeviceId { get; set; }

    }
}
