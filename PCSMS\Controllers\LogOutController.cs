﻿using System.Web.Http;
using PCSMS.Models.Models_Shared;
using PCSMS.Services.Services_Company;
using PCSMS.Services.Services_Shared;

namespace PCSMS.Controllers
{
    [RoutePrefix("api/LogOut")]
    public class LogOutController : ApiController
    {
        private readonly ICP_TokenServices _tokenServices;

        public LogOutController()
        {
            _tokenServices = new TokenServices();
        }


        [Route("LogOutByCompanyId/{companyId:int}")]
        [HttpPost]
        public IHttpActionResult LogOutByCompanyId(int companyId)
        {
            return Ok(_tokenServices.DeleteCPTokenByCompanyId(companyId));
        }

        [Route("LogOutByUserId/{userId:int}")]
        [HttpPost]
        public IHttpActionResult DeleteTPTokenByUserId(int userId)
        {
            return Ok(_tokenServices.DeleteTPTokenByUserId(userId));
        }

    }
}
