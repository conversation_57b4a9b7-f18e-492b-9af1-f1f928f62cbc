﻿PCSMSApp.factory("databaseServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetCompanyListWhoHasScreenshots: function () {
            return $http({
                url: "/api/CP_Profile/GetCompanyListWhoHasScreenshots",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DeleteSingularCompanyScreenshotsFromFolderByDateRange: function (companyId, dateUpTo) {
            return $http({
                url: "/api/Device/DeleteSingularCompanyScreenshotsFromFolderByDateRange/" + companyId + "/" + dateUpTo,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DeleteAllCompanyScreenshotsFromFolderByDateRange: function (dateUpTo) {
            return $http({
                url: "/api/Device/DeleteAllCompanyScreenshotsFromFolderByDateRange/" +  dateUpTo,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        }

    };

}]);