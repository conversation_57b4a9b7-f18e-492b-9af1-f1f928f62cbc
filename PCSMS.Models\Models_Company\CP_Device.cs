﻿using System;
using PCSMS.Models.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PCSMS.Services.Services_Company;

namespace PCSMS.Models.Models_Company
{
    public class CP_Device : Entity<int>
    {
        [ForeignKey("CompanyId")]
        public virtual CP_Profile CP_Profile { get; set; }
        public int? CompanyId { get; set; }

        
        [MaxLength(150)]
        public string DeviceName { get; set; }
        public string DeviceUniqueId { get; set; }
        [MaxLength(10)]
        public string Status { get; set; }  //Registered

        public int? Interval { get; set; }
        [MaxLength(5)]
        public string IsRandom { get; set; }
        public TimeSpan? StartTime { get; set; }
        public TimeSpan? EndTime { get; set; }
        [MaxLength(5)]
        public string Mon { get; set; }
        [MaxLength(5)]
        public string Tues { get; set; }
        [MaxLength(5)]
        public string Wed { get; set; }
        [MaxLength(5)]
        public string Thurs { get; set; }
        [MaxLength(5)]
        public string Fri { get; set; }
        [MaxLength(5)]
        public string Sat { get; set; }
        [MaxLength(5)]
        public string Sun { get; set; }

        [ForeignKey("DefaultScheduleId")]
        public virtual CP_Device_Schedule CP_Device_Schedule { get; set; }
        public int? DefaultScheduleId { get; set; }


        
        
        public int? GroupId { get; set; }


        
        
        public int? GroupScheduleId { get; set; }


    }
}
