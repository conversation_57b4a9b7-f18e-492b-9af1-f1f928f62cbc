﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!-- main content -->

<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-legal" aria-hidden="true"></i><a ui-sref="designation">Designation</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-header">
                        <button type="button" class="btn btn-default btn-o pull-right btn-addon btn-o-addon" id="btnAddDesignation" data-toggle="modal" data-target="#DesignationModal">
                            <i class="fa fa-plus" aria-hidden="true"></i>
                            Add Designation
                        </button>
                    </div>
                    <div class="panel-content">
                        <div class="table-responsive">
                            <!-- DataTable -->
                            <table class="data-table table table-striped nowrap table-bordered table-condensed" id="basic-table" datatable="ng" dt-options="vm.dtOptions" dt-instance="vm.dtInstance" dt-column-defs="vm.dtColumnDefs" cellspacing="0">
                                <thead>
                                    <tr class="animated fadeIn">
                                        <th>Designation</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="animated fadeIn" ng-repeat="x in DesignationList">
                                        <td>{{x.DesignationName}}</td>
                                        <td>
                                            <button class="btn btn-default btn-Line-Height btn-datatables" tooltip-placement="top" uib-tooltip="Edit" ng-click="openDesignationModal(x.Id)" style="border:1px solid #02715a; background-color:#ffffff"><img src="Assets_SProvider/images/datatables/edit.png" width="20" /></button>
                                            <!--<button class="btn btn-default btn-Line-Height btn-datatables" ng-click="deleteDesignationAlert(x.Id, x.DesignationName)" style="border-color: #abaaaa"><img src="Assets_SProvider/img/datatables/delete.png" width="20" /></button>-->
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>

<form novalidate name="DesignationForm">
    <div class="modal fade" id="DesignationModal" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog  modal-dialog-at">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="custom-close" data-dismiss="modal" id="Designation-btn-top-close" ng-click="cancelDesignationListModal()" tooltip-placement="left" uib-tooltip="Close">&times;</button>
                    <h4 ng-if="Designation.Id === undefined">Create New Designation</h4>
                    <h4 ng-if="Designation.Id >=1">Update Designation</h4>
                    <div class="form-sep">
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-10 col-md-offset-2">
                            <div class="col-md-3">
                                <label class="control-label label-for-textbox-center-direction">Designation</label>
                            </div>
                            <div class="col-md-9">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Enter Designation" name="DesignationName"
                                           ng-required="true"
                                           ng-model="Designation.DesignationName"
                                           ng-change="LiveValidation(Designation.DesignationName)"
                                           ng-minlength="4"
                                           ng-maxlength="50"
                                           ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" />
                                    <div class="row custom-row">
                                        <ul class="list-unstyled text-danger pull-left" ng-show="LiveValidation(Designation.DesignationName)==false || DesignationForm.DesignationName.$dirty && DesignationForm.DesignationName.$invalid">
                                            <li><span class="pull-left" ng-show="LiveValidation(Designation.DesignationName)==false">{{LiveValidationError}}</span></li>
                                            <li><span class="pull-left" ng-show="DesignationForm.DesignationName.$error.required">*Required</span></li>
                                            <li><span class="pull-left" ng-show="DesignationForm.DesignationName.$error.pattern">Start with capital letter, don't use special character.</span></li>
                                            <li><span class="pull-left" ng-show="!DesignationForm.DesignationName.$error.pattern && DesignationForm.DesignationName.$error.minlength">Minimum length is 4</span></li>
                                            <li><span class="pull-left" ng-show="!DesignationForm.DesignationName.$error.pattern && DesignationForm.DesignationName.$error.maxlength">Maximum length is 50</span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="col-md-5 col-md-offset-7">
                        <div class="btn-group btn-group-justified" role="group" aria-label="group button" ng-if="Designation.Id === undefined">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-default pull-right" ng-click="cancelDesignationListModal()" role="button">Close</button>
                            </div>
                            <div class="btn-group" role="group">
                                <button ng-disabled="DesignationForm.$invalid || LiveValidation(Designation.DesignationName)==false"
                                        type="button"
                                        ng-click="SaveOrUpdateDesignation()" class="btn btn-sm btn-primary">
                                    Save &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                </button>
                            </div>
                        </div>
                        <div class="btn-group btn-group-justified" role="group" aria-label="group button" ng-if="Designation.Id >=1">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-default pull-right" ng-click="cancelDesignationListModal()" role="button">Close</button>
                            </div>
                            <div class="btn-group" role="group">
                                <button ng-disabled="DesignationForm.$invalid || LiveValidation(Designation.DesignationName)==false"
                                        type="button"
                                        ng-click="SaveOrUpdateDesignation()" class="btn btn-sm btn-primary">
                                    Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

