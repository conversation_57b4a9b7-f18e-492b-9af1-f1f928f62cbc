﻿/// <reference path="../app.js" />
//directive for Image info:
PCSMSApp.directive('ngFiles', [
    '$parse', function ($parse) {
        function fn_link(scope, element, attrs) {
            var onChange = $parse(attrs.ngFiles);
            element.on('change', function (event) {
                onChange(scope, { $files: event.target.files });
            });
        };

        return {
            link: fn_link
        }
    }
]);


//Custom Directive
PCSMSApp.directive('ignoreMouseWheel', function ($rootScope) {
    return {
        restrict: 'A',
        link: function (scope, element, attrs) {
            element.bind('mousewheel', function (event) {
                element.blur();
            });
        }
    }
});

PCSMSApp.directive('tmpl', function ($compile) {
    return {
        restrict: 'A',
        templateUrl: 'app/package/PackageHistory.html',
        transclude: true,
        link: function (scope, element, attrs) {
        }
    }
});

//PCSMSApp.directive('dynamicFormElement', function ($compile) {
//    return {
//        restrict: "E",
//        link: function (scope, element) {
//            $templateRequest("app/order/manage_product/DynamicFormElement.html").then(function (html) {
//                var template = angular.element(html);
//                element.append(template);
//                $compile(template)(scope);
//            });
//        },
//        scope: {
//            content:'='
//        }
//    };
//})