﻿PCSMSApp.factory("licenseServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetLicenseListByCompanyId: function (companyId) {
            return $http({
                url: "/api/Subscription/GetLicenseListByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetLicenseDetailsByLicenseId: function (licenseId) {
            return $http({
                url: "/api/Subscription/GetLicenseDetailsByLicenseId/" + licenseId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        Requestlicense: function (companyId) {
            return $http({
                url: "/api/Subscription/Requestlicense/" + companyId,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        RequestForLicenseRenewal: function (licenseId) {
            return $http({
                url: "/api/Subscription/RequestForLicenseRenewal/" + licenseId,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
    };
}]);