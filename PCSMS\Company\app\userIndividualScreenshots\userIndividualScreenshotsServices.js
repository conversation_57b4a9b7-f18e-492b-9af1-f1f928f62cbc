﻿PCSMSApp.factory("userIndividualScreenshotsServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetCP_UserListByCompanyId: function (companyId) {
            return $http({
                url: "/api/CP_User/GetCP_UserListByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
       GetScreenshotsListForSpecificSessionByUserId: function (userId, session) {
           return $http({
               url: "/api/Device/GetScreenshotsListForSpecificSessionByUserId/" + userId + "/" + session,
               method: "GET",
               headers: {
                   "content-type": "application/json",
                   "cache-control": "no-cache"
               },
               async: false
           });
       },
       GetScreenshotsListForSpecificSessionByUserIdAndLastId: function (userId, session, lastId) {
           return $http({
               url: "/api/Device/GetScreenshotsListForSpecificSessionByUserId/" + userId + "/" + session + "/" + lastId,
               method: "GET",
               headers: {
                   "content-type": "application/json",
                   "cache-control": "no-cache"
               },
               async: false
           });
       },
       GetScreenshotsListByCustomRangeByUserId: function (userId, from, to) {
           return $http({
               url: "/api/Device/GetScreenshotsListByCustomRangeByUserId/" + userId + "/" + from + "/" + to,
               method: "GET",
               headers: {
                   "content-type": "application/json",
                   "cache-control": "no-cache"
               },
               async: false
           });
       },
       GetScreenshotsListByCustomRangeByUserIdAndLastId: function (userId, from, to, lastId) {
            return $http({
                url: "/api/Device/GetScreenshotsListByCustomRangeByUserId/" + userId + "/" + from + "/" + to + "/" + lastId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        
    };
}]);