﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PCSMS.Models.Common;

namespace PCSMS.Models.Models_Company
{
    public class CP_ScreenCapture : Entity<int>
    {
        [ForeignKey("UserId")]
        public virtual CP_User CP_User { get; set; }
        public int? UserId { get; set; }

        [ForeignKey("DeviceId")]
        public virtual CP_Device CP_Device { get; set; }
        public int? DeviceId { get; set; }


        [ForeignKey("CompanyId")]
        public virtual CP_Profile CP_Profile { get; set; }
        public int? CompanyId { get; set; }

        [MaxLength(50)]
        public string DeviceUniqueId { get; set; }
        
        public string ScreenShot { get; set; }
        
        public int? MouseClick { get; set; }
        
        public int? KeyStroke { get; set; }

        public DateTime? CapturedOn { get; set; }
        public DateTime? EntriedOn { get; set; }

        [MaxLength(10)]
        public string Status { get; set; } //Deleted; when admin deletes any file from server
    }
}
