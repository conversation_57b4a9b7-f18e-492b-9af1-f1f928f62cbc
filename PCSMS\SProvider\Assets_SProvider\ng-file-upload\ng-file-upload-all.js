﻿!function () { function e(e, t) { window.XMLHttpRequest.prototype[e] = t(window.XMLHttpRequest.prototype[e]) } function t(e, t, n) { try { Object.defineProperty(e, t, { get: n }) } catch (e) { } } if (window.FileAPI || (window.FileAPI = {}), !window.XMLHttpRequest) throw "AJAX is not supported. XMLHttpRequest is not defined."; if (FileAPI.shouldLoad = !window.FormData || FileAPI.forceLoad, FileAPI.shouldLoad) { var n = function (e) { if (!e.__listeners) { e.upload || (e.upload = {}), e.__listeners = []; var t = e.upload.addEventListener; e.upload.addEventListener = function (n, r) { e.__listeners[n] = r, t && t.apply(this, arguments) } } }; e("open", function (e) { return function (t, r, i) { n(this), this.__url = r; try { e.apply(this, [t, r, i]) } catch (n) { n.message.indexOf("Access is denied") > -1 && (this.__origError = n, e.apply(this, [t, "_fix_for_ie_crossdomain__", i])) } } }), e("getResponseHeader", function (e) { return function (t) { return this.__fileApiXHR && this.__fileApiXHR.getResponseHeader ? this.__fileApiXHR.getResponseHeader(t) : null == e ? null : e.apply(this, [t]) } }), e("getAllResponseHeaders", function (e) { return function () { return this.__fileApiXHR && this.__fileApiXHR.getAllResponseHeaders ? this.__fileApiXHR.getAllResponseHeaders() : null == e ? null : e.apply(this) } }), e("abort", function (e) { return function () { return this.__fileApiXHR && this.__fileApiXHR.abort ? this.__fileApiXHR.abort() : null == e ? null : e.apply(this) } }), e("setRequestHeader", function (e) { return function (t, r) { if ("__setXHR_" === t) { n(this); var i = r(this); i instanceof Function && i(this) } else this.__requestHeaders = this.__requestHeaders || {}, this.__requestHeaders[t] = r, e.apply(this, arguments) } }), e("send", function (e) { return function () { var n = this; if (arguments[0] && arguments[0].__isFileAPIShim) { for (var r = arguments[0], i = { url: n.__url, jsonp: !1, cache: !0, complete: function (e, r) { e && angular.isString(e) && -1 !== e.indexOf("#2174") && (e = null), n.__completed = !0, !e && n.__listeners.load && n.__listeners.load({ type: "load", loaded: n.__loaded, total: n.__total, target: n, lengthComputable: !0 }), !e && n.__listeners.loadend && n.__listeners.loadend({ type: "loadend", loaded: n.__loaded, total: n.__total, target: n, lengthComputable: !0 }), "abort" === e && n.__listeners.abort && n.__listeners.abort({ type: "abort", loaded: n.__loaded, total: n.__total, target: n, lengthComputable: !0 }), void 0 !== r.status && t(n, "status", function () { return 0 === r.status && e && "abort" !== e ? 500 : r.status }), void 0 !== r.statusText && t(n, "statusText", function () { return r.statusText }), t(n, "readyState", function () { return 4 }), void 0 !== r.response && t(n, "response", function () { return r.response }); var i = r.responseText || (e && 0 === r.status && "abort" !== e ? e : void 0); t(n, "responseText", function () { return i }), t(n, "response", function () { return i }), e && t(n, "err", function () { return e }), n.__fileApiXHR = r, n.onreadystatechange && n.onreadystatechange(), n.onload && n.onload() }, progress: function (e) { if (e.target = n, n.__listeners.progress && n.__listeners.progress(e), n.__total = e.total, n.__loaded = e.loaded, e.total === e.loaded) { var t = this; setTimeout(function () { n.__completed || (n.getAllResponseHeaders = function () { }, t.complete(null, { status: 204, statusText: "No Content" })) }, FileAPI.noContentTimeout || 1e4) } }, headers: n.__requestHeaders, data: {}, files: {} }, a = 0; a < r.data.length; a++) { var o = r.data[a]; null != o.val && null != o.val.name && null != o.val.size && null != o.val.type ? i.files[o.key] = o.val : i.data[o.key] = o.val } setTimeout(function () { if (!FileAPI.hasFlash) throw 'Adode Flash Player need to be installed. To check ahead use "FileAPI.hasFlash"'; n.__fileApiXHR = FileAPI.upload(i) }, 1) } else { if (this.__origError) throw this.__origError; e.apply(n, arguments) } } }), window.XMLHttpRequest.__isFileAPIShim = !0, window.FormData = FormData = function () { return { append: function (e, t, n) { t.__isFileAPIBlobShim && (t = t.data[0]), this.data.push({ key: e, val: t, name: n }) }, data: [], __isFileAPIShim: !0 } }, window.Blob = Blob = function (e) { return { data: e, __isFileAPIBlobShim: !0 } } } }(), function () { function e() { try { if (new ActiveXObject("ShockwaveFlash.ShockwaveFlash")) return !0 } catch (e) { if (void 0 !== navigator.mimeTypes["application/x-shockwave-flash"]) return !0 } return !1 } function t(e) { var t = 0, n = 0; if (window.jQuery) return jQuery(e).offset(); if (e.offsetParent) do { t += e.offsetLeft - e.scrollLeft, n += e.offsetTop - e.scrollTop, e = e.offsetParent } while (e); return { left: t, top: n } } if (FileAPI.shouldLoad) { if (FileAPI.hasFlash = e(), FileAPI.forceLoad && (FileAPI.html5 = !1), !FileAPI.upload) { var n, r, i, a, o, l = document.createElement("script"), s = document.getElementsByTagName("script"); if (window.FileAPI.jsUrl) n = window.FileAPI.jsUrl; else if (window.FileAPI.jsPath) r = window.FileAPI.jsPath; else for (i = 0; i < s.length; i++) if ((a = (o = s[i].src).search(/\/ng\-file\-upload[\-a-zA-z0-9\.]*\.js/)) > -1) { r = o.substring(0, a + 1); break } null == FileAPI.staticPath && (FileAPI.staticPath = r), l.setAttribute("src", n || r + "FileAPI.min.js"), document.getElementsByTagName("head")[0].appendChild(l) } FileAPI.ngfFixIE = function (n, r, i) { if (!e()) throw 'Adode Flash Player need to be installed. To check ahead use "FileAPI.hasFlash"'; n.bind("mouseenter", function () { var e, o = r.parent(); n.attr("disabled") ? o && o.removeClass("js-fileapi-wrapper") : (r.attr("__ngf_flash_") || (r.unbind("change"), r.unbind("click"), r.bind("change", function (e) { a.apply(this, [e]), i.apply(this, [e]) }), r.attr("__ngf_flash_", "true")), o.addClass("js-fileapi-wrapper"), "input" === (e = n)[0].tagName.toLowerCase() && e.attr("type") && "file" === e.attr("type").toLowerCase() || (o.css("position", "absolute").css("top", t(n[0]).top + "px").css("left", t(n[0]).left + "px").css("width", n[0].offsetWidth + "px").css("height", n[0].offsetHeight + "px").css("filter", "alpha(opacity=0)").css("display", n.css("display")).css("overflow", "hidden").css("z-index", "900000").css("visibility", "visible"), r.css("width", n[0].offsetWidth + "px").css("height", n[0].offsetHeight + "px").css("position", "absolute").css("top", "0px").css("left", "0px"))) }); var a = function (e) { for (var t = FileAPI.getFiles(e), n = 0; n < t.length; n++) void 0 === t[n].size && (t[n].size = 0), void 0 === t[n].name && (t[n].name = "file"), void 0 === t[n].type && (t[n].type = "undefined"); e.target || (e.target = {}), e.target.files = t, e.target.files !== t && (e.__files_ = t), (e.__files_ || e.target.files).item = function (t) { return (e.__files_ || e.target.files)[t] || null } } }, FileAPI.disableFileInput = function (e, t) { t ? e.removeClass("js-fileapi-wrapper") : e.addClass("js-fileapi-wrapper") } } }(), window.FileReader || (window.FileReader = function () { var e = this, t = !1; this.listeners = {}, this.addEventListener = function (t, n) { e.listeners[t] = e.listeners[t] || [], e.listeners[t].push(n) }, this.removeEventListener = function (t, n) { e.listeners[t] && e.listeners[t].splice(e.listeners[t].indexOf(n), 1) }, this.dispatchEvent = function (t) { var n = e.listeners[t.type]; if (n) for (var r = 0; r < n.length; r++) n[r].call(e, t) }, this.onabort = this.onerror = this.onload = this.onloadstart = this.onloadend = this.onprogress = null; var n = function (t, n) { var r = { type: t, target: e, loaded: n.loaded, total: n.total, error: n.error }; return null != n.result && (r.target.result = n.result), r }, r = function (r) { var i; t || (t = !0, e.onloadstart && e.onloadstart(n("loadstart", r))), "load" === r.type ? (e.onloadend && e.onloadend(n("loadend", r)), i = n("load", r), e.onload && e.onload(i), e.dispatchEvent(i)) : "progress" === r.type ? (i = n("progress", r), e.onprogress && e.onprogress(i), e.dispatchEvent(i)) : (i = n("error", r), e.onerror && e.onerror(i), e.dispatchEvent(i)) }; this.readAsDataURL = function (e) { FileAPI.readAsDataURL(e, r) }, this.readAsText = function (e) { FileAPI.readAsText(e, r) } }), !window.XMLHttpRequest || window.FileAPI && FileAPI.shouldLoad || (window.XMLHttpRequest.prototype.setRequestHeader = function (e) { return function (t, n) { if ("__setXHR_" === t) { var r = n(this); r instanceof Function && r(this) } else e.apply(this, arguments) } }(window.XMLHttpRequest.prototype.setRequestHeader)); var ngFileUpload = angular.module("ngFileUpload", []); ngFileUpload.version = "12.2.13", ngFileUpload.service("UploadBase", ["$http", "$q", "$timeout", function (e, t, n) { var r = this; r.promisesCount = 0, this.isResumeSupported = function () { return window.Blob && window.Blob.prototype.slice }; var i = this.isResumeSupported(); function a(a) { a.method = a.method || "POST", a.headers = a.headers || {}; var o = a._deferred = a._deferred || t.defer(), l = o.promise; function s(e) { o.notify && o.notify(e), l.progressFunc && n(function () { l.progressFunc(e) }) } function u(e) { return null != a._start && i ? { loaded: e.loaded + a._start, total: a._file && a._file.size || e.total, type: e.type, config: a, lengthComputable: !0, target: e.target } : e } function f() { e(a).then(function (e) { if (i && a._chunkSize && !a._finished && a._file) { var t = a._file && a._file.size || 0; s({ loaded: Math.min(a._end, t), total: t, config: a, type: "progress" }), r.upload(a, !0) } else a._finished && delete a._finished, o.resolve(e) }, function (e) { o.reject(e) }, function (e) { o.notify(e) }) } return a.disableProgress || (a.headers.__setXHR_ = function () { return function (e) { e && e.upload && e.upload.addEventListener && (a.__XHR = e, a.xhrFn && a.xhrFn(e), e.upload.addEventListener("progress", function (e) { e.config = a, s(u(e)) }, !1), e.upload.addEventListener("load", function (e) { e.lengthComputable && (e.config = a, s(u(e))) }, !1)) } }), i ? a._chunkSize && a._end && !a._finished ? (a._start = a._end, a._end += a._chunkSize, f()) : a.resumeSizeUrl ? e.get(a.resumeSizeUrl).then(function (e) { a.resumeSizeResponseReader ? a._start = a.resumeSizeResponseReader(e.data) : a._start = parseInt((null == e.data.size ? e.data : e.data.size).toString()), a._chunkSize && (a._end = a._start + a._chunkSize), f() }, function (e) { throw e }) : a.resumeSize ? a.resumeSize().then(function (e) { a._start = e, a._chunkSize && (a._end = a._start + a._chunkSize), f() }, function (e) { throw e }) : (a._chunkSize && (a._start = 0, a._end = a._start + a._chunkSize), f()) : f(), l.success = function (e) { return l.then(function (t) { e(t.data, t.status, t.headers, a) }), l }, l.error = function (e) { return l.then(null, function (t) { e(t.data, t.status, t.headers, a) }), l }, l.progress = function (e) { return l.progressFunc = e, l.then(null, null, function (t) { e(t) }), l }, l.abort = l.pause = function () { return a.__XHR && n(function () { a.__XHR.abort() }), l }, l.xhr = function (e) { var t; return a.xhrFn = (t = a.xhrFn, function () { t && t.apply(l, arguments), e.apply(l, arguments) }), l }, r.promisesCount++, l.finally && l.finally instanceof Function && l.finally(function () { r.promisesCount-- }), l } function o(e) { var t = {}; for (var n in e) e.hasOwnProperty(n) && (t[n] = e[n]); return t } this.isUploadInProgress = function () { return r.promisesCount > 0 }, this.rename = function (e, t) { return e.ngfName = t, e }, this.jsonBlob = function (e) { null == e || angular.isString(e) || (e = JSON.stringify(e)); var t = new window.Blob([e], { type: "application/json" }); return t._ngfBlob = !0, t }, this.json = function (e) { return angular.toJson(e) }, this.isFile = function (e) { return null != e && (e instanceof window.Blob || e.flashId && e.name && e.size) }, this.upload = function (e, t) { function n(t, a, o) { if (void 0 !== a) if (angular.isDate(a) && (a = a.toISOString()), angular.isString(a)) t.append(o, a); else if (r.isFile(a)) { var l = function (t, n) { if (t._ngfBlob) return t; if (e._file = e._file || t, null != e._start && i) { e._end && e._end >= t.size && (e._finished = !0, e._end = t.size); var r = t.slice(e._start, e._end || t.size); return r.name = t.name, r.ngfName = t.ngfName, e._chunkSize && (n.append("_chunkSize", e._chunkSize), n.append("_currentChunkSize", e._end - e._start), n.append("_chunkNumber", Math.floor(e._start / e._chunkSize)), n.append("_totalSize", e._file.size)), r } return t }(a, t), s = o.split(","); s[1] && (l.ngfName = s[1].replace(/^\s+|\s+$/g, ""), o = s[0]), e._fileKey = e._fileKey || o, t.append(o, l, l.ngfName || l.name) } else if (angular.isObject(a)) { if (a.$$ngfCircularDetection) throw "ngFileUpload: Circular reference in config.data. Make sure specified data for Upload.upload() has no circular reference: " + o; a.$$ngfCircularDetection = !0; try { for (var u in a) if (a.hasOwnProperty(u) && "$$ngfCircularDetection" !== u) { var f = null == e.objectKey ? "[i]" : e.objectKey; a.length && parseInt(u) > -1 && (f = null == e.arrayKey ? f : e.arrayKey), n(t, a[u], o + f.replace(/[ik]/g, u)) } } finally { delete a.$$ngfCircularDetection } } else t.append(o, a) } return t || (e = o(e)), e._isDigested || (e._isDigested = !0, e._chunkSize = r.translateScalars(e.resumeChunkSize), e._chunkSize = e._chunkSize ? parseInt(e._chunkSize.toString()) : null, e.headers = e.headers || {}, e.headers["Content-Type"] = void 0, e.transformRequest = e.transformRequest ? angular.isArray(e.transformRequest) ? e.transformRequest : [e.transformRequest] : [], e.transformRequest.push(function (t) { var r, i = new window.FormData; t = t || e.fields || {}, e.file && (t.file = e.file); for (r in t) if (t.hasOwnProperty(r)) { var a = t[r]; e.formDataAppender ? e.formDataAppender(i, r, a) : n(i, a, r) } return i })), a(e) }, this.http = function (t) { return (t = o(t)).transformRequest = t.transformRequest || function (t) { return window.ArrayBuffer && t instanceof window.ArrayBuffer || t instanceof window.Blob ? t : e.defaults.transformRequest[0].apply(this, arguments) }, t._chunkSize = r.translateScalars(t.resumeChunkSize), t._chunkSize = t._chunkSize ? parseInt(t._chunkSize.toString()) : null, a(t) }, this.translateScalars = function (e) { if (angular.isString(e)) { if (e.search(/kb/i) === e.length - 2) return parseFloat(1024 * e.substring(0, e.length - 2)); if (e.search(/mb/i) === e.length - 2) return parseFloat(1048576 * e.substring(0, e.length - 2)); if (e.search(/gb/i) === e.length - 2) return parseFloat(1073741824 * e.substring(0, e.length - 2)); if (e.search(/b/i) === e.length - 1) return parseFloat(e.substring(0, e.length - 1)); if (e.search(/s/i) === e.length - 1) return parseFloat(e.substring(0, e.length - 1)); if (e.search(/m/i) === e.length - 1) return parseFloat(60 * e.substring(0, e.length - 1)); if (e.search(/h/i) === e.length - 1) return parseFloat(3600 * e.substring(0, e.length - 1)) } return e }, this.urlToBlob = function (n) { var r = t.defer(); return e({ url: n, method: "get", responseType: "arraybuffer" }).then(function (e) { var t = new Uint8Array(e.data), i = e.headers("content-type") || "image/WebP", a = new window.Blob([t], { type: i }), o = n.match(/.*\/(.+?)(\?.*)?$/); o.length > 1 && (a.name = o[1]), r.resolve(a) }, function (e) { r.reject(e) }), r.promise }, this.setDefaults = function (e) { this.defaults = e || {} }, this.defaults = {}, this.version = ngFileUpload.version }]), ngFileUpload.service("Upload", ["$parse", "$timeout", "$compile", "$q", "UploadExif", function (e, t, n, r, i) { var a = i; function o(e, t, n, i, o) { var l = [a.emptyPromise()]; function s(r, s) { if (0 === r.type.indexOf("image")) { if (e.pattern && !a.validatePattern(r, e.pattern)) return; e.resizeIf = function (e, t) { return a.attrGetter("ngfResizeIf", n, i, { $width: e, $height: t, $file: r }) }; var u = a.resize(r, e); l.push(u), u.then(function (e) { t.splice(s, 1, e) }, function (e) { r.$error = "resize", (r.$errorMessages = r.$errorMessages || {}).resize = !0, r.$errorParam = (e ? (e.message ? e.message : e) + ": " : "") + (r && r.name), o.$ngfValidations.push({ name: "resize", valid: !1 }), a.applyModelValidation(o, t) }) } } for (var u = 0; u < t.length; u++) s(t[u], u); return r.all(l) } return a.getAttrWithDefaults = function (e, t) { if (null != e[t]) return e[t]; var n = a.defaults[t]; return null == n ? n : angular.isString(n) ? n : JSON.stringify(n) }, a.attrGetter = function (t, n, r, i) { var a = this.getAttrWithDefaults(n, t); if (!r) return a; try { return i ? e(a)(r, i) : e(a)(r) } catch (e) { if (t.search(/min|max|pattern/i)) return a; throw e } }, a.shouldUpdateOn = function (e, t, n) { var r = a.attrGetter("ngfModelOptions", t, n); return !r || !r.updateOn || r.updateOn.split(" ").indexOf(e) > -1 }, a.emptyPromise = function () { var e = r.defer(), n = arguments; return t(function () { e.resolve.apply(e, n) }), e.promise }, a.rejectPromise = function () { var e = r.defer(), n = arguments; return t(function () { e.reject.apply(e, n) }), e.promise }, a.happyPromise = function (e, n) { var i = r.defer(); return e.then(function (e) { i.resolve(e) }, function (e) { t(function () { throw e }), i.resolve(n) }), i.promise }, a.updateModel = function (n, i, l, s, u, f, c) { function d(r, o, u, c, d) { i.$$ngfPrevValidFiles = r, i.$$ngfPrevInvalidFiles = o; var p = r && r.length ? r[0] : null, h = o && o.length ? o[0] : null; n && (a.applyModelValidation(n, r), n.$setViewValue(d ? p : r)), s && e(s)(l, { $files: r, $file: p, $newFiles: u, $duplicateFiles: c, $invalidFiles: o, $invalidFile: h, $event: f }); var g = a.attrGetter("ngfModelInvalid", i); g && t(function () { e(g).assign(l, d ? h : o) }), t(function () { }) } var p, h, g, v, m = [], _ = [], y = []; function b() { function e() { t(function () { d(w ? h.concat(y) : y, w ? g.concat(_) : _, u, m, $) }, F && F.debounce ? F.debounce.change || F.debounce : 0) } var s = A ? p : y; (function (e, t, n, i) { var l = a.attrGetter("ngfResize", t, n); if (!l || !a.isResizeSupported() || !e.length) return a.emptyPromise(); if (l instanceof Function) { var s = r.defer(); return l(e).then(function (r) { o(r, e, t, n, i).then(function (e) { s.resolve(e) }, function (e) { s.reject(e) }) }, function (e) { s.reject(e) }) } return o(l, e, t, n, i) })(s, i, l, n).then(function () { A ? a.validate(p, w ? h.length : 0, n, i, l).then(function (t) { y = t.validsFiles, _ = t.invalidsFiles, e() }) : e() }, function () { for (var t = 0; t < s.length; t++) { var n = s[t]; if ("resize" === n.$error) { var r = y.indexOf(n); r > -1 && (y.splice(r, 1), _.push(n)), e() } } }) } h = i.$$ngfPrevValidFiles || [], g = i.$$ngfPrevInvalidFiles || [], n && n.$modelValue && (v = n.$modelValue, h = angular.isArray(v) ? v : [v]); var w = a.attrGetter("ngfKeep", i, l); p = (u || []).slice(0), "distinct" !== w && !0 !== a.attrGetter("ngfKeepDistinct", i, l) || function () { function e(e, t) { return e.name === t.name && (e.$ngfOrigSize || e.size) === (t.$ngfOrigSize || t.size) && e.type === t.type } function t(t) { var n; for (n = 0; n < h.length; n++) if (e(t, h[n])) return !0; for (n = 0; n < g.length; n++) if (e(t, g[n])) return !0; return !1 } if (u) { p = [], m = []; for (var n = 0; n < u.length; n++) t(u[n]) ? m.push(u[n]) : p.push(u[n]) } }(); var $ = !w && !a.attrGetter("ngfMultiple", i, l) && !a.attrGetter("multiple", i); if (!w || p.length) { a.attrGetter("ngfBeforeModelChange", i, l, { $files: u, $file: u && u.length ? u[0] : null, $newFiles: p, $duplicateFiles: m, $event: f }); var A = a.attrGetter("ngfValidateAfterResize", i, l), F = a.attrGetter("ngfModelOptions", i, l); a.validate(p, w ? h.length : 0, n, i, l).then(function (e) { var t, n, o, s; c ? d(p, [], u, m, $) : (F && F.allowInvalid || A ? y = p : (y = e.validFiles, _ = e.invalidFiles), a.attrGetter("ngfFixOrientation", i, l) && a.isExifSupported() ? (t = y, n = i, o = l, s = [a.emptyPromise()], angular.forEach(t, function (e, r) { 0 === e.type.indexOf("image/jpeg") && a.attrGetter("ngfFixOrientation", n, o, { $file: e }) && s.push(a.happyPromise(a.applyExifRotation(e), e).then(function (e) { t.splice(r, 1, e) })) }), r.all(s)).then(function () { b() }) : b()) }) } }, a }]), ngFileUpload.directive("ngfSelect", ["$parse", "$timeout", "$compile", "Upload", function (e, t, n, r) { var i = []; function a(e, t, n, a, o, l, s, u) { var f = function (e, t) { return u.attrGetter(e, n, t) }; function c() { return "input" === t[0].tagName.toLowerCase() && n.type && "file" === n.type.toLowerCase() } function d() { return f("ngfChange") || f("ngfSelect") } function p(t) { if (u.shouldUpdateOn("change", n, e)) { var r = t.__files_ || t.target && t.target.files, i = []; if (!r) return; for (var o = 0; o < r.length; o++) i.push(r[o]); u.updateModel(a, n, e, d(), i.length ? i : null, t) } } u.registerModelChangeValidator(a, n, e); var h = []; f("ngfMultiple") && h.push(e.$watch(f("ngfMultiple"), function () { m.attr("multiple", f("ngfMultiple", e)) })), f("ngfCapture") && h.push(e.$watch(f("ngfCapture"), function () { m.attr("capture", f("ngfCapture", e)) })), f("ngfAccept") && h.push(e.$watch(f("ngfAccept"), function () { m.attr("accept", f("ngfAccept", e)) })), h.push(n.$observe("accept", function () { m.attr("accept", f("accept")) })); var g = 0, v = 0; var m = t; function _(t) { u.shouldUpdateOn("click", n, e) && m.val() && (m.val(null), u.updateModel(a, n, e, d(), null, t, !0)) } c() || (m = function () { if (c()) return t; var e = angular.element('<input type="file">'), r = angular.element("<label>upload</label>"); return r.css("visibility", "hidden").css("position", "absolute").css("overflow", "hidden").css("width", "0px").css("height", "0px").css("border", "none").css("margin", "0px").css("padding", "0px").attr("tabindex", "-1"), function (e, r) { function i(t) { e.attr("id", "ngf-" + t), r.attr("id", "ngf-label-" + t) } for (var a = 0; a < t[0].attributes.length; a++) { var o = t[0].attributes[a]; "type" !== o.name && "class" !== o.name && "style" !== o.name && ("id" === o.name ? (i(o.value), h.push(n.$observe("id", i))) : e.attr(o.name, o.value || "required" !== o.name && "multiple" !== o.name ? o.value : o.name)) } }(e, r), i.push({ el: t, ref: r }), document.body.appendChild(r.append(e)[0]), e }()), m.bind("change", p), c() ? t.bind("click", _) : t.bind("click touchstart touchend", function (n) { if (t.attr("disabled")) return !1; if (!f("ngfSelectDisabled", e)) { var a = function (e) { var t = e.changedTouches || e.originalEvent && e.originalEvent.changedTouches; if (t) { if ("touchstart" === e.type) return v = t[0].clientX, g = t[0].clientY, !0; if ("touchend" === e.type) { var n = t[0].clientX, r = t[0].clientY; if (Math.abs(n - v) > 20 || Math.abs(r - g) > 20) return e.stopPropagation(), e.preventDefault(), !1 } return !0 } }(n); if (null != a) return a; _(n); try { c() || document.body.contains(m[0]) || (i.push({ el: t, ref: m.parent() }), document.body.appendChild(m.parent()[0]), m.bind("change", p)) } catch (e) { } return function (e) { var t = e.match(/Android[^\d]*(\d+)\.(\d+)/); if (t && t.length > 2) { var n = r.defaults.androidFixMinorVersion || 4; return parseInt(t[1]) < 4 || parseInt(t[1]) === n && parseInt(t[2]) < n } return -1 === e.indexOf("Chrome") && /.*Windows.*Safari.*/.test(e) }(navigator.userAgent) ? setTimeout(function () { m[0].click() }, 0) : m[0].click(), !1 } }), -1 !== navigator.appVersion.indexOf("MSIE 10") && m.bind("click", function e(t) { if (m && !m.attr("__ngf_ie10_Fix_")) { if (!m[0].parentNode) return void (m = null); t.preventDefault(), t.stopPropagation(), m.unbind("click"); var n = m.clone(); return m.replaceWith(n), (m = n).attr("__ngf_ie10_Fix_", "true"), m.bind("change", p), m.bind("click", e), m[0].click(), !1 } m.removeAttr("__ngf_ie10_Fix_") }), a && a.$formatters.push(function (e) { return null != e && 0 !== e.length || m.val() && m.val(null), e }), e.$on("$destroy", function () { c() || m.parent().remove(), angular.forEach(h, function (e) { e() }) }), l(function () { for (var e = 0; e < i.length; e++) { var t = i[e]; document.body.contains(t.el[0]) || (i.splice(e, 1), t.ref.remove()) } }), window.FileAPI && window.FileAPI.ngfFixIE && window.FileAPI.ngfFixIE(t, m, p) } return { restrict: "AEC", require: "?ngModel", link: function (e, n, i, o) { a(e, n, i, o, 0, t, 0, r) } } }]), function () { function e(e, t, n, r, i, a, o, l) { function s(a) { var o = e.attrGetter("ngfNoObjectUrl", i, n); e.dataUrl(a, o).finally(function () { t(function () { var e = (o ? a.$ngfDataUrl : a.$ngfBlobUrl) || a.$ngfDataUrl; l ? r.css("background-image", "url('" + (e || "") + "')") : r.attr("src", e), e ? r.removeClass("ng-hide") : r.addClass("ng-hide") }) }) } t(function () { var t = n.$watch(i[a], function (t) { var u, f = o; if ("ngfThumbnail" === a && (f || (f = { width: r[0].naturalWidth || r[0].clientWidth, height: r[0].naturalHeight || r[0].clientHeight }), 0 === f.width && window.getComputedStyle)) { var c = getComputedStyle(r[0]); c.width && c.width.indexOf("px") > -1 && c.height && c.height.indexOf("px") > -1 && (f = { width: parseInt(c.width.slice(0, -2)), height: parseInt(c.height.slice(0, -2)) }) } if (angular.isString(t)) return r.removeClass("ng-hide"), l ? r.css("background-image", "url('" + t + "')") : r.attr("src", t); !t || !t.type || 0 !== t.type.search("img" === (u = r[0]).tagName.toLowerCase() ? "image" : "audio" === u.tagName.toLowerCase() ? "audio" : "video" === u.tagName.toLowerCase() ? "video" : /./) || l && 0 !== t.type.indexOf("image") ? r.addClass("ng-hide") : f && e.isResizeSupported() ? (f.resizeIf = function (r, a) { return e.attrGetter("ngfResizeIf", i, n, { $width: r, $height: a, $file: t }) }, e.resize(t, f).then(function (e) { s(e) }, function (e) { throw e })) : s(t) }); n.$on("$destroy", function () { t() }) }) } ngFileUpload.service("UploadDataUrl", ["UploadBase", "$timeout", "$q", function (e, t, n) { var r = e; return r.base64DataUrl = function (e) { if (angular.isArray(e)) { var t = n.defer(), i = 0; return angular.forEach(e, function (n) { r.dataUrl(n, !0).finally(function () { if (++i === e.length) { var n = []; angular.forEach(e, function (e) { n.push(e.$ngfDataUrl) }), t.resolve(n, e) } }) }), t.promise } return r.dataUrl(e, !0) }, r.dataUrl = function (e, i) { if (!e) return r.emptyPromise(e, e); if (i && null != e.$ngfDataUrl || !i && null != e.$ngfBlobUrl) return r.emptyPromise(i ? e.$ngfDataUrl : e.$ngfBlobUrl, e); var a = i ? e.$$ngfDataUrlPromise : e.$$ngfBlobUrlPromise; if (a) return a; var o = n.defer(); return t(function () { if (window.FileReader && e && (!window.FileAPI || -1 === navigator.userAgent.indexOf("MSIE 8") || e.size < 2e4) && (!window.FileAPI || -1 === navigator.userAgent.indexOf("MSIE 9") || e.size < 4e6)) { var n = window.URL || window.webkitURL; if (n && n.createObjectURL && !i) { var a; try { a = n.createObjectURL(e) } catch (n) { return void t(function () { e.$ngfBlobUrl = "", o.reject() }) } t(function () { if (e.$ngfBlobUrl = a, a) { o.resolve(a, e), r.blobUrls = r.blobUrls || [], r.blobUrlsTotalSize = r.blobUrlsTotalSize || 0, r.blobUrls.push({ url: a, size: e.size }), r.blobUrlsTotalSize += e.size || 0; for (var t = r.defaults.blobUrlsMaxMemory || 268435456, i = r.defaults.blobUrlsMaxQueueSize || 200; (r.blobUrlsTotalSize > t || r.blobUrls.length > i) && r.blobUrls.length > 1;) { var l = r.blobUrls.splice(0, 1)[0]; n.revokeObjectURL(l.url), r.blobUrlsTotalSize -= l.size } } }) } else { var l = new FileReader; l.onload = function (n) { t(function () { e.$ngfDataUrl = n.target.result, o.resolve(n.target.result, e), t(function () { delete e.$ngfDataUrl }, 1e3) }) }, l.onerror = function () { t(function () { e.$ngfDataUrl = "", o.reject() }) }, l.readAsDataURL(e) } } else t(function () { e[i ? "$ngfDataUrl" : "$ngfBlobUrl"] = "", o.reject() }) }), (a = i ? e.$$ngfDataUrlPromise = o.promise : e.$$ngfBlobUrlPromise = o.promise).finally(function () { delete e[i ? "$$ngfDataUrlPromise" : "$$ngfBlobUrlPromise"] }), a }, r }]), ngFileUpload.directive("ngfSrc", ["Upload", "$timeout", function (t, n) { return { restrict: "AE", link: function (r, i, a) { e(t, n, r, i, a, "ngfSrc", t.attrGetter("ngfResize", a, r), !1) } } }]), ngFileUpload.directive("ngfBackground", ["Upload", "$timeout", function (t, n) { return { restrict: "AE", link: function (r, i, a) { e(t, n, r, i, a, "ngfBackground", t.attrGetter("ngfResize", a, r), !0) } } }]), ngFileUpload.directive("ngfThumbnail", ["Upload", "$timeout", function (t, n) { return { restrict: "AE", link: function (r, i, a) { var o = t.attrGetter("ngfSize", a, r); e(t, n, r, i, a, "ngfThumbnail", o, t.attrGetter("ngfAsBackground", a, r)) } } }]), ngFileUpload.config(["$compileProvider", function (e) { e.imgSrcSanitizationWhitelist && e.imgSrcSanitizationWhitelist(/^\s*(https?|ftp|mailto|tel|webcal|local|file|data|blob):/), e.aHrefSanitizationWhitelist && e.aHrefSanitizationWhitelist(/^\s*(https?|ftp|mailto|tel|webcal|local|file|data|blob):/) }]), ngFileUpload.filter("ngfDataUrl", ["UploadDataUrl", "$sce", function (e, t) { return function (n, r, i) { if (angular.isString(n)) return t.trustAsResourceUrl(n); var a = n && ((r ? n.$ngfDataUrl : n.$ngfBlobUrl) || n.$ngfDataUrl); return n && !a ? (!n.$ngfDataUrlFilterInProgress && angular.isObject(n) && (n.$ngfDataUrlFilterInProgress = !0, e.dataUrl(n, r)), "") : (n && delete n.$ngfDataUrlFilterInProgress, (n && a ? i ? t.trustAsResourceUrl(a) : a : n) || "") } }]) }(), ngFileUpload.service("UploadValidate", ["UploadDataUrl", "$q", "$timeout", function (e, t, n) { var r = e; return r.validatePattern = function (e, t) { if (!t) return !0; var n = function e(t) { var n = "", r = []; if (t.length > 2 && "/" === t[0] && "/" === t[t.length - 1]) n = t.substring(1, t.length - 1); else { var i = t.split(","); if (i.length > 1) for (var a = 0; a < i.length; a++) { var o = e(i[a]); o.regexp ? (n += "(" + o.regexp + ")", a < i.length - 1 && (n += "|")) : r = r.concat(o.excludes) } else 0 === t.indexOf("!") ? r.push("^((?!" + e(t.substring(1)).regexp + ").)*$") : (0 === t.indexOf(".") && (t = "*" + t), n = (n = "^" + t.replace(new RegExp("[.\\\\+*?\\[\\^\\]$(){}=!<>|:\\-]", "g"), "\\$&") + "$").replace(/\\\*/g, ".*").replace(/\\\?/g, ".")) } return { regexp: n, excludes: r } }(t), r = !0; if (n.regexp && n.regexp.length) { var i = new RegExp(n.regexp, "i"); r = null != e.type && i.test(e.type) || null != e.name && i.test(e.name) } for (var a = n.excludes.length; a--;) { var o = new RegExp(n.excludes[a], "i"); r = r && (null == e.type || o.test(e.type)) && (null == e.name || o.test(e.name)) } return r }, r.ratioToFloat = function (e) { var t = e.toString(), n = t.search(/[x:]/i); return t = n > -1 ? parseFloat(t.substring(0, n)) / parseFloat(t.substring(n + 1)) : parseFloat(t) }, r.registerModelChangeValidator = function (e, t, n) { e && e.$formatters.push(function (i) { if (e.$dirty) { var a = i; i && !angular.isArray(i) && (a = [i]), r.validate(a, 0, e, t, n).then(function () { r.applyModelValidation(e, a) }) } return i }) }, r.applyModelValidation = function (e, t) { var n; n = e, null == t || n.$dirty || (n.$setDirty ? n.$setDirty() : n.$dirty = !0), angular.forEach(e.$ngfValidations, function (t) { e.$setValidity(t.name, t.valid) }) }, r.getValidationAttr = function (e, t, n, i, a) { var o = "ngf" + n[0].toUpperCase() + n.substr(1), l = r.attrGetter(o, e, t, { $file: a }); if (null == l && (l = r.attrGetter("ngfValidate", e, t, { $file: a }))) { var s = (i || n).split("."); l = l[s[0]], s.length > 1 && (l = l && l[s[1]]) } return l }, r.validate = function (e, n, i, a, o) { (i = i || {}).$ngfValidations = i.$ngfValidations || [], angular.forEach(i.$ngfValidations, function (e) { e.valid = !0 }); var l = function (e, t) { return r.attrGetter(e, a, o, t) }, s = (r.attrGetter("ngfIgnoreInvalid", a, o) || "").split(" "), u = r.attrGetter("ngfRunAllValidations", a, o); if (null == e || 0 === e.length) return r.emptyPromise({ validFiles: e, invalidFiles: [] }); e = void 0 === e.length ? [e] : e.slice(0); var f = []; function c(t, n, l) { if (e) { for (var c = e.length, d = null; c--;) { var p = e[c]; if (p) { var h = r.getValidationAttr(a, o, t, n, p); null != h && (l(p, h, c) || (-1 === s.indexOf(t) ? (p.$error = t, (p.$errorMessages = p.$errorMessages || {})[t] = !0, p.$errorParam = h, -1 === f.indexOf(p) && f.push(p), u || e.splice(c, 1), d = !1) : e.splice(c, 1))) } } null !== d && i.$ngfValidations.push({ name: t, valid: d }) } } c("pattern", null, r.validatePattern), c("minSize", "size.min", function (e, t) { return e.size + .1 >= r.translateScalars(t) }), c("maxSize", "size.max", function (e, t) { return e.size - .1 <= r.translateScalars(t) }); var d = 0; if (c("maxTotalSize", null, function (t, n) { return !((d += t.size) > r.translateScalars(n)) || (e.splice(0, e.length), !1) }), c("validateFn", null, function (e, t) { return !0 === t || null === t || "" === t }), !e.length) return r.emptyPromise({ validFiles: [], invalidFiles: f }); function p(n, c, d, p, h) { function g(t, r, i) { function a(a) { if (a()) if (-1 === s.indexOf(n)) { if (r.$error = n, (r.$errorMessages = r.$errorMessages || {})[n] = !0, r.$errorParam = i, -1 === f.indexOf(r) && f.push(r), !u) { var o = e.indexOf(r); o > -1 && e.splice(o, 1) } t.resolve(!1) } else { var l = e.indexOf(r); l > -1 && e.splice(l, 1), t.resolve(!0) } else t.resolve(!0) } null != i ? p(r, i).then(function (e) { a(function () { return !h(e, i) }) }, function () { a(function () { return l("ngfValidateForce", { $file: r }) }) }) : t.resolve(!0) } var v = [r.emptyPromise(!0)]; e && (e = void 0 === e.length ? [e] : e, angular.forEach(e, function (e) { var i = t.defer(); v.push(i.promise), !d || null != e.type && 0 === e.type.search(d) ? "dimensions" === n && null != r.attrGetter("ngfDimensions", a) ? r.imageDimensions(e).then(function (t) { g(i, e, l("ngfDimensions", { $file: e, $width: t.width, $height: t.height })) }, function () { i.resolve(!1) }) : "duration" === n && null != r.attrGetter("ngfDuration", a) ? r.mediaDuration(e).then(function (t) { g(i, e, l("ngfDuration", { $file: e, $duration: t })) }, function () { i.resolve(!1) }) : g(i, e, r.getValidationAttr(a, o, n, c, e)) : i.resolve(!0) })); var m = t.defer(); return t.all(v).then(function (e) { for (var t = !0, r = 0; r < e.length; r++) if (!e[r]) { t = !1; break } i.$ngfValidations.push({ name: n, valid: t }), m.resolve(t) }), m.promise } var h = t.defer(), g = []; return g.push(p("maxHeight", "height.max", /image/, this.imageDimensions, function (e, t) { return e.height <= t })), g.push(p("minHeight", "height.min", /image/, this.imageDimensions, function (e, t) { return e.height >= t })), g.push(p("maxWidth", "width.max", /image/, this.imageDimensions, function (e, t) { return e.width <= t })), g.push(p("minWidth", "width.min", /image/, this.imageDimensions, function (e, t) { return e.width >= t })), g.push(p("dimensions", null, /image/, function (e, t) { return r.emptyPromise(t) }, function (e) { return e })), g.push(p("ratio", null, /image/, this.imageDimensions, function (e, t) { for (var n = t.toString().split(","), i = !1, a = 0; a < n.length; a++) Math.abs(e.width / e.height - r.ratioToFloat(n[a])) < .01 && (i = !0); return i })), g.push(p("maxRatio", "ratio.max", /image/, this.imageDimensions, function (e, t) { return e.width / e.height - r.ratioToFloat(t) < 1e-4 })), g.push(p("minRatio", "ratio.min", /image/, this.imageDimensions, function (e, t) { return e.width / e.height - r.ratioToFloat(t) > -1e-4 })), g.push(p("maxDuration", "duration.max", /audio|video/, this.mediaDuration, function (e, t) { return e <= r.translateScalars(t) })), g.push(p("minDuration", "duration.min", /audio|video/, this.mediaDuration, function (e, t) { return e >= r.translateScalars(t) })), g.push(p("duration", null, /audio|video/, function (e, t) { return r.emptyPromise(t) }, function (e) { return e })), g.push(p("validateAsyncFn", null, null, function (e, t) { return t }, function (e) { return !0 === e || null === e || "" === e })), t.all(g).then(function () { if (u) for (var t = 0; t < e.length; t++) { e[t].$error && e.splice(t--, 1) } u = !1, c("maxFiles", null, function (e, t, r) { return n + r < t }), h.resolve({ validFiles: e, invalidFiles: f }) }), h.promise }, r.imageDimensions = function (e) { if (e.$ngfWidth && e.$ngfHeight) { var i = t.defer(); return n(function () { i.resolve({ width: e.$ngfWidth, height: e.$ngfHeight }) }), i.promise } if (e.$ngfDimensionPromise) return e.$ngfDimensionPromise; var a = t.defer(); return n(function () { 0 === e.type.indexOf("image") ? r.dataUrl(e).then(function (t) { var r = angular.element("<img>").attr("src", t).css("visibility", "hidden").css("position", "fixed").css("max-width", "none !important").css("max-height", "none !important"); function i() { var t = r[0].naturalWidth || r[0].clientWidth, n = r[0].naturalHeight || r[0].clientHeight; r.remove(), e.$ngfWidth = t, e.$ngfHeight = n, a.resolve({ width: t, height: n }) } function o() { r.remove(), a.reject("load error") } r.on("load", i), r.on("error", o); var l = 0; !function e() { n(function () { r[0].parentNode && (r[0].clientWidth ? i() : l++ > 10 ? o() : e()) }, 1e3) }(), angular.element(document.getElementsByTagName("body")[0]).append(r) }, function () { a.reject("load error") }) : a.reject("not image") }), e.$ngfDimensionPromise = a.promise, e.$ngfDimensionPromise.finally(function () { delete e.$ngfDimensionPromise }), e.$ngfDimensionPromise }, r.mediaDuration = function (e) { if (e.$ngfDuration) { var i = t.defer(); return n(function () { i.resolve(e.$ngfDuration) }), i.promise } if (e.$ngfDurationPromise) return e.$ngfDurationPromise; var a = t.defer(); return n(function () { 0 === e.type.indexOf("audio") || 0 === e.type.indexOf("video") ? r.dataUrl(e).then(function (t) { var r = angular.element(0 === e.type.indexOf("audio") ? "<audio>" : "<video>").attr("src", t).css("visibility", "none").css("position", "fixed"); function i() { var t = r[0].duration; e.$ngfDuration = t, r.remove(), a.resolve(t) } function o() { r.remove(), a.reject("load error") } r.on("loadedmetadata", i), r.on("error", o); var l = 0; !function e() { n(function () { r[0].parentNode && (r[0].duration ? i() : l > 10 ? o() : e()) }, 1e3) }(), angular.element(document.body).append(r) }, function () { a.reject("load error") }) : a.reject("not media") }), e.$ngfDurationPromise = a.promise, e.$ngfDurationPromise.finally(function () { delete e.$ngfDurationPromise }), e.$ngfDurationPromise }, r }]), ngFileUpload.service("UploadResize", ["UploadValidate", "$q", function (e, t) { var n = e, r = function (e, r, i, a, o, l, s, u) { var f = t.defer(), c = document.createElement("canvas"), d = document.createElement("img"); return d.setAttribute("style", "visibility:hidden;position:fixed;z-index:-100000"), document.body.appendChild(d), d.onload = function () { var e, t, p, h, g, v = d.width, m = d.height; if (d.parentNode.removeChild(d), null == u || !1 !== u(v, m)) try { if (l) { var _ = n.ratioToFloat(l); v / m < _ ? i = (r = v) / _ : r = (i = m) * _ } r || (r = v), i || (i = m); var y = (e = v, t = m, p = r, h = i, g = s ? Math.max(p / e, h / t) : Math.min(p / e, h / t), { width: e * g, height: t * g, marginX: e * g - p, marginY: t * g - h }); c.width = Math.min(y.width, r), c.height = Math.min(y.height, i), c.getContext("2d").drawImage(d, Math.min(0, -y.marginX / 2), Math.min(0, -y.marginY / 2), y.width, y.height), f.resolve(c.toDataURL(o || "image/WebP", a || .934)) } catch (e) { f.reject(e) } else f.reject("resizeIf") }, d.onerror = function () { d.parentNode.removeChild(d), f.reject() }, d.src = e, f.promise }; return n.dataUrltoBlob = function (e, t, n) { for (var r = e.split(","), i = r[0].match(/:(.*?);/)[1], a = atob(r[1]), o = a.length, l = new Uint8Array(o) ; o--;) l[o] = a.charCodeAt(o); var s = new window.Blob([l], { type: i }); return s.name = t, s.$ngfOrigSize = n, s }, n.isResizeSupported = function () { var e = document.createElement("canvas"); return window.atob && e.getContext && e.getContext("2d") && window.Blob }, n.isResizeSupported() && Object.defineProperty(window.Blob.prototype, "name", { get: function () { return this.$ngfName }, set: function (e) { this.$ngfName = e }, configurable: !0 }), n.resize = function (e, i) { if (0 !== e.type.indexOf("image")) return n.emptyPromise(e); var a = t.defer(); return n.dataUrl(e, !0).then(function (t) { r(t, i.width, i.height, i.quality, i.type || e.type, i.ratio, i.centerCrop, i.resizeIf).then(function (r) { if ("image/jpeg" === e.type && !1 !== i.restoreExif) try { r = n.restoreExif(t, r) } catch (e) { setTimeout(function () { throw e }, 1) } try { var o = n.dataUrltoBlob(r, e.name, e.size); a.resolve(o) } catch (e) { a.reject(e) } }, function (t) { "resizeIf" === t && a.resolve(e), a.reject(t) }) }, function (e) { a.reject(e) }), a.promise }, n }]), function () { function e() { var e = document.createElement("div"); return "draggable" in e && "ondrop" in e && !/Edge\/12./i.test(navigator.userAgent) } ngFileUpload.directive("ngfDrop", ["$parse", "$timeout", "$window", "Upload", "$http", "$q", function (t, n, r, i, a, o) { return { restrict: "AEC", require: "?ngModel", link: function (a, l, s, u) { !function (t, n, r, i, a, o, l, s, u, f) { var c = e(), d = function (e, t, n) { return s.attrGetter(e, r, t, n) }; d("dropAvailable") && o(function () { t[d("dropAvailable")] ? t[d("dropAvailable")].value = c : t[d("dropAvailable")] = c }); if (!c) return void (!0 === d("ngfHideOnDropNotAvailable", t) && n.css("display", "none")); function p() { return n.attr("disabled") || d("ngfDropDisabled", t) } null == d("ngfSelect") && s.registerModelChangeValidator(i, r, t); var h, g = null, v = a(d("ngfStopPropagation")), m = 1; n[0].addEventListener("dragover", function (e) { if (!p() && s.shouldUpdateOn("drop", r, t)) { if (e.preventDefault(), v(t) && e.stopPropagation(), navigator.userAgent.indexOf("Chrome") > -1) { var i = e.dataTransfer.effectAllowed; e.dataTransfer.dropEffect = "move" === i || "linkMove" === i ? "move" : "copy" } o.cancel(g), h || (h = "C", function (e, t, n, r) { var i = d("ngfDragOverClass", e, { $event: n }), a = "dragover"; if (angular.isString(i)) a = i; else if (i && (i.delay && (m = i.delay), i.accept || i.reject)) { var o = n.dataTransfer.items; if (null != o && o.length) for (var l = i.pattern || d("ngfPattern", e, { $event: n }), u = o.length; u--;) { if (!s.validatePattern(o[u], l)) { a = i.reject; break } a = i.accept } else a = i.accept } r(a) }(t, 0, e, function (r) { h = r, n.addClass(h), d("ngfDrag", t, { $isDragging: !0, $class: h, $event: e }) })) } }, !1), n[0].addEventListener("dragenter", function (e) { !p() && s.shouldUpdateOn("drop", r, t) && (e.preventDefault(), v(t) && e.stopPropagation()) }, !1), n[0].addEventListener("dragleave", function (e) { !p() && s.shouldUpdateOn("drop", r, t) && (e.preventDefault(), v(t) && e.stopPropagation(), g = o(function () { h && n.removeClass(h), h = null, d("ngfDrag", t, { $isDragging: !1, $event: e }) }, m || 100)) }, !1), n[0].addEventListener("drop", function (e) { !p() && s.shouldUpdateOn("drop", r, t) && (e.preventDefault(), v(t) && e.stopPropagation(), h && n.removeClass(h), h = null, _(e.dataTransfer, e, "dropUrl")) }, !1), n[0].addEventListener("paste", function (e) { navigator.userAgent.toLowerCase().indexOf("firefox") > -1 && d("ngfEnableFirefoxPaste", t) && e.preventDefault(), !p() && s.shouldUpdateOn("paste", r, t) && _(e.clipboardData || e.originalEvent.clipboardData, e, "pasteUrl") }, !1), navigator.userAgent.toLowerCase().indexOf("firefox") > -1 && d("ngfEnableFirefoxPaste", t) && (n.attr("contenteditable", !0), n.on("keypress", function (e) { e.metaKey || e.ctrlKey || e.preventDefault() })); function _(e, n, i) { if (e) { var a; try { a = e && e.getData && e.getData("text/html") } catch (e) { } (function (e, n, i, a) { var o = s.getValidationAttr(r, t, "maxFiles"); null == o && (o = Number.MAX_VALUE); var u = s.getValidationAttr(r, t, "maxTotalSize"); null == u && (u = Number.MAX_VALUE); var c = d("ngfIncludeDir", t), p = [], h = 0; function g(e, t) { var n = f.defer(); if (null != e) if (e.isDirectory) { var r = [s.emptyPromise()]; if (c) { var i = { type: "directory" }; i.name = i.path = (t || "") + e.name, p.push(i) } var a = e.createReader(), l = [], d = function () { a.readEntries(function (i) { try { i.length ? (l = l.concat(Array.prototype.slice.call(i || [], 0)), d()) : (angular.forEach(l.slice(0), function (n) { p.length <= o && h <= u && r.push(g(n, (t || "") + e.name + "/")) }), f.all(r).then(function () { n.resolve() }, function (e) { n.reject(e) })) } catch (e) { n.reject(e) } }, function (e) { n.reject(e) }) }; d() } else e.file(function (e) { try { e.path = (t || "") + e.name, c && (e = s.rename(e, e.path)), p.push(e), h += e.size, n.resolve() } catch (e) { n.reject(e) } }, function (e) { n.reject(e) }); return n.promise } var v = [s.emptyPromise()]; if (e && e.length > 0 && "file:" !== l.location.protocol) for (var m = 0; m < e.length; m++) { if (e[m].webkitGetAsEntry && e[m].webkitGetAsEntry() && e[m].webkitGetAsEntry().isDirectory) { var _ = e[m].webkitGetAsEntry(); if (_.isDirectory && !i) continue; null != _ && v.push(g(_)) } else { var y = e[m].getAsFile(); null != y && (p.push(y), h += y.size) } if (p.length > o || h > u || !a && p.length > 0) break } else if (null != n) for (var b = 0; b < n.length; b++) { var w = n.item(b); if ((w.type || w.size > 0) && (p.push(w), h += w.size), p.length > o || h > u || !a && p.length > 0) break } var $ = f.defer(); return f.all(v).then(function () { if (a || c || !p.length) $.resolve(p); else { for (var e = 0; p[e] && "directory" === p[e].type;) e++; $.resolve([p[e]]) } }, function (e) { $.reject(e) }), $.promise })(e.items, e.files, !1 !== d("ngfAllowDir", t), d("multiple") || d("ngfMultiple", t)).then(function (e) { e.length ? y(e, n) : function (e, n) { if (!s.shouldUpdateOn(e, r, t) || "string" != typeof n) return s.rejectPromise([]); var i = []; n.replace(/<(img src|img [^>]* src) *=\"([^\"]*)\"/gi, function (e, t, n) { i.push(n) }); var a = [], o = []; if (i.length) { angular.forEach(i, function (e) { a.push(s.urlToBlob(e).then(function (e) { o.push(e) })) }); var l = f.defer(); return f.all(a).then(function () { l.resolve(o) }, function (e) { l.reject(e) }), l.promise } return s.emptyPromise() }(i, a).then(function (e) { y(e, n) }) }) } } function y(e, n) { s.updateModel(i, r, t, d("ngfChange") || d("ngfDrop"), e, n) } }(a, l, s, u, t, n, r, i, 0, o) } } }]), ngFileUpload.directive("ngfNoFileDrop", function () { return function (t, n) { e() && n.css("display", "none") } }), ngFileUpload.directive("ngfDropAvailable", ["$parse", "$timeout", "Upload", function (t, n, r) { return function (i, a, o) { if (e()) { var l = t(r.attrGetter("ngfDropAvailable", o)); n(function () { l(i), l.assign && l.assign(i, !0) }) } } }]) }(), ngFileUpload.service("UploadExif", ["UploadResize", "$q", function (e, t) { var n = e; return n.isExifSupported = function () { return window.FileReader && (new FileReader).readAsArrayBuffer && n.isResizeSupported() }, n.readOrientation = function (e) { var n = t.defer(), r = new FileReader, i = e.slice ? e.slice(0, 65536) : e; return r.readAsArrayBuffer(i), r.onerror = function (e) { return n.reject(e) }, r.onload = function (e) { var t = { orientation: 1 }, r = new DataView(this.result); if (65496 !== r.getUint16(0, !1)) return n.resolve(t); for (var i = r.byteLength, a = 2; a < i;) { var o = r.getUint16(a, !1); if (a += 2, 65505 === o) { if (1165519206 !== r.getUint32(a += 2, !1)) return n.resolve(t); var l = 18761 === r.getUint16(a += 6, !1); a += r.getUint32(a + 4, l); var s = r.getUint16(a, l); a += 2; for (var u = 0; u < s; u++) if (274 === r.getUint16(a + 12 * u, l)) { var f = r.getUint16(a + 12 * u + 8, l); return f >= 2 && f <= 8 && (r.setUint16(a + 12 * u + 8, 1, l), t.fixedArrayBuffer = e.target.result), t.orientation = f, n.resolve(t) } } else { if (65280 != (65280 & o)) break; a += r.getUint16(a, !1) } } return n.resolve(t) }, n.promise }, n.applyExifRotation = function (e) { if (0 !== e.type.indexOf("image/jpeg")) return n.emptyPromise(e); var r = t.defer(); return n.readOrientation(e).then(function (t) { if (t.orientation < 2 || t.orientation > 8) return r.resolve(e); n.dataUrl(e, !0).then(function (i) { var a = document.createElement("canvas"), o = document.createElement("img"); o.onload = function () { try { a.width = t.orientation > 4 ? o.height : o.width, a.height = t.orientation > 4 ? o.width : o.height; var i = a.getContext("2d"); !function (e, t, n, r) { switch (t) { case 2: return e.transform(-1, 0, 0, 1, n, 0); case 3: return e.transform(-1, 0, 0, -1, n, r); case 4: return e.transform(1, 0, 0, -1, 0, r); case 5: return e.transform(0, 1, 1, 0, 0, 0); case 6: return e.transform(0, 1, -1, 0, r, 0); case 7: return e.transform(0, -1, -1, 0, r, n); case 8: e.transform(0, -1, 1, 0, 0, n) } }(i, t.orientation, o.width, o.height), i.drawImage(o, 0, 0); var l = a.toDataURL(e.type || "image/WebP", .934); l = n.restoreExif(function (e) { for (var t = "", n = new Uint8Array(e), r = n.byteLength, i = 0; i < r; i++) t += String.fromCharCode(n[i]); return window.btoa(t) }(t.fixedArrayBuffer), l); var s = n.dataUrltoBlob(l, e.name); r.resolve(s) } catch (e) { return r.reject(e) } }, o.onerror = function () { r.reject() }, o.src = i }, function (e) { r.reject(e) }) }, function (e) { r.reject(e) }), r.promise }, n.restoreExif = function (e, t) { var n = { KEY_STR: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", encode64: function (e) { var t, n, r, i, a, o = "", l = "", s = "", u = 0; do { r = (t = e[u++]) >> 2, i = (3 & t) << 4 | (n = e[u++]) >> 4, a = (15 & n) << 2 | (l = e[u++]) >> 6, s = 63 & l, isNaN(n) ? a = s = 64 : isNaN(l) && (s = 64), o = o + this.KEY_STR.charAt(r) + this.KEY_STR.charAt(i) + this.KEY_STR.charAt(a) + this.KEY_STR.charAt(s), t = n = l = "", r = i = a = s = "" } while (u < e.length); return o }, restore: function (e, t) { e.match("data:image/jpeg;base64,") && (e = e.replace("data:image/jpeg;base64,", "")); var n = this.decode64(e), r = this.slice2Segments(n), i = this.exifManipulation(t, r); return "data:image/jpeg;base64," + this.encode64(i) }, exifManipulation: function (e, t) { var n = this.getExifArray(t), r = this.insertExif(e, n); return new Uint8Array(r) }, getExifArray: function (e) { for (var t, n = 0; n < e.length; n++) if (255 === (t = e[n])[0] & 225 === t[1]) return t; return [] }, insertExif: function (e, t) { var n = e.replace("data:image/jpeg;base64,", ""), r = this.decode64(n), i = r.indexOf(255, 3), a = r.slice(0, i), o = r.slice(i), l = a; return l = (l = l.concat(t)).concat(o) }, slice2Segments: function (e) { for (var t = 0, n = []; !(255 === e[t] & 218 === e[t + 1]) ;) { if (255 === e[t] & 216 === e[t + 1]) t += 2; else { var r = t + (256 * e[t + 2] + e[t + 3]) + 2, i = e.slice(t, r); n.push(i), t = r } if (t > e.length) break } return n }, decode64: function (e) { var t, n, r, i, a = "", o = "", l = 0, s = []; /[^A-Za-z0-9\+\/\=]/g.exec(e) && console.log("There were invalid base64 characters in the input text.\nValid base64 characters are A-Z, a-z, 0-9, NaNExpect errors in decoding."), e = e.replace(/[^A-Za-z0-9\+\/\=]/g, ""); do { t = this.KEY_STR.indexOf(e.charAt(l++)) << 2 | (r = this.KEY_STR.indexOf(e.charAt(l++))) >> 4, n = (15 & r) << 4 | (i = this.KEY_STR.indexOf(e.charAt(l++))) >> 2, a = (3 & i) << 6 | (o = this.KEY_STR.indexOf(e.charAt(l++))), s.push(t), 64 !== i && s.push(n), 64 !== o && s.push(a), t = n = a = "", r = i = o = "" } while (l < e.length); return s } }; return n.restore(e, t) }, n }]);