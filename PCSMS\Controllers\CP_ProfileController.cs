﻿using System.Web.Http;
using PCSMS.Filters;
using PCSMS.Models.Models_Company;
using PCSMS.Services.Services_Company;

namespace PCSMS.Controllers
{
    //NOTE: enabling below will cause problem in signing up
    //[AuthorizationRequired] 
    [RoutePrefix("Api/CP_Profile")]
    public class CP_ProfileController : ApiController
    {
        private readonly ICP_ProfileServices _services;
        public CP_ProfileController()
        {
            _services = new CP_ProfileServices();
        }

        [Route("SignUp")]
        [HttpPost]
        public IHttpActionResult SignUp(CP_Profile CP_Profile)
        {
            return Ok(_services.SignUp(CP_Profile).Data);
        }

        [Route("ChangeCompanyEmailForLogin/{companyId:int}/{newEmail}")]
        [HttpPost]
        public IHttpActionResult ChangeCompanyEmailForLogin(int companyId, string newEmail)
        {
            return Ok(_services.ChangeCompanyEmailForLogin(companyId, newEmail).Data);
        }
        

        [Route("ChangeCompanyPasswordForLogin/{companyId:int}/{extPassword}/{newPassword}")]
        [HttpPost]
        public IHttpActionResult ChangeCompanyPasswordForLogin(int companyId, string extPassword, string newPassword)
        {
            return Ok(_services.ChangeCompanyPasswordForLogin(companyId, extPassword, newPassword).Data);
        }


        [Route("UpdateCompanyInfo")]
        [HttpPost]
        public IHttpActionResult UpdateCompanyInfo(CP_Profile CP_Profile)
        {
            return Ok(_services.UpdateCompanyInfo(CP_Profile).Data);
        }

        [Route("UpdateContactPersonInfo")]
        [HttpPost]
        public IHttpActionResult UpdateContactPersonInfo(CP_Profile CP_Profile)
        {
            return Ok(_services.UpdateContactPersonInfo(CP_Profile).Data);
        }

        [Route("GetCompanyProfileDetails/{id:int}")]
        [HttpGet]
        public IHttpActionResult GetCompanyProfileDetails(int id)
        {
            return Ok(_services.GetCompanyProfileDetails(x => x.Id == id).Data);
        }

        [Route("UploadCP_Logo/{profileId:int}")]
        [HttpPost]
        public IHttpActionResult UploadCP_Logo(int profileId)
        {
            return Ok(_services.UploadCP_Logo(profileId).Data);
        }

        [Route("GetCompanyListWhoHasScreenshots")]
        [HttpGet]
        public IHttpActionResult GetCompanyListWhoHasScreenshots()
        {
            return Ok(_services.GetCompanyListWhoHasScreenshots().Data);
        }

        [Route("GetCompanyDetailsForPayload/{profileId:int}")]
        [HttpPost]
        public IHttpActionResult GetCompanyDetailsForPayload(int profileId)
        {
            return Ok(_services.GetCompanyDetailsForPayload(profileId).Data);
        }

        [Route("GetCompanyDetailsForCookies/{profileId:int}")]
        [HttpPost]
        public IHttpActionResult GetCompanyDetailsForCookies(int profileId)
        {
            return Ok(_services.GetCompanyDetailsForCookies(profileId).Data);
        }


        

    }
}
