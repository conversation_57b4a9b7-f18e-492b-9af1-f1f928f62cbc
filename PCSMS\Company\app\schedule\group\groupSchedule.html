﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!-- CONTENT -->
<!-- ========================================================= -->
<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-calendar-plus-o" aria-hidden="true"></i><a ui-sref="device">Group Schedule Settings</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-content">
                        <div class="panel-header">
                            <button type="button" class="btn btn-default btn-o pull-right btn-addon btn-o-addon" data-toggle="modal" data-target="#ScheduleModal">
                                <i class="fa fa-calendar-times-o" aria-hidden="true"></i>
                                Set Schedule For New Group
                            </button>
                        </div>
                        <div class="panel-content">
                            <div class="table-responsive">
                                <!-- DataTable -->
                                <table class="data-table table table-striped nowrap table-bordered table-condensed" id="basic-table" datatable="ng" dt-options="vm.dtOptions" dt-instance="vm.dtInstance" dt-column-defs="vm.dtColumnDefs" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Group Name</th>
                                            <th>Created On</th>
                                            <th>Updated On</th>
                                            <th>Member Devices</th>
                                            <th class="custom-datatabel-action-th">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="x in ListOfGroups">
                                            <td>{{x.GroupName}}</td>
                                            <td>{{ x.CreatedOn}}</td>
                                            <td>{{ x.UpdatedOn}}</td>
                                            <td>{{x.MemberDevices}}</td>
                                            <td>
                                                <button style="border: 1px solid #02715a; background-color: #ffffff" data-toggle="modal" data-target="#ScheduleEditModal"
                                                        type="button" tooltip-placement="top"
                                                        uib-tooltip="View Details" class="btn btn-default"
                                                        ng-click="GetGroupDetails(x.Id);">
                                                    <img src="Assets_Company/images/datatables/edit.png" width="20" />
                                                </button>
                                                <button style="border: 1px solid #02715a; background-color: #ffffff" "
                                                        type="button" tooltip-placement="top"
                                                        uib-tooltip="View device(s)"
                                                        class="btn btn-default"
                                                        ng-click="DeleteThisGroup(x.Id);">
                                                    <img src="Assets_Company/images/datatables/001-delete.png" width="20">
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>
<!--Schedule Create Modal-->
<div class="modal fade" id="ScheduleModal" data-backdrop="static" data-keyboard="false">
    <div class="{{Step==1?'modal-dialog modal-package':'modal-dialog  modal-lg'}}" style="transition:all 0.5s cubic-bezier(0.44, -0.35, 0.54, 1.35) !important">
        <div class="modal-content">
            <div class="modal-header" style="height:65px">
                <button type="button" class="custom-close" ng-click="cancelScheduleModal()" style="margin-top:0px;" aria-label="Close" tooltip-placement="left" uib-tooltip="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" style="margin-top:1.6%">Group Schedule Settings</h4>
            </div>
            <div class="modal-body">
                <div class="form-wizard wizard-block wizard-icons" style="padding-left:11px; padding-right:11px">
                    <div id="wizard-5" class="row row-modal">
                        <div class="tab-steps">
                            <ul class="steps">
                                <li ng-class="{'active':Step==1}">
                                    <a id="tab1" ng-click="SwitchTab1()">
                                        <span class="tab-icon"> <i class="fa fa-users"></i></span>
                                        <span class="tab-text">Create Group</span>
                                    </a>
                                </li>
                                <li ng-class="{'active':Step==2}">
                                    <a id="tab2" ng-click="SwitchTab2()">
                                        <span class="tab-icon"><i class="fa fa-calendar-check-o"></i></span>
                                        <span class="tab-text">Set Schedule For Group</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="tab-content" style="margin-bottom:0px; padding-bottom:0px">
                            <!--TAB PANE 1 : Group Creation-->
                            <div class="tab-pane" ng-class="{'active':Step==1}" id="w5-tab1">
                                <div class="col-md-6">
                                    <div class="panel panel-primary panel-bordered">
                                        <div class="panel-header">
                                            <h3 class="panel-title">CHOOSE LICENSE</h3>
                                        </div>
                                        <div class="panel-body">
                                            <!--License List-->
                                            <div class="table-responsive">
                                                <div style="overflow-y:auto">
                                                    <table class="table table-bordered" ng-if="LicenseList.length>0">
                                                        <thead>
                                                            <tr>
                                                                <th>License Code</th>
                                                                <th>Type</th>
                                                                <th>License Key</th>
                                                                <th>Status</th>
                                                                <th>Reg. Devices</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr ng-repeat="x in LicenseList">
                                                                <td>{{x.LCode}}</td>
                                                                <td>{{x.LicenseType}}</td>
                                                                <td>{{x.LicenseKey}}</td>
                                                                <td><span class="{{x.Status=='Requested'?'badge x-warning ':x.Status=='Active'?'badge x-success ':x.Status=='In-Active'?'badge ':x.Status=='Expired'?'badge x-danger ':'badge '}}">{{x.Status}}</span></td>
                                                                <td>
                                                                    <button style="background-color: #ffffff; margin-top: -7px;"
                                                                            type="button" tooltip-placement="top"
                                                                            uib-tooltip="View device(s)"
                                                                            class="btn btn-default"
                                                                            ng-if="x.Status=='Active'"
                                                                            ng-click="SeeDeviceList(x.Id);">
                                                                        <img src="Assets_Company/images/datatables/desktop (1).png" width="30">
                                                                        <img src="Assets_Company/images/datatables/desktop (1).png" width="30">
                                                                    </button>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div class="panel-content">
                                                    <span class="text-danger" ng-if="LicenseList.length==null">No License has been issued yet.</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="panel panel-primary panel-bordered">
                                        <div class="panel-header">
                                            <h3 class="panel-title">CHOOSE GROUP-NAME AND SELECT DEVICES FOR GROUPING</h3>
                                        </div>
                                        <div class="panel-body">
                                            <!--Group Name-->
                                            <form novalidate name="GroupForm">
                                                <div class="form-group-sm">
                                                    <label for="GroupName" class="control-label">Group Name<span class="required">*</span></label>
                                                    <input type="text" class="form-control" name="GroupName" placeholder="Enter Group Name"
                                                           ng-model="Schedule.GroupName"
                                                           ng-minlength="4"
                                                           ng-maxlength="50"
                                                           ng-pattern="/^[A-Z][a-zA-Z0-9\-_ ]*$/"
                                                           ng-required="true"
                                                           ng-disabled="Loading==true"/>
                                                    <div class="row custom-row">
                                                        <ul class="list-unstyled errormessage pull-left" ng-show="GroupForm.GroupName.$dirty && GroupForm.GroupName.$invalid">
                                                            <li><span class="pull-left" ng-show="GroupForm.GroupName.$error.required">*Required</span></li>
                                                            <li><span class="pull-left" ng-show="GroupForm.GroupName.$error.pattern">Invalid Name : Start with capital letter, don't use dot / special character.</span></li>
                                                            <li><span class="pull-left" ng-show="!GroupForm.GroupName.$error.pattern && EditGroupForm.EditGroupName.$error.minlength">Minimum length is 4</span></li>
                                                            <li><span class="pull-left" ng-show="!GroupForm.GroupName.$error.pattern && EditGroupForm.EditGroupName.$error.maxlength">Maximum length is 50</span></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </form>
                                            <br />
                                            <!--Device List-->
                                            <div class="table-responsive" ng-if="DeviceListByLicense.length>0">
                                                <div style="overflow-y: auto">
                                                    <table class="table table-bordered" ng-if="LicenseList.length>0">
                                                        <thead>
                                                            <tr>
                                                                <th>Device Name</th>
                                                                <th>Device Id</th>
                                                                <th ng-if="GroupNameAvailable == true">Group Name</th>
                                                                <th>Settings</th>
                                                                <th>Action</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr ng-repeat="x in DeviceListByLicense">
                                                                <td><img src="Assets_Company/images/datatables/design.png" width="20"> &nbsp; {{x.DeviceName}}</td>
                                                                <td>{{x.DeviceUniqueId}}</td>
                                                                <td ng-if="GroupNameAvailable == true">{{x.GroupName}}</td>
                                                                <td>{{x.Settings}}</td>
                                                                <td ng-if="x.Settings=='Default settings applied' || x.Settings=='Individual settings applied'">
                                                                    <div class="checkbox-custom checkbox-success">
                                                                        <input type="checkbox" name="{{x.Id}}SelectDevice" id="{{x.Id}}SelectDevice"
                                                                               ng-click="PushToSelectedDeviceArray(x.Id, x.DeviceName, x.DeviceUniqueId, $event)">
                                                                        <label class="check" for="{{x.Id}}SelectDevice" style="padding: 5px"></label>
                                                                    </div>
                                                                </td>
                                                                <td ng-if="x.Settings=='Group settings applied'"><span>N/A</span></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--TAB PANE 2 : Setting Group Schedule-->
                            <div class="tab-pane" id="w5-tab2" ng-class="{'active':Step==2}">
                                <div class="panel panel-primary panel-bordered">
                                    <div class="panel-body">
                                        <form name="GroupScheduleForm" class="form-horizontal" novalidate>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <!--Mandatory Fields Related to Capturing (01)-->
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            <div class="panel panel-primary panel-bordered">
                                                                <div class="panel-body">
                                                                    <div class="row">
                                                                        <!--CapturingStartTime-->
                                                                        <div class="col-lg-3">
                                                                            <div class="form-group-sm">
                                                                                <label class="control-label">Capture Start Time<span class="required">*</span></label>
                                                                                <div uib-timepicker="" ng-model="Schedule.StartTime" max="Schedule.EndTime" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ng-required="true" class="ng-pristine ng-untouched ng-valid ng-isolate-scope ng-not-empty ng-valid-time ng-valid-required" required="required">
                                                                                    <table class="uib-timepicker">
                                                                                        <tbody>
                                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                                <td class="uib-increment hours"><button class="btn-link" ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}" ng-disabled="noIncrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td>&nbsp;</td>
                                                                                                <td class="uib-increment minutes"><button class="btn-link" ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}" ng-disabled="noIncrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                                <td ng-show="showSeconds" class="uib-increment seconds ng-hide"><button class="btn-link" ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}" ng-disabled="noIncrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td ng-show="showMeridian" class=""></td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td class="form-group uib-time hours" ng-class="{'has-error': invalidHours}">
                                                                                                    <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-hours ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementHours()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td class="uib-separator" style="font-weight:bold">:</td>
                                                                                                <td class="form-group uib-time minutes" ng-class="{'has-error': invalidMinutes}">
                                                                                                    <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-minutes ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementMinutes()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td ng-show="showSeconds" class="uib-separator ng-hide" style="font-weight:bold">:</td>
                                                                                                <td class="form-group uib-time seconds ng-hide" ng-class="{'has-error': invalidSeconds}" ng-show="showSeconds">
                                                                                                    <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-seconds ng-not-empty ng-valid-maxlength" ng-readonly="readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementSeconds()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td ng-show="showMeridian" class="uib-time am-pm"><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-date text-center ng-binding disabled" style="height:30px; padding-top:4px; padding-bottom:4px" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="0" disabled="disabled">AM</button></td>
                                                                                            </tr>
                                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                                <td class="uib-decrement hours" style="padding-top:3px"><button class="btn-link" ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}" ng-disabled="noDecrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td>&nbsp;</td>
                                                                                                <td class="uib-decrement minutes" style="padding-top:3px"><button class="btn-link" ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}" ng-disabled="noDecrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                                <td ng-show="showSeconds" class="uib-decrement seconds ng-hide" style="padding-top:3px"><button class="btn-link" ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}" ng-disabled="noDecrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td ng-show="showMeridian" class=""></td>
                                                                                            </tr>
                                                                                        </tbody>
                                                                                    </table>
                                                                                </div>
                                                                            </div>
                                                                            <br>
                                                                        </div>
                                                                        <!--CapturingEndTime-->
                                                                        <div class="col-lg-3 separator">
                                                                            <div class="form-group-sm">
                                                                                <label class="control-label">Capture End Time<span class="required">*</span></label>
                                                                                <div uib-timepicker="" ng-model="Schedule.EndTime" min="Schedule.StartTime" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ng-required="true" class="ng-pristine ng-untouched ng-valid ng-isolate-scope ng-not-empty ng-valid-time ng-valid-required" required="required">
                                                                                    <table class="uib-timepicker">
                                                                                        <tbody>
                                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                                <td class="uib-increment hours"><button class="btn-link" ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}" ng-disabled="noIncrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td>&nbsp;</td>
                                                                                                <td class="uib-increment minutes"><button class="btn-link" ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}" ng-disabled="noIncrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                                <td ng-show="showSeconds" class="uib-increment seconds ng-hide"><button class="btn-link" ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}" ng-disabled="noIncrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td ng-show="showMeridian" class=""></td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td class="form-group uib-time hours" ng-class="{'has-error': invalidHours}">
                                                                                                    <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-hours ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementHours()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td class="uib-separator" style="font-weight:bold">:</td>
                                                                                                <td class="form-group uib-time minutes" ng-class="{'has-error': invalidMinutes}">
                                                                                                    <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-minutes ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementMinutes()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td ng-show="showSeconds" class="uib-separator ng-hide" style="font-weight:bold">:</td>
                                                                                                <td class="form-group uib-time seconds ng-hide" ng-class="{'has-error': invalidSeconds}" ng-show="showSeconds">
                                                                                                    <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-seconds ng-not-empty ng-valid-maxlength" ng-readonly="readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementSeconds()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td ng-show="showMeridian" class="uib-time am-pm"><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-date text-center ng-binding disabled" style="height:30px; padding-top:4px; padding-bottom:4px" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="0" disabled="disabled">PM</button></td>
                                                                                            </tr>
                                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                                <td class="uib-decrement hours" style="padding-top:3px"><button class="btn-link" ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}" ng-disabled="noDecrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td>&nbsp;</td>
                                                                                                <td class="uib-decrement minutes" style="padding-top:3px"><button class="btn-link" ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}" ng-disabled="noDecrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                                <td ng-show="showSeconds" class="uib-decrement seconds ng-hide" style="padding-top:3px"><button class="btn-link" ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}" ng-disabled="noDecrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td ng-show="showMeridian" class=""></td>
                                                                                            </tr>
                                                                                        </tbody>
                                                                                    </table>
                                                                                </div>
                                                                            </div>
                                                                            <br>
                                                                        </div>
                                                                        <div class="col-lg-6">
                                                                            <!--Capturing Interval-->
                                                                            <div class="form-group-sm">
                                                                                <div class="row">
                                                                                    <div class="col-lg-5">
                                                                                        <label class="control-label">Capture Interval<span class="required">*</span></label>
                                                                                    </div>
                                                                                    <div class="col-lg-7">
                                                                                        <select class="form-control" name="Timing"
                                                                                                ng-model="Schedule.Interval"
                                                                                                ng-required="true"
                                                                                                ng-options="Timing.Id as Timing.Timing for Timing in TimingList">
                                                                                            <option value="">Select</option>
                                                                                            <option ng-repeat="e in TimingList" ng-selected="Schedule.Interval==e.Id" value="{{e.Id}}">{{e.Timing}}</option>
                                                                                        </select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <br>
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            <div class="panel panel-primary panel-bordered">
                                                                <div class="panel-body">
                                                                    <!--ng-WeekDays-->
                                                                    <div class="form-group-sm">
                                                                        <div class="row">
                                                                            <div class="col-lg-3"><label class="control-label" style="margin-top:16px">Day (s) of the week<span class="required">*</span></label></div>
                                                                            <div class="col-lg-9">
                                                                                <div class="col-sm-4">
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="Saturday" ng-model="Schedule.Sat" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Saturday
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Sun" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Sunday
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Mon" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Monday
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-sm-4">
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Tues" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Tuesday
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Wed" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Wednesday
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Thurs" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Thursday
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-sm-4">
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Fri" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Friday
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="row row-modal">
                    <button type="button" class="btn btn-sm btn-default pull-left btn-prev" role="button"
                            ng-if="Step>1"
                            ng-click="StepBack()">
                        <i class="fa fa-arrow-circle-left fa-1x"></i>  &nbsp;&nbsp;&nbsp;&nbsp;Previous
                    </button>
                    <button type="button"
                            ng-if="Step<2"
                            ng-disabled="FirstSegmentValid()==false"
                            ng-click="StepUp()" class="btn btn-sm pull-right btn-next">
                        Next &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-arrow-circle-right fa-1x"></i>
                    </button>
                    <button type="button"
                            ng-if="Step==2"
                            class="btn btn-sm btn-primary pull-right btn-next"
                            ng-disabled="GroupScheduleForm.$invalid || Loading==true || IsAtLeastOneDaySelected()==false"
                            ng-click="CreateGroupAndSetSchedule()">
                        Set schedule for&nbsp;{{Schedule.GroupName}} &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>


<!--Schedule Edit Modal-->
<div class="modal fade" id="ScheduleEditModal" data-backdrop="static" data-keyboard="false">
    <div class="{{Step==1?'modal-dialog modal-custom':'modal-dialog  modal-lg'}}" style="transition:all 0.5s cubic-bezier(0.44, -0.35, 0.54, 1.35) !important">
        <div class="modal-content">
            <div class="modal-header" style="height:65px">
                <button type="button" class="custom-close" ng-click="cancelScheduleEditModal()" style="margin-top:0px;" aria-label="Close" tooltip-placement="left" uib-tooltip="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" style="margin-top:1.6%">Group Schedule Settings</h4>
            </div>
            <div class="modal-body">
                <div class="form-wizard wizard-block wizard-icons" style="padding-left:11px; padding-right:11px">
                    <div id="wizard-5" class="row row-modal">
                        <div class="tab-steps">
                            <ul class="steps">
                                <li ng-class="{'active':Step==1}">
                                    <a id="tab1" ng-click="SwitchTab1()">
                                        <span class="tab-icon"> <i class="fa fa-users"></i></span>
                                        <span class="tab-text">Edit Group</span>
                                    </a>
                                </li>
                                <li ng-class="{'active':Step==2}">
                                    <a id="tab2" ng-click="SwitchTab2()">
                                        <span class="tab-icon"><i class="fa fa-calendar-check-o"></i></span>
                                        <span class="tab-text">Set Schedule For Group</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="tab-content" style="margin-bottom:0px; padding-bottom:0px">
                            <!--TAB PANE 1 : Group Creation-->
                            <div class="tab-pane" ng-class="{'active':Step==1}" id="w5-tab1">
                                <div class="col-md-5">
                                    <div class="panel panel-primary panel-bordered">
                                        <div class="panel-header">
                                            <h3 class="panel-title">GROUPABLE DEVICE (S)</h3>
                                        </div>
                                        <div class="panel-body">
                                            <div class="table-responsive">
                                                <div style="overflow-y: auto">
                                                    <table class="table table-bordered">
                                                        <thead>
                                                            <tr>
                                                                <th>Device Name</th>
                                                                <th>Device Id</th>
                                                                <th>Settings</th>
                                                                <th>Action</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr ng-repeat="x in GroupableDeviceList">
                                                                <td><img src="Assets_Company/images/datatables/design.png" width="20"> &nbsp; {{x.DeviceName}}</td>
                                                                <td>{{x.DeviceUniqueId}}</td>
                                                                <td>{{x.Settings}}</td>
                                                                <td>
                                                                    <div class="checkbox-custom checkbox-success">
                                                                        <input type="checkbox" name="{{x.Id}}" id="{{x.Id}}"
                                                                               ng-click="PushToSelectedDeviceArray(x.Id, x.DeviceName, x.DeviceUniqueId, $event)"
                                                                               ng-checked="{{x.Settings=='Group settings applied'}}">
                                                                        <label class="check" for="{{x.Id}}" style="padding: 5px"></label>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-7">
                                    <div class="panel panel-primary panel-bordered">
                                        <div class="panel-header">
                                            <h3 class="panel-title">EDIT GROUP</h3>
                                        </div>
                                        <div class="panel-body">
                                            <!--Group Name-->
                                            <form novalidate name="EditGroupForm">
                                                <div class="form-group-sm">
                                                    <label for="EditGroupName" class="control-label">Group Name<span class="required">*</span></label>
                                                    <input type="text" class="form-control  col-md-6" name="EditGroupName" placeholder="Enter Group Name"
                                                           ng-model="Schedule.GroupName"
                                                           ng-minlength="4"
                                                           ng-maxlength="50"
                                                           ng-pattern="/^[A-Z][a-zA-Z0-9\-_ ]*$/"
                                                           ng-required="true"
                                                           ng-disabled="Loading==true" />
                                                    <div class="row custom-row">
                                                        <ul class="list-unstyled errormessage pull-left" ng-show="EditGroupForm.EditGroupName.$dirty && EditGroupForm.EditGroupName.$invalid">
                                                            <li><span class="pull-left" ng-show="EditGroupForm.EditGroupName.$error.required">*Required</span></li>
                                                            <li><span class="pull-left" ng-show="EditGroupForm.EditGroupName.$error.pattern">Invalid Name : Start with capital letter, don't use dot / special character.</span></li>
                                                            <li><span class="pull-left" ng-show="!EditGroupForm.EditGroupName.$error.pattern && EditGroupForm.EditGroupName.$error.minlength">Minimum length is 4</span></li>
                                                            <li><span class="pull-left" ng-show="!EditGroupForm.EditGroupName.$error.pattern && EditGroupForm.EditGroupName.$error.maxlength">Maximum length is 50</span></li> 
                                                        </ul>
                                                    </div>
                                                </div>
                                            </form>
                                            <br />
                                            <!--Device List-->
                                            <div class="table-responsive">
                                                <div style="overflow-y: auto">
                                                    <table class="table table-bordered">
                                                        <thead>
                                                            <tr>
                                                                <th>Device Name</th>
                                                                <th>Device Id</th>
                                                                <th>Action</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr ng-repeat="x in SelectedDeviceArray">
                                                                <td><img src="Assets_Company/images/datatables/design.png" width="20"> &nbsp; {{x.DeviceName}}</td>
                                                                <td>{{x.DeviceUniqueId}}</td>
                                                                <td>
                                                                    <div class="checkbox-custom checkbox-success">
                                                                        <input type="checkbox" name="{{x.Id}}" id="{{x.Id}}"
                                                                               ng-click="PushToSelectedDeviceArray(x.Id, x.DeviceName, x.DeviceUniqueId, $event)"
                                                                               ng-checked="{{x.Id}}">
                                                                        <label class="check" for="{{x.Id}}" style="padding: 5px"></label>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--TAB PANE 2 : Setting Group Schedule-->
                            <div class="tab-pane" id="w5-tab2" ng-class="{'active':Step==2}">
                                <div class="panel panel-primary panel-bordered">
                                    <div class="panel-body">
                                        <form name="GroupScheduleForm" class="form-horizontal" novalidate>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <!--Mandatory Fields Related to Capturing (01)-->
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            <div class="panel panel-primary panel-bordered">
                                                                <div class="panel-body">
                                                                    <div class="row">
                                                                        <!--CapturingStartTime-->
                                                                        <div class="col-lg-3">
                                                                            <div class="form-group-sm">
                                                                                <label class="control-label">Capture Start Time<span class="required">*</span></label>
                                                                                <div uib-timepicker="" ng-model="Schedule.StartTime" max="Schedule.EndTime" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ng-required="true" class="ng-pristine ng-untouched ng-valid ng-isolate-scope ng-not-empty ng-valid-time ng-valid-required" required="required">
                                                                                    <table class="uib-timepicker">
                                                                                        <tbody>
                                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                                <td class="uib-increment hours"><button class="btn-link" ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}" ng-disabled="noIncrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td>&nbsp;</td>
                                                                                                <td class="uib-increment minutes"><button class="btn-link" ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}" ng-disabled="noIncrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                                <td ng-show="showSeconds" class="uib-increment seconds ng-hide"><button class="btn-link" ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}" ng-disabled="noIncrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td ng-show="showMeridian" class=""></td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td class="form-group uib-time hours" ng-class="{'has-error': invalidHours}">
                                                                                                    <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-hours ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementHours()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td class="uib-separator" style="font-weight:bold">:</td>
                                                                                                <td class="form-group uib-time minutes" ng-class="{'has-error': invalidMinutes}">
                                                                                                    <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-minutes ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementMinutes()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td ng-show="showSeconds" class="uib-separator ng-hide" style="font-weight:bold">:</td>
                                                                                                <td class="form-group uib-time seconds ng-hide" ng-class="{'has-error': invalidSeconds}" ng-show="showSeconds">
                                                                                                    <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-seconds ng-not-empty ng-valid-maxlength" ng-readonly="readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementSeconds()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td ng-show="showMeridian" class="uib-time am-pm"><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-date text-center ng-binding disabled" style="height:30px; padding-top:4px; padding-bottom:4px" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="0" disabled="disabled">AM</button></td>
                                                                                            </tr>
                                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                                <td class="uib-decrement hours" style="padding-top:3px"><button class="btn-link" ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}" ng-disabled="noDecrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td>&nbsp;</td>
                                                                                                <td class="uib-decrement minutes" style="padding-top:3px"><button class="btn-link" ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}" ng-disabled="noDecrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                                <td ng-show="showSeconds" class="uib-decrement seconds ng-hide" style="padding-top:3px"><button class="btn-link" ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}" ng-disabled="noDecrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td ng-show="showMeridian" class=""></td>
                                                                                            </tr>
                                                                                        </tbody>
                                                                                    </table>
                                                                                </div>
                                                                            </div>
                                                                            <br>
                                                                        </div>
                                                                        <!--CapturingEndTime-->
                                                                        <div class="col-lg-3 separator">
                                                                            <div class="form-group-sm">
                                                                                <label class="control-label">Capture End Time<span class="required">*</span></label>
                                                                                <div uib-timepicker="" ng-model="Schedule.EndTime" min="Schedule.StartTime" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ng-required="true" class="ng-pristine ng-untouched ng-valid ng-isolate-scope ng-not-empty ng-valid-time ng-valid-required" required="required">
                                                                                    <table class="uib-timepicker">
                                                                                        <tbody>
                                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                                <td class="uib-increment hours"><button class="btn-link" ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}" ng-disabled="noIncrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td>&nbsp;</td>
                                                                                                <td class="uib-increment minutes"><button class="btn-link" ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}" ng-disabled="noIncrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                                <td ng-show="showSeconds" class="uib-increment seconds ng-hide"><button class="btn-link" ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}" ng-disabled="noIncrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                                <td ng-show="showMeridian" class=""></td>
                                                                                            </tr>
                                                                                            <tr>
                                                                                                <td class="form-group uib-time hours" ng-class="{'has-error': invalidHours}">
                                                                                                    <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-hours ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementHours()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td class="uib-separator" style="font-weight:bold">:</td>
                                                                                                <td class="form-group uib-time minutes" ng-class="{'has-error': invalidMinutes}">
                                                                                                    <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-minutes ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementMinutes()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td ng-show="showSeconds" class="uib-separator ng-hide" style="font-weight:bold">:</td>
                                                                                                <td class="form-group uib-time seconds ng-hide" ng-class="{'has-error': invalidSeconds}" ng-show="showSeconds">
                                                                                                    <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-seconds ng-not-empty ng-valid-maxlength" ng-readonly="readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementSeconds()" ng-blur="blur()">
                                                                                                </td>
                                                                                                <td ng-show="showMeridian" class="uib-time am-pm"><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-date text-center ng-binding disabled" style="height:30px; padding-top:4px; padding-bottom:4px" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="0" disabled="disabled">PM</button></td>
                                                                                            </tr>
                                                                                            <tr class="text-center" ng-show="::showSpinners">
                                                                                                <td class="uib-decrement hours" style="padding-top:3px"><button class="btn-link" ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}" ng-disabled="noDecrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td>&nbsp;</td>
                                                                                                <td class="uib-decrement minutes" style="padding-top:3px"><button class="btn-link" ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}" ng-disabled="noDecrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                                <td ng-show="showSeconds" class="uib-decrement seconds ng-hide" style="padding-top:3px"><button class="btn-link" ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}" ng-disabled="noDecrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                                <td ng-show="showMeridian" class=""></td>
                                                                                            </tr>
                                                                                        </tbody>
                                                                                    </table>
                                                                                </div>
                                                                            </div>
                                                                            <br>
                                                                        </div>
                                                                        <div class="col-lg-6">
                                                                            <!--Capturing Interval-->
                                                                            <div class="form-group-sm">
                                                                                <div class="row">
                                                                                    <div class="col-lg-5">
                                                                                        <label class="control-label">Capture Interval<span class="required">*</span></label>
                                                                                    </div>
                                                                                    <div class="col-lg-7">
                                                                                        <select class="form-control" name="Timing"
                                                                                                ng-model="Schedule.Interval"
                                                                                                ng-required="true"
                                                                                                ng-options="Timing.Id as Timing.Timing for Timing in TimingList">
                                                                                            <option value="">Select</option>
                                                                                            <option ng-repeat="e in TimingList" ng-selected="Schedule.Interval==e.Id" value="{{e.Id}}">{{e.Timing}}</option>
                                                                                        </select>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <br>
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            <div class="panel panel-primary panel-bordered">
                                                                <div class="panel-body">
                                                                    <!--ng-WeekDays-->
                                                                    <div class="form-group-sm">
                                                                        <div class="row">
                                                                            <div class="col-lg-3"><label class="control-label" style="margin-top:16px">Day (s) of the week<span class="required">*</span></label></div>
                                                                            <div class="col-lg-9">
                                                                                <div class="col-sm-4">
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="Saturday" ng-model="Schedule.Sat" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Saturday
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Sun" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Sunday
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Mon" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Monday
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-sm-4">
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Tues" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Tuesday
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Wed" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Wednesday
                                                                                        </label>
                                                                                    </div>
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Thurs" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Thursday
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-sm-4">
                                                                                    <div class="checkbox">
                                                                                        <label>
                                                                                            <input type="checkbox" value="" ng-model="Schedule.Fri" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                            <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                            Friday
                                                                                        </label>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="row row-modal">
                    <button type="button" class="btn btn-sm btn-default pull-left btn-prev" role="button"
                            ng-if="Step>1"
                            ng-click="StepBack()">
                        <i class="fa fa-arrow-circle-left fa-1x"></i>  &nbsp;&nbsp;&nbsp;&nbsp;Previous
                    </button>
                    <button type="button"
                            ng-if="Step<2"
                            ng-disabled="FirstSegmentValid()==false"
                            ng-click="StepUp()" class="btn btn-sm pull-right btn-next">
                        Next &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-arrow-circle-right fa-1x"></i>
                    </button>
                    <button type="button"
                            ng-if="Step==2"
                            class="btn btn-sm btn-primary pull-right btn-next"
                            ng-disabled="GroupScheduleForm.$invalid || Loading==true || IsAtLeastOneDaySelected()==false"
                            ng-click="UpdateGroup()">
                        Update&nbsp;{{Schedule.GroupName}} &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>




<style>
    .checkbox label:after,
    .radio label:after {
        content: '';
        display: table;
        clear: both;
    }

    .checkbox .cr,
    .radio .cr {
        position: relative;
        display: inline-block;
        border: 1px solid #a9a9a9;
        border-radius: .25em;
        width: 1.3em;
        height: 1.3em;
        float: left;
        margin-right: .5em;
    }

    .radio .cr {
        border-radius: 50%;
    }

        .checkbox .cr .cr-icon,
        .radio .cr .cr-icon {
            position: absolute;
            font-size: .8em;
            line-height: 0;
            top: 50%;
            left: 20%;
        }

        .radio .cr .cr-icon {
            margin-left: 0.04em;
        }

    .checkbox label input[type="checkbox"],
    .radio label input[type="radio"] {
        display: none;
    }

        .checkbox label input[type="checkbox"] + .cr > .cr-icon,
        .radio label input[type="radio"] + .cr > .cr-icon {
            transform: scale(3) rotateZ(-20deg);
            opacity: 0;
            transition: all .3s ease-in;
        }

        .checkbox label input[type="checkbox"]:checked + .cr > .cr-icon,
        .radio label input[type="radio"]:checked + .cr > .cr-icon {
            transform: scale(1) rotateZ(0deg);
            opacity: 1;
        }

        .checkbox label input[type="checkbox"]:disabled + .cr,
        .radio label input[type="radio"]:disabled + .cr {
            opacity: .5;
        }
</style>
