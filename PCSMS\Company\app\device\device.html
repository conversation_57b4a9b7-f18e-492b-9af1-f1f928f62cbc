﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>


<!-- CONTENT -->
<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-clone" aria-hidden="true"></i><a ui-sref="device">Your Device (s)</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <!--LEFT SIDE OPTIONS-->
            <div class="col-sm-12 col-md-5">
                <div class="panel b-primary bt-md" style="height: 400px;overflow: auto" >
                    <div class="panel-content">
                        <div>
                            <span class="text-bold text-lg"><u>CHOOSE LICENSE</u></span>
                        </div><br />
                        <table class="table table-bordered" ng-if="LicenseList.length>0">
                            <thead>
                                <tr>
                                    <th>License Code</th>
                                    <th>Type</th>
                                    <th>License Key</th>
                                    <th>Status</th>
                                    <th>Reg. Devices</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="x in LicenseList">
                                    <td>{{x.LCode}}</td>
                                    <td>{{x.LicenseType}}</td>
                                    <td>{{x.LicenseKey}}</td>
                                    <td><span class="{{x.Status=='Requested'?'badge x-warning ':x.Status=='Active'?'badge x-success ':x.Status=='In-Active'?'badge ':x.Status=='Expired'?'badge x-danger ':'badge '}}">{{x.Status}}</span></td>
                                    <td>
                                        <button style="background-color: #ffffff; margin-top: -7px;"
                                                type="button" tooltip-placement="top"
                                                uib-tooltip="View device(s)"
                                                class="btn btn-default"
                                                ng-click="SeeDeviceList(x.Id);">
                                            <img src="Assets_Company/images/datatables/desktop (1).png" width="30">
                                            <img src="Assets_Company/images/datatables/desktop (1).png" width="30">

                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="panel-content">
                        <span class="text-danger" ng-if="LicenseList.length==null">No License has been issued yet.</span>
                    </div>
                </div>
            </div>
            <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
            <!--DEVICES-->
            <div class="col-sm-12 col-md-7">
                <div class="panel b-primary bt-md" >
                    <div class="panel-content">
                        <div>
                            <span class="text-bold text-lg"><u>LIST OF DEVICE (S)</u></span>
                        </div>
                        <div ng-show="DeviceList.length == 0 && LicenseList.length >0 && LicenseDetails == null">
                            <br />
                            <span class="color-primary">Choose a license to see registered device(s) under it.</span>
                        </div>
                        <div ng-show="DeviceList.length == 0 && LicenseList.length == null && LicenseDetails ==null">
                            <br />
                            <br />
                            <span class="text-danger">No device is yet registered under any license.</span>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-md-7" ng-if="LicenseDetails.LCode !=null">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <tbody>
                                            <tr>
                                                <th>License Code</th>
                                                <th>{{LicenseDetails.LCode}}</th>
                                            </tr>
                                            <tr>
                                                <th>License Key</th>
                                                <th>{{LicenseDetails.LicenseKey}}</th>
                                            </tr>
                                            <tr>
                                                <th>Reg. Device (s)</th>
                                                <th>{{DeviceList.length}}</th>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div ng-show="DeviceList.length == 0 && LicenseList.length >0 && LicenseDetails != null">
                            <br />
                            <br />
                            <span class="color-primary">No device is yet registered under {{LicenseDetails.LCode}} license.</span>
                        </div>
                        <br />

                        <div class="panel-group faq-accordion" id="accordion_faq" ng-show="DeviceList.length >0" style="height: 500px;overflow: auto" >
                            <div class="panel panel-accordion" ng-repeat="x in DeviceList track by $index" >
                                <div class="panel-header bg-scale-0">
                                    <a class="panel-title" data-toggle="collapse" data-parent="#accordion_faq" data-target="#q1{{x.Id}}">
                                        <img src="Assets_Company/images/datatables/design.png" width="30"><span class="color-primary text-bold"> &nbsp; {{x.DeviceName}}&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span class=""><i class="fa fa-pencil-square-o" aria-hidden="true" ng-click="OpenDeviceNameChangeModal(x.DeviceUniqueId, x.DeviceName, $event)"></i></span></span>
                                        <span class="pull-right"><i>{{x.Settings}}</i></span>
                                    </a>
                                </div>
                                <div id="q1{{x.Id}}" class="panel-collapse collapse">
                                    <div class="panel-content"  style="overflow: auto" >
                                        Device Id: &nbsp; <span class="color-primary">{{x.DeviceUniqueId}}</span>.<br />
                                        <span ng-if="x.IsRandom=='N' || x.IsRandom==null">The device captures screenshots in every <span class="color-primary">{{x.Interval }}</span> minute (s).</span>
                                        <span ng-if="x.IsRandom=='Y'">The device captures screenshots randomly (1-5 minutes).</span>
                                        Screen capturing time starts at <span class="color-primary">{{x.StartTime | date:'medium'}}</span> and it ends at <span class="color-primary">{{x.EndTime | date:'hh:mm'}}</span>.
                                        <h5><b><u>Screen Capture Schedule</u></b></h5>
                                        <table class="table table-bordered" >
                                            <thead>
                                                <tr>
                                                    <th class="text-center" >Saturday</th>
                                                    <th class="text-center" >Sunday</th>
                                                    <th class="text-center" >Monday</th>
                                                    <th class="text-center" >Tuesday</th>
                                                    <th class="text-center" >Wednesday</th>
                                                    <th class="text-center" >Thursday</th>
                                                    <th class="text-center" >Friday </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="text-center color-primary" ng-if="x.Sat == 'Y'"><i class="fa fa-check" aria-hidden="true"></i></td><td class="text-center text-danger" ng-if="x.Sat == 'N'"><i class="fa fa-close" aria-hidden="true"></i></td>
                                                    <td class="text-center color-primary" ng-if="x.Sun == 'Y'"><i class="fa fa-check" aria-hidden="true"></i></td><td class="text-center text-danger" ng-if="x.Sun == 'N'"><i class="fa fa-close" aria-hidden="true"></i></td>
                                                    <td class="text-center color-primary" ng-if="x.Mon == 'Y'"><i class="fa fa-check" aria-hidden="true"></i></td><td class="text-center text-danger" ng-if="x.Mon == 'N'"><i class="fa fa-close" aria-hidden="true"></i></td>
                                                    <td class="text-center color-primary" ng-if="x.Tues == 'Y'"><i class="fa fa-check" aria-hidden="true"></i></td><td class="text-center text-danger" ng-if="x.Tues == 'N'"><i class="fa fa-close" aria-hidden="true"></i></td>
                                                    <td class="text-center color-primary" ng-if="x.Wed == 'Y'"><i class="fa fa-check" aria-hidden="true"></i></td><td class="text-center text-danger" ng-if="x.Wed == 'N'"><i class="fa fa-close" aria-hidden="true"></i></td>
                                                    <td class="text-center color-primary" ng-if="x.Thurs == 'Y'"><i class="fa fa-check" aria-hidden="true"></i></td><td class="text-center text-danger" ng-if="x.Thurs == 'N'"><i class="fa fa-close" aria-hidden="true"></i></td>
                                                    <td class="text-center color-primary" ng-if="x.Fri == 'Y'"><i class="fa fa-check" aria-hidden="true"></i></td><td class="text-center text-danger" ng-if="x.Fri == 'N'"><i class="fa fa-close" aria-hidden="true"></i></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>

<div class="modal fade" id="DeviceDetailsModal" tabindex="-1" role="dialog" aria-labelledby="modal-label" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="height:65px">
                <button type="button" class="custom-close" ng-click="cancelDeviceDetailsModal()" style="margin-top:0px;" aria-label="Close" tooltip-placement="left" uib-tooltip="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" style="margin-top:1.6%">Change Device Name</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-primary panel-bordered">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <!--First Name-->
                                <div class="form-group-sm">
                                    <form name="DeviceNameChangeForm">
                                        <label class="req control-label">Device Name</label>
                                        <input type="text" name="DeviceName" class="form-control" placeholder="Device Name"
                                               ng-model="Device.DeviceName"
                                               ng-required="true"
                                               ng-minlength="5"
                                               ng-maxlength="50"
                                               ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" />
                                        <div class="row custom-row">
                                            <ul class="list-unstyled text-danger pull-left" ng-show="DeviceNameChangeForm.DeviceName.$dirty && DeviceNameChangeForm.DeviceName.$invalid">
                                                <li><span class="pull-left" ng-show="DeviceNameChangeForm.DeviceName.$error.required">*Required</span></li>
                                                <li><span class="pull-left" ng-show="DeviceNameChangeForm.DeviceName.$error.pattern">Start with capital letter, don't use special characters.</span></li>
                                                <li><span class="pull-left" ng-show="!DeviceNameChangeForm.DeviceName.$error.pattern && DeviceNameChangeForm.DeviceName.$error.minlength">Minimum length is 5</span></li>
                                                <li><span class="pull-left" ng-show="!DeviceNameChangeForm.DeviceName.$error.pattern && DeviceNameChangeForm.DeviceName.$error.maxlength">Maximum length is 50</span></li>
                                            </ul>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button"
                        class="btn  btn-default btn-wide pull-right btn-left-border-radius"
                        ng-click="cancelDeviceDetailsModal()"
                        role="button">
                    Close &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-close fa-1x"></i>
                </button>
                <button type="button"
                        class="btn btn-wide btn-primary pull-right btn-right-border-radius"
                        ng-disabled="DeviceNameChangeForm.$invalid"
                        ng-click="UpdateDeviceName()">
                    <span>
                        Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>