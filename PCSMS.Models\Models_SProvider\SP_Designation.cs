﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using PCSMS.Models.Common;
using PCSMS.Models.Models_SProvider;

namespace PCSMS.Models.Models_SProvider
{
    public class SP_Designation : Entity<int>
    {
        [MaxLength(50)]
        [Required]
        public string DesignationName { get; set; }

        public virtual ICollection<SP_User> SP_Users { get; set; }
    }
}
