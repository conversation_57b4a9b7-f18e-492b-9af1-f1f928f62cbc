(function(e,t,n){function o(e,t){return t.match(/:\d+$/)?t:t+":"+("http:"===(n=e)?80:"https:"===n?443:void 0);var n}var r={nojQuery:"jQuery was not found. Please ensure jQ<PERSON>y is referenced before the SignalR client JavaScript file.",noTransportOnInit:"No transport could be initialized successfully. Try specifying a different transport or none at all for auto initialization.",errorOnNegotiate:"Error during negotiation request.",stoppedWhileLoading:"The connection was stopped during page load.",stoppedWhileNegotiating:"The connection was stopped during the negotiate request.",errorParsingNegotiateResponse:"Error parsing negotiate response.",errorDuringStartRequest:"Error during start request. Stopping the connection.",stoppedDuringStartRequest:"The connection was stopped during the start request.",errorParsingStartResponse:"Error parsing start response: '{0}'. Stopping the connection.",invalidStartResponse:"Invalid start response: '{0}'. Stopping the connection.",protocolIncompatible:"You are using a version of the client that isn't compatible with the server. Client version {0}, server version {1}.",sendFailed:"Send failed.",parseFailed:"Failed at parsing response: {0}",longPollFailed:"Long polling request failed.",eventSourceFailedToConnect:"EventSource failed to connect.",eventSourceError:"Error raised by EventSource",webSocketClosed:"WebSocket closed.",pingServerFailedInvalidResponse:"Invalid ping response when pinging server: '{0}'.",pingServerFailed:"Failed to ping server.",pingServerFailedStatusCode:"Failed to ping server.  Server responded with status code {0}, stopping the connection.",pingServerFailedParse:"Failed to parse ping server response, stopping the connection.",noConnectionTransport:"Connection is in an invalid state, there is no transport active.",webSocketsInvalidState:"The Web Socket transport is in an invalid state, transitioning into reconnecting.",reconnectTimeout:"Couldn't reconnect within the configured timeout of {0} ms, disconnecting.",reconnectWindowTimeout:"The client has been inactive since {0} and it has exceeded the inactivity timeout of {1} ms. Stopping the connection."};if("function"!=typeof e)throw new Error(r.nojQuery);var i,a,c,s,l="complete"===t.document.readyState,u=e(t),d="__Negotiate Aborted__",p={onStart:"onStart",onStarting:"onStarting",onReceived:"onReceived",onError:"onError",onConnectionSlow:"onConnectionSlow",onReconnecting:"onReconnecting",onReconnect:"onReconnect",onStateChanged:"onStateChanged",onDisconnect:"onDisconnect"},g=function(t,n,o){return n===t.state&&(t.state=o,e(t).triggerHandler(p.onStateChanged,[{oldState:n,newState:o}]),!0)},f=function(e){return e._.keepAliveData.activated&&e.transport.supportsKeepAlive(e)};(i=function(e,t,n){return new i.fn.init(e,t,n)})._={defaultContentType:"application/x-www-form-urlencoded; charset=UTF-8",ieVersion:("Microsoft Internet Explorer"===t.navigator.appName&&(s=/MSIE ([0-9]+\.[0-9]+)/.exec(t.navigator.userAgent))&&(c=t.parseFloat(s[1])),c),error:function(e,t,n){var o=new Error(e);return o.source=t,void 0!==n&&(o.context=n),o},transportError:function(e,t,n,o){var r=this.error(e,n,o);return r.transport=t?t.name:void 0,r},format:function(){for(var e=arguments[0],t=0;t<arguments.length-1;t++)e=e.replace("{"+t+"}",arguments[t+1]);return e},firefoxMajorVersion:function(e){var t=e.match(/Firefox\/(\d+)/);return!t||!t.length||t.length<2?0:parseInt(t[1],10)},configurePingInterval:function(n){var o=n._.config,r=function(t){e(n).triggerHandler(p.onError,[t])};o&&!n._.pingIntervalId&&o.pingInterval&&(n._.pingIntervalId=t.setInterval(function(){i.transports._logic.pingServer(n).fail(r)},o.pingInterval))}},i.events=p,i.resources=r,i.ajaxDefaults={processData:!0,timeout:null,async:!0,global:!1,cache:!1},i.changeState=g,i.isDisconnecting=function(e){return e.state===i.connectionState.disconnected},i.connectionState={connecting:0,connected:1,reconnecting:2,disconnected:4},i.hub={start:function(){throw new Error("SignalR: Error loading hubs. Ensure your hubs reference is correct, e.g. <script src='/signalr/js'><\/script>.")}},"function"==typeof u.on?u.on("load",function(){l=!0}):u.load(function(){l=!0}),i.fn=i.prototype={init:function(t,n,o){var r=e(this);this.url=t,this.qs=n,this.lastError=null,this._={keepAliveData:{},connectingMessageBuffer:new function(t,n){var o=this,r=[];o.tryBuffer=function(n){return t.state===e.signalR.connectionState.connecting&&(r.push(n),!0)},o.drain=function(){if(t.state===e.signalR.connectionState.connected)for(;r.length>0;)n(r.shift())},o.clear=function(){r=[]}}(this,function(e){r.triggerHandler(p.onReceived,[e])}),lastMessageAt:(new Date).getTime(),lastActiveAt:(new Date).getTime(),beatInterval:5e3,beatHandle:null,totalTransportConnectTimeout:0},"boolean"==typeof o&&(this.logging=o)},_parseResponse:function(e){return e&&"string"==typeof e?this.json.parse(e):e},_originalJson:t.JSON,json:t.JSON,isCrossDomain:function(n,r){var i;return n=e.trim(n),r=r||t.location,0===n.indexOf("http")&&((i=t.document.createElement("a")).href=n,i.protocol+o(i.protocol,i.host)!==r.protocol+o(r.protocol,r.host))},ajaxDataType:"text",contentType:"application/json; charset=UTF-8",logging:!1,state:i.connectionState.disconnected,clientProtocol:"1.5",reconnectDelay:2e3,transportConnectTimeout:0,disconnectTimeout:3e4,reconnectWindow:3e4,keepAliveWarnAt:2/3,start:function(n,o){var a,c,s,v,m,h,S=this,b={pingInterval:3e5,waitForPageLoad:!0,transport:"auto",jsonp:!1},_=S._deferral||e.Deferred(),T=t.document.createElement("a");if(S.lastError=null,S._deferral=_,!S.json)throw new Error("SignalR: No JSON parser found. Please ensure json2.js is referenced before the SignalR.js file if you need to support clients without native JSON parsing support, e.g. IE<8.");if("function"===e.type(n)?o=n:"object"===e.type(n)&&(e.extend(b,n),"function"===e.type(b.callback)&&(o=b.callback)),b.transport=function(t,n){var o,r;if(e.isArray(t)){for(o=t.length-1;o>=0;o--)r=t[o],"string"===e.type(r)&&i.transports[r]||(n.log("Invalid transport: "+r+", removing it from the transports list."),t.splice(o,1));0===t.length&&(n.log("No transports remain within the specified transport array."),t=null)}else if(i.transports[t]||"auto"===t){if("auto"===t&&i._.ieVersion<=8)return["longPolling"]}else n.log("Invalid transport: "+t.toString()+"."),t=null;return t}(b.transport,S),!b.transport)throw new Error("SignalR: Invalid transport(s) specified, aborting start.");return S._.config=b,l||!0!==b.waitForPageLoad?S.state===i.connectionState.connecting?_.promise():!1===g(S,i.connectionState.disconnected,i.connectionState.connecting)?(_.resolve(S),_.promise()):((v=S)._.configuredStopReconnectingTimeout||(h=function(t){var n=i._.format(i.resources.reconnectTimeout,t.disconnectTimeout);t.log(n),e(t).triggerHandler(p.onError,[i._.error(n,"TimeoutException")]),t.stop(!1,!1)},v.reconnecting(function(){var e=this;e.state===i.connectionState.reconnecting&&(m=t.setTimeout(function(){h(e)},e.disconnectTimeout))}),v.stateChanged(function(e){e.oldState===i.connectionState.reconnecting&&t.clearTimeout(m)}),v._.configuredStopReconnectingTimeout=!0),T.href=S.url,T.protocol&&":"!==T.protocol?(S.protocol=T.protocol,S.host=T.host):(S.protocol=t.document.location.protocol,S.host=T.host||t.document.location.host),S.baseUrl=S.protocol+"//"+S.host,S.wsProtocol="https:"===S.protocol?"wss://":"ws://","auto"===b.transport&&!0===b.jsonp&&(b.transport="longPolling"),0===S.url.indexOf("//")&&(S.url=t.location.protocol+S.url,S.log("Protocol relative URL detected, normalizing it to '"+S.url+"'.")),this.isCrossDomain(S.url)&&(S.log("Auto detected cross domain url."),"auto"===b.transport&&(b.transport=["webSockets","serverSentEvents","longPolling"]),void 0===b.withCredentials&&(b.withCredentials=!0),b.jsonp||(b.jsonp=!e.support.cors,b.jsonp&&S.log("Using jsonp because this browser doesn't support CORS.")),S.contentType=i._.defaultContentType),S.withCredentials=b.withCredentials,S.ajaxDataType=b.jsonp?"jsonp":"text",e(S).bind(p.onStart,function(){"function"===e.type(o)&&o.call(S),_.resolve(S)}),S._.initHandler=i.transports._logic.initHandler(S),a=function(n,o){var c=i._.error(r.noTransportOnInit);if((o=o||0)>=n.length)return 0===o?S.log("No transports supported by the server were selected."):1===o?S.log("No fallback transports were selected."):S.log("Fallback transports exhausted."),e(S).triggerHandler(p.onError,[c]),_.reject(c),void S.stop();if(S.state!==i.connectionState.disconnected){var s=n[o],l=i.transports[s],d=function(){a(n,o+1)};S.transport=l;try{S._.initHandler.start(l,function(){var n=i._.firefoxMajorVersion(t.navigator.userAgent)>=11,o=!!S.withCredentials&&n;S.log("The start request succeeded. Transitioning to the connected state."),f(S)&&i.transports._logic.monitorKeepAlive(S),i.transports._logic.startHeartbeat(S),i._.configurePingInterval(S),g(S,i.connectionState.connecting,i.connectionState.connected)||S.log("WARNING! The connection was not in the connecting state."),S._.connectingMessageBuffer.drain(),e(S).triggerHandler(p.onStart),u.bind("unload",function(){S.log("Window unloading, stopping the connection."),S.stop(o)}),n&&u.bind("beforeunload",function(){t.setTimeout(function(){S.stop(o)},0)})},d)}catch(e){S.log(l.name+" transport threw '"+e.message+"' when attempting to start."),d()}}},c=S.url+"/negotiate",s=function(t,n){var o=i._.error(r.errorOnNegotiate,t,n._.negotiateRequest);e(n).triggerHandler(p.onError,o),_.reject(o),n.stop()},e(S).triggerHandler(p.onStarting),c=i.transports._logic.prepareQueryString(S,c),S.log("Negotiating with '"+c+"'."),S._.negotiateRequest=i.transports._logic.ajax(S,{url:c,error:function(e,t){t!==d?s(e,S):_.reject(i._.error(r.stoppedWhileNegotiating,null,S._.negotiateRequest))},success:function(t){var n,o,c,l=[],u=[];try{n=S._parseResponse(t)}catch(e){return void s(i._.error(r.errorParsingNegotiateResponse,e),S)}if(o=S._.keepAliveData,S.appRelativeUrl=n.Url,S.id=n.ConnectionId,S.token=n.ConnectionToken,S.webSocketServerUrl=n.WebSocketServerUrl,S._.pollTimeout=1e3*n.ConnectionTimeout+1e4,S.disconnectTimeout=1e3*n.DisconnectTimeout,S._.totalTransportConnectTimeout=S.transportConnectTimeout+1e3*n.TransportConnectTimeout,n.KeepAliveTimeout?(o.activated=!0,o.timeout=1e3*n.KeepAliveTimeout,o.timeoutWarning=o.timeout*S.keepAliveWarnAt,S._.beatInterval=(o.timeout-o.timeoutWarning)/3):o.activated=!1,S.reconnectWindow=S.disconnectTimeout+(o.timeout||0),!n.ProtocolVersion||n.ProtocolVersion!==S.clientProtocol)return c=i._.error(i._.format(r.protocolIncompatible,S.clientProtocol,n.ProtocolVersion)),e(S).triggerHandler(p.onError,[c]),void _.reject(c);e.each(i.transports,function(e){if(0===e.indexOf("_")||"webSockets"===e&&!n.TryWebSockets)return!0;u.push(e)}),e.isArray(b.transport)?e.each(b.transport,function(t,n){e.inArray(n,u)>=0&&l.push(n)}):"auto"===b.transport?l=u:e.inArray(b.transport,u)>=0&&l.push(b.transport),a(l)}}),_.promise()):(S._.deferredStartHandler=function(){S.start(n,o)},u.bind("load",S._.deferredStartHandler),_.promise())},starting:function(t){var n=this;return e(n).bind(p.onStarting,function(){t.call(n)}),n},send:function(e){var t=this;if(t.state===i.connectionState.disconnected)throw new Error("SignalR: Connection must be started before data can be sent. Call .start() before .send()");if(t.state===i.connectionState.connecting)throw new Error("SignalR: Connection has not been fully initialized. Use .start().done() or .start().fail() to run logic after the connection has started.");return t.transport.send(t,e),t},received:function(t){var n=this;return e(n).bind(p.onReceived,function(e,o){t.call(n,o)}),n},stateChanged:function(t){var n=this;return e(n).bind(p.onStateChanged,function(e,o){t.call(n,o)}),n},error:function(t){var n=this;return e(n).bind(p.onError,function(e,o,r){n.lastError=o,t.call(n,o,r)}),n},disconnected:function(t){var n=this;return e(n).bind(p.onDisconnect,function(){t.call(n)}),n},connectionSlow:function(t){var n=this;return e(n).bind(p.onConnectionSlow,function(){t.call(n)}),n},reconnecting:function(t){var n=this;return e(n).bind(p.onReconnecting,function(){t.call(n)}),n},reconnected:function(t){var n=this;return e(n).bind(p.onReconnect,function(){t.call(n)}),n},stop:function(n,o){var a=this,c=a._deferral;return a._.deferredStartHandler&&u.unbind("load",a._.deferredStartHandler),delete a._.config,delete a._.deferredStartHandler,l||a._.config&&!0!==a._.config.waitForPageLoad?a.state!==i.connectionState.disconnected?(a.log("Stopping connection."),t.clearTimeout(a._.beatHandle),t.clearInterval(a._.pingIntervalId),a.transport&&(a.transport.stop(a),!1!==o&&a.transport.abort(a,n),f(a)&&i.transports._logic.stopMonitoringKeepAlive(a),a.transport=null),a._.negotiateRequest&&(a._.negotiateRequest.abort(d),delete a._.negotiateRequest),a._.initHandler&&a._.initHandler.stop(),delete a._deferral,delete a.messageId,delete a.groupsToken,delete a.id,delete a._.pingIntervalId,delete a._.lastMessageAt,delete a._.lastActiveAt,a._.connectingMessageBuffer.clear(),e(a).unbind(p.onStart),g(a,a.state,i.connectionState.disconnected),e(a).triggerHandler(p.onDisconnect),a):void 0:(a.log("Stopping connection prior to negotiate."),void(c&&c.reject(i._.error(r.stoppedWhileLoading))))},log:function(e){var n,o;n=e,!1!==this.logging&&void 0!==t.console&&(o="["+(new Date).toTimeString()+"] SignalR: "+n,t.console.debug?t.console.debug(o):t.console.log&&t.console.log(o))}},i.fn.init.prototype=i.fn,i.noConflict=function(){return e.connection===i&&(e.connection=a),i},e.connection&&(a=e.connection),e.connection=e.signalR=i})(window.jQuery,window),function(e,t,n){function o(n){var r,i,l;n._.keepAliveData.monitoring&&(l=(r=n)._.keepAliveData,r.state===c.connectionState.connected&&((i=(new Date).getTime()-r._.lastMessageAt)>=l.timeout?(r.log("Keep alive timed out.  Notifying transport that connection has been lost."),r.transport.lostConnection(r)):i>=l.timeoutWarning?l.userNotified||(r.log("Keep alive has been missed, connection may be dead/slow."),e(r).triggerHandler(s.onConnectionSlow),l.userNotified=!0):l.userNotified=!1)),a.markActive(n)&&(n._.beatHandle=t.setTimeout(function(){o(n)},n._.beatInterval))}function r(e,t){var n=e.url+t;return e.transport&&(n+="?transport="+e.transport.name),a.prepareQueryString(e,n)}function i(e){this.connection=e,this.startRequested=!1,this.startCompleted=!1,this.connectionStopped=!1}var a,c=e.signalR,s=e.signalR.events,l=e.signalR.changeState,u="__Start Aborted__";c.transports={},i.prototype={start:function(e,n,o){var r=this,i=r.connection,a=!1;r.startRequested||r.connectionStopped?i.log("WARNING! "+e.name+" transport cannot be started. Initialization ongoing or completed."):(i.log(e.name+" transport starting."),e.start(i,function(){a||r.initReceived(e,n)},function(t){return a||(a=!0,r.transportFailed(e,t,o)),!r.startCompleted||r.connectionStopped}),r.transportTimeoutHandle=t.setTimeout(function(){a||(a=!0,i.log(e.name+" transport timed out when trying to connect."),r.transportFailed(e,void 0,o))},i._.totalTransportConnectTimeout))},stop:function(){this.connectionStopped=!0,t.clearTimeout(this.transportTimeoutHandle),c.transports._logic.tryAbortStartRequest(this.connection)},initReceived:function(e,n){var o=this,r=o.connection;o.startRequested?r.log("WARNING! The client received multiple init messages."):o.connectionStopped||(o.startRequested=!0,t.clearTimeout(o.transportTimeoutHandle),r.log(e.name+" transport connected. Initiating start request."),c.transports._logic.ajaxStart(r,function(){o.startCompleted=!0,n()}))},transportFailed:function(n,o,r){var i,a=this.connection,l=a._deferral;this.connectionStopped||(t.clearTimeout(this.transportTimeoutHandle),this.startRequested?this.startCompleted||(i=c._.error(c.resources.errorDuringStartRequest,o),a.log(n.name+" transport failed during the start request. Stopping the connection."),e(a).triggerHandler(s.onError,[i]),l&&l.reject(i),a.stop()):(n.stop(a),a.log(n.name+" transport failed to connect. Attempting to fall back."),r()))}},a=c.transports._logic={ajax:function(t,n){return e.ajax(e.extend(!0,{},e.signalR.ajaxDefaults,{type:"GET",data:{},xhrFields:{withCredentials:t.withCredentials},contentType:t.contentType,dataType:t.ajaxDataType},n))},pingServer:function(t){var n,o,r=e.Deferred();return t.transport?(n=t.url+"/ping",n=a.addQs(n,t.qs),o=a.ajax(t,{url:n,success:function(e){var n;try{n=t._parseResponse(e)}catch(e){return r.reject(c._.transportError(c.resources.pingServerFailedParse,t.transport,e,o)),void t.stop()}"pong"===n.Response?r.resolve():r.reject(c._.transportError(c._.format(c.resources.pingServerFailedInvalidResponse,e),t.transport,null,o))},error:function(e){401===e.status||403===e.status?(r.reject(c._.transportError(c._.format(c.resources.pingServerFailedStatusCode,e.status),t.transport,e,o)),t.stop()):r.reject(c._.transportError(c.resources.pingServerFailed,t.transport,e,o))}})):r.reject(c._.transportError(c.resources.noConnectionTransport,t.transport)),r.promise()},prepareQueryString:function(e,n){var o;return o=a.addQs(n,"clientProtocol="+e.clientProtocol),o=a.addQs(o,e.qs),e.token&&(o+="&connectionToken="+t.encodeURIComponent(e.token)),e.data&&(o+="&connectionData="+t.encodeURIComponent(e.data)),o},addQs:function(t,n){var o,r=-1!==t.indexOf("?")?"&":"?";if(!n)return t;if("object"==typeof n)return t+r+e.param(n);if("string"==typeof n)return o=n.charAt(0),("?"===o||"&"===o)&&(r=""),t+r+n;throw new Error("Query string property must be either a string or object.")},getUrl:function(e,n,o,r,i){var c=("webSockets"===n?"":e.baseUrl)+e.appRelativeUrl,s="transport="+n;return!i&&e.groupsToken&&(s+="&groupsToken="+t.encodeURIComponent(e.groupsToken)),o?(c+=r?"/poll":"/reconnect",!i&&e.messageId&&(s+="&messageId="+t.encodeURIComponent(e.messageId))):c+="/connect",c+="?"+s,c=a.prepareQueryString(e,c),i||(c+="&tid="+Math.floor(11*Math.random())),c},maximizePersistentResponse:function(e){return{MessageId:e.C,Messages:e.M,Initialized:void 0!==e.S,ShouldReconnect:void 0!==e.T,LongPollDelay:e.L,GroupsToken:e.G}},updateGroups:function(e,t){t&&(e.groupsToken=t)},stringifySend:function(e,t){return"string"==typeof t||void 0===t||null===t?t:e.json.stringify(t)},ajaxSend:function(t,n){var o,i=a.stringifySend(t,n),l=r(t,"/send"),u=function(t,r){e(r).triggerHandler(s.onError,[c._.transportError(c.resources.sendFailed,r.transport,t,o),n])};return o=a.ajax(t,{url:l,type:"jsonp"===t.ajaxDataType?"GET":"POST",contentType:c._.defaultContentType,data:{data:i},success:function(e){var n;if(e){try{n=t._parseResponse(e)}catch(e){return u(e,t),void t.stop()}a.triggerReceived(t,n)}},error:function(e,n){"abort"!==n&&"parsererror"!==n&&u(e,t)}})},ajaxAbort:function(e,t){if(void 0!==e.transport){t=void 0===t||t;var n=r(e,"/abort");a.ajax(e,{url:n,async:t,timeout:1e3,type:"POST"}),e.log("Fired ajax abort async = "+t+".")}},ajaxStart:function(t,n){var o=function(e){var n=t._deferral;n&&n.reject(e)},i=function(n){t.log("The start request failed. Stopping the connection."),e(t).triggerHandler(s.onError,[n]),o(n),t.stop()};t._.startRequest=a.ajax(t,{url:r(t,"/start"),success:function(e,o,r){var a;try{a=t._parseResponse(e)}catch(t){return void i(c._.error(c._.format(c.resources.errorParsingStartResponse,e),t,r))}"started"===a.Response?n():i(c._.error(c._.format(c.resources.invalidStartResponse,e),null,r))},error:function(e,n,r){n!==u?i(c._.error(c.resources.errorDuringStartRequest,r,e)):(t.log("The start request aborted because connection.stop() was called."),o(c._.error(c.resources.stoppedDuringStartRequest,null,e)))}})},tryAbortStartRequest:function(e){e._.startRequest&&(e._.startRequest.abort(u),delete e._.startRequest)},tryInitialize:function(e,t,n){t.Initialized&&n?n():t.Initialized&&e.log("WARNING! The client received an init message after reconnecting.")},triggerReceived:function(t,n){t._.connectingMessageBuffer.tryBuffer(n)||e(t).triggerHandler(s.onReceived,[n])},processMessages:function(t,n,o){var r;a.markLastMessage(t),n&&(r=a.maximizePersistentResponse(n),a.updateGroups(t,r.GroupsToken),r.MessageId&&(t.messageId=r.MessageId),r.Messages&&(e.each(r.Messages,function(e,n){a.triggerReceived(t,n)}),a.tryInitialize(t,r,o)))},monitorKeepAlive:function(t){var n=t._.keepAliveData;n.monitoring?t.log("Tried to monitor keep alive but it's already being monitored."):(n.monitoring=!0,a.markLastMessage(t),t._.keepAliveData.reconnectKeepAliveUpdate=function(){a.markLastMessage(t)},e(t).bind(s.onReconnect,t._.keepAliveData.reconnectKeepAliveUpdate),t.log("Now monitoring keep alive with a warning timeout of "+n.timeoutWarning+", keep alive timeout of "+n.timeout+" and disconnecting timeout of "+t.disconnectTimeout))},stopMonitoringKeepAlive:function(t){var n=t._.keepAliveData;n.monitoring&&(n.monitoring=!1,e(t).unbind(s.onReconnect,t._.keepAliveData.reconnectKeepAliveUpdate),t._.keepAliveData={},t.log("Stopping the monitoring of the keep alive."))},startHeartbeat:function(e){e._.lastActiveAt=(new Date).getTime(),o(e)},markLastMessage:function(e){e._.lastMessageAt=(new Date).getTime()},markActive:function(e){return!!a.verifyLastActive(e)&&(e._.lastActiveAt=(new Date).getTime(),!0)},isConnectedOrReconnecting:function(e){return e.state===c.connectionState.connected||e.state===c.connectionState.reconnecting},ensureReconnectingState:function(t){return!0===l(t,c.connectionState.connected,c.connectionState.reconnecting)&&e(t).triggerHandler(s.onReconnecting),t.state===c.connectionState.reconnecting},clearReconnectTimeout:function(e){e&&e._.reconnectTimeout&&(t.clearTimeout(e._.reconnectTimeout),delete e._.reconnectTimeout)},verifyLastActive:function(t){if((new Date).getTime()-t._.lastActiveAt>=t.reconnectWindow){var n=c._.format(c.resources.reconnectWindowTimeout,new Date(t._.lastActiveAt),t.reconnectWindow);return t.log(n),e(t).triggerHandler(s.onError,[c._.error(n,"TimeoutException")]),t.stop(!1,!1),!1}return!0},reconnect:function(e,n){var o=c.transports[n];if(a.isConnectedOrReconnecting(e)&&!e._.reconnectTimeout){if(!a.verifyLastActive(e))return;e._.reconnectTimeout=t.setTimeout(function(){a.verifyLastActive(e)&&(o.stop(e),a.ensureReconnectingState(e)&&(e.log(n+" reconnecting."),o.start(e)))},e.reconnectDelay)}},handleParseFailure:function(t,n,o,r,i){var a=c._.transportError(c._.format(c.resources.parseFailed,n),t.transport,o,i);r&&r(a)?t.log("Failed to parse server response while attempting to connect."):(e(t).triggerHandler(s.onError,[a]),t.stop())},initHandler:function(e){return new i(e)},foreverFrame:{count:0,connections:{}}}}(window.jQuery,window),function(e,t){var n=e.signalR,o=e.signalR.events,r=e.signalR.changeState,i=n.transports._logic;n.transports.webSockets={name:"webSockets",supportsKeepAlive:function(){return!0},send:function(t,r){var a=i.stringifySend(t,r);try{t.socket.send(a)}catch(i){e(t).triggerHandler(o.onError,[n._.transportError(n.resources.webSocketsInvalidState,t.transport,i,t.socket),r])}},start:function(a,c,s){var l,u=!1,d=this,p=!c,g=e(a);t.WebSocket?a.socket||(l=a.webSocketServerUrl?a.webSocketServerUrl:a.wsProtocol+a.host,l+=i.getUrl(a,this.name,p),a.log("Connecting to websocket endpoint '"+l+"'."),a.socket=new t.WebSocket(l),a.socket.onopen=function(){u=!0,a.log("Websocket opened."),i.clearReconnectTimeout(a),!0===r(a,n.connectionState.reconnecting,n.connectionState.connected)&&g.triggerHandler(o.onReconnect)},a.socket.onclose=function(t){var r;this===a.socket&&(u&&void 0!==t.wasClean&&!1===t.wasClean?(r=n._.transportError(n.resources.webSocketClosed,a.transport,t),a.log("Unclean disconnect from websocket: "+(t.reason||"[no reason given]."))):a.log("Websocket closed."),s&&s(r)||(r&&e(a).triggerHandler(o.onError,[r]),d.reconnect(a)))},a.socket.onmessage=function(t){var n;try{n=a._parseResponse(t.data)}catch(e){return void i.handleParseFailure(a,t.data,e,s,t)}n&&(e.isEmptyObject(n)||n.M?i.processMessages(a,n,c):i.triggerReceived(a,n))}):s()},reconnect:function(e){i.reconnect(e,this.name)},lostConnection:function(e){this.reconnect(e)},stop:function(e){i.clearReconnectTimeout(e),e.socket&&(e.log("Closing the Websocket."),e.socket.close(),e.socket=null)},abort:function(e,t){i.ajaxAbort(e,t)}}}(window.jQuery,window),function(e,t){var n=e.signalR,o=e.signalR.events,r=e.signalR.changeState,i=n.transports._logic,a=function(e){t.clearTimeout(e._.reconnectAttemptTimeoutHandle),delete e._.reconnectAttemptTimeoutHandle};n.transports.serverSentEvents={name:"serverSentEvents",supportsKeepAlive:function(){return!0},timeOut:3e3,start:function(c,s,l){var u,d=this,p=!1,g=e(c),f=!s;if(c.eventSource&&(c.log("The connection already has an event source. Stopping it."),c.stop()),t.EventSource){u=i.getUrl(c,this.name,f);try{c.log("Attempting to connect to SSE endpoint '"+u+"'."),c.eventSource=new t.EventSource(u,{withCredentials:c.withCredentials})}catch(e){return c.log("EventSource failed trying to connect with error "+e.Message+"."),void(l?l():(g.triggerHandler(o.onError,[n._.transportError(n.resources.eventSourceFailedToConnect,c.transport,e)]),f&&d.reconnect(c)))}f&&(c._.reconnectAttemptTimeoutHandle=t.setTimeout(function(){!1===p&&c.eventSource.readyState!==t.EventSource.OPEN&&d.reconnect(c)},d.timeOut)),c.eventSource.addEventListener("open",function(){c.log("EventSource connected."),a(c),i.clearReconnectTimeout(c),!1===p&&(p=!0,!0===r(c,n.connectionState.reconnecting,n.connectionState.connected)&&g.triggerHandler(o.onReconnect))},!1),c.eventSource.addEventListener("message",function(e){var t;if("initialized"!==e.data){try{t=c._parseResponse(e.data)}catch(t){return void i.handleParseFailure(c,e.data,t,l,e)}i.processMessages(c,t,s)}},!1),c.eventSource.addEventListener("error",function(e){var r=n._.transportError(n.resources.eventSourceError,c.transport,e);this===c.eventSource&&(l&&l(r)||(c.log("EventSource readyState: "+c.eventSource.readyState+"."),e.eventPhase===t.EventSource.CLOSED?(c.log("EventSource reconnecting due to the server connection ending."),d.reconnect(c)):(c.log("EventSource error."),g.triggerHandler(o.onError,[r]))))},!1)}else l&&(c.log("This browser doesn't support SSE."),l())},reconnect:function(e){i.reconnect(e,this.name)},lostConnection:function(e){this.reconnect(e)},send:function(e,t){i.ajaxSend(e,t)},stop:function(e){a(e),i.clearReconnectTimeout(e),e&&e.eventSource&&(e.log("EventSource calling close()."),e.eventSource.close(),e.eventSource=null,delete e.eventSource)},abort:function(e,t){i.ajaxAbort(e,t)}}}(window.jQuery,window),function(e,t){var n,o,r=e.signalR,i=e.signalR.events,a=e.signalR.changeState,c=r.transports._logic,s=function(){var e=t.document.createElement("iframe");return e.setAttribute("style","position:absolute;top:0;left:0;width:0;height:0;visibility:hidden;"),e},l=(n=null,o=0,{prevent:function(){r._.ieVersion<=8&&(0===o&&(n=t.setInterval(function(){var e=s();t.document.body.appendChild(e),t.document.body.removeChild(e),e=null},1e3)),o++)},cancel:function(){1===o&&t.clearInterval(n),o>0&&o--}});r.transports.foreverFrame={name:"foreverFrame",supportsKeepAlive:function(){return!0},iframeClearThreshold:50,start:function(e,n,o){var r,i=this,a=c.foreverFrame.count+=1,u=s(),d=function(){e.log("Forever frame iframe finished loading and is no longer receiving messages."),o&&o()||i.reconnect(e)};t.EventSource?o&&(e.log("Forever Frame is not supported by SignalR on browsers with SSE support."),o()):(u.setAttribute("data-signalr-connection-id",e.id),l.prevent(),r=c.getUrl(e,this.name),r+="&frameId="+a,t.document.documentElement.appendChild(u),e.log("Binding to iframe's load event."),u.addEventListener?u.addEventListener("load",d,!1):u.attachEvent&&u.attachEvent("onload",d),u.src=r,c.foreverFrame.connections[a]=e,e.frame=u,e.frameId=a,n&&(e.onSuccess=function(){e.log("Iframe transport started."),n()}))},reconnect:function(e){var n=this;c.isConnectedOrReconnecting(e)&&c.verifyLastActive(e)&&t.setTimeout(function(){if(c.verifyLastActive(e)&&e.frame&&c.ensureReconnectingState(e)){var t=e.frame,o=c.getUrl(e,n.name,!0)+"&frameId="+e.frameId;e.log("Updating iframe src to '"+o+"'."),t.src=o}},e.reconnectDelay)},lostConnection:function(e){this.reconnect(e)},send:function(e,t){c.ajaxSend(e,t)},receive:function(t,n){var o,i,a;if(t.json!==t._originalJson&&(n=t._originalJson.stringify(n)),a=t._parseResponse(n),c.processMessages(t,a,t.onSuccess),t.state===e.signalR.connectionState.connected&&(t.frameMessageCount=(t.frameMessageCount||0)+1,t.frameMessageCount>r.transports.foreverFrame.iframeClearThreshold&&(t.frameMessageCount=0,(o=t.frame.contentWindow||t.frame.contentDocument)&&o.document&&o.document.body)))for(i=o.document.body;i.firstChild;)i.removeChild(i.firstChild)},stop:function(e){var n=null;if(l.cancel(),e.frame){if(e.frame.stop)e.frame.stop();else try{(n=e.frame.contentWindow||e.frame.contentDocument).document&&n.document.execCommand&&n.document.execCommand("Stop")}catch(t){e.log("Error occurred when stopping foreverFrame transport. Message = "+t.message+".")}e.frame.parentNode===t.document.documentElement&&t.document.documentElement.removeChild(e.frame),delete c.foreverFrame.connections[e.frameId],e.frame=null,e.frameId=null,delete e.frame,delete e.frameId,delete e.onSuccess,delete e.frameMessageCount,e.log("Stopping forever frame.")}},abort:function(e,t){c.ajaxAbort(e,t)},getConnection:function(e){return c.foreverFrame.connections[e]},started:function(t){!0===a(t,r.connectionState.reconnecting,r.connectionState.connected)&&e(t).triggerHandler(i.onReconnect)}}}(window.jQuery,window),function(e,t){var n=e.signalR,o=e.signalR.events,r=e.signalR.changeState,i=e.signalR.isDisconnecting,a=n.transports._logic;n.transports.longPolling={name:"longPolling",supportsKeepAlive:function(){return!1},reconnectDelay:3e3,start:function(c,s,l){var u=this,d=function(){d=e.noop,c.log("LongPolling connected."),s?s():c.log("WARNING! The client received an init message after reconnecting.")},p=function(e){return!!l(e)&&(c.log("LongPolling failed to connect."),!0)},g=c._,f=0,v=function(i){t.clearTimeout(g.reconnectTimeoutId),g.reconnectTimeoutId=null,!0===r(i,n.connectionState.reconnecting,n.connectionState.connected)&&(i.log("Raising the reconnect event"),e(i).triggerHandler(o.onReconnect))};c.pollXhr&&(c.log("Polling xhr requests already exists, aborting."),c.stop()),c.messageId=null,g.reconnectTimeoutId=null,g.pollTimeoutId=t.setTimeout(function(){!function r(s,l){var m=!(null===s.messageId),h=!l,S=a.getUrl(s,u.name,m,h,!0),b={};s.messageId&&(b.messageId=s.messageId),s.groupsToken&&(b.groupsToken=s.groupsToken),!0!==i(s)&&(c.log("Opening long polling request to '"+S+"'."),s.pollXhr=a.ajax(c,{xhrFields:{onprogress:function(){a.markLastMessage(c)}},url:S,type:"POST",contentType:n._.defaultContentType,data:b,timeout:c._.pollTimeout,success:function(n){var o,l,u,m=0;c.log("Long poll complete."),f=0;try{o=c._parseResponse(n)}catch(e){return void a.handleParseFailure(s,n,e,p,s.pollXhr)}null!==g.reconnectTimeoutId&&v(s),o&&(l=a.maximizePersistentResponse(o)),a.processMessages(s,o,d),l&&"number"===e.type(l.LongPollDelay)&&(m=l.LongPollDelay),!0!==i(s)&&(!(u=l&&l.ShouldReconnect)||a.ensureReconnectingState(s))&&(m>0?g.pollTimeoutId=t.setTimeout(function(){r(s,u)},m):r(s,u))},error:function(i,l){var d=n._.transportError(n.resources.longPollFailed,c.transport,i,s.pollXhr);if(t.clearTimeout(g.reconnectTimeoutId),g.reconnectTimeoutId=null,"abort"!==l){if(!p(d)){if(f++,c.state!==n.connectionState.reconnecting&&(c.log("An error occurred using longPolling. Status = "+l+".  Response = "+i.responseText+"."),e(s).triggerHandler(o.onError,[d])),(c.state===n.connectionState.connected||c.state===n.connectionState.reconnecting)&&!a.verifyLastActive(c))return;if(!a.ensureReconnectingState(s))return;g.pollTimeoutId=t.setTimeout(function(){r(s,!0)},u.reconnectDelay)}}else c.log("Aborted xhr request.")}}),m&&!0===l&&(g.reconnectTimeoutId=t.setTimeout(function(){v(s)},Math.min(1e3*(Math.pow(2,f)-1),36e5))))}(c)},250)},lostConnection:function(e){e.pollXhr&&e.pollXhr.abort("lostConnection")},send:function(e,t){a.ajaxSend(e,t)},stop:function(e){t.clearTimeout(e._.pollTimeoutId),t.clearTimeout(e._.reconnectTimeoutId),delete e._.pollTimeoutId,delete e._.reconnectTimeoutId,e.pollXhr&&(e.pollXhr.abort(),e.pollXhr=null,delete e.pollXhr)},abort:function(e,t){a.ajaxAbort(e,t)}}}(window.jQuery,window),function(e){function t(e){return e+c}function n(t){return e.isFunction(t)?null:"undefined"===e.type(t)?null:t}function o(e){for(var t in e)if(e.hasOwnProperty(t))return!0;return!1}function r(e,t){var n,r,i=e._.invocationCallbacks;o(i)&&e.log("Clearing hub invocation callbacks with error: "+t+"."),e._.invocationCallbackId=0,delete e._.invocationCallbacks,e._.invocationCallbacks={};for(r in i)n=i[r],n.method.call(n.scope,{E:t})}function i(e,t){return new i.fn.init(e,t)}function a(t,n){var o={qs:null,logging:!1,useDefaultPath:!0};return e.extend(o,n),(!t||o.useDefaultPath)&&(t=(t||"")+"/signalr"),new a.fn.init(t,o)}var c=".hubProxy",s=e.signalR;i.fn=i.prototype={init:function(e,t){this.state={},this.connection=e,this.hubName=t,this._={callbackMap:{}}},constructor:i,hasSubscriptions:function(){return o(this._.callbackMap)},on:function(n,o){var r=this,i=r._.callbackMap;return i[n=n.toLowerCase()]||(i[n]={}),i[n][o]=function(e,t){o.apply(r,t)},e(r).bind(t(n),i[n][o]),r},off:function(n,r){var i,a=this._.callbackMap;return(i=a[n=n.toLowerCase()])&&(i[r]?(e(this).unbind(t(n),i[r]),delete i[r],o(i)||delete a[n]):r||(e(this).unbind(t(n)),delete a[n])),this},invoke:function(t){var o=this,r=o.connection,i=function(e,t,n){for(var o=e.length,r=[],i=0;i<o;i+=1)e.hasOwnProperty(i)&&(r[i]=t.call(n,e[i],i,e));return r}(e.makeArray(arguments).slice(1),n),a={H:o.hubName,M:t,A:i,I:r._.invocationCallbackId},c=e.Deferred();return r._.invocationCallbacks[r._.invocationCallbackId.toString()]={scope:o,method:function(n){var i,a,l=o._maximizeHubResponse(n);e.extend(o.state,l.State),l.Progress?c.notifyWith?c.notifyWith(o,[l.Progress.Data]):r._.progressjQueryVersionLogged||(r.log("A hub method invocation progress update was received but the version of jQuery in use ("+e.prototype.jquery+") does not support progress updates. Upgrade to jQuery 1.7+ to receive progress notifications."),r._.progressjQueryVersionLogged=!0):l.Error?(l.StackTrace&&r.log(l.Error+"\n"+l.StackTrace+"."),i=l.IsHubException?"HubException":"Exception",(a=s._.error(l.Error,i)).data=l.ErrorData,r.log(o.hubName+"."+t+" failed to execute. Error: "+a.message),c.rejectWith(o,[a])):(r.log("Invoked "+o.hubName+"."+t),c.resolveWith(o,[l.Result]))}},r._.invocationCallbackId+=1,e.isEmptyObject(o.state)||(a.S=o.state),r.log("Invoking "+o.hubName+"."+t),r.send(a),c.promise()},_maximizeHubResponse:function(e){return{State:e.S,Result:e.R,Progress:e.P?{Id:e.P.I,Data:e.P.D}:null,Id:e.I,IsHubException:e.H,Error:e.E,StackTrace:e.T,ErrorData:e.D}}},i.fn.init.prototype=i.fn,a.fn=a.prototype=e.connection(),a.fn.init=function(n,o){var i={qs:null,logging:!1,useDefaultPath:!0},a=this;e.extend(i,o),e.signalR.fn.init.call(a,n,i.qs,i.logging),a.proxies={},a._.invocationCallbackId=0,a._.invocationCallbacks={},a.received(function(n){var o,r,i,c,s,l;n&&(void 0!==n.P?(i=n.P.I.toString(),(c=a._.invocationCallbacks[i])&&c.method.call(c.scope,n)):void 0!==n.I?(i=n.I.toString(),(c=a._.invocationCallbacks[i])&&(a._.invocationCallbacks[i]=null,delete a._.invocationCallbacks[i],c.method.call(c.scope,n))):(o=this._maximizeClientHubInvocation(n),a.log("Triggering client hub event '"+o.Method+"' on hub '"+o.Hub+"'."),s=o.Hub.toLowerCase(),l=o.Method.toLowerCase(),r=this.proxies[s],e.extend(r.state,o.State),e(r).triggerHandler(t(l),[o.Args])))}),a.error(function(e,t){var n,o;t&&(n=t.I,(o=a._.invocationCallbacks[n])&&(a._.invocationCallbacks[n]=null,delete a._.invocationCallbacks[n],o.method.call(o.scope,{E:e})))}),a.reconnecting(function(){a.transport&&"webSockets"===a.transport.name&&r(a,"Connection started reconnecting before invocation result was received.")}),a.disconnected(function(){r(a,"Connection was disconnected before invocation result was received.")})},a.fn._maximizeClientHubInvocation=function(e){return{Hub:e.H,Method:e.M,Args:e.A,State:e.S}},a.fn._registerSubscribedHubs=function(){var t=this;t._subscribedToHubs||(t._subscribedToHubs=!0,t.starting(function(){var n=[];e.each(t.proxies,function(e){this.hasSubscriptions()&&(n.push({name:e}),t.log("Client subscribed to hub '"+e+"'."))}),0===n.length&&t.log("No hubs have been subscribed to.  The client will not receive data from hubs.  To fix, declare at least one client side function prior to connection start for each hub you wish to subscribe to."),t.data=t.json.stringify(n)}))},a.fn.createHubProxy=function(e){e=e.toLowerCase();var t=this.proxies[e];return t||(t=i(this,e),this.proxies[e]=t),this._registerSubscribedHubs(),t},a.fn.init.prototype=a.fn,e.hubConnection=a}(window.jQuery,window),window.jQuery.signalR.version="2.3.0";