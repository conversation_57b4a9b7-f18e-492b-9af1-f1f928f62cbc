﻿angular.module("ui.bootstrap.datetimepicker", ["ui.bootstrap.dateparser", "ui.bootstrap.position"]).constant("uiDatetimePickerConfig", { dateFormat: "yyyy-MM-dd HH:mm", defaultTime: "00:00:00", html5Types: { date: "yyyy-MM-dd", "datetime-local": "yyyy-MM-ddTHH:mm:ss.sss", month: "yyyy-MM" }, initialPicker: "date", reOpenDefault: !1, disableFocusStealing: !1, enableDate: !0, enableTime: !0, buttonBar: { show: !0, now: { show: !0, text: "Now", cls: "btn-sm btn-info" }, today: { show: !0, text: "Today", cls: "btn-sm btn-info" }, clear: { show: !0, text: "Clear", cls: "btn-sm btn-danger" }, date: { show: !0, text: "Date", cls: "btn-sm btn-darker-1" }, time: { show: !0, text: "Time", cls: "btn-sm btn-darker-1" }, close: { show: !0, text: "Okay", cls: "btn-sm btn-success" }, cancel: { show: !1, text: "Cancel", cls: "btn-sm btn-default" } }, closeOnDateSelection: !0, closeOnTimeNow: !0, appendToBody: !1, altInputFormats: [], ngModelOptions: { timezone: null }, saveAs: !1, readAs: !1 }).controller("DateTimePickerController", ["$scope", "$element", "$attrs", "$compile", "$parse", "$document", "$timeout", "$uibPosition", "dateFilter", "uibDateParser", "uiDatetimePickerConfig", "$rootScope", function (e, t, n, a, i, o, s, r, l, c, p, u) { var d, m, g, f = p.dateFormat, b = {}, D = [], w = angular.isDefined(n.closeOnDateSelection) ? e.$parent.$eval(n.closeOnDateSelection) : p.closeOnDateSelection, k = angular.isDefined(n.closeOnTimeNow) ? e.$parent.$eval(n.closeOnTimeNow) : p.closeOnTimeNow, h = angular.isDefined(n.datepickerAppendToBody) ? e.$parent.$eval(n.datepickerAppendToBody) : p.appendToBody, v = angular.isDefined(n.altInputFormats) ? e.$parent.$eval(n.altInputFormats) : p.altInputFormats, y = angular.isDefined(n.saveAs) ? e.$parent.$eval(n.saveAs) || n.saveAs : p.saveAs, O = angular.isDefined(n.readAs) ? e.$parent.$eval(n.readAs) : p.readAs, $ = null; function x(n) { var a = g[0], i = t[0].contains(n.target), o = void 0 !== a.contains && a.contains(n.target); !e.isOpen || i || o || e.$apply(function () { e.close(!1) }) } function S(n) { 27 === n.which && e.isOpen ? (n.preventDefault(), n.stopPropagation(), e.$apply(function () { e.close(!1) }), t[0].focus()) : 40 !== n.which || e.isOpen || (n.preventDefault(), n.stopPropagation(), e.$apply(function () { e.isOpen = !0 })) } function T(t) { var n = c.parse(t, f, e.date); if (isNaN(n)) for (var a = 0; a < v.length; a++) if (n = c.parse(t, v[a], e.date), !isNaN(n)) return n; return n } function P(e) { if (angular.isNumber(e) && !isNaN(e) && (e = new Date(e)), !e) return null; if (angular.isDate(e) && !isNaN(e)) return e; if (angular.isString(e)) { var t = T(e); return isNaN(t) ? void 0 : c.toTimezone(t, m.getOption("timezone")) } } function M(t) { return !(e.datepickerOptions.minDate && t < e.datepickerOptions.minDate) && !(e.datepickerOptions.maxDate && t > e.datepickerOptions.maxDate) } function N(e, t) { var a = e || t; return !(n.ngRequired || n.required || a) || (angular.isNumber(a) && (a = new Date(a)), !a || (angular.isDate(a) && !isNaN(a) ? M(a) : angular.isDate(new Date(a)) && !isNaN(new Date(a).valueOf()) ? M(new Date(a)) : !!angular.isString(a) && (!isNaN(T(t)) && M(T(t))))) } this.init = function (i) { if (m = function (e) { var t; angular.version.minor < 6 ? (t = angular.isObject(e.$options) ? e.$options : { timezone: null }).getOption = function (e) { return t[e] } : t = e.$options; return t }(d = i), e.buttonBar = angular.isDefined(n.buttonBar) ? e.$parent.$eval(n.buttonBar) : p.buttonBar, e.enableDate = angular.isDefined(e.enableDate) ? e.enableDate : p.enableDate, e.enableTime = angular.isDefined(e.enableTime) ? e.enableTime : p.enableTime, e.initialPicker = angular.isDefined(n.initialPicker) ? n.initialPicker : e.enableDate ? p.initialPicker : "time", e.reOpenDefault = angular.isDefined(n.reOpenDefault) ? n.reOpenDefault : p.reOpenDefault, e.disableFocusStealing = angular.isDefined(n.disableFocusStealing) ? n.disableFocusStealing : p.disableFocusStealing, "date" === e.initialPicker && !e.enableDate) throw new Error("datetimePicker can't have initialPicker set to date and have enableDate set to false."); e.showPicker = e.enableDate ? e.initialPicker : "time"; var s = !1; if (p.html5Types[n.type] ? (f = p.html5Types[n.type], s = !0) : (f = n.datetimePicker || p.dateFormat, n.$observe("datetimePicker", function (e) { var t = e || p.dateFormat; if (t !== f && (f = t, d.$modelValue = null, !f)) throw new Error("datetimePicker must have a date format specified.") })), !f) throw new Error("datetimePicker must have a date format specified."); var r = angular.element('<div date-picker-wrap><div uib-datepicker></div></div><div time-picker-wrap><div uib-timepicker style="margin:0 auto"></div></div>'); r.attr({ "ng-model": "date", "ng-change": "dateSelection(date)" }); var l = angular.element(r.children()[0]); e.datepickerOptions || (e.datepickerOptions = {}), s && "month" === n.type && (e.datepickerOptions.datepickerMode = "month", e.datepickerOptions.minMode = "month"), l.attr("datepicker-options", "datepickerOptions"), angular.isDefined(e.datepickerOptions.datepickerMode) || (e.datepickerOptions.datepickerMode = "day"); var u = angular.element(r.children()[1]); e.timepickerOptions || (e.timepickerOptions = { showMeridian: !0 }); for (var b in e.timepickerOptions) u.attr(b.replace(/([A-Z])/g, function (e) { return "-" + e.toLowerCase() }), "timepickerOptions." + b); angular.forEach(["minDate", "maxDate", "initDate"], function (t) { e.datepickerOptions[t] && ("minDate" === t ? e.timepickerOptions.min ? u.attr("min", "timepickerOptions.min") : u.attr("min", "datepickerOptions.minDate") : "maxDate" === t && (e.timepickerOptions.max ? u.attr("max", "timepickerOptions.max") : u.attr("max", "datepickerOptions.maxDate"))) }), s ? d.$formatters.push(function (t) { return e.date = c.fromTimezone(t, m.getOption("timezone")), t }) : (d.$$parserName = "datetime", d.$validators.datetime = N, d.$parsers.unshift(P), d.$formatters.push(function (t) { return d.$isEmpty(t) ? (e.date = t, t) : (e.date = c.fromTimezone(t, m.getOption("timezone")), f = f.replace(/M!/, "MM").replace(/d!/, "dd"), c.filter(e.date, f)) })), y && (angular.isFunction(y) ? d.$parsers.push(y) : d.$parsers.push(function (e) { if (!e || angular.isString(e) || !angular.isDate(e) || isNaN(e)) return e; if ("ISO" === y) return e.toISOString(); if ("json" === y) return e.toJSON(); if ("number" === y) return e.valueOf(); return s ? c.fromTimezone(e, m.getOption("timezone")).toLocaleString() : (f = f.replace(/M!/, "MM").replace(/d!/, "dd"), c.filter(c.fromTimezone(e, m.getOption("timezone")), f)) }), angular.isFunction(O) ? d.$formatters.push(O) : d.$formatters.push(function (e) { if (d.$isEmpty(e)) return e; var t = new Date(e); return angular.isDate(t) && !isNaN(t) ? t : e })), d.$viewChangeListeners.push(function () { if (e.timepickerOptions.min) { var t = new Date(e.timepickerOptions.min).getHours(), n = new Date(e.timepickerOptions.min).getMinutes(), a = new Date(e.date); a.setHours(t), a.setMinutes(n), e.timepickerOptions.min = a } if (e.timepickerOptions.max) { var i = new Date(e.timepickerOptions.max).getHours(), o = new Date(e.timepickerOptions.max).getMinutes(), s = new Date(e.date); s.setHours(i), s.setMinutes(o), e.timepickerOptions.max = s } e.date = T(d.$viewValue) }), t.bind("keydown", S), g = a(r)(e), r.remove(), h ? o.find("body").append(g) : t.after(g) }, e.getText = function (t) { return e.buttonBar[t].text || p.buttonBar[t].text }, e.getClass = function (t) { return e.buttonBar[t].cls || p.buttonBar[t].cls }, e.keydown = function (n) { 27 === n.which && (n.preventDefault(), n.stopPropagation(), e.close(!1), s(function () { t[0].focus() }, 0)) }, e.doShow = function (t) { return angular.isDefined(e.buttonBar[t].show) ? e.buttonBar[t].show : p.buttonBar[t].show }, e.dateSelection = function (a, i) { if (e.enableTime && "time" === e.showPicker) if (a || null != a) { if (angular.isDefined(e.date) && null != e.date || (e.date = new Date), a && null != a) { var o = new Date(e.date); o.setHours(a.getHours()), o.setMinutes(a.getMinutes()), o.setSeconds(a.getSeconds()), o.setMilliseconds(a.getMilliseconds()), a = o } } else e.oldDate = e.date; if (angular.isDefined(a)) { if (!e.date) { var s = angular.isDefined(n.defaultTime) ? n.defaultTime : p.defaultTime, r = new Date("2001/01/01 " + s); isNaN(r) || null == a || (a.setHours(r.getHours()), a.setMinutes(r.getMinutes()), a.setSeconds(r.getSeconds()), a.setMilliseconds(r.getMilliseconds())) } e.date = a, a && e.oldDate && (a.setDate(e.oldDate.getDate()), a.setMonth(e.oldDate.getMonth()), a.setFullYear(e.oldDate.getFullYear()), delete e.oldDate) } var c = e.date ? l(e.date, f) : null; t.val(c), d.$setViewValue(c), w && ("time" !== e.showPicker && null != c ? e.enableTime ? e.open("time") : e.close(!1) : k && "time" === e.showPicker && null != c && "now" === i && e.close(!1)) }, e.$watch("isOpen", function (n) { if (e.dropdownStyle = { display: n ? "block" : "none" }, n) { b.openDate = e.date; var a = h ? r.offset(t) : r.position(t); e.dropdownStyle.top = h ? a.top + t.prop("offsetHeight") + "px" : void 0, e.dropdownStyle.left = a.left + "px", s(function () { e.disableFocusStealing || e.$broadcast("uib:datepicker.focus"), o.bind("click", x) }, 0, !1), e.open(e.showPicker) } else o.unbind("click", x) }), e.isDisabled = function (t) { "today" !== t && "now" !== t || (t = c.fromTimezone(new Date, m.getOption("timezone"))); var n = {}; return angular.forEach(["minDate", "maxDate"], function (t) { e.datepickerOptions[t] ? angular.isDate(e.datepickerOptions[t]) ? n[t] = c.fromTimezone(new Date(e.datepickerOptions[t]), m.getOption("timezone")) : n[t] = new Date(l(e.datepickerOptions[t], "medium")) : n[t] = null }), e.datepickerOptions && n.minDate && e.compare(t, n.minDate) < 0 || n.maxDate && e.compare(t, n.maxDate) > 0 }, e.compare = function (e, t) { return new Date(e.getFullYear(), e.getMonth(), e.getDate()) - new Date(t.getFullYear(), t.getMonth(), t.getDate()) }, e.select = function (t, n) { angular.isDefined(n) && (n.preventDefault(), n.stopPropagation()); var a = null; if ("today" === t || "now" === t) { var i = new Date; angular.isDate(e.date) ? ((a = new Date(e.date)).setFullYear(i.getFullYear(), i.getMonth(), i.getDate()), a.setHours(i.getHours(), i.getMinutes(), i.getSeconds(), i.getMilliseconds())) : a = i } e.dateSelection(a, t) }, e.cancel = function (n) { angular.isDefined(n) && (n.preventDefault(), n.stopPropagation()), t.val(l($, f)), d.$setViewValue(l($, f)), e.close(!1) }, e.open = function (n, a) { angular.isDefined(a) && (a.preventDefault(), a.stopPropagation()), $ = t.val(), s(function () { e.showPicker = n }, 0), "time" === n && s(function () { e.date = T(d.$viewValue) }, 50) }, e.close = function (n, a) { angular.isDefined(a) && (a.preventDefault(), a.stopPropagation()), e.isOpen = !1, e.enableDate && e.enableTime && (e.showPicker = !1 === e.reOpenDefault ? "date" : e.reOpenDefault), "blur" === m.getOption("updateOn") && (t[0].focus(), s(function () { t[0].blur() }, 50)), angular.isDefined(n) ? e.whenClosed({ args: { closePressed: n, openDate: b.openDate || null, closeDate: e.date } }) : t[0].focus() }, e.$on("$destroy", function () { !0 === e.isOpen && (u.$$phase || e.$apply(function () { e.close() })), D.forEach(function (e) { e() }), g.remove(), t.unbind("keydown", S), o.unbind("click", x) }) }]).directive("datetimePicker", function () { return { restrict: "A", require: ["ngModel", "datetimePicker"], controller: "DateTimePickerController", scope: { isOpen: "=?", datepickerOptions: "=?", timepickerOptions: "=?", enableDate: "=?", enableTime: "=?", initialPicker: "=?", reOpenDefault: "=?", whenClosed: "&" }, link: function (e, t, n, a) { var i = a[0]; a[1].init(i) } } }).directive("datePickerWrap", function () { return { restrict: "EA", replace: !0, transclude: !0, templateUrl: "template/date-picker.html" } }).directive("timePickerWrap", function () { return { restrict: "EA", replace: !0, transclude: !0, templateUrl: "template/time-picker.html" } }), angular.module("ui.bootstrap.datetimepicker").run(["$templateCache", function (e) { "use strict"; e.put("template/date-picker.html", "<ul class=\"dropdown-menu dropdown-menu-left datetime-picker-dropdown\" ng-if=\"isOpen && showPicker == 'date'\" ng-style=dropdownStyle style=left:inherit ng-keydown=keydown($event) ng-click=\"$event.preventDefault(); $event.stopPropagation()\"><li style=\"padding:0 5px 5px 5px\" class=date-picker-menu><div ng-transclude></div></li><li style=padding:5px ng-if=buttonBar.show><span class=\"btn-group pull-left\" style=margin-right:10px ng-if=\"doShow('today') || doShow('clear')\"><button type=button class=btn ng-class=\"getClass('today')\" ng-if=\"doShow('today')\" ng-click=\"select('today', $event)\" ng-disabled=\"isDisabled('today')\">{{ getText('today') }}</button> <button type=button class=btn ng-class=\"getClass('clear')\" ng-if=\"doShow('clear')\" ng-click=\"select('clear', $event)\">{{ getText('clear') }}</button></span> <span class=\"btn-group pull-right\" ng-if=\"(doShow('time') && enableTime) || doShow('close') || doShow('cancel')\"><button type=button class=btn ng-class=\"getClass('time')\" ng-if=\"doShow('time') && enableTime\" ng-click=\"open('time', $event)\">{{ getText('time')}}</button> <button type=button class=btn ng-class=\"getClass('close')\" ng-if=\"doShow('close')\" ng-click=\"close(true, $event)\">{{ getText('close') }}</button> <button type=button class=btn ng-class=\"getClass('cancel')\" ng-if=\"doShow('cancel')\" ng-click=cancel($event)>{{ getText('cancel') }}</button></span> <span class=clearfix></span></li></ul>"), e.put("template/time-picker.html", "<ul class=\"dropdown-menu dropdown-menu-left datetime-picker-dropdown\" style=\"width:84%\" ng-if=\"isOpen && showPicker == 'time'\" ng-style=dropdownStyle style=left:inherit ng-keydown=keydown($event) ng-click=\"$event.preventDefault(); $event.stopPropagation()\"><li style=\"padding:5px 5px 5px 5px\" class=time-picker-menu><div ng-transclude></div></li><li style=padding:5px ng-if=buttonBar.show><span class=\"btn-group pull-left\" style=margin-right:10px ng-if=\"doShow('now') || doShow('clear')\"><button type=button class=btn ng-class=\"getClass('now')\" ng-if=\"doShow('now')\" ng-click=\"select('now', $event)\" ng-disabled=\"isDisabled('now')\">{{ getText('now') }}</button> <button type=button class=btn ng-class=\"getClass('clear')\" ng-if=\"doShow('clear')\" ng-click=\"select('clear', $event)\">{{ getText('clear') }}</button></span> <span class=\"btn-group pull-right\" ng-if=\"(doShow('date') && enableDate) || doShow('close') || doShow('cancel')\"><button type=button class=btn ng-class=\"getClass('date')\" ng-if=\"doShow('date') && enableDate\" ng-click=\"open('date', $event)\">{{ getText('date')}}</button> <button type=button class=btn ng-class=\"getClass('close')\" ng-if=\"doShow('close')\" ng-click=\"close(true, $event)\">{{ getText('close') }}</button> <button type=button class=btn ng-class=\"getClass('cancel')\" ng-if=\"doShow('cancel')\" ng-click=cancel($event)>{{ getText('cancel') }}</button></span> <span class=clearfix></span></li></ul>") }]), "object" == typeof exports && "object" == typeof module && (module.exports = "ui.bootstrap.datetimepicker");