﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Mvc;
using PCSMS.Common;
using PCSMS.Models;
using PCSMS.Models.Models_SProvider;
using PCSMS.Services.Services_Shared;
using System.Text.RegularExpressions;

namespace PCSMS.Services.Services_SProvider
{
    public class SP_ProfileServices : ISP_ProfileServices
    {
        private readonly PCSMSDbContext _context;
        private readonly IEntityService<SP_Profile> _services;

        public SP_ProfileServices()
        {
            _context = new PCSMSDbContext();
            _services = new EntityService<SP_Profile>(_context);
        }


        //Public Services :
        public JsonResult SaveSP_Profile(SP_Profile obj)
        {

            string message;
            try
            {
                if (_services.DoesExist(x => x.OrganizationName == obj.OrganizationName))
                {
                    Generator.IsReport = "OrganizationExists";
                    message = "This Organization already exists !";
                }
                else
                {
                    _services.Save(obj);
                    _services.SaveChanges();

                    Generator.IsReport = "Ok";
                    message = "Profile created successfully !";
                }
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UpdateSP_Profile(SP_Profile obj)
        {
            string message;
            List<object> avoidProperties = new List<object>();

            try
            {
                _services.Update(obj, avoidProperties);
                _services.SaveChanges();
                Generator.IsReport = "Ok";
                message = "Profile updated successfully !";
            }
            catch (Exception ex)
            {
                message = ex.Message;
                Generator.IsReport = "NotOk";
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetSP_ProfileDetails()
        {
            return new JsonResult
            {
                Data = _context.SP_Profile
                .GroupJoin(_context.CompanyType,
                x => x.CompanyTypeId,
                y => y.Id,
                (x, y) => new { SP_Profile = x, CompanyType = y })
                 .SelectMany(x => x.CompanyType.DefaultIfEmpty(),
                (x, y) => new { x.SP_Profile, CompanyType = y })
                .Select(x => new
                {
                    x.SP_Profile.Id,
                    x.SP_Profile.OrganizationName,
                    x.SP_Profile.OrganizationAddress,
                    x.SP_Profile.OrganizationPhone,
                    x.SP_Profile.OrganizationMobile,
                    x.SP_Profile.OrganizationWebsite,
                    x.SP_Profile.OrganizationEmail,
                    x.SP_Profile.OrganizationLat,
                    x.SP_Profile.OrganizationLong,
                    x.SP_Profile.OrganizationLogo,
                    x.SP_Profile.CompanyTypeId,
                    x.CompanyType.CompanyTypeName,
                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UploadSP_Logo()
        {
            //Add System.Web to Services References

            string message = "";
            int iUploadedCnt = 0;


            string myPath = "";
            myPath = System.Web.Hosting.HostingEnvironment.MapPath("~/SProvider_Images/Profile_Images/");

            var obj = _context.SP_Profile.Select(x => new
            {
                x.Id,
                x.OrganizationLogo,
                x.OrganizationName
            }).FirstOrDefault();

            var getOldImageName = obj.OrganizationLogo;
            string trimmedFirstName = Regex.Replace(obj.OrganizationName, " ", "");



            string randNum = GenericServices.CreateRandomNumberWithLetter();

            System.Web.HttpFileCollection httpFileCollection = System.Web.HttpContext.Current.Request.Files;
            if (httpFileCollection.Count > 0)
            {

                try
                {
                    for (int iCnt = 0; iCnt <= httpFileCollection.Count - 1; iCnt++)
                    {
                        System.Web.HttpPostedFile hpf = httpFileCollection[iCnt];

                        var imaginaryNameWithoutExtension = trimmedFirstName + "_" + obj.Id + "_" + randNum;
                        var extension = Path.GetExtension(hpf.FileName);
                        var newImageName = imaginaryNameWithoutExtension + extension;

                        if (extension != ".pdf")
                        {
                            if (getOldImageName != null)
                            {
                                File.Delete(myPath + getOldImageName);
                            }

                            //now moving new image to folder:
                            hpf.SaveAs(myPath + newImageName);
                            _context.Database.ExecuteSqlCommand($"UPDATE [SP_Profile] SET OrganizationLogo = '{newImageName}' WHERE Id = {obj.Id}");
                            _context.SaveChanges();


                            iUploadedCnt = iUploadedCnt + 1;


                            Generator.IsReport = "Ok";
                            message = "Image(s) Uploaded Successfully";

                        }
                        else
                        {
                            message = "This is not an image type.";
                            Generator.IsReport = "NotOk";
                        }

                    }
                }
                catch (Exception ex)
                {
                    message = ex.Message;
                    Generator.IsReport = "NotOk";
                }
            }
            else
            {
                Generator.IsReport = "NotOk";
                message = "There is no image to be saved";
            }

            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
    }

    public interface ISP_ProfileServices
    {
        JsonResult SaveSP_Profile(SP_Profile obj);
        JsonResult UpdateSP_Profile(SP_Profile obj);
        JsonResult GetSP_ProfileDetails();
        JsonResult UploadSP_Logo();
    }
}
