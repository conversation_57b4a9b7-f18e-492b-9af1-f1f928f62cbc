﻿ 
 
 
 visit here and check your quota for google map api (Make sure your are logged in)

 https://console.cloud.google.com/


 working api (at this moment)
 from sajeeb:
 <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDuNHRed3RS3qqxSH9Yk2PFr60DI0rsSG8&libraries=places"></script>
 the below api has been used when i started this onlineTracking project, it is working too
 <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyB9XKuAdYKmQcN-j_YpiU55zjKFWEKw43w&libraries=places" defer type="text/javascript"></script>

 the one NOT working (this is created by me) :
 Note: Google asking me to enable Billing, then the quota will increase, at this moment it is only 1 for a day
 <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAsRq9xc86mRpawSWZQb3BnN7KTlA3hE6U&libraries=places" defer type="text/javascript"></script>

 Check here to see pricing
 https://developers.google.com/maps/previous-pricing