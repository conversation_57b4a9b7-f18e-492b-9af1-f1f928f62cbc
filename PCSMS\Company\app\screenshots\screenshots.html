﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>


<!-- CONTENT -->
<!-- ========================================================= -->
<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-clone" aria-hidden="true"></i><a ui-sref="device"> Screenshot (s)</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="col-xs-12">
        <div class="container">
            <div class="well color-danger text-italic pull-right text-bold">Images below marked in 'red border' indicates no mouse and keystroke activity</div>
        </div>
    <div class="animated fadeInUp">

        <div class="panel panel-default">

            <div class="col-xs-12">
                <div class="panel panel-default " ng-repeat="item in MyList">
                    <div class="panel-heading">
                        <h4 class="text-bold color-primary">{{item.DeviceName}}</h4>
                        <!--<h4 class="text-bold color-primary">{{item.LastUser}}</h4>-->
                    </div>

                    <div class="panel-body" ng-if="item.Screenshots.length >0 ">
                        <div class="col-xs-1">
                            <label class="left carousel-control btn-pointer arrow" ng-click="GetNew(item.DeviceUniqueId,item.Screenshots[0].Id)"><i class="glyphicon glyphicon-chevron-left"></i></label>
                        </div>
                        <div id="myId" class="col-xs-2 custom-height logEntry" ng-repeat="x in item.Screenshots" ng-class="{'custom-greenish-border':x.MouseClick >0 || x.KeyStroke > 0, 'custom-red-border':x.MouseClick==0 && x.KeyStroke == 0}" >
                            <label class="color-primary">{{x.FirstName}} {{x.LastName}}</label><br />
                            <label class="color-primary">{{x.CapturedOn | date : "MMM d, y h:mm:ss a"}}</label><br />
                            <label class="color-primary"><a><img src="{{x.Url}}" width="150" ng-click="openFilePreviewModal(x)" style="border: 1px solid #cbc3c3" /></a></label><br>
                            <div class="panel-body">
                                <a ng-hide="x.Status=='Deleted'"><i class="fa fa-trash-o fa-2x" style="color: red" ng-click="DeleteScreenshot(x)"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <a><i class="fa fa-info-circle fa-2x" ng-click="openFilePreviewModal(x)"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <a ng-hide="x.Status=='Deleted'"><i class="fa fa-download fa-2x " style="color: #1667ac" ng-click="DownloadScreenshot(x)"></i></a>
                            </div>
                        </div>
                        <div class="col-xs-1">
                            <label class="right carousel-control btn-pointer arrow" ng-click="GetPrevious(item.DeviceUniqueId,item.Screenshots[item.Screenshots.length - 1].Id)" data-slide="next"><i class="glyphicon glyphicon-chevron-right"></i></label>
                        </div>
                        <!--<span class="pull-right"><button class="btn btn-primary btn-sm" ng-click="RefreshSS(item.DeviceUniqueId)">Update / Refresh</button></span>-->
                    </div>
                </div>
            </div>
        </div>
        <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    </div>
</div>
    <div class="modal fade" id="FilePreviewModal" tabindex="-1" role="dialog" aria-labelledby="modal-label" data-backdrop="static" data-keyboard="false" style="padding-top: 20px">
    <div class="{{file.Status==null?'modal-dialog modal-80':'modal-dialog  modal-50'}}" role="document">
        <div class="modal-content">
            <div class="modal-header" style="height: 65px">
                <button type="button" class="custom-close" ng-click="cancelFilePreviewModal()" tooltip-placement="left" uib-tooltip="Close" style="margin-top: 0px;" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" style="margin-top: 1.6%">Screenshot Details</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-primary panel-bordered">
                    <div class="panel-body">
                        <!--File Preview For Images-->
                        <img class="img-responsive img-rounded animated fadeIn" style="margin: auto auto" src="{{file.Url}}" ng-if="file.Status==null" />
                        <!--<img class="img-responsive img-rounded animated fadeIn" style="width: 200px" src="{{file.Url}}" ng-if="file.Status=='Deleted'"/>-->
                        <p class="text-bold support-color text-italic" style="padding-left: 15px;" ng-if="file.Status=='Deleted'">The screenshot has been deleted</p>
                        <br />
                        <div class="col-md-6">
                            <table class="table table-condensed">
                                <tbody>
                                    <tr>
                                        <td>
                                            Captured On
                                        </td>
                                        <td>
                                            {{file.CapturedOn | date:"medium"}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Mouse Click
                                        </td>
                                        <td>
                                            {{file.MouseClick}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Key Stroke
                                        </td>
                                        <td>
                                            {{file.KeyStroke}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Device Name
                                        </td>
                                        <td>
                                            {{file.DeviceName}}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-condensed">
                                <tbody>
                                    <tr>
                                        <td width="25%">
                                            User Full Name
                                        </td>
                                        <td width="35%">
                                            {{file.UserFullName}}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            User Photo
                                        </td>
                                        <td>
                                            <img class="img-responsive animated fadeIn" style="width: 21%; border: 1px solid #dbe0de; padding: 5px; border-radius: 15px" src="{{file.UserPhotoUrl}}">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Designation
                                        </td>
                                        <td>
                                            {{file.Designation}}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
            <div class="modal-footer">
                <div class="row row-modal">
                    <button type="button" class="btn btn-wide btn-primary pull-right btn-right-border-radius"
                            ng-click="DownloadScreenshot(file)"
                            ng-if="file.Status == null">
                        Download &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-download fa-1x"></i>
                    </button>
                    <button type="button"
                            class="btn  btn-default btn-wide pull-right btn-left-border-radius"
                            ng-click="cancelFilePreviewModal()"
                            role="button">
                        Close
                    </button>

                </div>
            </div>
        </div>
    </div>
</div>

</div>
<style>
    .custom-black-border {
        border: 1px solid black;
    }
    .custom-greenish-border {
        border: 1px solid #4ca492;
    }

    .custom-red-border {
        border: 1px solid red;
    }

    .custom-height {
        min-height: 205px;
    }

    .btn-pointer {
        cursor: pointer;
    }

    .arrow {
        color: #189279;
        margin-top: 85px !important;
    }

    .logEntry.ng-enter {
        -webkit-transition: 1s;
        transition: 2s;
        opacity: 0;
    }

    .logEntry.ng-enter-active {
        opacity: 1;
    }


    
</style>