﻿using System.Linq;
using System.Web.Http;
using Newtonsoft.Json.Linq;
using PCSMS.Filters;
using PCSMS.Models.Models_Company;
using PCSMS.Models.Models_SProvider;
using PCSMS.Models.Models_Temp;
using PCSMS.Services.Services_SProvider;

namespace PCSMS.Controllers
{
    //[AuthorizationRequired]
    [RoutePrefix("Api/SP_User")]
    public class SP_UserController : ApiController
    {
        private readonly ISP_UserServices _services;
        public SP_UserController()
        {
            _services = new SP_UserServices();
        }
        
        
        
        [Route("SaveSP_User")]
        [HttpPost]
        public IHttpActionResult SaveSP_User(JObject jObject)
        {
            dynamic jsonData = jObject;

            //userObj
            JObject userObjJson = jsonData.UserObj;
            var userObj = userObjJson.ToObject<SP_User>();

            //userObj
            JObject loggedInUserObjJson = jsonData.LoggedInUserObj;
            var loggedInUserObj = loggedInUserObjJson.ToObject<LoggedInUser>();
            


            return Ok(_services.SaveSP_User(userObj, loggedInUserObj).Data);
        }


        [Route("UpdateSP_UserAdministrativeInfo")]
        [HttpPost]
        public IHttpActionResult UpdateSP_UserAdministrativeInfo(JObject jObject)
        {
            dynamic jsonData = jObject;

            //userObj
            JObject userObjJson = jsonData.UserObj;
            var userObj = userObjJson.ToObject<SP_User>();

            //userObj
            JObject loggedInUserObjJson = jsonData.LoggedInUserObj;
            var loggedInUserObj = loggedInUserObjJson.ToObject<LoggedInUser>();



            return Ok(_services.UpdateSP_UserAdministrativeInfo(userObj, loggedInUserObj).Data);
        }

        [Route("UpdateSP_UserPersonalInfo")]
        [HttpPost]
        public IHttpActionResult UpdateSP_UserPersonalInfo(SP_User SP_User)
        {
            return Ok(_services.UpdateSP_UserPersonalInfo(SP_User).Data);
        }



        [Route("ChangeSP_UserPassword/{userId:int}/{extPassword}/{newPassword}")]
        [HttpPost]
        public IHttpActionResult ChangeSP_UserPassword(int userId, string extPassword, string newPassword)
        {
            return Ok(_services.ChangeSP_UserPassword(userId, extPassword, newPassword).Data);
        }


        [Route("DeleteSP_User/{userId:int}")]
        [HttpPost]
        public IHttpActionResult DeleteSP_User(long userId)
        {
            return Ok(_services.DeleteSP_User(userId).Data);
        }

        [Route("GetSP_UserList")]
        [HttpGet]
        public IHttpActionResult GetSP_UserList()
        {

            return Ok(_services.GetSP_UserList().Data);
        }

        [Route("GetSP_UserDetails/{id:int}")]
        [HttpGet]
        public IHttpActionResult GetSP_UserDetails(int id)
        {
            return Ok(_services.GetSP_UserDetails(id).Data);
        }

        [Route("UploadSP_UserPhoto/{userId:int}")]
        [HttpPost]
        public IHttpActionResult UploadSP_UserPhoto(int userId)
        {
            return Ok(_services.UploadSP_UserPhoto(userId).Data);
        }

        [Route("GetCountOfSP_User")]
        [HttpGet]
        public IHttpActionResult GetCountOfSP_User()
        {
            return Ok(_services.GetCountOfSP_User().Data);
        }


        [Route("GetCountOfActiveAndBlockedSP_Users")]
        [HttpGet]
        public IHttpActionResult GetCountOfActiveAndBlockedSP_Users()
        {
            return Ok(_services.GetCountOfActiveAndBlockedSP_Users().Data);
        }

    }
}
