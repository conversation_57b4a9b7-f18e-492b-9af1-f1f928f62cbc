﻿using System.Web.Http;
using PCSMS.Filters;
using PCSMS.Models.Models_SProvider;
using PCSMS.Services.Services_SProvider;

namespace PCSMS.Controllers
{
    [AuthorizationRequired]
    [RoutePrefix("Api/SP_Designation")]
    public class SP_DesignationController : ApiController
    {
        private readonly ISP_DesignationServices _services;
        public SP_DesignationController()
        {
            _services = new SP_DesignationServices();
        }

        [Route("SaveSP_Designation")]
        [HttpPost]
        public IHttpActionResult SaveSP_Designation(SP_Designation SP_Designation)
        {
            return Ok(_services.SaveSP_Designation(SP_Designation).Data);
        }

        [Route("UpdateSP_Designation")]
        [HttpPost]
        public IHttpActionResult UpdateSP_Designation(SP_Designation SP_Designation)
        {
            return Ok(_services.UpdateSP_Designation(SP_Designation).Data);
        }

        [Route("DeleteSP_Designation/{designationId:int}")]
        [HttpPost]
        public IHttpActionResult DeleteSP_Designation(long designationId)
        {
            return Ok(_services.DeleteSP_Designation(designationId).Data);
        }

        [Route("GetSP_DesignationList")]
        [HttpGet]
        public IHttpActionResult GetSP_DesignationList()
        {

            return Ok(_services.GetSP_DesignationList().Data);
        }

        [Route("GetSP_DesignationDetails/{id:int}")]
        [HttpGet]
        public IHttpActionResult GetSP_DesignationDetails(int id)
        {
            return Ok(_services.GetSP_DesignationDetails(x => x.Id == id).Data);
        }


    }
}
