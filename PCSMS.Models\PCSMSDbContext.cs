using System.Data.Entity;
using System.Data.Entity.ModelConfiguration.Conventions;
using PCSMS.Models.Models_SProvider;
using PCSMS.Models.Models_Shared;
using PCSMS.Models.Models_Company;
using PCSMS.Services.Services_Company;

namespace PCSMS.Models
{
    public class PCSMSDbContext : DbContext
    {
        public PCSMSDbContext() : base("Cn")
        {
            Database.SetInitializer(new MigrateDatabaseToLatestVersion<PCSMSDbContext, Migrations.Configuration>("Cn"));
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();
            modelBuilder.Conventions.Remove<ManyToManyCascadeDeleteConvention>();
        }

        #region Compnay DbSet
        public DbSet<CP_Device> CP_Device { get; set; }
        public DbSet<CP_Device_Group> CP_Device_Group { get; set; }
        public DbSet<CP_Device_License> CP_Device_License { get; set; }
        public DbSet<CP_Device_Schedule> CP_Device_Schedule { get; set; }
        public DbSet<CP_License> CP_License { get; set; }
        public DbSet<CP_License_Period> CP_License_Period { get; set; }
        public DbSet<CP_Profile> CP_Profile { get; set; }
        public DbSet<CP_ScreenCapture> CP_ScreenCapture { get; set; }
        public DbSet<CP_Token> CP_Token { get; set; }
        public DbSet<CP_User> CP_User { get; set; }
        public DbSet<CP_User_LSession> CP_User_LSession { get; set; }
        #endregion

        #region Shared DbSet
        public DbSet<AccessType> AccessType { get; set; }
        public DbSet<CompanyType> CompanyType { get; set; }
        #endregion

        #region Service Provider DbSet
        public DbSet<SP_Profile> SP_Profile { get; set; }
        public DbSet<SP_User> SP_User { get; set; }
        public DbSet<SP_Branch> SP_Branch { get; set; }
        public DbSet<SP_Designation> SP_Designation { get; set; }
        public DbSet<SP_Token> SP_Token { get; set; }
        #endregion


    }
}
