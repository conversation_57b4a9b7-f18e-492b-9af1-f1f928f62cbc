﻿<style>
    .page-body {
        padding-left: 10px;
        padding-right: 10px;
        padding-bottom: 50px;
    }

    .logo {
        max-width: 420px;
        margin: 20px auto 0px;
        margin-bottom: 40px;
    }

        .logo img {
            display: block;
            margin: auto;
            width: 50%;
            min-width: 140px;
        }

    .box {
        background: #ffffff;
        border-radius: 5px;
        padding: 15px;
        max-width: 420px;
        margin: 20px auto 0px;
    }
</style>


<div class="animated slideInDown">
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="row">
        <div class="col-md-offset-2 col-md-5">
            <div class="box">
                <!--SIGN IN FORM-->
                <div class="panel mb-none">
                    <div class="panel-content bg-scale-0">
                        <img alt="logo" src="Assets_Company/images/register-now.png" style="width: 97%"/>
                        <form name="RegisterForm" novalidate>
                            <!--Name-->
                            <div class="form-group mt-md">
                                <span class="input-with-icon">
                                    <input type="text" class="form-control" name="Name" placeholder="Name"
                                           ng-model="Company.CompanyName"
                                           ng-required="true"
                                           ng-minlength="2"
                                           ng-maxlength="50" />
                                    <i class="fa fa-envelope"></i>
                                </span>
                                <div class="row custom-row">
                                    <ul class="list-unstyled errormessage pull-left"
                                        ng-show="RegisterForm.Name.$dirty && RegisterForm.Name.$invalid">
                                        <li><span class="pull-left" ng-show="RegisterForm.Name.$error.required">*Required</span></li>
                                        <li><span class="pull-left" ng-show="!RegisterForm.Name.$error.pattern && RegisterForm.Name.$error.minlength">Minimum length is 2</span></li>
                                        <li><span class="pull-left" ng-show="!RegisterForm.Name.$error.pattern && RegisterForm.Name.$error.maxlength">Maximum length is 50</span></li>
                                    </ul>
                                </div>
                            </div>
                            <!--Address-->
                            <div class="form-group mt-md">
                                <span class="input-with-icon">
                                    <input type="text" class="form-control" name="Address" placeholder="Adress"
                                           ng-model="Company.CompanyAddress"
                                           ng-required="true"
                                           ng-minlength="5"
                                           ng-maxlength="200" />
                                    <i class="fa fa-address-card"></i>
                                </span>
                                <div class="row custom-row">
                                    <ul class="list-unstyled errormessage pull-left"
                                        ng-show="RegisterForm.Address.$dirty && RegisterForm.Address.$invalid">
                                        <li><span class="pull-left" ng-show="RegisterForm.Address.$error.minlength">Minimum length is 5</span></li>
                                        <li><span class="pull-left" ng-show="RegisterForm.Address.$error.maxlength">Maximum length is 200</span></li>
                                    </ul>
                                </div>
                            </div>
                            <!--Mobile-->
                            <div class="form-group mt-md">
                                <span class="input-with-icon">
                                    <input type="text" class="form-control" name="Mobile" placeholder="Mobile Number"
                                           ng-model="Company.CompanyMobile"
                                           ng-required="true"
                                           ng-minlength="11"
                                           ng-maxlength="30">
                                    <i class="fa fa-mobile"></i>
                                </span>
                                <div class="row custom-row">
                                    <ul class="list-unstyled errormessage pull-left"
                                        ng-show="RegisterForm.Mobile.$dirty && RegisterForm.Mobile.$invalid">
                                        <li class="pull-left" ng-show="RegisterForm.Mobile.$error.pattern">Not a valid Mobile Number</li>
                                        <li><span class="pull-left" ng-show="RegisterForm.Mobile.$error.required">*Either Phone or Mobile Number is Required</span></li>
                                        <li><span class="pull-left" ng-show="!RegisterForm.Mobile.$error.pattern && RegisterForm.Mobile.$error.minlength">Minimum length is 11</span></li>
                                        <li><span class="pull-left" ng-show="!RegisterForm.Mobile.$error.pattern && RegisterForm.Mobile.$error.maxlength">Maximum length is 30</span></li>
                                    </ul>
                                </div>
                            </div>
                            <!--Email-->
                            <div class="form-group mt-md">
                                <span class="input-with-icon">
                                    <input type="email" class="form-control" name="Email" placeholder="Email"
                                           ng-model="Company.CompanyEmail"
                                           ng-required="true"
                                           ng-minlength="11"
                                           ng-maxlength="30"
                                           ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/' />
                                    <i class="fa fa-envelope"></i>
                                </span>
                                <div class="row custom-row">
                                    <ul class="list-unstyled errormessage pull-left"
                                        ng-show="RegisterForm.Email.$dirty && RegisterForm.Email.$invalid">
                                        <li class="pull-left" ng-show="RegisterForm.Email.$error.pattern">Not a valid Email Address</li>
                                        <li><span class="pull-left" ng-show="RegisterForm.Email.$error.required">*Required</span></li>
                                        <li><span class="pull-left" ng-show="!RegisterForm.Email.$error.pattern && RegisterForm.Email.$error.minlength">Minimum length is 11</span></li>
                                        <li><span class="pull-left" ng-show="!RegisterForm.Email.$error.pattern && RegisterForm.Email.$error.maxlength">Maximum length is 30</span></li>
                                    </ul>
                                </div>
                            </div>
                            <!--Password-->
                            <div class="form-group">
                                <span class="input-with-icon">
                                    <input type="password" class="form-control" name="Password" placeholder="Password"
                                           ng-model="Company.CompanyPassword"
                                           ng-required="true"
                                           ng-minlength="6"
                                           ng-maxlength="50"
                                           ng-pattern="/([a-zA-Z0-9 ])\w+/" />
                                    <i class="fa fa-key"></i>
                                </span>
                                <div class="row custom-row">
                                    <ul class="list-unstyled errormessage pull-left" ng-show="RegisterForm.Password.$dirty && RegisterForm.Password.$invalid">
                                        <li><span class="pull-left" ng-show="RegisterForm.Password.$error.required">*Required</span></li>
                                        <li><span class="pull-left" ng-show="RegisterForm.Password.$error.pattern">Not a valid Password</span></li>
                                        <li><span class="pull-left" ng-show="!RegisterForm.Password.$error.pattern && RegisterForm.Password.$error.minlength">Minimum length is 6</span></li>
                                        <li><span class="pull-left" ng-show="!RegisterForm.Password.$error.pattern && RegisterForm.Password.$error.maxlength">Maximum length is 50</span></li>
                                    </ul>
                                </div>
                            </div>
                            <!--Confirm Password-->
                            <div class="form-group">
                                <span class="input-with-icon">
                                    <input type="password" class="form-control" name="ConfirmPassword" placeholder="Confirm Password"
                                           ng-required="true"
                                           ng-model="Company.CompanyConfirmPassword"
                                           ng-pattern="Company.CompanyPassword" />
                                    <i class="fa fa-key"></i>
                                </span>
                                <div class="row custom-row">
                                    <ul class="list-unstyled text-danger pull-left" ng-show="RegisterForm.ConfirmPassword.$dirty && RegisterForm.ConfirmPassword.$invalid">
                                        <li><span class="pull-left" ng-show="RegisterForm.ConfirmPassword.$error.required">*Required</span></li>
                                        <li><span class="pull-left" ng-show="RegisterForm.ConfirmPassword.$error.pattern">Password is not matched</span></li>
                                    </ul>
                                </div>
                            </div>
                            <!--HasAccepted-->
                            <div class="form-group">
                                <div class="checkbox-custom checkbox-primary">
                                    <input type="checkbox" name="IsAccepted" id="IsAccepted"
                                           ng-required="true"
                                           ng-true-value="1"
                                           ng-false-value="0"
                                           ng-model="IsAccepted">
                                    <label class="check" for="IsAccepted">I agree </label> to the <a ng-click="openTandCModal()">Terms and Conditions</a>
                                </div>
                                <div class="row custom-row">
                                    <ul class="list-unstyled text-danger pull-left" ng-show="RegisterForm.IsAccepted.$dirty && RegisterForm.IsAccepted.$invalid">
                                        <li><span class="pull-left" ng-show="RegisterForm.IsAccepted.$error.required">You've to accept the terms & conditions</span></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="form-group">
                                <button ng-click="SaveRegister()" class="btn btn-primary btn-block"
                                        ng-disabled="RegisterForm.$invalid">
                                    Register
                                </button>
                            </div>

                            <!--Server Side Message-->
                            <div class="form-group text-center">
                                <h1>{{Message}}</h1>
                            </div>
                            <div class="form-group text-center">
                                Have an account?, <a ui-sref="logIn">Sign In</a>
                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-5">
            <div class="box">
                <!--SIGN IN FORM-->
                <div class="panel mb-none">
                    <div class="panel-content bg-scale-0">
                        <a><img alt="logo" src="Assets_Company/images/download-desktop-app.png" class="pull-right" ng-click="DownloadDesktopApp()" style="width: 100%"/></a>
                        <div class="form-group text-center">
                            Haven't downloaded app?, <a ui-sref="logIn" ng-click="DownloadDesktopApp()">Download Desktop App</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>

<div class="modal fade" id="TandCModal" tabindex="-1" role="dialog" aria-labelledby="modal-label" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header" style="height:65px">
                <button type="button" class="custom-close" ng-click="closeTandCModal()" style="margin-top:0px;" aria-label="Close" tooltip-placement="left" uib-tooltip="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" style="margin-top:1.6%">Terms & Conditions</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-primary panel-bordered">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <h3>ACCEPTANCE</h3>
                                <p>
                                    PCSMS (PC Screen Monitoring System) shall provide its software and website merely as a service ("Service") to you,
                                    subject to the PCSMS (PC Screen Monitoring System) Terms of Service ("PCSMSTOS") and shall not be construed in any way as a contract
                                    for any other purpose. PCSMS (PC Screen Monitoring System) reserves the right to amend the PCSMSTOS at any time.
                                    Should there be such amendments, these shall become effective only upon the posting of the PCSMSTOS,
                                    as amended, on our website.
                                </p>
                                <h3>REGISTERING FOR THE SERVICE</h3>
                                <p>
                                    Membership registration is required before you can access the Service.  You agree to provide complete,
                                    true and accurate information, as may be required, when registering for the Service and to promptly update
                                    these should there be any subsequent changes thereto.<br><br>

                                    PCSMS (PC Screen Monitoring System) reserves the right to suspend or terminate your membership and/or access to the Service should
                                    it be found that any of the information you provided in your membership registration is untrue, inaccurate,
                                    or incomplete, or should reasonable grounds arise for  PCSMS (PC Screen Monitoring System) to suspect that the information you provided
                                    are untrue, inaccurate, or incomplete.<br><br>

                                    PCSMS (PC Screen Monitoring System) does not assume any obligation to provide any third party facilities for access to the Service.
                                    You are responsible for obtaining access to the Service, which responsibility may include providing the
                                    necessary equipment and payments for fees and charges to third parties for data, Internet, and other services.<br><br>

                                    Every time you access the Service shall be possible only upon entering your login name and password which you
                                    initially set during the registration process. You shall be responsible for maintaining the confidentiality
                                    of your password and login name, and shall likewise be responsible for all your activities and transactions,
                                    including payment of purchases and fees incurred, arising from the use of your login name and password.<br><br>

                                    PCSMS (PC Screen Monitoring System) does not assume any liability whatsoever for the use of your login name, password or credit card
                                    information.  Any and all purchases and fees incurred arising from your membership shall be for your account.<br><br>

                                    You undertake to immediately notify  PCSMS (PC Screen Monitoring System) of any unauthorized use of your membership or any other
                                    breach of security upon discovery thereof.   PCSMS (PC Screen Monitoring System) shall assume no liability whatsoever for any unauthorized
                                    access to the Service using your login name and password prior to such notice.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--Action Buttons-->
            <div class="modal-footer">
                <div class="row row-modal">
                    <button type="button"
                            class="btn  btn-default btn-wide pull-right btn-left-border-radius"
                            ng-click="closeTandCModal()"
                            role="button">
                        Close
                    </button>
                    <button type="button"
                            class="btn  btn-primary btn-wide pull-right btn-left-border-radius"
                            ng-click="AcceptTandC()"
                            role="button">
                        Accept
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>