﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>


<!-- CONTENT -->
<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-key" aria-hidden="true"></i><a ui-sref="license">License</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-content">
                        <div class="panel-header">
                            <button type="button" class="btn btn-default btn-o pull-right btn-addon btn-o-addon" ng-disabled="CurrentlyALicenseBeingRequested" ng-click="Requestlicense()">
                                <i class="fa fa-key" aria-hidden="true"></i>
                                Request For New License
                            </button>
                            <!--<button type="button" class="btn btn-default btn-o pull-right btn-addon btn-o-addon" ng-click="Requestlicense()" ng-hide="HasActiveTrialLicense">
                                <i class="fa fa-key" aria-hidden="true"></i>
                                Request For New License
                            </button>-->
                        </div>
                        <div class="panel-content">
                            <div class="table-responsive">
                                <!-- DataTable -->
                                <table class="data-table table table-striped nowrap table-bordered table-condensed" id="basic-table" datatable="ng" dt-options="vm.dtOptions" dt-instance="vm.dtInstance" dt-column-defs="vm.dtColumnDefs" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>License Code</th>
                                            <th>License Key</th>
                                            <th>Requested On</th>
                                            <th>Expiry Date</th>
                                            <th>License Type</th>
                                            <th>Status</th>
                                            <th class="custom-datatabel-action-th">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="x in LicenseList">
                                            <td>{{x.LCode}}</td>
                                            <td>{{x.LicenseKey}}</td>
                                            <td>{{ x.RequestedOn}}</td>
                                            <td>{{ x.ExpiryDate}}</td>
                                            <td>{{x.LicenseType}}</td>
                                            <td>
                                                <span class="{{x.Status=='Requested'?'badge x-warning ':x.Status=='Active'?'badge x-success ':x.Status=='In-Active'?'badge ':x.Status=='Expired'?'badge x-danger ':'badge '}}">{{x.Status}}</span>
                                                <span class="badge x-primary">{{x.SecondaryStatus}}</span>
                                            </td>
                                            <td>
                                                <button style="border: 1px solid #02715a; background-color: #ffffff"
                                                        type="button" tooltip-placement="top"
                                                        uib-tooltip="View Details" class="btn btn-default"
                                                        ng-if="x.Status=='Active' ||x.Status=='In-Active' || x.Status=='Expired' || x.Status=='Renewal Requested'"
                                                        ng-click="GetLicenseDetailsByLicenseId(x.Id);">
                                                    <img src="Assets_Company/images/datatables/info.png" width="20"/>
                                                </button>
                                                <button style="border: 1px solid #02715a; background-color: #ffffff"
                                                        type="button"
                                                        tooltip-placement="top"
                                                        uib-tooltip="Renew This License"
                                                        class="btn btn-default"
                                                        ng-hide="x.LicenseType =='Trial' || x.Status =='Requested' || x.SecondaryStatus =='Renewal Requested'"
                                                        ng-click="RequestForLicenseRenewal(x.Id, x.Status, x.SecondaryStatus);">
                                                    <img src="Assets_Company/images/datatables/renew.png" width="20"/>
                                                </button>
                                              </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>        
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>


<div class="modal fade" id="LicenseDetailsModal" tabindex="-1" role="dialog" aria-labelledby="modal-label" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="height:65px">
                <button type="button" class="custom-close" ng-click="cancelLicenseDetailsModal()" style="margin-top:0px;" aria-label="Close" tooltip-placement="left" uib-tooltip="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" style="margin-top:1.6%">License Details</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-primary panel-bordered">
                    <div class="panel-body">
                        <div class="row">
                            <!--Task Description-->
                            <div class="col-lg-12">
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered table-condensed">
                                        <tbody>
                                            <tr>
                                                <th>License Code</th>
                                                <td><label class="control-label">{{License.LCode}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>License Key</th>
                                                <td><label class="control-label">{{License.LicenseKey}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Status</th>
                                                <td>
                                                    <span class="{{License.Status=='Requested'?'badge x-warning ':License.Status=='Active'?'badge x-success ':License.Status=='In-Active'?'badge ':License.Status=='Expired'?'badge x-danger ':'badge '}}">{{License.Status}}</span>
                                                    <span class="badge x-primary">{{License.SecondaryStatus}}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Requested On</th>
                                                <td><label class="control-label">{{License.RequestedOn}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Activated On</th>
                                                <td><label class="control-label">{{License.ActivatedOn}}</label></td>                                                
                                            </tr>
                                            <tr ng-hide="License.Status == 'In-Active'">
                                                <th>Expiry Date</th>
                                                <td><label class="control-label">{{License.ExpiryDate}}</label></td>                                                
                                            </tr>
                                            <tr>
                                                <th>License Type</th>
                                                <td><label class="control-label">{{License.LicenseType}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Device Limit</th>
                                                <td><label class="control-label">{{License.DeviceLimit}}</label></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button"
                        class="btn  btn-default btn-wide pull-right btn-left-border-radius"
                        ng-click="cancelLicenseDetailsModal()"
                        role="button">
                    Close &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                </button>
            </div>
        </div>
    </div>
</div>
