﻿

//First I signed up with greenweb website : http://sms.greenweb.com.bd/
//It gives me some credit for sending sms
//Then i need to generate a token from their website
//Then i generate a token from there and paste it below 
//Every time i send sms, money will cut down from the greenweb balance. not from my mobile i registered (LOL)
//Then, 
			string result = "";
            WebRequest request = null;
            HttpWebResponse response = null;
            try
            {
                String to = "01912144881,017.....90"; //Recipient Phone Number multiple number must be separated by comma
                String token = "5038bf4024cf48d6eaa8454ea05091a6"; //generate token from the control panel
                String message = System.Uri.EscapeUriString("This is a test message"); //do not use single quotation (') in the message to avoid forbidden result
                String url = "http://sms.greenweb.com.bd/api.php?token=" + token + "&to=" + to + "&message=" + message;
                request = WebRequest.Create(url);

                // Send the 'HttpWebRequest' and wait for response.
                response = (HttpWebResponse)request.GetResponse();
                Stream stream = response.GetResponseStream();
                Encoding ec = System.Text.Encoding.GetEncoding("utf-8");
                StreamReader reader = new
                System.IO.StreamReader(stream, ec);
                result = reader.ReadToEnd();
                Console.WriteLine(result);
                reader.Close();
                stream.Close();
            }
            catch (Exception exp)
            {
                Console.WriteLine(exp.ToString());
            }
            finally
            {
                if (response != null)
                    response.Close();
            }