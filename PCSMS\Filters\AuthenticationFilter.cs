﻿using System.IO;
using System.Threading;
using System.Web;
using System.Web.Http.Controllers;
using Newtonsoft.Json;
using PCSMS.Services.Services_Company;
using PCSMS.Services.Services_SProvider;

namespace PCSMS.Filters
{
    
    public class AuthenticationFilter : GenericAuthenticationFilter
    {
        private readonly ICP_ProfileServices _companyServices;
        private readonly ISP_UserServices _tpUserServices;
      
        public AuthenticationFilter()
        {
            _companyServices=new CP_ProfileServices();
            _tpUserServices = new SP_UserServices();
        }
       
        public AuthenticationFilter(bool isActive): base(isActive)
        {
        }
        public class LoginType
        {
            public string Type { get; set; }
        }
        protected override bool OnAuthorizeUser(string username, string password, HttpActionContext actionContext)
        {

            //Finding whether the request is from Admin or Tutor
            var bodyStream = new StreamReader(HttpContext.Current.Request.InputStream);
            bodyStream.BaseStream.Seek(0, SeekOrigin.Begin);
            var bodyText = bodyStream.ReadToEnd();

            var obj = JsonConvert.DeserializeObject<LoginType>(bodyText);

            var loginType = obj.Type;

            if (loginType == "Company")
            {
                var companyId = _companyServices.CP_Authenticate(username, password);
                if (companyId > 0)
                {
                    var basicAuthenticationIdentity = Thread.CurrentPrincipal.Identity as BasicIdentity;
                    if (basicAuthenticationIdentity != null)
                        basicAuthenticationIdentity.CompanyId = companyId;
                    return true;
                }
            }
            else if (loginType == "SProviderUser")
            {
                var SProviderUserId = _tpUserServices.SP_Authenticate(username, password);
                if (SProviderUserId > 0)
                {
                    var basicAuthenticationIdentity = Thread.CurrentPrincipal.Identity as BasicIdentity;
                    if (basicAuthenticationIdentity != null)
                        basicAuthenticationIdentity.SProviderUserId = SProviderUserId;
                    return true;
                }
            }
            else
            {
                return false;
            }

            return false;
        }
    }
}