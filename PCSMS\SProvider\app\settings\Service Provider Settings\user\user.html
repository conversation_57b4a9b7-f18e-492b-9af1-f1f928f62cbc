﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!-- main content -->
<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-user" aria-hidden="true"></i><a ui-sref="user">User (s)</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-header">
                        <button type="button" class="btn btn-default btn-o pull-right btn-addon btn-o-addon" id="btnAddUser" data-toggle="modal" data-target="#UserModal">
                            <i class="fa fa-user-plus" aria-hidden="true"></i>
                            Add User
                        </button>
                    </div>
                    <div class="panel-content">
                        <!--Table Should be plaed here-->
                        <div class="table-responsive">
                            <table class="data-table table table-striped nowrap table-bordered table-condensed" id="basic-table" datatable="ng" dt-options="vm.dtOptions" dt-instance="vm.dtInstance" dt-column-defs="vm.dtColumnDefs" cellspacing="0">
                                <thead>
                                    <tr class="animated fadeIn">
                                        <th>Name</th>
                                        <th>Email / Username</th>
                                        <th>Access Type</th>
                                        <th>Birth Date</th>
                                        <th>Gender</th>
                                        <th>Mobile</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="animated fadeIn" ng-repeat="x in UserList">
                                        <td class="{{x.Id==$root.UserId?'logged-in-user':''}}">{{x.FirstName}} {{x.LastName}}</td>
                                        <td class="{{x.Id==$root.UserId?'logged-in-user':''}}">{{x.Email}}</td>
                                        <td class="{{x.Id==$root.UserId?'logged-in-user':''}}">{{x.AccessTypeName}}</td>
                                        <td class="{{x.Id==$root.UserId?'logged-in-user':''}}">{{x.BirthDate}}</td>
                                        <td class="{{x.Id==$root.UserId?'logged-in-user':''}}">{{x.Gender}}</td>
                                        <td class="{{x.Id==$root.UserId?'logged-in-user':''}}">{{x.Mobile}}</td>
                                        <td>
                                            <button class="btn btn-default btn-Line-Height btn-datatables" tooltip-placement="top" uib-tooltip="Edit" ng-click="openUserModal(x.Id)" ng-if="(x.Id==$root.UserId && $root.UserAccessTypeId<3) || (x.AccessTypeId>$root.UserAccessTypeId && $root.UserAccessTypeId<3)" style="border:1px solid #02715a; background-color:#ffffff"><img src="Assets_SProvider/images/datatables/edit.png" width="20" /></button>
                                            <button class="btn btn-default btn-Line-Height btn-datatables" tooltip-placement="top" uib-tooltip="View Details" ng-click="openUserModalInfo(x.Id)" style="border:1px solid #02715a; background-color:#ffffff"><img src="Assets_SProvider/images/datatables/info.png" width="20" /></button>
                                            <!--<button style="border:1px solid #02715a; background-color:#ffffff" type="button" class="btn btn-default" ng-click="deleteUserAlert(x);"><img src="Assets_SProvider/images/datatables/delete.png" width="20" /></button>-->
                                            <!-- the user should not be deleted becausse he or she might have been engaged with license approval transaction and other things, Deleting him or her will result a inconsistency in transaction-->
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!--Content to be injected here-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>


<form novalidate name="UserForm">
    <div class="modal fade" id="UserModal" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="custom-close" data-dismiss="modal" ng-click="cancelUserListModal()" tooltip-placement="left" uib-tooltip="Close">&times;</button>
                    <h4 ng-if="User.Id === undefined">Add New User</h4>
                    <h4 ng-if="User.Id >=1 && User.Id ==$root.UserId">Update Your Administrative Information here</h4>
                    <h4 ng-if='User.Id >=1 && User.Id !=$root.UserId'>Update {{User.FirstName+" "+User.LastName+"'s "}}Information</h4>

                    <div class="form-sep"></div>
                </div>
                <div class="modal-body">
                    <!--CREATE USER-->
                    <div ng-if="User.Id === undefined">
                        <div class="panel panel-primary panel-bordered">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-lg-9">
                                        <div class="custom-panel custom-panel-theme">
                                            <div class="custom-panel-body" style="min-height:201px;">
                                                <div class="row">
                                                    <div class="col-lg-6 separator">
                                                        <div class="form-group-sm">
                                                            <label class="req control-label">First Name</label>
                                                            <input type="text" name="FirstName" class="form-control" placeholder="Enter First Name"
                                                                   ng-model="User.FirstName"
                                                                   ng-required="true"
                                                                   ng-minlength="2"
                                                                   ng-maxlength="50"
                                                                   ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" />
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="UserForm.FirstName.$dirty && UserForm.FirstName.$invalid">
                                                                    <li><span class="pull-left" ng-show="UserForm.FirstName.$error.required">*Required</span></li>
                                                                    <li><span class="pull-left" ng-show="UserForm.FirstName.$error.pattern">Start with capital letter, don't use special character.</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserForm.FirstName.$error.pattern && UserForm.FirstName.$error.minlength">Minimum length is 2</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserForm.FirstName.$error.pattern && UserForm.FirstName.$error.maxlength">Maximum length is 50</span></li>
                                                                </ul>
                                                            </div>

                                                        </div>
                                                        <div class="form-group-sm">
                                                            <label class="req control-label">Last Name</label>
                                                            <input type="text" name="LastName" class="form-control" placeholder="Enter Last Name"
                                                                   ng-model="User.LastName"
                                                                   ng-required="true"
                                                                   ng-minlength="2"
                                                                   ng-maxlength="50"
                                                                   ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" />
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="UserForm.LastName.$dirty && UserForm.LastName.$invalid">
                                                                    <li><span class="pull-left" ng-show="UserForm.LastName.$error.required">*Required</span></li>
                                                                    <li><span class="pull-left" ng-show="UserForm.LastName.$error.pattern">Start with capital letter, don't use special character.</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserForm.LastName.$error.pattern && UserForm.LastName.$error.minlength">Minimum length is 2</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserForm.LastName.$error.pattern && UserForm.LastName.$error.maxlength">Maximum length is 50</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="form-group-sm">
                                                            <label class="req">Status</label>
                                                            <div class="form-group-sm">


                                                                <div class="radio radio-custom radio-inline radio-primary">
                                                                    <input type="radio" name="Status" id="StatusActive" value="1"
                                                                           ng-required="true"
                                                                           ng-model="User.IsActive" />
                                                                    <label for="StatusActive">Active</label>
                                                                </div>
                                                                <div class="radio radio-custom radio-inline">
                                                                    <input type="radio" name="Status" id="StatusInActive" value="2"
                                                                           ng-required="true"
                                                                           ng-model="User.IsActive" />
                                                                    <label for="StatusInActive">In-Active</label>
                                                                </div>

                                                                <div class="row custom-row">
                                                                    <ul class="list-unstyled text-danger pull-left" ng-show="UserForm.Status.$dirty && UserForm.Status.$invalid">
                                                                        <li><span class="pull-left" ng-show="UserForm.Status.$error.required">*Required</span></li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 separator">
                                                        <div class="form-group-sm">
                                                            <label class="req">Username / Email</label>
                                                            <input type="text" name="Email" class="form-control" placeholder="Enter your user email"
                                                                   ng-model="User.Email"
                                                                   ng-change="LiveValidation(User.Email)"
                                                                   ng-required="true"
                                                                   ng-minlength="11"
                                                                   ng-maxlength="40"
                                                                   ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/' />
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="(UserForm.Email.$dirty && UserForm.Email.$invalid) || LiveValidation(User.Email)==false">
                                                                    <li><span class="pull-left" ng-show="UserForm.Email.$error.required">*Required</span></li>
                                                                    <li><span class="pull-left" ng-show="UserForm.Email.$error.pattern">Not a valid Email.</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserForm.Email.$error.pattern && UserForm.Email.$error.minlength">Minimum length is 11</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserForm.Email.$error.pattern && UserForm.Email.$error.maxlength">Maximum length is 40</span></li>
                                                                    <li><span class="pull-left" ng-show="LiveValidationError">{{LiveValidationError}}</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div ng-if="User.Id != undefined">
                                                            <div class="form-group-sm">
                                                                <div class="row custom-row">
                                                                    <br/>
                                                                    <br/>
                                                                    <br/>
                                                                </div>
                                                            </div>
                                                            <div class="form-group-sm">
                                                                <div class="row custom-row">
                                                                    <br/>
                                                                    <br/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <br/>
                                                        <br/>
                                                        <br/>
                                                        <div class="">
                                                            <label for="">The user password will be created automatically </label>
                                                        </div>
                                                        <!--<div ng-if="User.Id === undefined">
                                                            <div class="form-group-sm">
                                                                <label class="req">Password</label>
                                                                <input type="password" name="Password" class="form-control" placeholder="Enter Password"
                                                                       ng-required="true"
                                                                       ng-model="User.Password"
                                                                       ng-minlength="6"
                                                                       ng-maxlength="50"
                                                                       ng-pattern="/([a-zA-Z0-9 ])\w+/" />
                                                                <div class="row custom-row">
                                                                    <ul class="list-unstyled text-danger pull-left" ng-show="UserForm.Password.$dirty && UserForm.Password.$invalid">
                                                                        <li><span class="pull-left" ng-show="UserForm.Password.$error.required">*Required</span></li>
                                                                        <li><span class="pull-left" ng-show="UserForm.Password.$error.pattern">Not a valid password</span></li>
                                                                        <li><span class="pull-left" ng-show="!UserForm.Password.$error.pattern && UserForm.Password.$error.minlength">Minimum length is 6</span></li>
                                                                        <li><span class="pull-left" ng-show="!UserForm.Password.$error.pattern && UserForm.Password.$error.maxlength">Maximum length is 50</span></li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                            <div class="form-group-sm">
                                                                <label class="req">Confirm Password</label>
                                                                <input type="password" name="ConfirmPassword" class="form-control" placeholder="Re-Type Passsword"
                                                                       ng-required="true"
                                                                       ng-model="User.ConfirmPassword"
                                                                       ng-pattern="User.Password" />
                                                                <div class="row custom-row">
                                                                    <ul class="list-unstyled text-danger pull-left" ng-show="UserForm.ConfirmPassword.$dirty && UserForm.ConfirmPassword.$invalid">
                                                                        <li><span class="pull-left" ng-show="UserForm.ConfirmPassword.$error.required">*Required</span></li>
                                                                        <li><span class="pull-left" ng-show="UserForm.ConfirmPassword.$error.pattern">Password is not matched</span></li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>-->

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="custom-panel custom-panel-theme">
                                            <div class="custom-panel-body">
                                                <div>
                                                    <div class="form-group-sm">
                                                        <label class="req">Select Branch</label>
                                                        <select class="form-control" name="SP_BranchId"
                                                                ng-model="User.SP_BranchId"
                                                                ng-required="true"
                                                                ng-options="Branch.Id as Branch.BranchName for Branch in BranchList">
                                                            <option value="">Select</option>
                                                        </select>
                                                        <div class="row custom-row">
                                                            <span class="text-danger pull-left"
                                                                  ng-show="UserForm.SP_BranchId.$dirty && UserForm.SP_BranchId.$invalid">
                                                                <span ng-show="UserForm.SP_BranchId.$error.required">*Required</span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="form-group-sm">
                                                        <label class="req">Select Access Type</label>
                                                        <select class="form-control" name="AccessTypeId"
                                                                ng-model="User.AccessTypeId"
                                                                ng-required="true"
                                                                ng-options="AccessType.Id as AccessType.AccessTypeName disable when AccessType.Id<$root.UserAccessTypeId for AccessType in AccessTypeList">
                                                            <option value="">Select</option>
                                                        </select>
                                                        <div class="row custom-row">
                                                            <span class="text-danger pull-left"
                                                                  ng-show="UserForm.AccessTypeId.$dirty && UserForm.AccessTypeId.$invalid">
                                                                <span ng-show="UserForm.AccessTypeId.$error.required">*Required</span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="form-group-sm">
                                                        <label class="req">Select Designation</label>
                                                        <select class="form-control" name="SP_DesignationId"
                                                                ng-model="User.SP_DesignationId"
                                                                ng-required="true"
                                                                ng-options="Designation.Id as Designation.DesignationName for Designation in DesignationList">
                                                            <option value="">Select</option>
                                                            <option ng-repeat="e in DesignationList" ng-selected="User.SP_DesignationId==e.Id" value="{{e.Id}}">{{e.DesignationName}}</option>
                                                        </select>
                                                        <div class="row custom-row">
                                                            <span class="text-danger pull-left"
                                                                  ng-show="UserForm.SP_DesignationId.$dirty && UserForm.SP_DesignationId.$invalid">
                                                                <span ng-show="UserForm.SP_DesignationId.$error.required">*Required</span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!--UPDATE USER-->
                    <div ng-if="User.Id >=1">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-condensed">
                                        <tbody>
                                        <tr>
                                            <th style="line-height: 2.7">Name</th>
                                            <td style="line-height: 2.7">{{User.FirstName}} {{User.LastName}}</td>
                                            <td rowspan="6">
                                                <div class="form-group">
                                                    <img class="img-responsive" src="{{src}}" style="margin: auto; width: 125px; border-radius: 500px"/>
                                                </div>
                                            </td>
                                            <td rowspan="6">
                                                <div class="col-lg-12">
                                                    <!--Select Status-->
                                                    <div class="form-group-sm" style="line-height: 3" ng-if="User.Id != $root.UserId">
                                                        <label class="req">Status</label>
                                                        <div class="form-group-sm">

                                                            <div class="radio radio-custom radio-inline radio-primary">
                                                                <input type="radio" name="Status" id="StatusActive" value="1"
                                                                       ng-required="true"
                                                                       ng-model="User.IsActive"/>
                                                                <label for="StatusActive">Active</label>
                                                            </div>
                                                            <div class="radio radio-custom radio-inline">
                                                                <input type="radio" name="Status" id="StatusInActive" value="2"
                                                                       ng-required="true"
                                                                       ng-model="User.IsActive"/>
                                                                <label for="StatusInActive">In-Active</label>
                                                            </div>

                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="UserForm.Status.$dirty && UserForm.Status.$invalid">
                                                                    <li><span class="pull-left" ng-show="UserForm.Status.$error.required">*Required</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!--Select Branch-->
                                                    <div class="form-group-sm" style="line-height: 3">
                                                        <label class="req">Select Branch</label>
                                                        <select class="form-control" name="SP_BranchId"
                                                                ng-model="User.SP_BranchId"
                                                                ng-required="true"
                                                                ng-options="Branch.Id as Branch.BranchName for Branch in BranchList">
                                                            <option value="">Select</option>
                                                        </select>
                                                        <div class="row custom-row">
                                                            <span class="text-danger pull-left"
                                                                      ng-show="UserForm.SP_BranchId.$dirty && UserForm.SP_BranchId.$invalid">
                                                                    <span ng-show="UserForm.SP_BranchId.$error.required">*Required</span>
                                                                </span>
                                                        </div>
                                                    </div>

                                                    <!--Select AccessType-->
                                                    <!--This is for Super Admin who can't change his access type-->
                                                    <div class="form-group-sm" style="line-height: 3" ng-if="User.Id == $root.UserId && User.AccessTypeId==$root.UserAccessTypeId">
                                                        <label class="req">Select Access Type</label>
                                                        <select class="form-control" name="AccessTypeId"
                                                                ng-model="User.AccessTypeId"
                                                                ng-required="true"
                                                                ng-options="AccessType.Id as AccessType.AccessTypeName disable when AccessType.Id>$root.UserAccessTypeId for AccessType in AccessTypeList | filter:FilterAccessType()">
                                                            <option value="">Select</option>
                                                        </select>
                                                        <div class="row custom-row">
                                                            <span class="text-danger pull-left"
                                                                      ng-show="UserForm.AccessTypeId.$dirty && UserForm.AccessTypeId.$invalid">
                                                                    <span ng-show="UserForm.AccessTypeId.$error.required">*Required</span>
                                                                </span>
                                                        </div>
                                                    </div>

                                                    <!--Select AccessType-->
                                                    <div class="form-group-sm" style="line-height: 3" ng-if="User.Id != $root.UserId">
                                                        <label class="req">Select Access Type</label>
                                                        <select class="form-control" name="AccessTypeId"
                                                                ng-model="User.AccessTypeId"
                                                                ng-required="true"
                                                                ng-options="AccessType.Id as AccessType.AccessTypeName for AccessType in AccessTypeList | filter:FilterAccessType()">
                                                            <option value="">Select</option>
                                                        </select>
                                                        <div class="row custom-row">
                                                            <span class="text-danger pull-left"
                                                                      ng-show="UserForm.AccessTypeId.$dirty && UserForm.AccessTypeId.$invalid">
                                                                    <span ng-show="UserForm.AccessTypeId.$error.required">*Required</span>
                                                                </span>
                                                        </div>
                                                    </div>

                                                    <!--Select AccessType-->
                                                    <div class="form-group-sm" style="line-height: 3" ng-if="User.Id == $root.UserId && User.AccessTypeId==$root.UserAccessTypeId && $root.UserAccessTypeId<1">
                                                        <label class="req">Select Access Type</label>
                                                        <select class="form-control" name="AccessTypeId"
                                                                ng-model="User.AccessTypeId"
                                                                ng-required="true"
                                                                ng-options="AccessType.Id as AccessType.AccessTypeName disable when AccessType.Id>$root.UserAccessTypeId for AccessType in AccessTypeList">
                                                            <option value="">Select</option>
                                                        </select>
                                                        <div class="row custom-row">
                                                            <span class="text-danger pull-left"
                                                                      ng-show="UserForm.AccessTypeId.$dirty && UserForm.AccessTypeId.$invalid">
                                                                    <span ng-show="UserForm.AccessTypeId.$error.required">*Required</span>
                                                                </span>
                                                        </div>
                                                    </div>

                                                    <!--Select Designation-->
                                                    <div class="form-group-sm" style="line-height: 3">
                                                        <label class="req">Select Designation</label>
                                                        <select class="form-control" name="SP_DesignationId"
                                                                ng-model="User.SP_DesignationId"
                                                                ng-required="true"
                                                                ng-options="Designation.Id as Designation.DesignationName for Designation in DesignationList">
                                                            <option value="">Select</option>
                                                            <option ng-repeat="e in DesignationList" ng-selected="User.SP_DesignationId==e.Id" value="{{e.Id}}">{{e.DesignationName}}</option>
                                                        </select>
                                                        <div class="row custom-row">
                                                            <span class="text-danger pull-left"
                                                                      ng-show="UserForm.SP_DesignationId.$dirty && UserForm.SP_DesignationId.$invalid">
                                                                    <span ng-show="UserForm.SP_DesignationId.$error.required">*Required</span>
                                                                </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>

                                        <tr>
                                            <th style="line-height: 2.7">Gender</th>
                                            <td style="line-height: 2.7">{{User.Gender}}</td>
                                        </tr>

                                        <tr>
                                            <th style="line-height: 2.7">Birth Date</th>
                                            <td style="line-height: 2.7">{{User.BirthDate}}</td>
                                        </tr>

                                        <tr>
                                            <th style="line-height: 2.7">Address</th>
                                            <td style="line-height: 2.7">{{User.Address}}</td>
                                        </tr>

                                        <tr>
                                            <th style="line-height: 2.7">Mobile</th>
                                            <td style="line-height: 2.7">{{User.Mobile}}</td>
                                        </tr>

                                        <tr>
                                            <th style="line-height: 2.7">Username / Email</th>
                                            <td style="line-height: 2.7">{{User.Email}}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <div>
                                        <span>NOTE: In this section, you can only edit administrative info (i.e. Branch, Designation, Access-Type)</span><br/>
                                        <span>In order to update user personal details, user is advised to go to user's profile page. </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="btn-group custom-btn-group-justified" role="group" aria-label="group button" ng-if="User.Id === undefined">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-default pull-right" ng-click="cancelUserListModal()" role="button">Close  &nbsp;&nbsp;&nbsp;&nbsp;</button>
                        </div>
                        <div class="btn-group" role="group">
                            <button ng-disabled="UserForm.$invalid || LiveValidation(User.Email)==false"
                                    type="button"
                                    ng-click="SaveOrUpdateUser()" class="btn btn-sm btn-primary">
                                Save &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                            </button>
                        </div>
                    </div>
                    <div class="btn-group custom-btn-group-justified" role="group" aria-label="group button" ng-if="User.Id >=1">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-default pull-right" ng-click="cancelUserListModal()" role="button">Close  &nbsp;&nbsp;&nbsp;&nbsp;</button>
                        </div>
                        <div class="btn-group" role="group">
                            <button ng-disabled="UserForm.$invalid || LiveValidation(User.Email)==false"
                                    type="button"
                                    ng-click="SaveOrUpdateUser()" class="btn btn-sm btn-primary">
                                Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>



<div class="modal fade" id="UserModalInfo" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-dialog-at custom-modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button class="custom-close" data-dismiss="modal" ng-click="cancelUserListModalInfo()" tooltip-placement="left" uib-tooltip="Close">&times;</button>
                <h4 ng-if='User.Id >=1 && User.Id !=$root.UserId'>{{User.FirstName+" "+User.LastName+"'s Information"}}</h4>
                <h4 ng-if="User.Id ==$root.UserId">Your Information</h4>
                <div class="form-sep"></div>
            </div>
            <div class="modal-body">
                <!--Update User-->
                <div ng-if="User.Id >=1">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="table-responsive">
                                <table class="table table-bordered table-condensed">
                                    <tbody>
                                        <tr>
                                            <th style="line-height:2.7">Name</th>
                                            <td style="line-height:2.7">{{User.FirstName}} {{User.LastName}}</td>
                                            <td rowspan="7">
                                                <div class="form-group">
                                                    <img class="img-responsive" src="{{src}}" style="margin:auto; width:125px; border-radius:500px" />
                                                    <br />
                                                </div>

                                                <div class="form-group text-center">
                                                    <div class="col-lg-12">
                                                        <label class="control-label"><span class="{{User.IsActive==1?'badge badge-success':'badge badge-danger'}}">{{User.IsActive==1?'Active':'In-Active'}}</span></label>
                                                    </div>
                                                </div>

                                                <div class="form-group text-center">
                                                    <div class="col-lg-12">
                                                        <label class="control-label"><span class="badge badge-info">{{User.AccessTypeName}}</span></label>
                                                    </div>
                                                </div>

                                            </td>
                                        </tr>

                                        <tr>
                                            <th style="line-height:2.7">Designation</th>
                                            <td style="line-height:2.7">{{User.DesignationName}}</td>
                                        </tr>

                                        <tr>
                                            <th style="line-height:2.7">Gender</th>
                                            <td style="line-height:2.7">{{User.Gender}}</td>
                                        </tr>

                                        <tr>
                                            <th style="line-height:2.7">Birth Date</th>
                                            <td style="line-height:2.7">{{User.BirthDate}}</td>
                                        </tr>

                                        <tr>
                                            <th style="line-height:2.7">Address</th>
                                            <td style="line-height:2.7">{{User.Address}}</td>
                                        </tr>

                                        <tr>
                                            <th style="line-height:2.7">Mobile</th>
                                            <td style="line-height:2.7">{{User.Mobile}}</td>
                                        </tr>

                                        <tr>
                                            <th style="line-height:2.7">Username / Email</th>
                                            <td style="line-height:2.7">{{User.Email}}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>


                
            </div>
            <div class="modal-footer">
                <button type="button"  class="btn btn-default pull-right" ng-click="cancelUserListModalInfo()" role="button">Close</button>
            </div>
        </div>
    </div>
</div>
<style>
    .logged-in-user {
        color: #3eae31;
        font-weight: bold;
    }

    select option:disabled {
        /*color: #c5c5c5;*/
        color: #e6e6e6;
        cursor: not-allowed;
    }
</style>