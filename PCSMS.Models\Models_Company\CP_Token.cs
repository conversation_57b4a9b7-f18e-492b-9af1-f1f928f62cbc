﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PCSMS.Models.Common;
using PCSMS.Models.Models_Shared;
using PCSMS.Models.Models_SProvider;
using System.Data.Entity;
namespace PCSMS.Models.Models_Company
{
    public class CP_Token : Entity<int>
    {

        [ForeignKey("CompanyId")]
        public virtual CP_Profile CP_Profile { get; set; }
        public int? CompanyId { get; set; }

        public string AuthToken { get; set; }
        public DateTime? IssuedOn { get; set; }
        public DateTime? ExpiresOn { get; set; }

        
    }
}
