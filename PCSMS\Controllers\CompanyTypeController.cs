﻿using System.Web.Http;
using PCSMS.Filters;
using PCSMS.Models.Models_Shared;
using PCSMS.Services.Services_Shared;

namespace PCSMS.Controllers
{
    [AuthorizationRequired]
    [RoutePrefix("api/CompanyType")]
    public class CompanyTypeController : ApiController
    {
        private readonly ICompanyTypeServices _services;
        public CompanyTypeController()
        {
            _services = new CompanyTypeServices();
        }

        [Route("SaveCompanyType")]
        [HttpPost]
        public IHttpActionResult SaveCompanyType(CompanyType companyType)
        {
            return Ok(_services.SaveCompanyType(companyType).Data);
        }

        [Route("UpdateCompanyType")]
        [HttpPost]
        public IHttpActionResult UpdateCompanyType(CompanyType companyType)
        {
            return Ok(_services.UpdateCompanyType(companyType).Data);
        }

        [Route("DeleteCompanyType/{companyTypeId:int}")]
        [HttpPost]
        public IHttpActionResult DeleteCompanyType(int companyTypeId)
        {
            return Ok(_services.DeleteCompanyType(companyTypeId).Data);
        }

        [Route("GetCompanyTypeList")]
        [HttpGet]
        public IHttpActionResult GetCompanyTypeList()
        {

            return Ok(_services.GetCompanyTypeList().Data);
        }

        [Route("GetCompanyTypeDetails/{id:int}")]
        [HttpGet]
        public IHttpActionResult GetCompanyTypeDetails(int id)
        {
            //return Content(HttpStatusCode.BadRequest, "Any object");
            return Ok(_services.GetCompanyTypeDetails(x => x.Id == id).Data);
        }


    }
}
