﻿using System.Web.Http;
using PCSMS.Filters;
using PCSMS.Models.Models_Shared;
using PCSMS.Services.Services_Shared;

namespace PCSMS.Controllers
{
    [AuthorizationRequired]
    [RoutePrefix("api/AccessType")]
    public class AccessTypeController : ApiController
    {
        private readonly IAccessTypeServices _services;
        public AccessTypeController()
        {
            _services = new AccessTypeServices();
        }

        [Route("SaveAccessType")]
        [HttpPost]
        public IHttpActionResult SaveAccessType(AccessType accessType)
        {
            return Ok(_services.SaveAccessType(accessType).Data);
        }

        [Route("UpdateAccessType")]
        [HttpPost]
        public IHttpActionResult UpdateAccessType(AccessType accessType)
        {
            return Ok(_services.UpdateAccessType(accessType).Data);
        }

        [Route("DeleteAccessType/{accessTypeId:int}")]
        [HttpPost]
        public IHttpActionResult DeleteAccessType(int accessTypeId)
        {
            return Ok(_services.DeleteAccessType(accessTypeId).Data);
        }

        [Route("GetAccessTypeList")]
        [HttpGet]
        public IHttpActionResult GetAccessTypeList()
        {

            return Ok(_services.GetAccessTypeList().Data);
        }

        [Route("GetAccessTypeDetails/{id:int}")]
        [HttpGet]
        public IHttpActionResult GetAccessTypeDetails(int id)
        {
            //return Content(HttpStatusCode.BadRequest, "Any object");
            return Ok(_services.GetAccessTypeDetails(x => x.Id == id).Data);
        }


    }
}
