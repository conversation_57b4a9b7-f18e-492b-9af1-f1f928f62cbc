﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.2.0" targetFramework="net46" />
  <package id="jQuery" version="1.6.4" targetFramework="net46" />
  <package id="JWT" version="1.3.0" targetFramework="net46" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.6" targetFramework="net46" />
  <package id="Microsoft.AspNet.Razor" version="3.2.6" targetFramework="net46" />
  <package id="Microsoft.AspNet.SignalR" version="2.3.0" targetFramework="net46" />
  <package id="Microsoft.AspNet.SignalR.Core" version="2.3.0" targetFramework="net46" />
  <package id="Microsoft.AspNet.SignalR.JS" version="2.3.0" targetFramework="net46" />
  <package id="Microsoft.AspNet.SignalR.SystemWeb" version="2.3.0" targetFramework="net46" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.6" targetFramework="net46" />
  <package id="Microsoft.Owin" version="2.1.0" targetFramework="net46" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="2.1.0" targetFramework="net46" />
  <package id="Microsoft.Owin.Security" version="2.1.0" targetFramework="net46" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net46" />
  <package id="Newtonsoft.Json" version="11.0.2" targetFramework="net46" />
  <package id="Owin" version="1.0" targetFramework="net46" />
  <package id="SharpZipLib" version="1.0.0" targetFramework="net46" />
</packages>