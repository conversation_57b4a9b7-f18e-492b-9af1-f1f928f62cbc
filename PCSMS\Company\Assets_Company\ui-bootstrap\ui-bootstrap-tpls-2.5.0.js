﻿angular.module("ui.bootstrap", ["ui.bootstrap.tpls", "ui.bootstrap.collapse", "ui.bootstrap.tabindex", "ui.bootstrap.accordion", "ui.bootstrap.alert", "ui.bootstrap.buttons", "ui.bootstrap.carousel", "ui.bootstrap.dateparser", "ui.bootstrap.isClass", "ui.bootstrap.datepicker", "ui.bootstrap.position", "ui.bootstrap.datepickerPopup", "ui.bootstrap.debounce", "ui.bootstrap.multiMap", "ui.bootstrap.dropdown", "ui.bootstrap.stackedMap", "ui.bootstrap.modal", "ui.bootstrap.paging", "ui.bootstrap.pager", "ui.bootstrap.pagination", "ui.bootstrap.tooltip", "ui.bootstrap.popover", "ui.bootstrap.progressbar", "ui.bootstrap.rating", "ui.bootstrap.tabs", "ui.bootstrap.timepicker", "ui.bootstrap.typeahead"]), angular.module("ui.bootstrap.tpls", ["uib/template/accordion/accordion-group.html", "uib/template/accordion/accordion.html", "uib/template/alert/alert.html", "uib/template/carousel/carousel.html", "uib/template/carousel/slide.html", "uib/template/datepicker/datepicker.html", "uib/template/datepicker/day.html", "uib/template/datepicker/month.html", "uib/template/datepicker/year.html", "uib/template/datepickerPopup/popup.html", "uib/template/modal/window.html", "uib/template/pager/pager.html", "uib/template/pagination/pagination.html", "uib/template/tooltip/tooltip-html-popup.html", "uib/template/tooltip/tooltip-popup.html", "uib/template/tooltip/tooltip-template-popup.html", "uib/template/popover/popover-html.html", "uib/template/popover/popover-template.html", "uib/template/popover/popover.html", "uib/template/progressbar/bar.html", "uib/template/progressbar/progress.html", "uib/template/progressbar/progressbar.html", "uib/template/rating/rating.html", "uib/template/tabs/tab.html", "uib/template/tabs/tabset.html", "uib/template/timepicker/timepicker.html", "uib/template/typeahead/typeahead-match.html", "uib/template/typeahead/typeahead-popup.html"]), angular.module("ui.bootstrap.collapse", []).directive("uibCollapse", ["$animate", "$q", "$parse", "$injector", function (e, t, n, a) { var i = a.has("$animateCss") ? a.get("$animateCss") : null; return { link: function (a, o, r) { var l = n(r.expanding), s = n(r.expanded), u = n(r.collapsing), p = n(r.collapsed), c = !1, d = {}, m = {}; function f(e) { return c ? { width: e.scrollWidth + "px" } : { height: e.scrollHeight + "px" } } function g() { o.removeClass("collapsing").addClass("collapse").css(d), s(a) } function h() { o.css(m), o.removeClass("collapsing").addClass("collapse"), p(a) } !function () { (c = !!("horizontal" in r)) ? (d = { width: "" }, m = { width: "0" }) : (d = { height: "" }, m = { height: "0" }); a.$eval(r.uibCollapse) || o.addClass("in").addClass("collapse").attr("aria-expanded", !0).attr("aria-hidden", !1).css(d) }(), a.$watch(r.uibCollapse, function (n) { n ? function () { if (!o.hasClass("collapse") && !o.hasClass("in")) return h(); t.resolve(u(a)).then(function () { o.css(f(o[0])).removeClass("collapse").addClass("collapsing").attr("aria-expanded", !1).attr("aria-hidden", !0), i ? i(o, { removeClass: "in", to: m }).start().finally(h) : e.removeClass(o, "in", { to: m }).then(h) }, angular.noop) }() : o.hasClass("collapse") && o.hasClass("in") || t.resolve(l(a)).then(function () { o.removeClass("collapse").addClass("collapsing").attr("aria-expanded", !0).attr("aria-hidden", !1), i ? i(o, { addClass: "in", easing: "ease", css: { overflow: "hidden" }, to: f(o[0]) }).start().finally(g) : e.addClass(o, "in", { css: { overflow: "hidden" }, to: f(o[0]) }).then(g) }, angular.noop) }) } } }]), angular.module("ui.bootstrap.tabindex", []).directive("uibTabindexToggle", function () { return { restrict: "A", link: function (e, t, n) { n.$observe("disabled", function (e) { n.$set("tabindex", e ? -1 : null) }) } } }), angular.module("ui.bootstrap.accordion", ["ui.bootstrap.collapse", "ui.bootstrap.tabindex"]).constant("uibAccordionConfig", { closeOthers: !0 }).controller("UibAccordionController", ["$scope", "$attrs", "uibAccordionConfig", function (e, t, n) { this.groups = [], this.closeOthers = function (a) { (angular.isDefined(t.closeOthers) ? e.$eval(t.closeOthers) : n.closeOthers) && angular.forEach(this.groups, function (e) { e !== a && (e.isOpen = !1) }) }, this.addGroup = function (e) { var t = this; this.groups.push(e), e.$on("$destroy", function (n) { t.removeGroup(e) }) }, this.removeGroup = function (e) { var t = this.groups.indexOf(e); -1 !== t && this.groups.splice(t, 1) } }]).directive("uibAccordion", function () { return { controller: "UibAccordionController", controllerAs: "accordion", transclude: !0, templateUrl: function (e, t) { return t.templateUrl || "uib/template/accordion/accordion.html" } } }).directive("uibAccordionGroup", function () { return { require: "^uibAccordion", transclude: !0, restrict: "A", templateUrl: function (e, t) { return t.templateUrl || "uib/template/accordion/accordion-group.html" }, scope: { heading: "@", panelClass: "@?", isOpen: "=?", isDisabled: "=?" }, controller: function () { this.setHeading = function (e) { this.heading = e } }, link: function (e, t, n, a) { t.addClass("panel"), a.addGroup(e), e.openClass = n.openClass || "panel-open", e.panelClass = n.panelClass || "panel-default", e.$watch("isOpen", function (n) { t.toggleClass(e.openClass, !!n), n && a.closeOthers(e) }), e.toggleOpen = function (t) { e.isDisabled || t && 32 !== t.which || (e.isOpen = !e.isOpen) }; var i = "accordiongroup-" + e.$id + "-" + Math.floor(1e4 * Math.random()); e.headingId = i + "-tab", e.panelId = i + "-panel" } } }).directive("uibAccordionHeading", function () { return { transclude: !0, template: "", replace: !0, require: "^uibAccordionGroup", link: function (e, t, n, a, i) { a.setHeading(i(e, angular.noop)) } } }).directive("uibAccordionTransclude", function () { return { require: "^uibAccordionGroup", link: function (e, t, n, a) { e.$watch(function () { return a[n.uibAccordionTransclude] }, function (e) { if (e) { var n = angular.element(t[0].querySelector("uib-accordion-header,data-uib-accordion-header,x-uib-accordion-header,uib\\:accordion-header,[uib-accordion-header],[data-uib-accordion-header],[x-uib-accordion-header]")); n.html(""), n.append(e) } }) } } }), angular.module("ui.bootstrap.alert", []).controller("UibAlertController", ["$scope", "$element", "$attrs", "$interpolate", "$timeout", function (e, t, n, a, i) { e.closeable = !!n.close, t.addClass("alert"), n.$set("role", "alert"), e.closeable && t.addClass("alert-dismissible"); var o = angular.isDefined(n.dismissOnTimeout) ? a(n.dismissOnTimeout)(e.$parent) : null; o && i(function () { e.close() }, parseInt(o, 10)) }]).directive("uibAlert", function () { return { controller: "UibAlertController", controllerAs: "alert", restrict: "A", templateUrl: function (e, t) { return t.templateUrl || "uib/template/alert/alert.html" }, transclude: !0, scope: { close: "&" } } }), angular.module("ui.bootstrap.buttons", []).constant("uibButtonConfig", { activeClass: "active", toggleEvent: "click" }).controller("UibButtonsController", ["uibButtonConfig", function (e) { this.activeClass = e.activeClass || "active", this.toggleEvent = e.toggleEvent || "click" }]).directive("uibBtnRadio", ["$parse", function (e) { return { require: ["uibBtnRadio", "ngModel"], controller: "UibButtonsController", controllerAs: "buttons", link: function (t, n, a, i) { var o = i[0], r = i[1], l = e(a.uibUncheckable); n.find("input").css({ display: "none" }), r.$render = function () { n.toggleClass(o.activeClass, angular.equals(r.$modelValue, t.$eval(a.uibBtnRadio))) }, n.on(o.toggleEvent, function () { if (!a.disabled) { var e = n.hasClass(o.activeClass); e && !angular.isDefined(a.uncheckable) || t.$apply(function () { r.$setViewValue(e ? null : t.$eval(a.uibBtnRadio)), r.$render() }) } }), a.uibUncheckable && t.$watch(l, function (e) { a.$set("uncheckable", e ? "" : void 0) }) } } }]).directive("uibBtnCheckbox", function () { return { require: ["uibBtnCheckbox", "ngModel"], controller: "UibButtonsController", controllerAs: "button", link: function (e, t, n, a) { var i = a[0], o = a[1]; function r() { return l(n.btnCheckboxTrue, !0) } function l(t, n) { return angular.isDefined(t) ? e.$eval(t) : n } t.find("input").css({ display: "none" }), o.$render = function () { t.toggleClass(i.activeClass, angular.equals(o.$modelValue, r())) }, t.on(i.toggleEvent, function () { n.disabled || e.$apply(function () { o.$setViewValue(t.hasClass(i.activeClass) ? l(n.btnCheckboxFalse, !1) : r()), o.$render() }) }) } } }), angular.module("ui.bootstrap.carousel", []).controller("UibCarouselController", ["$scope", "$element", "$interval", "$timeout", "$animate", function (e, t, n, a, i) { var o, r, l = this, s = l.slides = e.slides = [], u = "uib-slideDirection", p = e.active, c = !1; function d(e) { for (var t = 0; t < s.length; t++) s[t].slide.active = t === e } function m(e) { for (var t = 0; t < s.length; t++) if (s[t].slide === e) return t } function f() { o && (n.cancel(o), o = null) } function g() { f(); var t = +e.interval; !isNaN(t) && t > 0 && (o = n(h, t)) } function h() { var t = +e.interval; r && !isNaN(t) && t > 0 && s.length ? e.next() : e.pause() } t.addClass("carousel"), l.addSlide = function (t, n) { s.push({ slide: t, element: n }), s.sort(function (e, t) { return +e.slide.index - +t.slide.index }), (t.index === e.active || 1 === s.length && !angular.isNumber(e.active)) && (e.$currentTransition && (e.$currentTransition = null), p = t.index, e.active = t.index, d(p), l.select(s[m(t)]), 1 === s.length && e.play()) }, l.getCurrentIndex = function () { for (var e = 0; e < s.length; e++) if (s[e].slide.index === p) return e }, l.next = e.next = function () { var t = (l.getCurrentIndex() + 1) % s.length; if (0 !== t || !e.noWrap()) return l.select(s[t], "next"); e.pause() }, l.prev = e.prev = function () { var t = l.getCurrentIndex() - 1 < 0 ? s.length - 1 : l.getCurrentIndex() - 1; if (!e.noWrap() || t !== s.length - 1) return l.select(s[t], "prev"); e.pause() }, l.removeSlide = function (t) { var n = m(t); s.splice(n, 1), s.length > 0 && p === n ? n >= s.length ? (p = s.length - 1, e.active = p, d(p), l.select(s[s.length - 1])) : (p = n, e.active = p, d(p), l.select(s[n])) : p > n && (p--, e.active = p), 0 === s.length && (p = null, e.active = null) }, l.select = e.select = function (n, a) { var o = m(n.slide); void 0 === a && (a = o > l.getCurrentIndex() ? "next" : "prev"), n.slide.index === p || e.$currentTransition || function (n, a, o) { if (c) return; if (angular.extend(n, { direction: o }), angular.extend(s[p].slide || {}, { direction: o }), i.enabled(t) && !e.$currentTransition && s[a].element && l.slides.length > 1) { s[a].element.data(u, n.direction); var r = l.getCurrentIndex(); angular.isNumber(r) && s[r].element && s[r].element.data(u, n.direction), e.$currentTransition = !0, i.on("addClass", s[a].element, function (t, n) { "close" === n && (e.$currentTransition = null, i.off("addClass", t)) }) } e.active = n.index, p = n.index, d(a), g() }(n.slide, o, a) }, e.indexOfSlide = function (e) { return +e.slide.index }, e.isActive = function (t) { return e.active === t.slide.index }, e.isPrevDisabled = function () { return 0 === e.active && e.noWrap() }, e.isNextDisabled = function () { return e.active === s.length - 1 && e.noWrap() }, e.pause = function () { e.noPause || (r = !1, f()) }, e.play = function () { r || (r = !0, g()) }, t.on("mouseenter", e.pause), t.on("mouseleave", e.play), e.$on("$destroy", function () { c = !0, f() }), e.$watch("noTransition", function (e) { i.enabled(t, !e) }), e.$watch("interval", g), e.$watchCollection("slides", function (t) { t.length || (e.$currentTransition = null) }), e.$watch("active", function (e) { if (angular.isNumber(e) && p !== e) { for (var t = 0; t < s.length; t++) if (s[t].slide.index === e) { e = t; break } s[e] && (d(e), l.select(s[e]), p = e) } }) }]).directive("uibCarousel", function () { return { transclude: !0, controller: "UibCarouselController", controllerAs: "carousel", restrict: "A", templateUrl: function (e, t) { return t.templateUrl || "uib/template/carousel/carousel.html" }, scope: { active: "=", interval: "=", noTransition: "=", noPause: "=", noWrap: "&" } } }).directive("uibSlide", ["$animate", function (e) { return { require: "^uibCarousel", restrict: "A", transclude: !0, templateUrl: function (e, t) { return t.templateUrl || "uib/template/carousel/slide.html" }, scope: { actual: "=?", index: "=?" }, link: function (t, n, a, i) { n.addClass("item"), i.addSlide(t, n), t.$on("$destroy", function () { i.removeSlide(t) }), t.$watch("active", function (t) { e[t ? "addClass" : "removeClass"](n, "active") }) } } }]).animation(".item", ["$animateCss", function (e) { var t = "uib-slideDirection"; function n(e, t, n) { e.removeClass(t), n && n() } return { beforeAddClass: function (a, i, o) { if ("active" === i) { var r = a.data(t), l = "next" === r ? "left" : "right", s = n.bind(this, a, l + " " + r, o); return a.addClass(r), e(a, { addClass: l }).start().done(s), function () { !0 } } o() }, beforeRemoveClass: function (a, i, o) { if ("active" === i) { var r = "next" === a.data(t) ? "left" : "right", l = n.bind(this, a, r, o); return e(a, { addClass: r }).start().done(l), function () { !0 } } o() } } }]), angular.module("ui.bootstrap.dateparser", []).service("uibDateParser", ["$log", "$locale", "dateFilter", "orderByFilter", "filterFilter", function (e, t, n, a, i) { var o, r, l = /[\\\^\$\*\+\?\|\[\]\(\)\.\{\}]/g; function s(e) { return i(r, { key: e }, !0)[0] } function u(e, t, n) { return function () { return e.substr(t + 1, n - t - 1) } } function p(e, t) { for (var n = e.substr(t), a = 0; a < r.length; a++) if (new RegExp("^" + r[a].key).test(n)) { var i = r[a]; return { endIdx: t + i.key.length, parser: i.formatter } } return { endIdx: t + 1, parser: function () { return n.charAt(0) } } } function c(e) { return parseInt(e, 10) } function d(e, t) { e = e.replace(/:/g, ""); var n = Date.parse("Jan 01, 1970 00:00:00 " + e) / 6e4; return isNaN(n) ? t : n } function m(e, t) { return (e = new Date(e.getTime())).setMinutes(e.getMinutes() + t), e } function f(e, t, n) { n = n ? -1 : 1; var a = e.getTimezoneOffset(); return m(e, n * (d(t, a) - a)) } this.init = function () { o = t.id, this.parsers = {}, this.formatters = {}, r = [{ key: "yyyy", regex: "\\d{4}", apply: function (e) { this.year = +e }, formatter: function (e) { var t = new Date; return t.setFullYear(Math.abs(e.getFullYear())), n(t, "yyyy") } }, { key: "yy", regex: "\\d{2}", apply: function (e) { e = +e, this.year = e < 69 ? e + 2e3 : e + 1900 }, formatter: function (e) { var t = new Date; return t.setFullYear(Math.abs(e.getFullYear())), n(t, "yy") } }, { key: "y", regex: "\\d{1,4}", apply: function (e) { this.year = +e }, formatter: function (e) { var t = new Date; return t.setFullYear(Math.abs(e.getFullYear())), n(t, "y") } }, { key: "M!", regex: "0?[1-9]|1[0-2]", apply: function (e) { this.month = e - 1 }, formatter: function (e) { var t = e.getMonth(); return /^[0-9]$/.test(t) ? n(e, "MM") : n(e, "M") } }, { key: "MMMM", regex: t.DATETIME_FORMATS.MONTH.join("|"), apply: function (e) { this.month = t.DATETIME_FORMATS.MONTH.indexOf(e) }, formatter: function (e) { return n(e, "MMMM") } }, { key: "MMM", regex: t.DATETIME_FORMATS.SHORTMONTH.join("|"), apply: function (e) { this.month = t.DATETIME_FORMATS.SHORTMONTH.indexOf(e) }, formatter: function (e) { return n(e, "MMM") } }, { key: "MM", regex: "0[1-9]|1[0-2]", apply: function (e) { this.month = e - 1 }, formatter: function (e) { return n(e, "MM") } }, { key: "M", regex: "[1-9]|1[0-2]", apply: function (e) { this.month = e - 1 }, formatter: function (e) { return n(e, "M") } }, { key: "d!", regex: "[0-2]?[0-9]{1}|3[0-1]{1}", apply: function (e) { this.date = +e }, formatter: function (e) { var t = e.getDate(); return /^[1-9]$/.test(t) ? n(e, "dd") : n(e, "d") } }, { key: "dd", regex: "[0-2][0-9]{1}|3[0-1]{1}", apply: function (e) { this.date = +e }, formatter: function (e) { return n(e, "dd") } }, { key: "d", regex: "[1-2]?[0-9]{1}|3[0-1]{1}", apply: function (e) { this.date = +e }, formatter: function (e) { return n(e, "d") } }, { key: "EEEE", regex: t.DATETIME_FORMATS.DAY.join("|"), formatter: function (e) { return n(e, "EEEE") } }, { key: "EEE", regex: t.DATETIME_FORMATS.SHORTDAY.join("|"), formatter: function (e) { return n(e, "EEE") } }, { key: "HH", regex: "(?:0|1)[0-9]|2[0-3]", apply: function (e) { this.hours = +e }, formatter: function (e) { return n(e, "HH") } }, { key: "hh", regex: "0[0-9]|1[0-2]", apply: function (e) { this.hours = +e }, formatter: function (e) { return n(e, "hh") } }, { key: "H", regex: "1?[0-9]|2[0-3]", apply: function (e) { this.hours = +e }, formatter: function (e) { return n(e, "H") } }, { key: "h", regex: "[0-9]|1[0-2]", apply: function (e) { this.hours = +e }, formatter: function (e) { return n(e, "h") } }, { key: "mm", regex: "[0-5][0-9]", apply: function (e) { this.minutes = +e }, formatter: function (e) { return n(e, "mm") } }, { key: "m", regex: "[0-9]|[1-5][0-9]", apply: function (e) { this.minutes = +e }, formatter: function (e) { return n(e, "m") } }, { key: "sss", regex: "[0-9][0-9][0-9]", apply: function (e) { this.milliseconds = +e }, formatter: function (e) { return n(e, "sss") } }, { key: "ss", regex: "[0-5][0-9]", apply: function (e) { this.seconds = +e }, formatter: function (e) { return n(e, "ss") } }, { key: "s", regex: "[0-9]|[1-5][0-9]", apply: function (e) { this.seconds = +e }, formatter: function (e) { return n(e, "s") } }, { key: "a", regex: t.DATETIME_FORMATS.AMPMS.join("|"), apply: function (e) { 12 === this.hours && (this.hours = 0), "PM" === e && (this.hours += 12) }, formatter: function (e) { return n(e, "a") } }, { key: "Z", regex: "[+-]\\d{4}", apply: function (e) { var t = e.match(/([+-])(\d{2})(\d{2})/), n = t[1], a = t[2], i = t[3]; this.hours += c(n + a), this.minutes += c(n + i) }, formatter: function (e) { return n(e, "Z") } }, { key: "ww", regex: "[0-4][0-9]|5[0-3]", formatter: function (e) { return n(e, "ww") } }, { key: "w", regex: "[0-9]|[1-4][0-9]|5[0-3]", formatter: function (e) { return n(e, "w") } }, { key: "GGGG", regex: t.DATETIME_FORMATS.ERANAMES.join("|").replace(/\s/g, "\\s"), formatter: function (e) { return n(e, "GGGG") } }, { key: "GGG", regex: t.DATETIME_FORMATS.ERAS.join("|"), formatter: function (e) { return n(e, "GGG") } }, { key: "GG", regex: t.DATETIME_FORMATS.ERAS.join("|"), formatter: function (e) { return n(e, "GG") } }, { key: "G", regex: t.DATETIME_FORMATS.ERAS.join("|"), formatter: function (e) { return n(e, "G") } }], angular.version.major >= 1 && angular.version.minor > 4 && r.push({ key: "LLLL", regex: t.DATETIME_FORMATS.STANDALONEMONTH.join("|"), apply: function (e) { this.month = t.DATETIME_FORMATS.STANDALONEMONTH.indexOf(e) }, formatter: function (e) { return n(e, "LLLL") } }) }, this.init(), this.getParser = function (e) { var t = s(e); return t && t.apply || null }, this.overrideParser = function (e, t) { var n = s(e); n && angular.isFunction(t) && (this.parsers = {}, n.apply = t) }.bind(this), this.filter = function (e, n) { return angular.isDate(e) && !isNaN(e) && n ? (n = t.DATETIME_FORMATS[n] || n, t.id !== o && this.init(), this.formatters[n] || (this.formatters[n] = function (e) { for (var t, n, a = [], i = 0; i < e.length;) if (angular.isNumber(n)) { if ("'" === e.charAt(i)) (i + 1 >= e.length || "'" !== e.charAt(i + 1)) && (a.push(u(e, n, i)), n = null); else if (i === e.length) for (; n < e.length;) t = p(e, n), a.push(t), n = t.endIdx; i++ } else "'" !== e.charAt(i) ? (t = p(e, i), a.push(t.parser), i = t.endIdx) : (n = i, i++); return a }(n)), this.formatters[n].reduce(function (t, n) { return t + n(e) }, "")) : "" }, this.parse = function (n, i, s) { if (!angular.isString(n) || !i) return n; i = (i = t.DATETIME_FORMATS[i] || i).replace(l, "\\$&"), t.id !== o && this.init(), this.parsers[i] || (this.parsers[i] = function (e) { var t = [], n = e.split(""), i = e.indexOf("'"); if (i > -1) { var o = !1; e = e.split(""); for (var l = i; l < e.length; l++) o ? ("'" === e[l] && (l + 1 < e.length && "'" === e[l + 1] ? (e[l + 1] = "$", n[l + 1] = "") : (n[l] = "", o = !1)), e[l] = "$") : "'" === e[l] && (e[l] = "$", n[l] = "", o = !0); e = e.join("") } return angular.forEach(r, function (a) { var i = e.indexOf(a.key); if (i > -1) { e = e.split(""), n[i] = "(" + a.regex + ")", e[i] = "$"; for (var o = i + 1, r = i + a.key.length; o < r; o++) n[o] = "", e[o] = "$"; e = e.join(""), t.push({ index: i, key: a.key, apply: a.apply, matcher: a.regex }) } }), { regex: new RegExp("^" + n.join("") + "$"), map: a(t, "index") } }(i)); var u = this.parsers[i], p = u.regex, c = u.map, d = n.match(p), m = !1; if (d && d.length) { var f, g; angular.isDate(s) && !isNaN(s.getTime()) ? f = { year: s.getFullYear(), month: s.getMonth(), date: s.getDate(), hours: s.getHours(), minutes: s.getMinutes(), seconds: s.getSeconds(), milliseconds: s.getMilliseconds() } : (s && e.warn("dateparser:", "baseDate is not a valid date"), f = { year: 1900, month: 0, date: 1, hours: 0, minutes: 0, seconds: 0, milliseconds: 0 }); for (var h = 1, b = d.length; h < b; h++) { var v = c[h - 1]; "Z" === v.matcher && (m = !0), v.apply && v.apply.call(f, d[h]) } var $ = m ? Date.prototype.setUTCFullYear : Date.prototype.setFullYear, y = m ? Date.prototype.setUTCHours : Date.prototype.setHours; return function (e, t, n) { if (n < 1) return !1; if (1 === t && n > 28) return 29 === n && (e % 4 == 0 && e % 100 != 0 || e % 400 == 0); if (3 === t || 5 === t || 8 === t || 10 === t) return n < 31; return !0 }(f.year, f.month, f.date) && (!angular.isDate(s) || isNaN(s.getTime()) || m ? (g = new Date(0), $.call(g, f.year, f.month, f.date), y.call(g, f.hours || 0, f.minutes || 0, f.seconds || 0, f.milliseconds || 0)) : (g = new Date(s), $.call(g, f.year, f.month, f.date), y.call(g, f.hours, f.minutes, f.seconds, f.milliseconds))), g } }, this.toTimezone = function (e, t) { return e && t ? f(e, t) : e }, this.fromTimezone = function (e, t) { return e && t ? f(e, t, !0) : e }, this.timezoneToOffset = d, this.addDateMinutes = m, this.convertTimezoneToLocal = f }]), angular.module("ui.bootstrap.isClass", []).directive("uibIsClass", ["$animate", function (e) { var t = /^\s*([\s\S]+?)\s+on\s+([\s\S]+?)\s*$/, n = /^\s*([\s\S]+?)\s+for\s+([\s\S]+?)\s*$/; return { restrict: "A", compile: function (a, i) { var o = [], r = [], l = {}, s = i.uibIsClass.match(t), u = s[2], p = s[1].split(","); return function (t, a, i) { o.push(t), r.push({ scope: t, element: a }), p.forEach(function (a, i) { !function (t, a) { var i = t.match(n), o = a.$eval(i[1]), s = i[2], p = l[t]; if (!p) { var c = function (t) { var n = null; r.some(function (e) { var a = e.scope.$eval(u); if (a === t) return n = e, !0 }), p.lastActivated !== n && (p.lastActivated && e.removeClass(p.lastActivated.element, o), n && e.addClass(n.element, o), p.lastActivated = n) }; l[t] = p = { lastActivated: null, scope: a, watchFn: c, compareWithExp: s, watcher: a.$watch(s, c) } } p.watchFn(a.$eval(s)) }(a, t) }), t.$on("$destroy", c) }; function c(e) { var t = e.targetScope, n = o.indexOf(t); if (o.splice(n, 1), r.splice(n, 1), o.length) { var a = o[0]; angular.forEach(l, function (e) { e.scope === t && (e.watcher = a.$watch(e.compareWithExp, e.watchFn), e.scope = a) }) } else l = {} } } } }]), angular.module("ui.bootstrap.datepicker", ["ui.bootstrap.dateparser", "ui.bootstrap.isClass"]).value("$datepickerSuppressError", !1).value("$datepickerLiteralWarning", !0).constant("uibDatepickerConfig", { datepickerMode: "day", formatDay: "dd", formatMonth: "MMMM", formatYear: "yyyy", formatDayHeader: "EEE", formatDayTitle: "MMMM yyyy", formatMonthTitle: "yyyy", maxDate: null, maxMode: "year", minDate: null, minMode: "day", monthColumns: 3, ngModelOptions: {}, shortcutPropagation: !1, showWeeks: !0, yearColumns: 5, yearRows: 4 }).controller("UibDatepickerController", ["$scope", "$element", "$attrs", "$parse", "$interpolate", "$locale", "$log", "dateFilter", "uibDatepickerConfig", "$datepickerLiteralWarning", "$datepickerSuppressError", "uibDateParser", function (e, t, n, a, i, o, r, l, s, u, p, c) { var d = this, m = { $setViewValue: angular.noop }, f = {}, g = []; t.addClass("uib-datepicker"), n.$set("role", "application"), e.datepickerOptions || (e.datepickerOptions = {}), this.modes = ["day", "month", "year"], ["customClass", "dateDisabled", "datepickerMode", "formatDay", "formatDayHeader", "formatDayTitle", "formatMonth", "formatMonthTitle", "formatYear", "maxDate", "maxMode", "minDate", "minMode", "monthColumns", "showWeeks", "shortcutPropagation", "startingDay", "yearColumns", "yearRows"].forEach(function (t) { switch (t) { case "customClass": case "dateDisabled": e[t] = e.datepickerOptions[t] || angular.noop; break; case "datepickerMode": e.datepickerMode = angular.isDefined(e.datepickerOptions.datepickerMode) ? e.datepickerOptions.datepickerMode : s.datepickerMode; break; case "formatDay": case "formatDayHeader": case "formatDayTitle": case "formatMonth": case "formatMonthTitle": case "formatYear": d[t] = angular.isDefined(e.datepickerOptions[t]) ? i(e.datepickerOptions[t])(e.$parent) : s[t]; break; case "monthColumns": case "showWeeks": case "shortcutPropagation": case "yearColumns": case "yearRows": d[t] = angular.isDefined(e.datepickerOptions[t]) ? e.datepickerOptions[t] : s[t]; break; case "startingDay": angular.isDefined(e.datepickerOptions.startingDay) ? d.startingDay = e.datepickerOptions.startingDay : angular.isNumber(s.startingDay) ? d.startingDay = s.startingDay : d.startingDay = (o.DATETIME_FORMATS.FIRSTDAYOFWEEK + 8) % 7; break; case "maxDate": case "minDate": e.$watch("datepickerOptions." + t, function (e) { e ? angular.isDate(e) ? d[t] = c.fromTimezone(new Date(e), f.getOption("timezone")) : (u && r.warn("Literal date support has been deprecated, please switch to date object usage"), d[t] = new Date(l(e, "medium"))) : d[t] = s[t] ? c.fromTimezone(new Date(s[t]), f.getOption("timezone")) : null, d.refreshView() }); break; case "maxMode": case "minMode": e.datepickerOptions[t] ? e.$watch(function () { return e.datepickerOptions[t] }, function (n) { d[t] = e[t] = angular.isDefined(n) ? n : e.datepickerOptions[t], ("minMode" === t && d.modes.indexOf(e.datepickerOptions.datepickerMode) < d.modes.indexOf(d[t]) || "maxMode" === t && d.modes.indexOf(e.datepickerOptions.datepickerMode) > d.modes.indexOf(d[t])) && (e.datepickerMode = d[t], e.datepickerOptions.datepickerMode = d[t]) }) : d[t] = e[t] = s[t] || null } }), e.uniqueId = "datepicker-" + e.$id + "-" + Math.floor(1e4 * Math.random()), e.disabled = angular.isDefined(n.disabled) || !1, angular.isDefined(n.ngDisabled) && g.push(e.$parent.$watch(n.ngDisabled, function (t) { e.disabled = t, d.refreshView() })), e.isActive = function (t) { return 0 === d.compare(t.date, d.activeDate) && (e.activeDateId = t.uid, !0) }, this.init = function (t) { f = function (t) { var n; if (angular.version.minor < 6) (n = t.$options || e.datepickerOptions.ngModelOptions || s.ngModelOptions || {}).getOption = function (e) { return n[e] }; else { var a = t.$options.getOption("timezone") || (e.datepickerOptions.ngModelOptions ? e.datepickerOptions.ngModelOptions.timezone : null) || (s.ngModelOptions ? s.ngModelOptions.timezone : null); n = t.$options.createChild(s.ngModelOptions).createChild(e.datepickerOptions.ngModelOptions).createChild(t.$options).createChild({ timezone: a }) } return n }(m = t), e.datepickerOptions.initDate ? (d.activeDate = c.fromTimezone(e.datepickerOptions.initDate, f.getOption("timezone")) || new Date, e.$watch("datepickerOptions.initDate", function (e) { e && (m.$isEmpty(m.$modelValue) || m.$invalid) && (d.activeDate = c.fromTimezone(e, f.getOption("timezone")), d.refreshView()) })) : d.activeDate = new Date; var n = m.$modelValue ? new Date(m.$modelValue) : new Date; this.activeDate = isNaN(n) ? c.fromTimezone(new Date, f.getOption("timezone")) : c.fromTimezone(n, f.getOption("timezone")), m.$render = function () { d.render() } }, this.render = function () { if (m.$viewValue) { var e = new Date(m.$viewValue); !isNaN(e) ? this.activeDate = c.fromTimezone(e, f.getOption("timezone")) : p || r.error('Datepicker directive: "ng-model" value must be a Date object') } this.refreshView() }, this.refreshView = function () { if (this.element) { e.selectedDt = null, this._refreshView(), e.activeDt && (e.activeDateId = e.activeDt.uid); var t = m.$viewValue ? new Date(m.$viewValue) : null; t = c.fromTimezone(t, f.getOption("timezone")), m.$setValidity("dateDisabled", !t || this.element && !this.isDisabled(t)) } }, this.createDateObject = function (t, n) { var a = m.$viewValue ? new Date(m.$viewValue) : null; a = c.fromTimezone(a, f.getOption("timezone")); var i = new Date; i = c.fromTimezone(i, f.getOption("timezone")); var o = this.compare(t, i), r = { date: t, label: c.filter(t, n), selected: a && 0 === this.compare(t, a), disabled: this.isDisabled(t), past: o < 0, current: 0 === o, future: o > 0, customClass: this.customClass(t) || null }; return a && 0 === this.compare(t, a) && (e.selectedDt = r), d.activeDate && 0 === this.compare(r.date, d.activeDate) && (e.activeDt = r), r }, this.isDisabled = function (t) { return e.disabled || this.minDate && this.compare(t, this.minDate) < 0 || this.maxDate && this.compare(t, this.maxDate) > 0 || e.dateDisabled && e.dateDisabled({ date: t, mode: e.datepickerMode }) }, this.customClass = function (t) { return e.customClass({ date: t, mode: e.datepickerMode }) }, this.split = function (e, t) { for (var n = []; e.length > 0;) n.push(e.splice(0, t)); return n }, e.select = function (t) { if (e.datepickerMode === d.minMode) { var n = m.$viewValue ? c.fromTimezone(new Date(m.$viewValue), f.getOption("timezone")) : new Date(0, 0, 0, 0, 0, 0, 0); n.setFullYear(t.getFullYear(), t.getMonth(), t.getDate()), n = c.toTimezone(n, f.getOption("timezone")), m.$setViewValue(n), m.$render() } else d.activeDate = t, h(d.modes[d.modes.indexOf(e.datepickerMode) - 1]), e.$emit("uib:datepicker.mode"); e.$broadcast("uib:datepicker.focus") }, e.move = function (e) { var t = d.activeDate.getFullYear() + e * (d.step.years || 0), n = d.activeDate.getMonth() + e * (d.step.months || 0); d.activeDate.setFullYear(t, n, 1), d.refreshView() }, e.toggleMode = function (t) { t = t || 1, e.datepickerMode === d.maxMode && 1 === t || e.datepickerMode === d.minMode && -1 === t || (h(d.modes[d.modes.indexOf(e.datepickerMode) + t]), e.$emit("uib:datepicker.mode")) }, e.keys = { 13: "enter", 32: "space", 33: "pageup", 34: "pagedown", 35: "end", 36: "home", 37: "left", 38: "up", 39: "right", 40: "down" }; function h(t) { e.datepickerMode = t, e.datepickerOptions.datepickerMode = t } e.$on("uib:datepicker.focus", function () { d.element[0].focus() }), e.keydown = function (t) { var n = e.keys[t.which]; if (n && !t.shiftKey && !t.altKey && !e.disabled) if (t.preventDefault(), d.shortcutPropagation || t.stopPropagation(), "enter" === n || "space" === n) { if (d.isDisabled(d.activeDate)) return; e.select(d.activeDate) } else !t.ctrlKey || "up" !== n && "down" !== n ? (d.handleKeyDown(n, t), d.refreshView()) : e.toggleMode("up" === n ? 1 : -1) }, t.on("keydown", function (t) { e.$apply(function () { e.keydown(t) }) }), e.$on("$destroy", function () { for (; g.length;) g.shift()() }) }]).controller("UibDaypickerController", ["$scope", "$element", "dateFilter", function (e, t, n) { var a = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]; function i(e, t) { return 1 !== t || e % 4 != 0 || e % 100 == 0 && e % 400 != 0 ? a[t] : 29 } function o(e) { var t = new Date(e); t.setDate(t.getDate() + 4 - (t.getDay() || 7)); var n = t.getTime(); return t.setMonth(0), t.setDate(1), Math.floor(Math.round((n - t) / 864e5) / 7) + 1 } this.step = { months: 1 }, this.element = t, this.init = function (t) { angular.extend(t, this), e.showWeeks = t.showWeeks, t.refreshView() }, this.getDates = function (e, t) { for (var n, a = new Array(t), i = new Date(e), o = 0; o < t;) n = new Date(i), a[o++] = n, i.setDate(i.getDate() + 1); return a }, this._refreshView = function () { var t = this.activeDate.getFullYear(), a = this.activeDate.getMonth(), i = new Date(this.activeDate); i.setFullYear(t, a, 1); var r = this.startingDay - i.getDay(), l = r > 0 ? 7 - r : -r, s = new Date(i); l > 0 && s.setDate(1 - l); for (var u = this.getDates(s, 42), p = 0; p < 42; p++) u[p] = angular.extend(this.createDateObject(u[p], this.formatDay), { secondary: u[p].getMonth() !== a, uid: e.uniqueId + "-" + p }); e.labels = new Array(7); for (var c = 0; c < 7; c++) e.labels[c] = { abbr: n(u[c].date, this.formatDayHeader), full: n(u[c].date, "EEEE") }; if (e.title = n(this.activeDate, this.formatDayTitle), e.rows = this.split(u, 7), e.showWeeks) { e.weekNumbers = []; for (var d = (11 - this.startingDay) % 7, m = e.rows.length, f = 0; f < m; f++) e.weekNumbers.push(o(e.rows[f][d].date)) } }, this.compare = function (e, t) { var n = new Date(e.getFullYear(), e.getMonth(), e.getDate()), a = new Date(t.getFullYear(), t.getMonth(), t.getDate()); return n.setFullYear(e.getFullYear()), a.setFullYear(t.getFullYear()), n - a }, this.handleKeyDown = function (e, t) { var n = this.activeDate.getDate(); if ("left" === e) n -= 1; else if ("up" === e) n -= 7; else if ("right" === e) n += 1; else if ("down" === e) n += 7; else if ("pageup" === e || "pagedown" === e) { var a = this.activeDate.getMonth() + ("pageup" === e ? -1 : 1); this.activeDate.setMonth(a, 1), n = Math.min(i(this.activeDate.getFullYear(), this.activeDate.getMonth()), n) } else "home" === e ? n = 1 : "end" === e && (n = i(this.activeDate.getFullYear(), this.activeDate.getMonth())); this.activeDate.setDate(n) } }]).controller("UibMonthpickerController", ["$scope", "$element", "dateFilter", function (e, t, n) { this.step = { years: 1 }, this.element = t, this.init = function (e) { angular.extend(e, this), e.refreshView() }, this._refreshView = function () { for (var t, a = new Array(12), i = this.activeDate.getFullYear(), o = 0; o < 12; o++) (t = new Date(this.activeDate)).setFullYear(i, o, 1), a[o] = angular.extend(this.createDateObject(t, this.formatMonth), { uid: e.uniqueId + "-" + o }); e.title = n(this.activeDate, this.formatMonthTitle), e.rows = this.split(a, this.monthColumns), e.yearHeaderColspan = this.monthColumns > 3 ? this.monthColumns - 2 : 1 }, this.compare = function (e, t) { var n = new Date(e.getFullYear(), e.getMonth()), a = new Date(t.getFullYear(), t.getMonth()); return n.setFullYear(e.getFullYear()), a.setFullYear(t.getFullYear()), n - a }, this.handleKeyDown = function (e, t) { var n = this.activeDate.getMonth(); if ("left" === e) n -= 1; else if ("up" === e) n -= this.monthColumns; else if ("right" === e) n += 1; else if ("down" === e) n += this.monthColumns; else if ("pageup" === e || "pagedown" === e) { var a = this.activeDate.getFullYear() + ("pageup" === e ? -1 : 1); this.activeDate.setFullYear(a) } else "home" === e ? n = 0 : "end" === e && (n = 11); this.activeDate.setMonth(n) } }]).controller("UibYearpickerController", ["$scope", "$element", "dateFilter", function (e, t, n) { var a, i; function o(e) { return parseInt((e - 1) / i, 10) * i + 1 } this.element = t, this.yearpickerInit = function () { a = this.yearColumns, i = this.yearRows * a, this.step = { years: i } }, this._refreshView = function () { for (var t, n = new Array(i), r = 0, l = o(this.activeDate.getFullYear()) ; r < i; r++) (t = new Date(this.activeDate)).setFullYear(l + r, 0, 1), n[r] = angular.extend(this.createDateObject(t, this.formatYear), { uid: e.uniqueId + "-" + r }); e.title = [n[0].label, n[i - 1].label].join(" - "), e.rows = this.split(n, a), e.columns = a }, this.compare = function (e, t) { return e.getFullYear() - t.getFullYear() }, this.handleKeyDown = function (e, t) { var n = this.activeDate.getFullYear(); "left" === e ? n -= 1 : "up" === e ? n -= a : "right" === e ? n += 1 : "down" === e ? n += a : "pageup" === e || "pagedown" === e ? n += ("pageup" === e ? -1 : 1) * i : "home" === e ? n = o(this.activeDate.getFullYear()) : "end" === e && (n = o(this.activeDate.getFullYear()) + i - 1), this.activeDate.setFullYear(n) } }]).directive("uibDatepicker", function () { return { templateUrl: function (e, t) { return t.templateUrl || "uib/template/datepicker/datepicker.html" }, scope: { datepickerOptions: "=?" }, require: ["uibDatepicker", "^ngModel"], restrict: "A", controller: "UibDatepickerController", controllerAs: "datepicker", link: function (e, t, n, a) { var i = a[0], o = a[1]; i.init(o) } } }).directive("uibDaypicker", function () { return { templateUrl: function (e, t) { return t.templateUrl || "uib/template/datepicker/day.html" }, require: ["^uibDatepicker", "uibDaypicker"], restrict: "A", controller: "UibDaypickerController", link: function (e, t, n, a) { var i = a[0]; a[1].init(i) } } }).directive("uibMonthpicker", function () { return { templateUrl: function (e, t) { return t.templateUrl || "uib/template/datepicker/month.html" }, require: ["^uibDatepicker", "uibMonthpicker"], restrict: "A", controller: "UibMonthpickerController", link: function (e, t, n, a) { var i = a[0]; a[1].init(i) } } }).directive("uibYearpicker", function () { return { templateUrl: function (e, t) { return t.templateUrl || "uib/template/datepicker/year.html" }, require: ["^uibDatepicker", "uibYearpicker"], restrict: "A", controller: "UibYearpickerController", link: function (e, t, n, a) { var i = a[0]; angular.extend(i, a[1]), i.yearpickerInit(), i.refreshView() } } }), angular.module("ui.bootstrap.position", []).factory("$uibPosition", ["$document", "$window", function (e, t) { var n, a, i = { normal: /(auto|scroll)/, hidden: /(auto|scroll|hidden)/ }, o = { auto: /\s?auto?\s?/i, primary: /^(top|bottom|left|right)$/, secondary: /^(top|bottom|left|right|center)$/, vertical: /^(top|bottom)$/ }, r = /(HTML|BODY)/; return { getRawNode: function (e) { return e.nodeName ? e : e[0] || e }, parseStyle: function (e) { return e = parseFloat(e), isFinite(e) ? e : 0 }, offsetParent: function (n) { var a, i = (n = this.getRawNode(n)).offsetParent || e[0].documentElement; for (; i && i !== e[0].documentElement && (a = i, "static" === (t.getComputedStyle(a).position || "static")) ;) i = i.offsetParent; return i || e[0].documentElement }, scrollbarWidth: function (i) { if (i) { if (angular.isUndefined(a)) { var o = e.find("body"); o.addClass("uib-position-body-scrollbar-measure"), a = t.innerWidth - o[0].clientWidth, a = isFinite(a) ? a : 0, o.removeClass("uib-position-body-scrollbar-measure") } return a } if (angular.isUndefined(n)) { var r = angular.element('<div class="uib-position-scrollbar-measure"></div>'); e.find("body").append(r), n = r[0].offsetWidth - r[0].clientWidth, n = isFinite(n) ? n : 0, r.remove() } return n }, scrollbarPadding: function (e) { e = this.getRawNode(e); var n = t.getComputedStyle(e), a = this.parseStyle(n.paddingRight), i = this.parseStyle(n.paddingBottom), o = this.scrollParent(e, !1, !0), l = this.scrollbarWidth(r.test(o.tagName)); return { scrollbarWidth: l, widthOverflow: o.scrollWidth > o.clientWidth, right: a + l, originalRight: a, heightOverflow: o.scrollHeight > o.clientHeight, bottom: i + l, originalBottom: i } }, isScrollable: function (e, n) { e = this.getRawNode(e); var a = n ? i.hidden : i.normal, o = t.getComputedStyle(e); return a.test(o.overflow + o.overflowY + o.overflowX) }, scrollParent: function (n, a, o) { n = this.getRawNode(n); var r = a ? i.hidden : i.normal, l = e[0].documentElement, s = t.getComputedStyle(n); if (o && r.test(s.overflow + s.overflowY + s.overflowX)) return n; var u = "absolute" === s.position, p = n.parentElement || l; if (p === l || "fixed" === s.position) return l; for (; p.parentElement && p !== l;) { var c = t.getComputedStyle(p); if (u && "static" !== c.position && (u = !1), !u && r.test(c.overflow + c.overflowY + c.overflowX)) break; p = p.parentElement } return p }, position: function (n, a) { n = this.getRawNode(n); var i = this.offset(n); if (a) { var o = t.getComputedStyle(n); i.top -= this.parseStyle(o.marginTop), i.left -= this.parseStyle(o.marginLeft) } var r = this.offsetParent(n), l = { top: 0, left: 0 }; return r !== e[0].documentElement && ((l = this.offset(r)).top += r.clientTop - r.scrollTop, l.left += r.clientLeft - r.scrollLeft), { width: Math.round(angular.isNumber(i.width) ? i.width : n.offsetWidth), height: Math.round(angular.isNumber(i.height) ? i.height : n.offsetHeight), top: Math.round(i.top - l.top), left: Math.round(i.left - l.left) } }, offset: function (n) { var a = (n = this.getRawNode(n)).getBoundingClientRect(); return { width: Math.round(angular.isNumber(a.width) ? a.width : n.offsetWidth), height: Math.round(angular.isNumber(a.height) ? a.height : n.offsetHeight), top: Math.round(a.top + (t.pageYOffset || e[0].documentElement.scrollTop)), left: Math.round(a.left + (t.pageXOffset || e[0].documentElement.scrollLeft)) } }, viewportOffset: function (n, a, i) { i = !1 !== i; var o = (n = this.getRawNode(n)).getBoundingClientRect(), r = { top: 0, left: 0, bottom: 0, right: 0 }, l = a ? e[0].documentElement : this.scrollParent(n), s = l.getBoundingClientRect(); if (r.top = s.top + l.clientTop, r.left = s.left + l.clientLeft, l === e[0].documentElement && (r.top += t.pageYOffset, r.left += t.pageXOffset), r.bottom = r.top + l.clientHeight, r.right = r.left + l.clientWidth, i) { var u = t.getComputedStyle(l); r.top += this.parseStyle(u.paddingTop), r.bottom -= this.parseStyle(u.paddingBottom), r.left += this.parseStyle(u.paddingLeft), r.right -= this.parseStyle(u.paddingRight) } return { top: Math.round(o.top - r.top), bottom: Math.round(r.bottom - o.bottom), left: Math.round(o.left - r.left), right: Math.round(r.right - o.right) } }, parsePlacement: function (e) { var t = o.auto.test(e); return t && (e = e.replace(o.auto, "")), (e = e.split("-"))[0] = e[0] || "top", o.primary.test(e[0]) || (e[0] = "top"), e[1] = e[1] || "center", o.secondary.test(e[1]) || (e[1] = "center"), e[2] = !!t, e }, positionElements: function (e, n, a, i) { e = this.getRawNode(e), n = this.getRawNode(n); var r = angular.isDefined(n.offsetWidth) ? n.offsetWidth : n.prop("offsetWidth"), l = angular.isDefined(n.offsetHeight) ? n.offsetHeight : n.prop("offsetHeight"); a = this.parsePlacement(a); var s = i ? this.offset(e) : this.position(e), u = { top: 0, left: 0, placement: "" }; if (a[2]) { var p = this.viewportOffset(e, i), c = t.getComputedStyle(n), d = r + Math.round(Math.abs(this.parseStyle(c.marginLeft) + this.parseStyle(c.marginRight))), m = l + Math.round(Math.abs(this.parseStyle(c.marginTop) + this.parseStyle(c.marginBottom))); if (a[0] = "top" === a[0] && m > p.top && m <= p.bottom ? "bottom" : "bottom" === a[0] && m > p.bottom && m <= p.top ? "top" : "left" === a[0] && d > p.left && d <= p.right ? "right" : "right" === a[0] && d > p.right && d <= p.left ? "left" : a[0], a[1] = "top" === a[1] && m - s.height > p.bottom && m - s.height <= p.top ? "bottom" : "bottom" === a[1] && m - s.height > p.top && m - s.height <= p.bottom ? "top" : "left" === a[1] && d - s.width > p.right && d - s.width <= p.left ? "right" : "right" === a[1] && d - s.width > p.left && d - s.width <= p.right ? "left" : a[1], "center" === a[1]) if (o.vertical.test(a[0])) { var f = s.width / 2 - r / 2; p.left + f < 0 && d - s.width <= p.right ? a[1] = "left" : p.right + f < 0 && d - s.width <= p.left && (a[1] = "right") } else { var g = s.height / 2 - m / 2; p.top + g < 0 && m - s.height <= p.bottom ? a[1] = "top" : p.bottom + g < 0 && m - s.height <= p.top && (a[1] = "bottom") } } switch (a[0]) { case "top": u.top = s.top - l; break; case "bottom": u.top = s.top + s.height; break; case "left": u.left = s.left - r; break; case "right": u.left = s.left + s.width } switch (a[1]) { case "top": u.top = s.top; break; case "bottom": u.top = s.top + s.height - l; break; case "left": u.left = s.left; break; case "right": u.left = s.left + s.width - r; break; case "center": o.vertical.test(a[0]) ? u.left = s.left + s.width / 2 - r / 2 : u.top = s.top + s.height / 2 - l / 2 } return u.top = Math.round(u.top), u.left = Math.round(u.left), u.placement = "center" === a[1] ? a[0] : a[0] + "-" + a[1], u }, adjustTop: function (e, t, n, a) { if (-1 !== e.indexOf("top") && n !== a) return { top: t.top - a + "px" } }, positionArrow: function (e, n) { var a = (e = this.getRawNode(e)).querySelector(".tooltip-inner, .popover-inner"); if (a) { var i = angular.element(a).hasClass("tooltip-inner"), r = i ? e.querySelector(".tooltip-arrow") : e.querySelector(".arrow"); if (r) { var l = { top: "", bottom: "", left: "", right: "" }; if ("center" !== (n = this.parsePlacement(n))[1]) { var s = "border-" + n[0] + "-width", u = t.getComputedStyle(r)[s], p = "border-"; o.vertical.test(n[0]) ? p += n[0] + "-" + n[1] : p += n[1] + "-" + n[0], p += "-radius"; var c = t.getComputedStyle(i ? a : e)[p]; switch (n[0]) { case "top": l.bottom = i ? "0" : "-" + u; break; case "bottom": l.top = i ? "0" : "-" + u; break; case "left": l.right = i ? "0" : "-" + u; break; case "right": l.left = i ? "0" : "-" + u } l[n[1]] = c, angular.element(r).css(l) } else angular.element(r).css(l) } } } } }]), angular.module("ui.bootstrap.datepickerPopup", ["ui.bootstrap.datepicker", "ui.bootstrap.position"]).value("$datepickerPopupLiteralWarning", !0).constant("uibDatepickerPopupConfig", { altInputFormats: [], appendToBody: !1, clearText: "Clear", closeOnDateSelection: !0, closeText: "Done", currentText: "Today", datepickerPopup: "yyyy-MM-dd", datepickerPopupTemplateUrl: "uib/template/datepickerPopup/popup.html", datepickerTemplateUrl: "uib/template/datepicker/datepicker.html", html5Types: { date: "yyyy-MM-dd", "datetime-local": "yyyy-MM-ddTHH:mm:ss.sss", month: "yyyy-MM" }, onOpenFocus: !0, showButtonBar: !0, placement: "auto bottom-left" }).controller("UibDatepickerPopupController", ["$scope", "$element", "$attrs", "$compile", "$log", "$parse", "$window", "$document", "$rootScope", "$uibPosition", "dateFilter", "uibDateParser", "uibDatepickerPopupConfig", "$timeout", "uibDatepickerConfig", "$datepickerPopupLiteralWarning", function (e, t, n, a, i, o, r, l, s, u, p, c, d, m, f, g) { var h, b, v, $, y, w, k, D, x, C, M, T, O, S = !1, P = []; function E(t) { var n = c.parse(t, h, e.date); if (isNaN(n)) for (var a = 0; a < O.length; a++) if (n = c.parse(t, O[a], e.date), !isNaN(n)) return n; return n } function A(e) { if (angular.isNumber(e) && (e = new Date(e)), !e) return null; if (angular.isDate(e) && !isNaN(e)) return e; if (angular.isString(e)) { var t = E(e); if (!isNaN(t)) return c.toTimezone(t, M.getOption("timezone")) } return M.getOption("allowInvalid") ? e : void 0 } function I(e, t) { var a = e || t; return !n.ngRequired && !a || (angular.isNumber(a) && (a = new Date(a)), !a || (!(!angular.isDate(a) || isNaN(a)) || !!angular.isString(a) && !isNaN(E(a)))) } function U(n) { if (e.isOpen || !e.disabled) { var a = T[0], i = t[0].contains(n.target), o = void 0 !== a.contains && a.contains(n.target); !e.isOpen || i || o || e.$apply(function () { e.isOpen = !1 }) } } function N(n) { 27 === n.which && e.isOpen ? (n.preventDefault(), n.stopPropagation(), e.$apply(function () { e.isOpen = !1 }), t[0].focus()) : 40 !== n.which || e.isOpen || (n.preventDefault(), n.stopPropagation(), e.$apply(function () { e.isOpen = !0 })) } function F() { if (e.isOpen) { var a = angular.element(T[0].querySelector(".uib-datepicker-popup")), i = n.popupPlacement ? n.popupPlacement : d.placement, o = u.positionElements(t, a, i, v); a.css({ top: o.top + "px", left: o.left + "px" }), a.hasClass("uib-position-measure") && a.removeClass("uib-position-measure") } } this.init = function (i) { if (M = function (e) { var t; angular.version.minor < 6 ? (t = angular.isObject(e.$options) ? e.$options : { timezone: null }).getOption = function (e) { return t[e] } : t = e.$options; return t }(C = i), b = angular.isDefined(n.closeOnDateSelection) ? e.$parent.$eval(n.closeOnDateSelection) : d.closeOnDateSelection, v = angular.isDefined(n.datepickerAppendToBody) ? e.$parent.$eval(n.datepickerAppendToBody) : d.appendToBody, $ = angular.isDefined(n.onOpenFocus) ? e.$parent.$eval(n.onOpenFocus) : d.onOpenFocus, y = angular.isDefined(n.datepickerPopupTemplateUrl) ? n.datepickerPopupTemplateUrl : d.datepickerPopupTemplateUrl, w = angular.isDefined(n.datepickerTemplateUrl) ? n.datepickerTemplateUrl : d.datepickerTemplateUrl, O = angular.isDefined(n.altInputFormats) ? e.$parent.$eval(n.altInputFormats) : d.altInputFormats, e.showButtonBar = angular.isDefined(n.showButtonBar) ? e.$parent.$eval(n.showButtonBar) : d.showButtonBar, d.html5Types[n.type] ? (h = d.html5Types[n.type], S = !0) : (h = n.uibDatepickerPopup || d.datepickerPopup, n.$observe("uibDatepickerPopup", function (e, t) { var n = e || d.datepickerPopup; if (n !== h && (h = n, C.$modelValue = null, !h)) throw new Error("uibDatepickerPopup must have a date format specified.") })), !h) throw new Error("uibDatepickerPopup must have a date format specified."); if (S && n.uibDatepickerPopup) throw new Error("HTML5 date input types do not support custom formats."); (k = angular.element("<div uib-datepicker-popup-wrap><div uib-datepicker></div></div>")).attr({ "ng-model": "date", "ng-change": "dateSelection(date)", "template-url": y }), (D = angular.element(k.children()[0])).attr("template-url", w), e.datepickerOptions || (e.datepickerOptions = {}), S && "month" === n.type && (e.datepickerOptions.datepickerMode = "month", e.datepickerOptions.minMode = "month"), D.attr("datepicker-options", "datepickerOptions"), S ? C.$formatters.push(function (t) { return e.date = c.fromTimezone(t, M.getOption("timezone")), t }) : (C.$$parserName = "date", C.$validators.date = I, C.$parsers.unshift(A), C.$formatters.push(function (t) { return C.$isEmpty(t) ? (e.date = t, t) : (angular.isNumber(t) && (t = new Date(t)), e.date = c.fromTimezone(t, M.getOption("timezone")), c.filter(e.date, h)) })), C.$viewChangeListeners.push(function () { e.date = E(C.$viewValue) }), t.on("keydown", N), T = a(k)(e), k.remove(), v ? l.find("body").append(T) : t.after(T), e.$on("$destroy", function () { for (!0 === e.isOpen && (s.$$phase || e.$apply(function () { e.isOpen = !1 })), T.remove(), t.off("keydown", N), l.off("click", U), x && x.off("scroll", F), angular.element(r).off("resize", F) ; P.length;) P.shift()() }) }, e.getText = function (t) { return e[t + "Text"] || d[t + "Text"] }, e.isDisabled = function (t) { "today" === t && (t = c.fromTimezone(new Date, M.getOption("timezone"))); var n = {}; return angular.forEach(["minDate", "maxDate"], function (t) { e.datepickerOptions[t] ? angular.isDate(e.datepickerOptions[t]) ? n[t] = new Date(e.datepickerOptions[t]) : (g && i.warn("Literal date support has been deprecated, please switch to date object usage"), n[t] = new Date(p(e.datepickerOptions[t], "medium"))) : n[t] = null }), e.datepickerOptions && n.minDate && e.compare(t, n.minDate) < 0 || n.maxDate && e.compare(t, n.maxDate) > 0 }, e.compare = function (e, t) { return new Date(e.getFullYear(), e.getMonth(), e.getDate()) - new Date(t.getFullYear(), t.getMonth(), t.getDate()) }, e.dateSelection = function (n) { e.date = n; var a = e.date ? c.filter(e.date, h) : null; t.val(a), C.$setViewValue(a), b && (e.isOpen = !1, t[0].focus()) }, e.keydown = function (n) { 27 === n.which && (n.stopPropagation(), e.isOpen = !1, t[0].focus()) }, e.select = function (t, n) { if (n.stopPropagation(), "today" === t) { var a = new Date; angular.isDate(e.date) ? (t = new Date(e.date)).setFullYear(a.getFullYear(), a.getMonth(), a.getDate()) : (t = c.fromTimezone(a, M.getOption("timezone"))).setHours(0, 0, 0, 0) } e.dateSelection(t) }, e.close = function (n) { n.stopPropagation(), e.isOpen = !1, t[0].focus() }, e.disabled = angular.isDefined(n.disabled) || !1, n.ngDisabled && P.push(e.$parent.$watch(o(n.ngDisabled), function (t) { e.disabled = t })), e.$watch("isOpen", function (a) { a ? e.disabled ? e.isOpen = !1 : m(function () { F(), $ && e.$broadcast("uib:datepicker.focus"), l.on("click", U); var a = n.popupPlacement ? n.popupPlacement : d.placement; v || u.parsePlacement(a)[2] ? (x = x || angular.element(u.scrollParent(t))) && x.on("scroll", F) : x = null, angular.element(r).on("resize", F) }, 0, !1) : (l.off("click", U), x && x.off("scroll", F), angular.element(r).off("resize", F)) }), e.$on("uib:datepicker.mode", function () { m(F, 0, !1) }) }]).directive("uibDatepickerPopup", function () { return { require: ["ngModel", "uibDatepickerPopup"], controller: "UibDatepickerPopupController", scope: { datepickerOptions: "=?", isOpen: "=?", currentText: "@", clearText: "@", closeText: "@" }, link: function (e, t, n, a) { var i = a[0]; a[1].init(i) } } }).directive("uibDatepickerPopupWrap", function () { return { restrict: "A", transclude: !0, templateUrl: function (e, t) { return t.templateUrl || "uib/template/datepickerPopup/popup.html" } } }), angular.module("ui.bootstrap.debounce", []).factory("$$debounce", ["$timeout", function (e) { return function (t, n) { var a; return function () { var i = this, o = Array.prototype.slice.call(arguments); a && e.cancel(a), a = e(function () { t.apply(i, o) }, n) } } }]), angular.module("ui.bootstrap.multiMap", []).factory("$$multiMap", function () { return { createNew: function () { var e = {}; return { entries: function () { return Object.keys(e).map(function (t) { return { key: t, value: e[t] } }) }, get: function (t) { return e[t] }, hasKey: function (t) { return !!e[t] }, keys: function () { return Object.keys(e) }, put: function (t, n) { e[t] || (e[t] = []), e[t].push(n) }, remove: function (t, n) { var a = e[t]; if (a) { var i = a.indexOf(n); -1 !== i && a.splice(i, 1), a.length || delete e[t] } } } } } }), angular.module("ui.bootstrap.dropdown", ["ui.bootstrap.multiMap", "ui.bootstrap.position"]).constant("uibDropdownConfig", { appendToOpenClass: "uib-dropdown-open", openClass: "open" }).service("uibDropdownService", ["$document", "$rootScope", "$$multiMap", function (e, t, n) { var a = null, i = n.createNew(); this.isOnlyOpen = function (e, t) { var n = i.get(t); if (n && n.reduce(function (t, n) { return n.scope === e ? n : t }, {})) return 1 === n.length; return !1 }, this.open = function (t, n, r) { if (a || e.on("click", o), a && a !== t && (a.isOpen = !1), a = t, r) { var l = i.get(r); if (l) -1 === l.map(function (e) { return e.scope }).indexOf(t) && i.put(r, { scope: t }); else i.put(r, { scope: t }) } }, this.close = function (t, n, r) { if (a === t && (e.off("click", o), e.off("keydown", this.keybindFilter), a = null), r) { var l = i.get(r); if (l) { var s = l.reduce(function (e, n) { return n.scope === t ? n : e }, {}); s && i.remove(r, s) } } }; var o = function (e) { if (a && a.isOpen && !(e && "disabled" === a.getAutoClose() || e && 3 === e.which)) { var n = a.getToggleElement(); if (!(e && n && n[0].contains(e.target))) { var i = a.getDropdownElement(); e && "outsideClick" === a.getAutoClose() && i && i[0].contains(e.target) || (a.focusToggleElement(), a.isOpen = !1, t.$$phase || a.$apply()) } } }; this.keybindFilter = function (e) { if (a) { var t = a.getDropdownElement(), n = a.getToggleElement(), i = t && t[0].contains(e.target), r = n && n[0].contains(e.target); 27 === e.which ? (e.stopPropagation(), a.focusToggleElement(), o()) : a.isKeynavEnabled() && -1 !== [38, 40].indexOf(e.which) && a.isOpen && (i || r) && (e.preventDefault(), e.stopPropagation(), a.focusDropdownEntry(e.which)) } } }]).controller("UibDropdownController", ["$scope", "$element", "$attrs", "$parse", "uibDropdownConfig", "uibDropdownService", "$animate", "$uibPosition", "$document", "$compile", "$templateRequest", function (e, t, n, a, i, o, r, l, s, u, p) { var c, d, m = this, f = e.$new(), g = i.appendToOpenClass, h = i.openClass, b = angular.noop, v = n.onToggle ? a(n.onToggle) : angular.noop, $ = !1, y = s.find("body"); function w() { t.append(m.dropdownMenu) } t.addClass("dropdown"), this.init = function () { n.isOpen && (d = a(n.isOpen), b = d.assign, e.$watch(d, function (e) { f.isOpen = !!e })), $ = angular.isDefined(n.keyboardNav) }, this.toggle = function (e) { return f.isOpen = arguments.length ? !!e : !f.isOpen, angular.isFunction(b) && b(f, f.isOpen), f.isOpen }, this.isOpen = function () { return f.isOpen }, f.getToggleElement = function () { return m.toggleElement }, f.getAutoClose = function () { return n.autoClose || "always" }, f.getElement = function () { return t }, f.isKeynavEnabled = function () { return $ }, f.focusDropdownEntry = function (e) { var n = m.dropdownMenu ? angular.element(m.dropdownMenu).find("a") : t.find("ul").eq(0).find("a"); switch (e) { case 40: angular.isNumber(m.selectedOption) ? m.selectedOption = m.selectedOption === n.length - 1 ? m.selectedOption : m.selectedOption + 1 : m.selectedOption = 0; break; case 38: angular.isNumber(m.selectedOption) ? m.selectedOption = 0 === m.selectedOption ? 0 : m.selectedOption - 1 : m.selectedOption = n.length - 1 } n[m.selectedOption].focus() }, f.getDropdownElement = function () { return m.dropdownMenu }, f.focusToggleElement = function () { m.toggleElement && m.toggleElement[0].focus() }, f.$watch("isOpen", function (i, d) { var $ = null, k = !1; if (angular.isDefined(n.dropdownAppendTo)) { var D = a(n.dropdownAppendTo)(f); D && ($ = angular.element(D)) } angular.isDefined(n.dropdownAppendToBody) && (!1 !== a(n.dropdownAppendToBody)(f) && (k = !0)); if (k && !$ && ($ = y), $ && m.dropdownMenu && (i ? ($.append(m.dropdownMenu), t.on("$destroy", w)) : (t.off("$destroy", w), w())), $ && m.dropdownMenu) { var x, C, M, T = l.positionElements(t, m.dropdownMenu, "bottom-left", !0), O = 0; if (x = { top: T.top + "px", display: i ? "block" : "none" }, (C = m.dropdownMenu.hasClass("dropdown-menu-right")) ? (x.left = "auto", (M = l.scrollbarPadding($)).heightOverflow && M.scrollbarWidth && (O = M.scrollbarWidth), x.right = window.innerWidth - O - (T.left + t.prop("offsetWidth")) + "px") : (x.left = T.left + "px", x.right = "auto"), !k) { var S = l.offset($); x.top = T.top - S.top + "px", C ? x.right = window.innerWidth - (T.left - S.left + t.prop("offsetWidth")) + "px" : x.left = T.left - S.left + "px" } m.dropdownMenu.css(x) } var P = $ || t, E = $ ? g : h, A = P.hasClass(E), I = o.isOnlyOpen(e, $); A === !i && r[$ ? I ? "removeClass" : "addClass" : i ? "addClass" : "removeClass"](P, E).then(function () { angular.isDefined(i) && i !== d && v(e, { open: !!i }) }); if (i) m.dropdownMenuTemplateUrl ? p(m.dropdownMenuTemplateUrl).then(function (e) { c = f.$new(), u(e.trim())(c, function (e) { var t = e; m.dropdownMenu.replaceWith(t), m.dropdownMenu = t, s.on("keydown", o.keybindFilter) }) }) : s.on("keydown", o.keybindFilter), f.focusToggleElement(), o.open(f, t, $); else { if (o.close(f, t, $), m.dropdownMenuTemplateUrl) { c && c.$destroy(); var U = angular.element('<ul class="dropdown-menu"></ul>'); m.dropdownMenu.replaceWith(U), m.dropdownMenu = U } m.selectedOption = null } angular.isFunction(b) && b(e, i) }) }]).directive("uibDropdown", function () { return { controller: "UibDropdownController", link: function (e, t, n, a) { a.init() } } }).directive("uibDropdownMenu", function () { return { restrict: "A", require: "?^uibDropdown", link: function (e, t, n, a) { if (a && !angular.isDefined(n.dropdownNested)) { t.addClass("dropdown-menu"); var i = n.templateUrl; i && (a.dropdownMenuTemplateUrl = i), a.dropdownMenu || (a.dropdownMenu = t) } } } }).directive("uibDropdownToggle", function () { return { require: "?^uibDropdown", link: function (e, t, n, a) { if (a) { t.addClass("dropdown-toggle"), a.toggleElement = t; var i = function (i) { i.preventDefault(), t.hasClass("disabled") || n.disabled || e.$apply(function () { a.toggle() }) }; t.on("click", i), t.attr({ "aria-haspopup": !0, "aria-expanded": !1 }), e.$watch(a.isOpen, function (e) { t.attr("aria-expanded", !!e) }), e.$on("$destroy", function () { t.off("click", i) }) } } } }), angular.module("ui.bootstrap.stackedMap", []).factory("$$stackedMap", function () { return { createNew: function () { var e = []; return { add: function (t, n) { e.push({ key: t, value: n }) }, get: function (t) { for (var n = 0; n < e.length; n++) if (t === e[n].key) return e[n] }, keys: function () { for (var t = [], n = 0; n < e.length; n++) t.push(e[n].key); return t }, top: function () { return e[e.length - 1] }, remove: function (t) { for (var n = -1, a = 0; a < e.length; a++) if (t === e[a].key) { n = a; break } return e.splice(n, 1)[0] }, removeTop: function () { return e.pop() }, length: function () { return e.length } } } } }), angular.module("ui.bootstrap.modal", ["ui.bootstrap.multiMap", "ui.bootstrap.stackedMap", "ui.bootstrap.position"]).provider("$uibResolve", function () { var e = this; this.resolver = null, this.setResolver = function (e) { this.resolver = e }, this.$get = ["$injector", "$q", function (t, n) { var a = e.resolver ? t.get(e.resolver) : null; return { resolve: function (e, i, o, r) { if (a) return a.resolve(e, i, o, r); var l = []; return angular.forEach(e, function (e) { angular.isFunction(e) || angular.isArray(e) ? l.push(n.resolve(t.invoke(e))) : angular.isString(e) ? l.push(n.resolve(t.get(e))) : l.push(n.resolve(e)) }), n.all(l).then(function (t) { var n = {}, a = 0; return angular.forEach(e, function (e, i) { n[i] = t[a++] }), n }) } } }] }).directive("uibModalBackdrop", ["$animate", "$injector", "$uibModalStack", function (e, t, n) { return { restrict: "A", compile: function (e, t) { return e.addClass(t.backdropClass), a } }; function a(t, a, i) { i.modalInClass && (e.addClass(a, i.modalInClass), t.$on(n.NOW_CLOSING_EVENT, function (n, o) { var r = o(); t.modalOptions.animation ? e.removeClass(a, i.modalInClass).then(r) : r() })) } }]).directive("uibModalWindow", ["$uibModalStack", "$q", "$animateCss", "$document", function (e, t, n, a) { return { scope: { index: "@" }, restrict: "A", transclude: !0, templateUrl: function (e, t) { return t.templateUrl || "uib/template/modal/window.html" }, link: function (i, o, r) { o.addClass(r.windowTopClass || ""), i.size = r.size, i.close = function (t) { var n = e.getTop(); n && n.value.backdrop && "static" !== n.value.backdrop && t.target === t.currentTarget && (t.preventDefault(), t.stopPropagation(), e.dismiss(n.key, "backdrop click")) }, o.on("click", i.close), i.$isRendered = !0; var l = t.defer(); i.$$postDigest(function () { l.resolve() }), l.promise.then(function () { var l = null; r.modalInClass && (l = n(o, { addClass: r.modalInClass }).start(), i.$on(e.NOW_CLOSING_EVENT, function (e, t) { var a = t(); n(o, { removeClass: r.modalInClass }).start().then(a) })), t.when(l).then(function () { var t = e.getTop(); if (t && e.modalRendered(t.key), !a[0].activeElement || !o[0].contains(a[0].activeElement)) { var n = o[0].querySelector("[autofocus]"); n ? n.focus() : o[0].focus() } }) }) } } }]).directive("uibModalAnimationClass", function () { return { compile: function (e, t) { t.modalAnimation && e.addClass(t.uibModalAnimationClass) } } }).directive("uibModalTransclude", ["$animate", function (e) { return { link: function (t, n, a, i, o) { o(t.$parent, function (t) { n.empty(), e.enter(t, n) }) } } }]).factory("$uibModalStack", ["$animate", "$animateCss", "$document", "$compile", "$rootScope", "$q", "$$multiMap", "$$stackedMap", "$uibPosition", function (e, t, n, a, i, o, r, l, s) { var u, p, c, d = "modal-open", m = l.createNew(), f = r.createNew(), g = { NOW_CLOSING_EVENT: "modal.stack.now-closing" }, h = 0, b = null, v = "data-bootstrap-modal-aria-hidden-count", $ = /[A-Z]/g; function y() { for (var e = -1, t = m.keys(), n = 0; n < t.length; n++) m.get(t[n]).value.backdrop && (e = n); return e > -1 && e < h && (e = h), e } function w(e, t) { var n = m.get(e).value, a = n.appendTo; m.remove(e), (b = m.top()) && (h = parseInt(b.value.modalDomEl.attr("index"), 10)), D(n.modalDomEl, n.modalScope, function () { var t = n.openedClass || d; f.remove(t, e); var i = f.hasKey(t); a.toggleClass(t, i), !i && c && c.heightOverflow && c.scrollbarWidth && (c.originalRight ? a.css({ paddingRight: c.originalRight + "px" }) : a.css({ paddingRight: "" }), c = null), k(!0) }, n.closedDeferred), function () { if (u && -1 === y()) { D(u, p, function () { null }), u = void 0, p = void 0 } }(), t && t.focus ? t.focus() : a.focus && a.focus() } function k(e) { var t; m.length() > 0 && (t = m.top().value).modalDomEl.toggleClass(t.windowTopClass || "", e) } function D(t, n, a, i) { var r, l = null; return n.$broadcast(g.NOW_CLOSING_EVENT, function () { return r || (r = o.defer(), l = r.promise), function () { r.resolve() } }), o.when(l).then(function o() { if (o.done) return; o.done = !0; e.leave(t).then(function () { a && a(), t.remove(), i && i.resolve() }); n.$destroy() }) } function x(e) { if (e.isDefaultPrevented()) return e; var t = m.top(); if (t) switch (e.which) { case 27: t.value.keyboard && (e.preventDefault(), i.$apply(function () { g.dismiss(t.key, "escape key press") })); break; case 9: var n = g.loadFocusElementList(t), a = !1; e.shiftKey ? (g.isFocusInFirstItem(e, n) || g.isModalFocused(e, t)) && (a = g.focusLastFocusableElement(n)) : g.isFocusInLastItem(e, n) && (a = g.focusFirstFocusableElement(n)), a && (e.preventDefault(), e.stopPropagation()) } } function C(e, t, n) { return !e.value.modalScope.$broadcast("modal.closing", t, n).defaultPrevented } function M() { Array.prototype.forEach.call(document.querySelectorAll("[" + v + "]"), function (e) { var t = parseInt(e.getAttribute(v), 10) - 1; e.setAttribute(v, t), t || (e.removeAttribute(v), e.removeAttribute("aria-hidden")) }) } return i.$watch(y, function (e) { p && (p.index = e) }), n.on("keydown", x), i.$on("$destroy", function () { n.off("keydown", x) }), g.open = function (t, o) { var r = n[0].activeElement, l = o.openedClass || d; k(!1), b = m.top(), m.add(t, { deferred: o.deferred, renderDeferred: o.renderDeferred, closedDeferred: o.closedDeferred, modalScope: o.scope, backdrop: o.backdrop, keyboard: o.keyboard, openedClass: o.openedClass, windowTopClass: o.windowTopClass, animation: o.animation, appendTo: o.appendTo }), f.put(l, t); var g, w = o.appendTo, D = y(); D >= 0 && !u && ((p = i.$new(!0)).modalOptions = o, p.index = D, (u = angular.element('<div uib-modal-backdrop="modal-backdrop"></div>')).attr({ class: "modal-backdrop", "ng-style": "{'z-index': 1040 + (index && 1 || 0) + index*10}", "uib-modal-animation-class": "fade", "modal-in-class": "in" }), o.backdropClass && u.addClass(o.backdropClass), o.animation && u.attr("modal-animation", "true"), a(u)(p), e.enter(u, w), s.isScrollable(w) && (c = s.scrollbarPadding(w)).heightOverflow && c.scrollbarWidth && w.css({ paddingRight: c.right + "px" })), o.component ? (g = document.createElement(o.component.name.replace($, function (e, t) { return (t ? "-" : "") + e.toLowerCase() })), (g = angular.element(g)).attr({ resolve: "$resolve", "modal-instance": "$uibModalInstance", close: "$close($value)", dismiss: "$dismiss($value)" })) : g = o.content, h = b ? parseInt(b.value.modalDomEl.attr("index"), 10) + 1 : 0; var x = angular.element('<div uib-modal-window="modal-window"></div>'); x.attr({ class: "modal", "template-url": o.windowTemplateUrl, "window-top-class": o.windowTopClass, role: "dialog", "aria-labelledby": o.ariaLabelledBy, "aria-describedby": o.ariaDescribedBy, size: o.size, index: h, animate: "animate", "ng-style": "{'z-index': 1050 + $$topModalIndex*10, display: 'block'}", tabindex: -1, "uib-modal-animation-class": "fade", "modal-in-class": "in" }).append(g), o.windowClass && x.addClass(o.windowClass), o.animation && x.attr("modal-animation", "true"), w.addClass(l), o.scope && (o.scope.$$topModalIndex = h), e.enter(a(x)(o.scope), w), m.top().value.modalDomEl = x, m.top().value.modalOpener = r, function e(t) { if (!t || "BODY" === t[0].tagName) return; (n = t, a = n.parent() ? n.parent().children() : [], Array.prototype.filter.call(a, function (e) { return e !== n[0] })).forEach(function (e) { var t = "true" === e.getAttribute("aria-hidden"), n = parseInt(e.getAttribute(v), 10); n || (n = t ? 1 : 0), e.setAttribute(v, n + 1), e.setAttribute("aria-hidden", "true") }); var n, a; return e(t.parent()) }(x) }, g.close = function (e, t) { var n = m.get(e); return M(), n && C(n, t, !0) ? (n.value.modalScope.$$uibDestructionScheduled = !0, n.value.deferred.resolve(t), w(e, n.value.modalOpener), !0) : !n }, g.dismiss = function (e, t) { var n = m.get(e); return M(), n && C(n, t, !1) ? (n.value.modalScope.$$uibDestructionScheduled = !0, n.value.deferred.reject(t), w(e, n.value.modalOpener), !0) : !n }, g.dismissAll = function (e) { for (var t = this.getTop() ; t && this.dismiss(t.key, e) ;) t = this.getTop() }, g.getTop = function () { return m.top() }, g.modalRendered = function (e) { var t = m.get(e); t && t.value.renderDeferred.resolve() }, g.focusFirstFocusableElement = function (e) { return e.length > 0 && (e[0].focus(), !0) }, g.focusLastFocusableElement = function (e) { return e.length > 0 && (e[e.length - 1].focus(), !0) }, g.isModalFocused = function (e, t) { if (e && t) { var n = t.value.modalDomEl; if (n && n.length) return (e.target || e.srcElement) === n[0] } return !1 }, g.isFocusInFirstItem = function (e, t) { return t.length > 0 && (e.target || e.srcElement) === t[0] }, g.isFocusInLastItem = function (e, t) { return t.length > 0 && (e.target || e.srcElement) === t[t.length - 1] }, g.loadFocusElementList = function (e) { if (e) { var t = e.value.modalDomEl; if (t && t.length) { var n = t[0].querySelectorAll("a[href], area[href], input:not([disabled]):not([tabindex='-1']), button:not([disabled]):not([tabindex='-1']),select:not([disabled]):not([tabindex='-1']), textarea:not([disabled]):not([tabindex='-1']), iframe, object, embed, *[tabindex]:not([tabindex='-1']), *[contenteditable=true]"); return n ? Array.prototype.filter.call(n, function (e) { return !!((t = e).offsetWidth || t.offsetHeight || t.getClientRects().length); var t }) : n } } }, g }]).provider("$uibModal", function () { var e = { options: { animation: !0, backdrop: !0, keyboard: !0 }, $get: ["$rootScope", "$q", "$document", "$templateRequest", "$controller", "$uibResolve", "$uibModalStack", function (t, n, a, i, o, r, l) { var s = {}; var u = null; return s.getPromiseChain = function () { return u }, s.open = function (s) { var p, c, d, m = n.defer(), f = n.defer(), g = n.defer(), h = n.defer(), b = { result: m.promise, opened: f.promise, closed: g.promise, rendered: h.promise, close: function (e) { return l.close(b, e) }, dismiss: function (e) { return l.dismiss(b, e) } }; if ((s = angular.extend({}, e.options, s)).resolve = s.resolve || {}, s.appendTo = s.appendTo || a.find("body").eq(0), !s.appendTo.length) throw new Error("appendTo element not found. Make sure that the element passed is in DOM."); if (!s.component && !s.template && !s.templateUrl) throw new Error("One of component or template or templateUrl options is required."); function v() { return p } return p = s.component ? n.when(r.resolve(s.resolve, {}, null, null)) : n.all([(c = s, c.template ? n.when(c.template) : i(angular.isFunction(c.templateUrl) ? c.templateUrl() : c.templateUrl)), r.resolve(s.resolve, {}, null, null)]), d = u = n.all([u]).then(v, v).then(function (e) { var n = s.scope || t, a = n.$new(); a.$close = b.close, a.$dismiss = b.dismiss, a.$on("$destroy", function () { a.$$uibDestructionScheduled || a.$dismiss("$uibUnscheduledDestruction") }); var i, r, u = { scope: a, deferred: m, renderDeferred: h, closedDeferred: g, animation: s.animation, backdrop: s.backdrop, keyboard: s.keyboard, backdropClass: s.backdropClass, windowTopClass: s.windowTopClass, windowClass: s.windowClass, windowTemplateUrl: s.windowTemplateUrl, ariaLabelledBy: s.ariaLabelledBy, ariaDescribedBy: s.ariaDescribedBy, size: s.size, openedClass: s.openedClass, appendTo: s.appendTo }, p = {}, c = {}; function d(t, n, i, o) { t.$scope = a, t.$scope.$resolve = {}, i ? t.$scope.$uibModalInstance = b : t.$uibModalInstance = b; var r = n ? e[1] : e; angular.forEach(r, function (e, n) { o && (t[n] = e), t.$scope.$resolve[n] = e }) } s.component ? (d(p, !1, !0, !1), p.name = s.component, u.component = p) : s.controller && (d(c, !0, !1, !0), r = o(s.controller, c, !0, s.controllerAs), s.controllerAs && s.bindToController && ((i = r.instance).$close = a.$close, i.$dismiss = a.$dismiss, angular.extend(i, { $resolve: c.$scope.$resolve }, n)), i = r(), angular.isFunction(i.$onInit) && i.$onInit()), s.component || (u.content = e[0]), l.open(b, u), f.resolve(!0) }, function (e) { f.reject(e), m.reject(e) }).finally(function () { u === d && (u = null) }), b }, s }] }; return e }), angular.module("ui.bootstrap.paging", []).factory("uibPaging", ["$parse", function (e) { return { create: function (t, n, a) { t.setNumPages = a.numPages ? e(a.numPages).assign : angular.noop, t.ngModelCtrl = { $setViewValue: angular.noop }, t._watchers = [], t.init = function (e, i) { t.ngModelCtrl = e, t.config = i, e.$render = function () { t.render() }, a.itemsPerPage ? t._watchers.push(n.$parent.$watch(a.itemsPerPage, function (e) { t.itemsPerPage = parseInt(e, 10), n.totalPages = t.calculateTotalPages(), t.updatePage() })) : t.itemsPerPage = i.itemsPerPage, n.$watch("totalItems", function (e, a) { (angular.isDefined(e) || e !== a) && (n.totalPages = t.calculateTotalPages(), t.updatePage()) }) }, t.calculateTotalPages = function () { var e = t.itemsPerPage < 1 ? 1 : Math.ceil(n.totalItems / t.itemsPerPage); return Math.max(e || 0, 1) }, t.render = function () { n.page = parseInt(t.ngModelCtrl.$viewValue, 10) || 1 }, n.selectPage = function (e, a) { a && a.preventDefault(), (!n.ngDisabled || !a) && n.page !== e && e > 0 && e <= n.totalPages && (a && a.target && a.target.blur(), t.ngModelCtrl.$setViewValue(e), t.ngModelCtrl.$render()) }, n.getText = function (e) { return n[e + "Text"] || t.config[e + "Text"] }, n.noPrevious = function () { return 1 === n.page }, n.noNext = function () { return n.page === n.totalPages }, t.updatePage = function () { t.setNumPages(n.$parent, n.totalPages), n.page > n.totalPages ? n.selectPage(n.totalPages) : t.ngModelCtrl.$render() }, n.$on("$destroy", function () { for (; t._watchers.length;) t._watchers.shift()() }) } } }]), angular.module("ui.bootstrap.pager", ["ui.bootstrap.paging", "ui.bootstrap.tabindex"]).controller("UibPagerController", ["$scope", "$attrs", "uibPaging", "uibPagerConfig", function (e, t, n, a) { e.align = angular.isDefined(t.align) ? e.$parent.$eval(t.align) : a.align, n.create(this, e, t) }]).constant("uibPagerConfig", { itemsPerPage: 10, previousText: "« Previous", nextText: "Next »", align: !0 }).directive("uibPager", ["uibPagerConfig", function (e) { return { scope: { totalItems: "=", previousText: "@", nextText: "@", ngDisabled: "=" }, require: ["uibPager", "?ngModel"], restrict: "A", controller: "UibPagerController", controllerAs: "pager", templateUrl: function (e, t) { return t.templateUrl || "uib/template/pager/pager.html" }, link: function (t, n, a, i) { n.addClass("pager"); var o = i[0], r = i[1]; r && o.init(r, e) } } }]), angular.module("ui.bootstrap.pagination", ["ui.bootstrap.paging", "ui.bootstrap.tabindex"]).controller("UibPaginationController", ["$scope", "$attrs", "$parse", "uibPaging", "uibPaginationConfig", function (e, t, n, a, i) { var o = this, r = angular.isDefined(t.maxSize) ? e.$parent.$eval(t.maxSize) : i.maxSize, l = angular.isDefined(t.rotate) ? e.$parent.$eval(t.rotate) : i.rotate, s = angular.isDefined(t.forceEllipses) ? e.$parent.$eval(t.forceEllipses) : i.forceEllipses, u = angular.isDefined(t.boundaryLinkNumbers) ? e.$parent.$eval(t.boundaryLinkNumbers) : i.boundaryLinkNumbers, p = angular.isDefined(t.pageLabel) ? function (n) { return e.$parent.$eval(t.pageLabel, { $page: n }) } : angular.identity; function c(e, t, n) { return { number: e, text: t, active: n } } e.boundaryLinks = angular.isDefined(t.boundaryLinks) ? e.$parent.$eval(t.boundaryLinks) : i.boundaryLinks, e.directionLinks = angular.isDefined(t.directionLinks) ? e.$parent.$eval(t.directionLinks) : i.directionLinks, t.$set("role", "menu"), a.create(this, e, t), t.maxSize && o._watchers.push(e.$parent.$watch(n(t.maxSize), function (e) { r = parseInt(e, 10), o.render() })); var d = this.render; this.render = function () { d(), e.page > 0 && e.page <= e.totalPages && (e.pages = function (e, t) { var n = [], a = 1, i = t, o = angular.isDefined(r) && r < t; o && (l ? (i = (a = Math.max(e - Math.floor(r / 2), 1)) + r - 1) > t && (a = (i = t) - r + 1) : (a = (Math.ceil(e / r) - 1) * r + 1, i = Math.min(a + r - 1, t))); for (var d = a; d <= i; d++) { var m = c(d, p(d), d === e); n.push(m) } if (o && r > 0 && (!l || s || u)) { if (a > 1) { if (!u || a > 3) { var f = c(a - 1, "...", !1); n.unshift(f) } if (u) { if (3 === a) { var g = c(2, "2", !1); n.unshift(g) } var h = c(1, "1", !1); n.unshift(h) } } if (i < t) { if (!u || i < t - 2) { var b = c(i + 1, "...", !1); n.push(b) } if (u) { if (i === t - 2) { var v = c(t - 1, t - 1, !1); n.push(v) } var $ = c(t, t, !1); n.push($) } } } return n }(e.page, e.totalPages)) } }]).constant("uibPaginationConfig", { itemsPerPage: 10, boundaryLinks: !1, boundaryLinkNumbers: !1, directionLinks: !0, firstText: "First", previousText: "Previous", nextText: "Next", lastText: "Last", rotate: !0, forceEllipses: !1 }).directive("uibPagination", ["$parse", "uibPaginationConfig", function (e, t) { return { scope: { totalItems: "=", firstText: "@", previousText: "@", nextText: "@", lastText: "@", ngDisabled: "=" }, require: ["uibPagination", "?ngModel"], restrict: "A", controller: "UibPaginationController", controllerAs: "pagination", templateUrl: function (e, t) { return t.templateUrl || "uib/template/pagination/pagination.html" }, link: function (e, n, a, i) { n.addClass("pagination"); var o = i[0], r = i[1]; r && o.init(r, t) } } }]), angular.module("ui.bootstrap.tooltip", ["ui.bootstrap.position", "ui.bootstrap.stackedMap"]).provider("$uibTooltip", function () { var e = { placement: "top", placementClassPrefix: "", animation: !0, popupDelay: 0, popupCloseDelay: 0, useContentExp: !1 }, t = { mouseenter: "mouseleave", click: "click", outsideClick: "outsideClick", focus: "blur", none: "" }, n = {}; this.options = function (e) { angular.extend(n, e) }, this.setTriggers = function (e) { angular.extend(t, e) }, this.$get = ["$window", "$compile", "$timeout", "$document", "$uibPosition", "$interpolate", "$rootScope", "$parse", "$$stackedMap", function (a, i, o, r, l, s, u, p, c) { var d = c.createNew(); function m(e) { if (27 === e.which) { var t = d.top(); t && (t.value.close(), t = null) } } return r.on("keyup", m), u.$on("$destroy", function () { r.off("keyup", m) }), function (a, u, c, m) { function f(e) { var n = (e || m.trigger || c).split(" "); return { show: n, hide: n.map(function (e) { return t[e] || e }) } } m = angular.extend({}, e, n, m); var g = a.replace(/[A-Z]/g, function (e, t) { return (t ? "-" : "") + e.toLowerCase() }), h = s.startSymbol(), b = s.endSymbol(), v = "<div " + g + '-popup uib-title="' + h + "title" + b + '" ' + (m.useContentExp ? 'content-exp="contentExp()" ' : 'content="' + h + "content" + b + '" ') + 'origin-scope="origScope" class="uib-position-measure ' + u + '" tooltip-animation-class="fade"uib-tooltip-classes ng-class="{ in: isOpen }" ></div>'; return { compile: function (e, t) { var n = i(v); return function (e, t, i, s) { var c, g, h, b, v, $, y, w, k = !!angular.isDefined(m.appendToBody) && m.appendToBody, D = f(void 0), x = angular.isDefined(i[u + "Enable"]), C = e.$new(!0), M = !1, T = !!angular.isDefined(i[u + "IsOpen"]) && p(i[u + "IsOpen"]), O = !!m.useContentExp && p(i[a]), S = [], P = function () { c && c.html() && ($ || ($ = o(function () { var e = l.positionElements(t, c, C.placement, k), n = angular.isDefined(c.offsetHeight) ? c.offsetHeight : c.prop("offsetHeight"), a = k ? l.offset(t) : l.position(t); c.css({ top: e.top + "px", left: e.left + "px" }); var i = e.placement.split("-"); c.hasClass(i[0]) || (c.removeClass(w.split("-")[0]), c.addClass(i[0])), c.hasClass(m.placementClassPrefix + e.placement) || (c.removeClass(m.placementClassPrefix + w), c.addClass(m.placementClassPrefix + e.placement)), y = o(function () { var e = angular.isDefined(c.offsetHeight) ? c.offsetHeight : c.prop("offsetHeight"), t = l.adjustTop(i, a, n, e); t && c.css(t), y = null }, 0, !1), c.hasClass("uib-position-measure") ? (l.positionArrow(c, e.placement), c.removeClass("uib-position-measure")) : w !== e.placement && l.positionArrow(c, e.placement), w = e.placement, $ = null }, 0, !1))) }; function E() { C.isOpen ? I() : A() } function A() { x && !e.$eval(i[u + "Enable"]) || (H(), function () { C.title = i[u + "Title"], C.content = O ? O(e) : i[a]; C.popupClass = i[u + "Class"], C.placement = angular.isDefined(i[u + "Placement"]) ? i[u + "Placement"] : m.placement; var t = l.parsePlacement(C.placement); w = t[1] ? t[0] + "-" + t[1] : t[0]; var n = parseInt(i[u + "PopupDelay"], 10), o = parseInt(i[u + "PopupCloseDelay"], 10); C.popupDelay = isNaN(n) ? m.popupDelay : n, C.popupCloseDelay = isNaN(o) ? m.popupCloseDelay : o }(), C.popupDelay ? b || (b = o(U, C.popupDelay, !1)) : U()) } function I() { N(), C.popupCloseDelay ? v || (v = o(F, C.popupCloseDelay, !1)) : F() } function U() { if (N(), H(), !C.content) return angular.noop; !function () { if (c) return; g = C.$new(), c = n(g, function (e) { k ? r.find("body").append(e) : t.after(e) }), d.add(C, { close: F }), function () { S.length = 0, O ? (S.push(e.$watch(O, function (e) { C.content = e, !e && C.isOpen && F() })), S.push(g.$watch(function () { M || (M = !0, g.$$postDigest(function () { M = !1, C && C.isOpen && P() })) }))) : S.push(i.$observe(a, function (e) { C.content = e, !e && C.isOpen ? F() : P() })); S.push(i.$observe(u + "Title", function (e) { C.title = e, C.isOpen && P() })), S.push(i.$observe(u + "Placement", function (e) { C.placement = e || m.placement, C.isOpen && P() })) }() }(), C.$evalAsync(function () { C.isOpen = !0, V(!0), P() }) } function N() { b && (o.cancel(b), b = null), $ && (o.cancel($), $ = null) } function F() { C && C.$evalAsync(function () { C && (C.isOpen = !1, V(!1), C.animation ? h || (h = o(R, 150, !1)) : R()) }) } function H() { v && (o.cancel(v), v = null), h && (o.cancel(h), h = null) } function R() { N(), H(), S.length && (angular.forEach(S, function (e) { e() }), S.length = 0), c && (c.remove(), c = null, y && o.cancel(y)), d.remove(C), g && (g.$destroy(), g = null) } function V(t) { T && angular.isFunction(T.assign) && T.assign(e, t) } function q(e) { C && C.isOpen && c && (t[0].contains(e.target) || c[0].contains(e.target) || I()) } function L(e) { 27 === e.which && I() } C.origScope = e, C.isOpen = !1, C.contentExp = function () { return C.content }, i.$observe("disabled", function (e) { e && N(), e && C.isOpen && F() }), T && e.$watch(T, function (e) { C && !e === C.isOpen && E() }); var z, Y, B, W = function () { D.show.forEach(function (e) { "outsideClick" === e ? t.off("click", E) : (t.off(e, A), t.off(e, E)), t.off("keypress", L) }), D.hide.forEach(function (e) { "outsideClick" === e ? r.off("click", q) : t.off(e, I) }) }; z = [], Y = [], B = e.$eval(i[u + "Trigger"]), W(), angular.isObject(B) ? (Object.keys(B).forEach(function (e) { z.push(e), Y.push(B[e]) }), D = { show: z, hide: Y }) : D = f(B), "none" !== D.show && D.show.forEach(function (e, n) { "outsideClick" === e ? (t.on("click", E), r.on("click", q)) : e === D.hide[n] ? t.on(e, E) : e && (t.on(e, A), t.on(D.hide[n], I)), t.on("keypress", L) }); var j, _ = e.$eval(i[u + "Animation"]); C.animation = angular.isDefined(_) ? !!_ : m.animation; var G = u + "AppendToBody"; j = G in i && void 0 === i[G] || e.$eval(i[G]), k = angular.isDefined(j) ? j : k, e.$on("$destroy", function () { W(), R(), C = null }) } } } } }] }).directive("uibTooltipTemplateTransclude", ["$animate", "$sce", "$compile", "$templateRequest", function (e, t, n, a) { return { link: function (i, o, r) { var l, s, u, p = i.$eval(r.tooltipTemplateTranscludeScope), c = 0, d = function () { s && (s.remove(), s = null), l && (l.$destroy(), l = null), u && (e.leave(u).then(function () { s = null }), s = u, u = null) }; i.$watch(t.parseAsResourceUrl(r.uibTooltipTemplateTransclude), function (t) { var r = ++c; t ? (a(t, !0).then(function (a) { if (r === c) { var i = p.$new(), s = n(a)(i, function (t) { d(), e.enter(t, o) }); u = s, (l = i).$emit("$includeContentLoaded", t) } }, function () { r === c && (d(), i.$emit("$includeContentError", t)) }), i.$emit("$includeContentRequested", t)) : d() }), i.$on("$destroy", d) } } }]).directive("uibTooltipClasses", ["$uibPosition", function (e) { return { restrict: "A", link: function (t, n, a) { if (t.placement) { var i = e.parsePlacement(t.placement); n.addClass(i[0]) } t.popupClass && n.addClass(t.popupClass), t.animation && n.addClass(a.tooltipAnimationClass) } } }]).directive("uibTooltipPopup", function () { return { restrict: "A", scope: { content: "@" }, templateUrl: "uib/template/tooltip/tooltip-popup.html" } }).directive("uibTooltip", ["$uibTooltip", function (e) { return e("uibTooltip", "tooltip", "mouseenter") }]).directive("uibTooltipTemplatePopup", function () { return { restrict: "A", scope: { contentExp: "&", originScope: "&" }, templateUrl: "uib/template/tooltip/tooltip-template-popup.html" } }).directive("uibTooltipTemplate", ["$uibTooltip", function (e) { return e("uibTooltipTemplate", "tooltip", "mouseenter", { useContentExp: !0 }) }]).directive("uibTooltipHtmlPopup", function () { return { restrict: "A", scope: { contentExp: "&" }, templateUrl: "uib/template/tooltip/tooltip-html-popup.html" } }).directive("uibTooltipHtml", ["$uibTooltip", function (e) { return e("uibTooltipHtml", "tooltip", "mouseenter", { useContentExp: !0 }) }]), angular.module("ui.bootstrap.popover", ["ui.bootstrap.tooltip"]).directive("uibPopoverTemplatePopup", function () { return { restrict: "A", scope: { uibTitle: "@", contentExp: "&", originScope: "&" }, templateUrl: "uib/template/popover/popover-template.html" } }).directive("uibPopoverTemplate", ["$uibTooltip", function (e) { return e("uibPopoverTemplate", "popover", "click", { useContentExp: !0 }) }]).directive("uibPopoverHtmlPopup", function () { return { restrict: "A", scope: { contentExp: "&", uibTitle: "@" }, templateUrl: "uib/template/popover/popover-html.html" } }).directive("uibPopoverHtml", ["$uibTooltip", function (e) { return e("uibPopoverHtml", "popover", "click", { useContentExp: !0 }) }]).directive("uibPopoverPopup", function () { return { restrict: "A", scope: { uibTitle: "@", content: "@" }, templateUrl: "uib/template/popover/popover.html" } }).directive("uibPopover", ["$uibTooltip", function (e) { return e("uibPopover", "popover", "click") }]), angular.module("ui.bootstrap.progressbar", []).constant("uibProgressConfig", { animate: !0, max: 100 }).controller("UibProgressController", ["$scope", "$attrs", "uibProgressConfig", function (e, t, n) { var a = this, i = angular.isDefined(t.animate) ? e.$parent.$eval(t.animate) : n.animate; function o() { return angular.isDefined(e.maxParam) ? e.maxParam : n.max } this.bars = [], e.max = o(), this.addBar = function (e, t, n) { i || t.css({ transition: "none" }), this.bars.push(e), e.max = o(), e.title = n && angular.isDefined(n.title) ? n.title : "progressbar", e.$watch("value", function (t) { e.recalculatePercentage() }), e.recalculatePercentage = function () { var t = a.bars.reduce(function (e, t) { return t.percent = +(100 * t.value / t.max).toFixed(2), e + t.percent }, 0); t > 100 && (e.percent -= t - 100) }, e.$on("$destroy", function () { t = null, a.removeBar(e) }) }, this.removeBar = function (e) { this.bars.splice(this.bars.indexOf(e), 1), this.bars.forEach(function (e) { e.recalculatePercentage() }) }, e.$watch("maxParam", function (e) { a.bars.forEach(function (e) { e.max = o(), e.recalculatePercentage() }) }) }]).directive("uibProgress", function () { return { replace: !0, transclude: !0, controller: "UibProgressController", require: "uibProgress", scope: { maxParam: "=?max" }, templateUrl: "uib/template/progressbar/progress.html" } }).directive("uibBar", function () { return { replace: !0, transclude: !0, require: "^uibProgress", scope: { value: "=", type: "@" }, templateUrl: "uib/template/progressbar/bar.html", link: function (e, t, n, a) { a.addBar(e, t, n) } } }).directive("uibProgressbar", function () { return { replace: !0, transclude: !0, controller: "UibProgressController", scope: { value: "=", maxParam: "=?max", type: "@" }, templateUrl: "uib/template/progressbar/progressbar.html", link: function (e, t, n, a) { a.addBar(e, angular.element(t.children()[0]), { title: n.title }) } } }), angular.module("ui.bootstrap.rating", []).constant("uibRatingConfig", { max: 5, stateOn: null, stateOff: null, enableReset: !0, titles: ["one", "two", "three", "four", "five"] }).controller("UibRatingController", ["$scope", "$attrs", "uibRatingConfig", function (e, t, n) { var a = { $setViewValue: angular.noop }, i = this; this.init = function (i) { (a = i).$render = this.render, a.$formatters.push(function (e) { return angular.isNumber(e) && e << 0 !== e && (e = Math.round(e)), e }), this.stateOn = angular.isDefined(t.stateOn) ? e.$parent.$eval(t.stateOn) : n.stateOn, this.stateOff = angular.isDefined(t.stateOff) ? e.$parent.$eval(t.stateOff) : n.stateOff, this.enableReset = angular.isDefined(t.enableReset) ? e.$parent.$eval(t.enableReset) : n.enableReset; var o = angular.isDefined(t.titles) ? e.$parent.$eval(t.titles) : n.titles; this.titles = angular.isArray(o) && o.length > 0 ? o : n.titles; var r = angular.isDefined(t.ratingStates) ? e.$parent.$eval(t.ratingStates) : new Array(angular.isDefined(t.max) ? e.$parent.$eval(t.max) : n.max); e.range = this.buildTemplateObjects(r) }, this.buildTemplateObjects = function (e) { for (var t = 0, n = e.length; t < n; t++) e[t] = angular.extend({ index: t }, { stateOn: this.stateOn, stateOff: this.stateOff, title: this.getTitle(t) }, e[t]); return e }, this.getTitle = function (e) { return e >= this.titles.length ? e + 1 : this.titles[e] }, e.rate = function (t) { if (!e.readonly && t >= 0 && t <= e.range.length) { var n = i.enableReset && a.$viewValue === t ? 0 : t; a.$setViewValue(n), a.$render() } }, e.enter = function (t) { e.readonly || (e.value = t), e.onHover({ value: t }) }, e.reset = function () { e.value = a.$viewValue, e.onLeave() }, e.onKeydown = function (t) { /(37|38|39|40)/.test(t.which) && (t.preventDefault(), t.stopPropagation(), e.rate(e.value + (38 === t.which || 39 === t.which ? 1 : -1))) }, this.render = function () { e.value = a.$viewValue, e.title = i.getTitle(e.value - 1) } }]).directive("uibRating", function () { return { require: ["uibRating", "ngModel"], restrict: "A", scope: { readonly: "=?readOnly", onHover: "&", onLeave: "&" }, controller: "UibRatingController", templateUrl: "uib/template/rating/rating.html", link: function (e, t, n, a) { var i = a[0], o = a[1]; i.init(o) } } }), angular.module("ui.bootstrap.tabs", []).controller("UibTabsetController", ["$scope", function (e) { var t, n, a = this; function i(e) { for (var t = 0; t < a.tabs.length; t++) if (a.tabs[t].index === e) return t } a.tabs = [], a.select = function (e, o) { if (!n) { var r = i(t), l = a.tabs[r]; if (l) { if (l.tab.onDeselect({ $event: o, $selectedIndex: e }), o && o.isDefaultPrevented()) return; l.tab.active = !1 } var s = a.tabs[e]; s ? (s.tab.onSelect({ $event: o }), s.tab.active = !0, a.active = s.index, t = s.index) : !s && angular.isDefined(t) && (a.active = null, t = null) } }, a.addTab = function (e) { if (a.tabs.push({ tab: e, index: e.index }), a.tabs.sort(function (e, t) { return e.index > t.index ? 1 : e.index < t.index ? -1 : 0 }), e.index === a.active || !angular.isDefined(a.active) && 1 === a.tabs.length) { var t = i(e.index); a.select(t) } }, a.removeTab = function (e) { for (var t, n = 0; n < a.tabs.length; n++) if (a.tabs[n].tab === e) { t = n; break } if (a.tabs[t].index === a.active) { var i = t === a.tabs.length - 1 ? t - 1 : t + 1 % a.tabs.length; a.select(i) } a.tabs.splice(t, 1) }, e.$watch("tabset.active", function (e) { angular.isDefined(e) && e !== t && a.select(i(e)) }), e.$on("$destroy", function () { n = !0 }) }]).directive("uibTabset", function () { return { transclude: !0, replace: !0, scope: {}, bindToController: { active: "=?", type: "@" }, controller: "UibTabsetController", controllerAs: "tabset", templateUrl: function (e, t) { return t.templateUrl || "uib/template/tabs/tabset.html" }, link: function (e, t, n) { e.vertical = !!angular.isDefined(n.vertical) && e.$parent.$eval(n.vertical), e.justified = !!angular.isDefined(n.justified) && e.$parent.$eval(n.justified) } } }).directive("uibTab", ["$parse", function (e) { return { require: "^uibTabset", replace: !0, templateUrl: function (e, t) { return t.templateUrl || "uib/template/tabs/tab.html" }, transclude: !0, scope: { heading: "@", index: "=?", classes: "@?", onSelect: "&select", onDeselect: "&deselect" }, controller: function () { }, controllerAs: "tab", link: function (t, n, a, i, o) { t.disabled = !1, a.disable && t.$parent.$watch(e(a.disable), function (e) { t.disabled = !!e }), angular.isUndefined(a.index) && (i.tabs && i.tabs.length ? t.index = Math.max.apply(null, i.tabs.map(function (e) { return e.index })) + 1 : t.index = 0), angular.isUndefined(a.classes) && (t.classes = ""), t.select = function (e) { if (!t.disabled) { for (var n, a = 0; a < i.tabs.length; a++) if (i.tabs[a].tab === t) { n = a; break } i.select(n, e) } }, i.addTab(t), t.$on("$destroy", function () { i.removeTab(t) }), t.$transcludeFn = o } } }]).directive("uibTabHeadingTransclude", function () { return { restrict: "A", require: "^uibTab", link: function (e, t) { e.$watch("headingElement", function (e) { e && (t.html(""), t.append(e)) }) } } }).directive("uibTabContentTransclude", function () { return { restrict: "A", require: "^uibTabset", link: function (e, t, n) { var a = e.$eval(n.uibTabContentTransclude).tab; a.$transcludeFn(a.$parent, function (e) { angular.forEach(e, function (e) { var n; (n = e).tagName && (n.hasAttribute("uib-tab-heading") || n.hasAttribute("data-uib-tab-heading") || n.hasAttribute("x-uib-tab-heading") || "uib-tab-heading" === n.tagName.toLowerCase() || "data-uib-tab-heading" === n.tagName.toLowerCase() || "x-uib-tab-heading" === n.tagName.toLowerCase() || "uib:tab-heading" === n.tagName.toLowerCase()) ? a.headingElement = e : t.append(e) }) }) } } }), angular.module("ui.bootstrap.timepicker", []).constant("uibTimepickerConfig", { hourStep: 1, minuteStep: 1, secondStep: 1, showMeridian: !0, showSeconds: !1, meridians: null, readonlyInput: !1, mousewheel: !0, arrowkeys: !0, showSpinners: !0, templateUrl: "uib/template/timepicker/timepicker.html" }).controller("UibTimepickerController", ["$scope", "$element", "$attrs", "$parse", "$log", "$locale", "uibTimepickerConfig", function (e, t, n, a, i, o, r) { var l, s, u, p = new Date, c = [], d = { $setViewValue: angular.noop }, m = angular.isDefined(n.meridians) ? e.$parent.$eval(n.meridians) : r.meridians || o.DATETIME_FORMATS.AMPMS, f = !angular.isDefined(n.padHours) || e.$parent.$eval(n.padHours); e.tabindex = angular.isDefined(n.tabindex) ? n.tabindex : 0, t.removeAttr("tabindex"), this.init = function (t, a) { (d = t).$render = this.render, d.$formatters.unshift(function (e) { return e ? new Date(e) : null }); var i = a.eq(0), o = a.eq(1), p = a.eq(2); l = i.controller("ngModel"), s = o.controller("ngModel"), u = p.controller("ngModel"), (angular.isDefined(n.mousewheel) ? e.$parent.$eval(n.mousewheel) : r.mousewheel) && this.setupMousewheelEvents(i, o, p), (angular.isDefined(n.arrowkeys) ? e.$parent.$eval(n.arrowkeys) : r.arrowkeys) && this.setupArrowkeyEvents(i, o, p), e.readonlyInput = angular.isDefined(n.readonlyInput) ? e.$parent.$eval(n.readonlyInput) : r.readonlyInput, this.setupInputEvents(i, o, p) }; var g = r.hourStep; n.hourStep && c.push(e.$parent.$watch(a(n.hourStep), function (e) { g = +e })); var h, b, v = r.minuteStep; n.minuteStep && c.push(e.$parent.$watch(a(n.minuteStep), function (e) { v = +e })), c.push(e.$parent.$watch(a(n.min), function (e) { var t = new Date(e); h = isNaN(t) ? void 0 : t })), c.push(e.$parent.$watch(a(n.max), function (e) { var t = new Date(e); b = isNaN(t) ? void 0 : t })); var $ = !1; n.ngDisabled && c.push(e.$parent.$watch(a(n.ngDisabled), function (e) { $ = e })), e.noIncrementHours = function () { var e = O(p, 60 * g); return $ || e > b || e < p && e < h }, e.noDecrementHours = function () { var e = O(p, 60 * -g); return $ || e < h || e > p && e > b }, e.noIncrementMinutes = function () { var e = O(p, v); return $ || e > b || e < p && e < h }, e.noDecrementMinutes = function () { var e = O(p, -v); return $ || e < h || e > p && e > b }, e.noIncrementSeconds = function () { var e = S(p, y); return $ || e > b || e < p && e < h }, e.noDecrementSeconds = function () { var e = S(p, -y); return $ || e < h || e > p && e > b }, e.noToggleMeridian = function () { return p.getHours() < 12 ? $ || O(p, 720) > b : $ || O(p, -720) < h }; var y = r.secondStep; function w() { var t = +e.hours; if ((e.showMeridian ? t > 0 && t < 13 : t >= 0 && t < 24) && "" !== e.hours) return e.showMeridian && (12 === t && (t = 0), e.meridian === m[1] && (t += 12)), t } function k() { var t = +e.minutes; if (t >= 0 && t < 60 && "" !== e.minutes) return t } function D(e, t) { return null === e ? "" : angular.isDefined(e) && e.toString().length < 2 && !t ? "0" + e : e.toString() } function x(e) { C(), d.$setViewValue(new Date(p)), M(e) } function C() { l && l.$setValidity("hours", !0), s && s.$setValidity("minutes", !0), u && u.$setValidity("seconds", !0), d.$setValidity("time", !0), e.invalidHours = !1, e.invalidMinutes = !1, e.invalidSeconds = !1 } function M(t) { if (d.$modelValue) { var n = p.getHours(), a = p.getMinutes(), i = p.getSeconds(); e.showMeridian && (n = 0 === n || 12 === n ? 12 : n % 12), e.hours = "h" === t ? n : D(n, !f), "m" !== t && (e.minutes = D(a)), e.meridian = p.getHours() < 12 ? m[0] : m[1], "s" !== t && (e.seconds = D(i)), e.meridian = p.getHours() < 12 ? m[0] : m[1] } else e.hours = null, e.minutes = null, e.seconds = null, e.meridian = m[0] } function T(e) { p = S(p, e), x() } function O(e, t) { return S(e, 60 * t) } function S(e, t) { var n = new Date(e.getTime() + 1e3 * t), a = new Date(e); return a.setHours(n.getHours(), n.getMinutes(), n.getSeconds()), a } function P() { return (null === e.hours || "" === e.hours) && (null === e.minutes || "" === e.minutes) && (!e.showSeconds || e.showSeconds && (null === e.seconds || "" === e.seconds)) } n.secondStep && c.push(e.$parent.$watch(a(n.secondStep), function (e) { y = +e })), e.showSeconds = r.showSeconds, n.showSeconds && c.push(e.$parent.$watch(a(n.showSeconds), function (t) { e.showSeconds = !!t })), e.showMeridian = r.showMeridian, n.showMeridian && c.push(e.$parent.$watch(a(n.showMeridian), function (t) { if (e.showMeridian = !!t, d.$error.time) { var n = w(), a = k(); angular.isDefined(n) && angular.isDefined(a) && (p.setHours(n), x()) } else M() })), this.setupMousewheelEvents = function (t, n, a) { var i = function (e) { e.originalEvent && (e = e.originalEvent); var t = e.wheelDelta ? e.wheelDelta : -e.deltaY; return e.detail || t > 0 }; t.on("mousewheel wheel", function (t) { $ || e.$apply(i(t) ? e.incrementHours() : e.decrementHours()), t.preventDefault() }), n.on("mousewheel wheel", function (t) { $ || e.$apply(i(t) ? e.incrementMinutes() : e.decrementMinutes()), t.preventDefault() }), a.on("mousewheel wheel", function (t) { $ || e.$apply(i(t) ? e.incrementSeconds() : e.decrementSeconds()), t.preventDefault() }) }, this.setupArrowkeyEvents = function (t, n, a) { t.on("keydown", function (t) { $ || (38 === t.which ? (t.preventDefault(), e.incrementHours(), e.$apply()) : 40 === t.which && (t.preventDefault(), e.decrementHours(), e.$apply())) }), n.on("keydown", function (t) { $ || (38 === t.which ? (t.preventDefault(), e.incrementMinutes(), e.$apply()) : 40 === t.which && (t.preventDefault(), e.decrementMinutes(), e.$apply())) }), a.on("keydown", function (t) { $ || (38 === t.which ? (t.preventDefault(), e.incrementSeconds(), e.$apply()) : 40 === t.which && (t.preventDefault(), e.decrementSeconds(), e.$apply())) }) }, this.setupInputEvents = function (t, n, a) { if (e.readonlyInput) return e.updateHours = angular.noop, e.updateMinutes = angular.noop, void (e.updateSeconds = angular.noop); var i = function (t, n, a) { d.$setViewValue(null), d.$setValidity("time", !1), angular.isDefined(t) && (e.invalidHours = t, l && l.$setValidity("hours", !1)), angular.isDefined(n) && (e.invalidMinutes = n, s && s.$setValidity("minutes", !1)), angular.isDefined(a) && (e.invalidSeconds = a, u && u.$setValidity("seconds", !1)) }; e.updateHours = function () { var e = w(), t = k(); d.$setDirty(), angular.isDefined(e) && angular.isDefined(t) ? (p.setHours(e), p.setMinutes(t), p < h || p > b ? i(!0) : x("h")) : i(!0) }, t.on("blur", function (t) { d.$setTouched(), P() ? C() : null === e.hours || "" === e.hours ? i(!0) : !e.invalidHours && e.hours < 10 && e.$apply(function () { e.hours = D(e.hours, !f) }) }), e.updateMinutes = function () { var e = k(), t = w(); d.$setDirty(), angular.isDefined(e) && angular.isDefined(t) ? (p.setHours(t), p.setMinutes(e), p < h || p > b ? i(void 0, !0) : x("m")) : i(void 0, !0) }, n.on("blur", function (t) { d.$setTouched(), P() ? C() : null === e.minutes ? i(void 0, !0) : !e.invalidMinutes && e.minutes < 10 && e.$apply(function () { e.minutes = D(e.minutes) }) }), e.updateSeconds = function () { var t, n = (t = +e.seconds) >= 0 && t < 60 ? t : void 0; d.$setDirty(), angular.isDefined(n) ? (p.setSeconds(n), x("s")) : i(void 0, void 0, !0) }, a.on("blur", function (t) { P() ? C() : !e.invalidSeconds && e.seconds < 10 && e.$apply(function () { e.seconds = D(e.seconds) }) }) }, this.render = function () { var t = d.$viewValue; isNaN(t) ? (d.$setValidity("time", !1), i.error('Timepicker directive: "ng-model" value must be a Date object, a number of milliseconds since 01.01.1970 or a string representing an RFC2822 or ISO 8601 date.')) : (t && (p = t), p < h || p > b ? (d.$setValidity("time", !1), e.invalidHours = !0, e.invalidMinutes = !0) : C(), M()) }, e.showSpinners = angular.isDefined(n.showSpinners) ? e.$parent.$eval(n.showSpinners) : r.showSpinners, e.incrementHours = function () { e.noIncrementHours() || T(60 * g * 60) }, e.decrementHours = function () { e.noDecrementHours() || T(60 * -g * 60) }, e.incrementMinutes = function () { e.noIncrementMinutes() || T(60 * v) }, e.decrementMinutes = function () { e.noDecrementMinutes() || T(60 * -v) }, e.incrementSeconds = function () { e.noIncrementSeconds() || T(y) }, e.decrementSeconds = function () { e.noDecrementSeconds() || T(-y) }, e.toggleMeridian = function () { var t = k(), n = w(); e.noToggleMeridian() || (angular.isDefined(t) && angular.isDefined(n) ? T(720 * (p.getHours() < 12 ? 60 : -60)) : e.meridian = e.meridian === m[0] ? m[1] : m[0]) }, e.blur = function () { d.$setTouched() }, e.$on("$destroy", function () { for (; c.length;) c.shift()() }) }]).directive("uibTimepicker", ["uibTimepickerConfig", function (e) { return { require: ["uibTimepicker", "?^ngModel"], restrict: "A", controller: "UibTimepickerController", controllerAs: "timepicker", scope: {}, templateUrl: function (t, n) { return n.templateUrl || e.templateUrl }, link: function (e, t, n, a) { var i = a[0], o = a[1]; o && i.init(o, t.find("input")) } } }]), angular.module("ui.bootstrap.typeahead", ["ui.bootstrap.debounce", "ui.bootstrap.position"]).factory("uibTypeaheadParser", ["$parse", function (e) { var t = /^\s*([\s\S]+?)(?:\s+as\s+([\s\S]+?))?\s+for\s+(?:([\$\w][\$\w\d]*))\s+in\s+([\s\S]+?)$/; return { parse: function (n) { var a = n.match(t); if (!a) throw new Error('Expected typeahead specification in form of "_modelValue_ (as _label_)? for _item_ in _collection_" but got "' + n + '".'); return { itemName: a[3], source: e(a[4]), viewMapper: e(a[2] || a[1]), modelMapper: e(a[1]) } } } }]).controller("UibTypeaheadController", ["$scope", "$element", "$attrs", "$compile", "$parse", "$q", "$timeout", "$document", "$window", "$rootScope", "$$debounce", "$uibPosition", "uibTypeaheadParser", function (e, t, n, a, i, o, r, l, s, u, p, c, d) { var m, f, g = [9, 13, 27, 38, 40], h = e.$eval(n.typeaheadMinLength); h || 0 === h || (h = 1), e.$watch(n.typeaheadMinLength, function (e) { h = e || 0 === e ? e : 1 }); var b = e.$eval(n.typeaheadWaitMs) || 0, v = !1 !== e.$eval(n.typeaheadEditable); e.$watch(n.typeaheadEditable, function (e) { v = !1 !== e }); var $, y, w = i(n.typeaheadLoading).assign || angular.noop, k = n.typeaheadShouldSelect ? i(n.typeaheadShouldSelect) : function (e, t) { var n = t.$event; return 13 === n.which || 9 === n.which }, D = i(n.typeaheadOnSelect), x = !!angular.isDefined(n.typeaheadSelectOnBlur) && e.$eval(n.typeaheadSelectOnBlur), C = i(n.typeaheadNoResults).assign || angular.noop, M = n.typeaheadInputFormatter ? i(n.typeaheadInputFormatter) : void 0, T = !!n.typeaheadAppendToBody && e.$eval(n.typeaheadAppendToBody), O = n.typeaheadAppendTo ? e.$eval(n.typeaheadAppendTo) : null, S = !1 !== e.$eval(n.typeaheadFocusFirst), P = !!n.typeaheadSelectOnExact && e.$eval(n.typeaheadSelectOnExact), E = i(n.typeaheadIsOpen).assign || angular.noop, A = e.$eval(n.typeaheadShowHint) || !1, I = i(n.ngModel), U = i(n.ngModel + "($$$p)"), N = d.parse(n.uibTypeahead), F = e.$new(), H = e.$on("$destroy", function () { F.$destroy() }); F.$on("$destroy", H); var R, V, q = "typeahead-" + F.$id + "-" + Math.floor(1e4 * Math.random()); t.attr({ "aria-autocomplete": "list", "aria-expanded": !1, "aria-owns": q }), A && ((R = angular.element("<div></div>")).css("position", "relative"), t.after(R), (V = t.clone()).attr("placeholder", ""), V.attr("tabindex", "-1"), V.val(""), V.css({ position: "absolute", top: "0px", left: "0px", "border-color": "transparent", "box-shadow": "none", opacity: 1, background: "none 0% 0% / auto repeat scroll padding-box border-box rgb(255, 255, 255)", color: "#999" }), t.css({ position: "relative", "vertical-align": "top", "background-color": "transparent" }), V.attr("id") && V.removeAttr("id"), R.append(V), V.after(t)); var L = angular.element("<div uib-typeahead-popup></div>"); L.attr({ id: q, matches: "matches", active: "activeIdx", select: "select(activeIdx, evt)", "move-in-progress": "moveInProgress", query: "query", position: "position", "assign-is-open": "assignIsOpen(isOpen)", debounce: "debounceUpdate" }), angular.isDefined(n.typeaheadTemplateUrl) && L.attr("template-url", n.typeaheadTemplateUrl), angular.isDefined(n.typeaheadPopupTemplateUrl) && L.attr("popup-template-url", n.typeaheadPopupTemplateUrl); var z = function () { F.matches = [], F.activeIdx = -1, t.attr("aria-expanded", !1), A && V.val("") }, Y = function (e) { return q + "-option-" + e }; F.$watch("activeIdx", function (e) { e < 0 ? t.removeAttr("aria-activedescendant") : t.attr("aria-activedescendant", Y(e)) }); var B = function (n, a) { var i = { $viewValue: n }; w(e, !0), C(e, !1), o.when(N.source(e, i)).then(function (o) { var r, l, s = n === m.$viewValue; if (s && $) if (o && o.length > 0) { F.activeIdx = S ? 0 : -1, C(e, !1), F.matches.length = 0; for (var u = 0; u < o.length; u++) i[N.itemName] = o[u], F.matches.push({ id: Y(u), label: N.viewMapper(F, i), model: o[u] }); if (F.query = n, G(), t.attr("aria-expanded", !0), P && 1 === F.matches.length && (r = n, l = 0, F.matches.length > l && r && r.toUpperCase() === F.matches[l].label.toUpperCase()) && (angular.isNumber(F.debounceUpdate) || angular.isObject(F.debounceUpdate) ? p(function () { F.select(0, a) }, angular.isNumber(F.debounceUpdate) ? F.debounceUpdate : F.debounceUpdate.default) : F.select(0, a)), A) { var c = F.matches[0].label; angular.isString(n) && n.length > 0 && c.slice(0, n.length).toUpperCase() === n.toUpperCase() ? V.val(n + c.slice(n.length)) : V.val("") } } else z(), C(e, !0); s && w(e, !1) }, function () { z(), w(e, !1), C(e, !0) }) }; T && (angular.element(s).on("resize", _), l.find("body").on("scroll", _)); var W, j = p(function () { F.matches.length && G(), F.moveInProgress = !1 }, 200); function _() { F.moveInProgress || (F.moveInProgress = !0, F.$digest()), j() } function G() { F.position = T ? c.offset(t) : c.position(t), F.position.top += t.prop("offsetHeight") } F.moveInProgress = !1, F.query = void 0; var K = function () { W && r.cancel(W) }; z(), F.assignIsOpen = function (t) { E(e, t) }, F.select = function (a, i) { var o, l, s, u, p = {}; y = !0, p[N.itemName] = l = F.matches[a].model, o = N.modelMapper(e, p), s = e, u = o, angular.isFunction(I(e)) && f.getOption("getterSetter") ? U(s, { $$$p: u }) : I.assign(s, u), m.$setValidity("editable", !0), m.$setValidity("parse", !0), D(e, { $item: l, $model: o, $label: N.viewMapper(e, p), $event: i }), z(), !1 !== F.$eval(n.typeaheadFocusOnSelect) && r(function () { t[0].focus() }, 0, !1) }, t.on("keydown", function (t) { if (0 !== F.matches.length && -1 !== g.indexOf(t.which)) { var n, a = k(e, { $event: t }); if (-1 === F.activeIdx && a || 9 === t.which && t.shiftKey) return z(), void F.$digest(); switch (t.preventDefault(), t.which) { case 27: t.stopPropagation(), z(), e.$digest(); break; case 38: F.activeIdx = (F.activeIdx > 0 ? F.activeIdx : F.matches.length) - 1, F.$digest(), (n = L[0].querySelectorAll(".uib-typeahead-match")[F.activeIdx]).parentNode.scrollTop = n.offsetTop; break; case 40: F.activeIdx = (F.activeIdx + 1) % F.matches.length, F.$digest(), (n = L[0].querySelectorAll(".uib-typeahead-match")[F.activeIdx]).parentNode.scrollTop = n.offsetTop; break; default: a && F.$apply(function () { angular.isNumber(F.debounceUpdate) || angular.isObject(F.debounceUpdate) ? p(function () { F.select(F.activeIdx, t) }, angular.isNumber(F.debounceUpdate) ? F.debounceUpdate : F.debounceUpdate.default) : F.select(F.activeIdx, t) }) } } }), t.on("focus", function (e) { $ = !0, 0 !== h || m.$viewValue || r(function () { B(m.$viewValue, e) }, 0) }), t.on("blur", function (e) { x && F.matches.length && -1 !== F.activeIdx && !y && (y = !0, F.$apply(function () { angular.isObject(F.debounceUpdate) && angular.isNumber(F.debounceUpdate.blur) ? p(function () { F.select(F.activeIdx, e) }, F.debounceUpdate.blur) : F.select(F.activeIdx, e) })), !v && m.$error.editable && (m.$setViewValue(), F.$apply(function () { m.$setValidity("editable", !0), m.$setValidity("parse", !0) }), t.val("")), $ = !1, y = !1 }); var X = function (n) { t[0] !== n.target && 3 !== n.which && 0 !== F.matches.length && (z(), u.$$phase || e.$digest()) }; l.on("click", X), e.$on("$destroy", function () { l.off("click", X), (T || O) && Z.remove(), T && (angular.element(s).off("resize", _), l.find("body").off("scroll", _)), L.remove(), A && R.remove() }); var Z = a(L)(F); T ? l.find("body").append(Z) : O ? angular.element(O).eq(0).append(Z) : t.after(Z), this.init = function (t) { f = function (e) { var t; angular.version.minor < 6 ? (t = e.$options || {}).getOption = function (e) { return t[e] } : t = e.$options; return t }(m = t), F.debounceUpdate = i(f.getOption("debounce"))(e), m.$parsers.unshift(function (t) { var n; return $ = !0, 0 === h || t && t.length >= h ? b > 0 ? (K(), n = t, W = r(function () { B(n) }, b)) : B(t) : (w(e, !1), K(), z()), v ? t : t ? void m.$setValidity("editable", !1) : (m.$setValidity("editable", !0), null) }), m.$formatters.push(function (t) { var n, a = {}; return v || m.$setValidity("editable", !0), M ? (a.$model = t, M(e, a)) : (a[N.itemName] = t, n = N.viewMapper(e, a), a[N.itemName] = void 0, n !== N.viewMapper(e, a) ? n : t) }) } }]).directive("uibTypeahead", function () { return { controller: "UibTypeaheadController", require: ["ngModel", "uibTypeahead"], link: function (e, t, n, a) { a[1].init(a[0]) } } }).directive("uibTypeaheadPopup", ["$$debounce", function (e) { return { scope: { matches: "=", query: "=", active: "=", position: "&", moveInProgress: "=", select: "&", assignIsOpen: "&", debounce: "&" }, replace: !0, templateUrl: function (e, t) { return t.popupTemplateUrl || "uib/template/typeahead/typeahead-popup.html" }, link: function (t, n, a) { t.templateUrl = a.templateUrl, t.isOpen = function () { var e = t.matches.length > 0; return t.assignIsOpen({ isOpen: e }), e }, t.isActive = function (e) { return t.active === e }, t.selectActive = function (e) { t.active = e }, t.selectMatch = function (n, a) { var i = t.debounce(); angular.isNumber(i) || angular.isObject(i) ? e(function () { t.select({ activeIdx: n, evt: a }) }, angular.isNumber(i) ? i : i.default) : t.select({ activeIdx: n, evt: a }) } } } }]).directive("uibTypeaheadMatch", ["$templateRequest", "$compile", "$parse", function (e, t, n) { return { scope: { index: "=", match: "=", query: "=" }, link: function (a, i, o) { var r = n(o.templateUrl)(a.$parent) || "uib/template/typeahead/typeahead-match.html"; e(r).then(function (e) { var n = angular.element(e.trim()); i.replaceWith(n), t(n)(a) }) } } }]).filter("uibTypeaheadHighlight", ["$sce", "$injector", "$log", function (e, t, n) { var a; return a = t.has("$sanitize"), function (t, i) { return !a && /<.*>/g.test(t) && n.warn("Unsafe use of typeahead please use ngSanitize"), t = i ? ("" + t).replace(new RegExp(i.replace(/([.?*+^$[\]\\(){}|-])/g, "\\$1"), "gi"), "<strong>$&</strong>") : t, a || (t = e.trustAsHtml(t)), t } }]), angular.module("uib/template/accordion/accordion-group.html", []).run(["$templateCache", function (e) { e.put("uib/template/accordion/accordion-group.html", '<div role="tab" id="{{::headingId}}" aria-selected="{{isOpen}}" class="panel-heading" ng-keypress="toggleOpen($event)">\n  <h4 class="panel-title">\n    <a role="button" data-toggle="collapse" href aria-expanded="{{isOpen}}" aria-controls="{{::panelId}}" tabindex="0" class="accordion-toggle" ng-click="toggleOpen()" uib-accordion-transclude="heading" ng-disabled="isDisabled" uib-tabindex-toggle><span uib-accordion-header ng-class="{\'text-muted\': isDisabled}">{{heading}}</span></a>\n  </h4>\n</div>\n<div id="{{::panelId}}" aria-labelledby="{{::headingId}}" aria-hidden="{{!isOpen}}" role="tabpanel" class="panel-collapse collapse" uib-collapse="!isOpen">\n  <div class="panel-body" ng-transclude></div>\n</div>\n') }]), angular.module("uib/template/accordion/accordion.html", []).run(["$templateCache", function (e) { e.put("uib/template/accordion/accordion.html", '<div role="tablist" class="panel-group" ng-transclude></div>') }]), angular.module("uib/template/alert/alert.html", []).run(["$templateCache", function (e) { e.put("uib/template/alert/alert.html", '<button ng-show="closeable" type="button" class="close" ng-click="close({$event: $event})">\n  <span aria-hidden="true">&times;</span>\n  <span class="sr-only">Close</span>\n</button>\n<div ng-transclude></div>\n') }]), angular.module("uib/template/carousel/carousel.html", []).run(["$templateCache", function (e) { e.put("uib/template/carousel/carousel.html", '<div class="carousel-inner" ng-transclude></div>\n<a role="button" href class="left carousel-control" ng-click="prev()" ng-class="{ disabled: isPrevDisabled() }" ng-show="slides.length > 1">\n  <span aria-hidden="true" class="glyphicon glyphicon-chevron-left"></span>\n  <span class="sr-only">previous</span>\n</a>\n<a role="button" href class="right carousel-control" ng-click="next()" ng-class="{ disabled: isNextDisabled() }" ng-show="slides.length > 1">\n  <span aria-hidden="true" class="glyphicon glyphicon-chevron-right"></span>\n  <span class="sr-only">next</span>\n</a>\n<ol class="carousel-indicators" ng-show="slides.length > 1">\n  <li ng-repeat="slide in slides | orderBy:indexOfSlide track by $index" ng-class="{ active: isActive(slide) }" ng-click="select(slide)">\n    <span class="sr-only">slide {{ $index + 1 }} of {{ slides.length }}<span ng-if="isActive(slide)">, currently active</span></span>\n  </li>\n</ol>\n') }]), angular.module("uib/template/carousel/slide.html", []).run(["$templateCache", function (e) { e.put("uib/template/carousel/slide.html", '<div class="text-center" ng-transclude></div>\n') }]), angular.module("uib/template/datepicker/datepicker.html", []).run(["$templateCache", function (e) { e.put("uib/template/datepicker/datepicker.html", '<div ng-switch="datepickerMode">\n  <div uib-daypicker ng-switch-when="day" tabindex="0" class="uib-daypicker"></div>\n  <div uib-monthpicker ng-switch-when="month" tabindex="0" class="uib-monthpicker"></div>\n  <div uib-yearpicker ng-switch-when="year" tabindex="0" class="uib-yearpicker"></div>\n</div>\n') }]), angular.module("uib/template/datepicker/day.html", []).run(["$templateCache", function (e) { e.put("uib/template/datepicker/day.html", '<table role="grid" aria-labelledby="{{::uniqueId}}-title" aria-activedescendant="{{activeDateId}}">\n  <thead>\n    <tr>\n      <th><button type="button" class="btn btn-date btn-sm pull-left uib-left" ng-click="move(-1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-left"></i><span class="sr-only">previous</span></button></th>\n      <th colspan="{{::5 + showWeeks}}"><button id="{{::uniqueId}}-title" role="heading" aria-live="assertive" aria-atomic="true" type="button" class="btn btn-date btn-sm uib-title" ng-click="toggleMode()" ng-disabled="datepickerMode === maxMode" tabindex="-1"><strong>{{title}}</strong></button></th>\n      <th><button type="button" class="btn btn-date btn-sm pull-right uib-right" ng-click="move(1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-right"></i><span class="sr-only">next</span></button></th>\n    </tr>\n    <tr>\n      <th ng-if="showWeeks" class="text-center"></th>\n      <th ng-repeat="label in ::labels track by $index" class="text-center"><small aria-label="{{::label.full}}">{{::label.abbr}}</small></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr class="uib-weeks" ng-repeat="row in rows track by $index" role="row">\n      <td ng-if="showWeeks" class="text-center h6"><em>{{ weekNumbers[$index] }}</em></td>\n      <td ng-repeat="dt in row" class="uib-day text-center" role="gridcell"\n        id="{{::dt.uid}}"\n        ng-class="::dt.customClass">\n        <button type="button" class="btn btn-date btn-sm"\n          uib-is-class="\n            \'btn-info\' for selectedDt,\n            \'active\' for activeDt\n            on dt"\n          ng-click="select(dt.date)"\n          ng-disabled="::dt.disabled"\n          tabindex="-1"><span ng-class="::{\'text-muted\': dt.secondary, \'text-info\': dt.current}">{{::dt.label}}</span></button>\n      </td>\n    </tr>\n  </tbody>\n</table>\n') }]), angular.module("uib/template/datepicker/month.html", []).run(["$templateCache", function (e) { e.put("uib/template/datepicker/month.html", '<table role="grid" aria-labelledby="{{::uniqueId}}-title" aria-activedescendant="{{activeDateId}}">\n  <thead>\n    <tr>\n      <th><button type="button" class="btn btn-date btn-sm pull-left uib-left" ng-click="move(-1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-left"></i><span class="sr-only">previous</span></button></th>\n      <th colspan="{{::yearHeaderColspan}}"><button id="{{::uniqueId}}-title" role="heading" aria-live="assertive" aria-atomic="true" type="button" class="btn btn-date btn-sm uib-title" ng-click="toggleMode()" ng-disabled="datepickerMode === maxMode" tabindex="-1"><strong>{{title}}</strong></button></th>\n      <th><button type="button" class="btn btn-date btn-sm pull-right uib-right" ng-click="move(1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-right"></i><span class="sr-only">next</span></i></button></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr class="uib-months" ng-repeat="row in rows track by $index" role="row">\n      <td ng-repeat="dt in row" class="uib-month text-center" role="gridcell"\n        id="{{::dt.uid}}"\n        ng-class="::dt.customClass">\n        <button type="button" class="btn btn-date"\n          uib-is-class="\n            \'btn-info\' for selectedDt,\n            \'active\' for activeDt\n            on dt"\n          ng-click="select(dt.date)"\n          ng-disabled="::dt.disabled"\n          tabindex="-1"><span ng-class="::{\'text-info\': dt.current}">{{::dt.label}}</span></button>\n      </td>\n    </tr>\n  </tbody>\n</table>\n') }]), angular.module("uib/template/datepicker/year.html", []).run(["$templateCache", function (e) { e.put("uib/template/datepicker/year.html", '<table role="grid" aria-labelledby="{{::uniqueId}}-title" aria-activedescendant="{{activeDateId}}">\n  <thead>\n    <tr>\n      <th><button type="button" class="btn btn-date btn-sm pull-left uib-left" ng-click="move(-1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-left"></i><span class="sr-only">previous</span></button></th>\n      <th colspan="{{::columns - 2}}"><button id="{{::uniqueId}}-title" role="heading" aria-live="assertive" aria-atomic="true" type="button" class="btn btn-date uib-title" ng-click="toggleMode()" ng-disabled="datepickerMode === maxMode" tabindex="-1"><strong>{{title}}</strong></button></th>\n      <th><button type="button" class="btn btn-date btn-sm pull-right uib-right" ng-click="move(1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-right"></i><span class="sr-only">next</span></button></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr class="uib-years" ng-repeat="row in rows track by $index" role="row">\n      <td ng-repeat="dt in row" class="uib-year text-center" role="gridcell"\n        id="{{::dt.uid}}"\n        ng-class="::dt.customClass">\n        <button type="button" class="btn btn-date"\n          uib-is-class="\n            \'btn-info\' for selectedDt,\n            \'active\' for activeDt\n            on dt"\n          ng-click="select(dt.date)"\n          ng-disabled="::dt.disabled"\n          tabindex="-1"><span ng-class="::{\'text-info\': dt.current}">{{::dt.label}}</span></button>\n      </td>\n    </tr>\n  </tbody>\n</table>\n') }]), angular.module("uib/template/datepickerPopup/popup.html", []).run(["$templateCache", function (e) { e.put("uib/template/datepickerPopup/popup.html", '<ul role="presentation" class="uib-datepicker-popup dropdown-menu uib-position-measure" dropdown-nested ng-if="isOpen" ng-keydown="keydown($event)" ng-click="$event.stopPropagation()">\n  <li ng-transclude></li>\n  <li ng-if="showButtonBar" class="uib-button-bar">\n    <span class="btn-group pull-left">\n      <button type="button" class="btn btn-sm  btn-darker-1 uib-datepicker-current" ng-click="select(\'today\', $event)" ng-disabled="isDisabled(\'today\')">{{ getText(\'current\') }}</button>\n      <button type="button" class="btn btn-sm btn-danger uib-clear" ng-click="select(null, $event)">{{ getText(\'clear\') }}</button>\n    </span>\n    <button type="button" class="btn btn-sm btn-success pull-right uib-close" ng-click="close($event)">{{ getText(\'close\') }}</button>\n  </li>\n</ul>\n') }]), angular.module("uib/template/modal/window.html", []).run(["$templateCache", function (e) { e.put("uib/template/modal/window.html", "<div class=\"modal-dialog {{size ? 'modal-' + size : ''}}\"><div class=\"modal-content\" uib-modal-transclude></div></div>\n") }]), angular.module("uib/template/pager/pager.html", []).run(["$templateCache", function (e) { e.put("uib/template/pager/pager.html", '<li ng-class="{disabled: noPrevious()||ngDisabled, previous: align}"><a href ng-click="selectPage(page - 1, $event)" ng-disabled="noPrevious()||ngDisabled" uib-tabindex-toggle>{{::getText(\'previous\')}}</a></li>\n<li ng-class="{disabled: noNext()||ngDisabled, next: align}"><a href ng-click="selectPage(page + 1, $event)" ng-disabled="noNext()||ngDisabled" uib-tabindex-toggle>{{::getText(\'next\')}}</a></li>\n') }]), angular.module("uib/template/pagination/pagination.html", []).run(["$templateCache", function (e) { e.put("uib/template/pagination/pagination.html", '<li role="menuitem" ng-if="::boundaryLinks" ng-class="{disabled: noPrevious()||ngDisabled}" class="pagination-first"><a href ng-click="selectPage(1, $event)" ng-disabled="noPrevious()||ngDisabled" uib-tabindex-toggle>{{::getText(\'first\')}}</a></li>\n<li role="menuitem" ng-if="::directionLinks" ng-class="{disabled: noPrevious()||ngDisabled}" class="pagination-prev"><a href ng-click="selectPage(page - 1, $event)" ng-disabled="noPrevious()||ngDisabled" uib-tabindex-toggle>{{::getText(\'previous\')}}</a></li>\n<li role="menuitem" ng-repeat="page in pages track by $index" ng-class="{active: page.active,disabled: ngDisabled&&!page.active}" class="pagination-page"><a href ng-click="selectPage(page.number, $event)" ng-disabled="ngDisabled&&!page.active" uib-tabindex-toggle>{{page.text}}</a></li>\n<li role="menuitem" ng-if="::directionLinks" ng-class="{disabled: noNext()||ngDisabled}" class="pagination-next"><a href ng-click="selectPage(page + 1, $event)" ng-disabled="noNext()||ngDisabled" uib-tabindex-toggle>{{::getText(\'next\')}}</a></li>\n<li role="menuitem" ng-if="::boundaryLinks" ng-class="{disabled: noNext()||ngDisabled}" class="pagination-last"><a href ng-click="selectPage(totalPages, $event)" ng-disabled="noNext()||ngDisabled" uib-tabindex-toggle>{{::getText(\'last\')}}</a></li>\n') }]), angular.module("uib/template/tooltip/tooltip-html-popup.html", []).run(["$templateCache", function (e) { e.put("uib/template/tooltip/tooltip-html-popup.html", '<div class="tooltip-arrow"></div>\n<div class="tooltip-inner" ng-bind-html="contentExp()"></div>\n') }]), angular.module("uib/template/tooltip/tooltip-popup.html", []).run(["$templateCache", function (e) { e.put("uib/template/tooltip/tooltip-popup.html", '<div class="tooltip-arrow"></div>\n<div class="tooltip-inner" ng-bind="content"></div>\n') }]), angular.module("uib/template/tooltip/tooltip-template-popup.html", []).run(["$templateCache", function (e) { e.put("uib/template/tooltip/tooltip-template-popup.html", '<div class="tooltip-arrow"></div>\n<div class="tooltip-inner"\n  uib-tooltip-template-transclude="contentExp()"\n  tooltip-template-transclude-scope="originScope()"></div>\n') }]), angular.module("uib/template/popover/popover-html.html", []).run(["$templateCache", function (e) { e.put("uib/template/popover/popover-html.html", '<div class="arrow"></div>\n\n<div class="popover-inner">\n    <h3 class="popover-title" ng-bind="uibTitle" ng-if="uibTitle"></h3>\n    <div class="popover-content" ng-bind-html="contentExp()"></div>\n</div>\n') }]), angular.module("uib/template/popover/popover-template.html", []).run(["$templateCache", function (e) { e.put("uib/template/popover/popover-template.html", '<div class="arrow"></div>\n\n<div class="popover-inner">\n    <h3 class="popover-title" ng-bind="uibTitle" ng-if="uibTitle"></h3>\n    <div class="popover-content"\n      uib-tooltip-template-transclude="contentExp()"\n      tooltip-template-transclude-scope="originScope()"></div>\n</div>\n') }]), angular.module("uib/template/popover/popover.html", []).run(["$templateCache", function (e) { e.put("uib/template/popover/popover.html", '<div class="arrow"></div>\n\n<div class="popover-inner">\n    <h3 class="popover-title" ng-bind="uibTitle" ng-if="uibTitle"></h3>\n    <div class="popover-content" ng-bind="content"></div>\n</div>\n') }]), angular.module("uib/template/progressbar/bar.html", []).run(["$templateCache", function (e) { e.put("uib/template/progressbar/bar.html", '<div class="progress-bar" ng-class="type && \'progress-bar-\' + type" role="progressbar" aria-valuenow="{{value}}" aria-valuemin="0" aria-valuemax="{{max}}" ng-style="{width: (percent < 100 ? percent : 100) + \'%\'}" aria-valuetext="{{percent | number:0}}%" aria-labelledby="{{::title}}" ng-transclude></div>\n') }]), angular.module("uib/template/progressbar/progress.html", []).run(["$templateCache", function (e) { e.put("uib/template/progressbar/progress.html", '<div class="progress" ng-transclude aria-labelledby="{{::title}}"></div>') }]), angular.module("uib/template/progressbar/progressbar.html", []).run(["$templateCache", function (e) { e.put("uib/template/progressbar/progressbar.html", '<div class="progress">\n  <div class="progress-bar" ng-class="type && \'progress-bar-\' + type" role="progressbar" aria-valuenow="{{value}}" aria-valuemin="0" aria-valuemax="{{max}}" ng-style="{width: (percent < 100 ? percent : 100) + \'%\'}" aria-valuetext="{{percent | number:0}}%" aria-labelledby="{{::title}}" ng-transclude></div>\n</div>\n') }]), angular.module("uib/template/rating/rating.html", []).run(["$templateCache", function (e) { e.put("uib/template/rating/rating.html", '<span ng-mouseleave="reset()" ng-keydown="onKeydown($event)" tabindex="0" role="slider" aria-valuemin="0" aria-valuemax="{{range.length}}" aria-valuenow="{{value}}" aria-valuetext="{{title}}">\n    <span ng-repeat-start="r in range track by $index" class="sr-only">({{ $index < value ? \'*\' : \' \' }})</span>\n    <i ng-repeat-end ng-mouseenter="enter($index + 1)" ng-click="rate($index + 1)" class="glyphicon" ng-class="$index < value && (r.stateOn || \'glyphicon-star\') || (r.stateOff || \'glyphicon-star-empty\')" ng-attr-title="{{r.title}}"></i>\n</span>\n') }]), angular.module("uib/template/tabs/tab.html", []).run(["$templateCache", function (e) { e.put("uib/template/tabs/tab.html", '<li ng-class="[{active: active, disabled: disabled}, classes]" class="uib-tab nav-item">\n  <a href ng-click="select($event)" class="nav-link" uib-tab-heading-transclude>{{heading}}</a>\n</li>\n') }]), angular.module("uib/template/tabs/tabset.html", []).run(["$templateCache", function (e) { e.put("uib/template/tabs/tabset.html", '<div>\n  <ul class="nav nav-{{tabset.type || \'tabs\'}}" ng-class="{\'nav-stacked\': vertical, \'nav-justified\': justified}" ng-transclude></ul>\n  <div class="tab-content">\n    <div class="tab-pane"\n         ng-repeat="tab in tabset.tabs"\n         ng-class="{active: tabset.active === tab.index}"\n         uib-tab-content-transclude="tab">\n    </div>\n  </div>\n</div>\n') }]), angular.module("uib/template/timepicker/timepicker.html", []).run(["$templateCache", function (e) { e.put("uib/template/timepicker/timepicker.html", '<table class="uib-timepicker">\n  <tbody>\n    <tr class="text-center" ng-show="::showSpinners">\n      <td class="uib-increment hours"><button class="btn-link" ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}"   ng-disabled="noIncrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>\n      <td>&nbsp;</td>\n      <td class="uib-increment minutes"><button class="btn-link" ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}"   ng-disabled="noIncrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>\n      <td ng-show="showSeconds">&nbsp;</td>\n      <td ng-show="showSeconds" class="uib-increment seconds"><button class="btn-link" ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}"   ng-disabled="noIncrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>\n      <td ng-show="showMeridian"></td>\n    </tr>\n    <tr>\n      <td class="form-group uib-time hours" ng-class="{\'has-error\': invalidHours}">\n        <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center" ng-readonly="::readonlyInput" maxlength="2" tabindex="{{::tabindex}}" ng-disabled="noIncrementHours()" ng-blur="blur()">\n      </td>\n      <td class="uib-separator" style="font-weight:bold">:</td>\n      <td class="form-group uib-time minutes" ng-class="{\'has-error\': invalidMinutes}">\n        <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center" ng-readonly="::readonlyInput" maxlength="2" tabindex="{{::tabindex}}" ng-disabled="noIncrementMinutes()" ng-blur="blur()">\n      </td>\n      <td ng-show="showSeconds" class="uib-separator" style="font-weight:bold">:</td>\n      <td class="form-group uib-time seconds" ng-class="{\'has-error\': invalidSeconds}" ng-show="showSeconds">\n        <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center" ng-readonly="readonlyInput" maxlength="2" tabindex="{{::tabindex}}" ng-disabled="noIncrementSeconds()" ng-blur="blur()">\n      </td>\n      <td ng-show="showMeridian" class="uib-time am-pm" ><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-date text-center" style="height:30px; padding-top:4px; padding-bottom:4px" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="{{::tabindex}}">{{meridian}}</button></td>\n    </tr>\n    <tr class="text-center" ng-show="::showSpinners">\n      <td class="uib-decrement hours" style="padding-top:3px"><button class="btn-link" ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}"  ng-disabled="noDecrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>\n      <td>&nbsp;</td>\n      <td class="uib-decrement minutes" style="padding-top:3px"><button class="btn-link" ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}"  ng-disabled="noDecrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>\n      <td ng-show="showSeconds">&nbsp;</td>\n      <td ng-show="showSeconds" class="uib-decrement seconds" style="padding-top:3px"><button class="btn-link" ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}"  ng-disabled="noDecrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>\n      <td ng-show="showMeridian"></td>\n    </tr>\n  </tbody>\n</table>\n') }]), angular.module("uib/template/typeahead/typeahead-match.html", []).run(["$templateCache", function (e) { e.put("uib/template/typeahead/typeahead-match.html", '<a href\n   tabindex="-1"\n   ng-bind-html="match.label | uibTypeaheadHighlight:query"\n   ng-attr-title="{{match.label}}"></a>\n') }]), angular.module("uib/template/typeahead/typeahead-popup.html", []).run(["$templateCache", function (e) { e.put("uib/template/typeahead/typeahead-popup.html", '<ul style="width:100%; height:auto; max-height:191px; padding:0px 0px;" class="dropdown-menu webkitscroller" ng-show="isOpen() && !moveInProgress" ng-style="{top: position().top+\'px\', left: position().left+\'px\'}" role="listbox" aria-hidden="{{!isOpen()}}">\n    <li style="padding:0px 0px;" class="uib-typeahead-match" ng-repeat="match in matches track by $index" ng-class="{active: isActive($index) }" ng-mouseenter="selectActive($index)" ng-click="selectMatch($index, $event)" role="option" id="{{::match.id}}">\n        <div uib-typeahead-match index="$index" match="match" query="query" template-url="templateUrl"></div>\n    </li>\n</ul>\n') }]), angular.module("ui.bootstrap.carousel").run(function () { !angular.$$csp().noInlineStyle && !angular.$$uibCarouselCss && angular.element(document).find("head").prepend('<style type="text/css">.ng-animate.item:not(.left):not(.right){-webkit-transition:0s ease-in-out left;transition:0s ease-in-out left}</style>'), angular.$$uibCarouselCss = !0 }), angular.module("ui.bootstrap.datepicker").run(function () { !angular.$$csp().noInlineStyle && !angular.$$uibDatepickerCss && angular.element(document).find("head").prepend('<style type="text/css">.uib-datepicker .uib-title{width:100%;}.uib-day button,.uib-month button,.uib-year button{min-width:100%;}.uib-left,.uib-right{width:100%}</style>'), angular.$$uibDatepickerCss = !0 }), angular.module("ui.bootstrap.position").run(function () { !angular.$$csp().noInlineStyle && !angular.$$uibPositionCss && angular.element(document).find("head").prepend('<style type="text/css">.uib-position-measure{display:block !important;visibility:hidden !important;position:absolute !important;top:-9999px !important;left:-9999px !important;}.uib-position-scrollbar-measure{position:absolute !important;top:-9999px !important;width:50px !important;height:50px !important;overflow:scroll !important;}.uib-position-body-scrollbar-measure{overflow:scroll !important;}</style>'), angular.$$uibPositionCss = !0 }), angular.module("ui.bootstrap.datepickerPopup").run(function () { !angular.$$csp().noInlineStyle && !angular.$$uibDatepickerpopupCss && angular.element(document).find("head").prepend('<style type="text/css">.uib-datepicker-popup.dropdown-menu{display:block;float:none;margin:0;}.uib-button-bar{padding:10px 9px 2px;}</style>'), angular.$$uibDatepickerpopupCss = !0 }), angular.module("ui.bootstrap.tooltip").run(function () { !angular.$$csp().noInlineStyle && !angular.$$uibTooltipCss && angular.element(document).find("head").prepend('<style type="text/css">[uib-tooltip-popup].tooltip.top-left > .tooltip-arrow,[uib-tooltip-popup].tooltip.top-right > .tooltip-arrow,[uib-tooltip-popup].tooltip.bottom-left > .tooltip-arrow,[uib-tooltip-popup].tooltip.bottom-right > .tooltip-arrow,[uib-tooltip-popup].tooltip.left-top > .tooltip-arrow,[uib-tooltip-popup].tooltip.left-bottom > .tooltip-arrow,[uib-tooltip-popup].tooltip.right-top > .tooltip-arrow,[uib-tooltip-popup].tooltip.right-bottom > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.top-left > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.top-right > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.bottom-left > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.bottom-right > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.left-top > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.left-bottom > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.right-top > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.right-bottom > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.top-left > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.top-right > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.bottom-left > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.bottom-right > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.left-top > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.left-bottom > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.right-top > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.right-bottom > .tooltip-arrow,[uib-popover-popup].popover.top-left > .arrow,[uib-popover-popup].popover.top-right > .arrow,[uib-popover-popup].popover.bottom-left > .arrow,[uib-popover-popup].popover.bottom-right > .arrow,[uib-popover-popup].popover.left-top > .arrow,[uib-popover-popup].popover.left-bottom > .arrow,[uib-popover-popup].popover.right-top > .arrow,[uib-popover-popup].popover.right-bottom > .arrow,[uib-popover-html-popup].popover.top-left > .arrow,[uib-popover-html-popup].popover.top-right > .arrow,[uib-popover-html-popup].popover.bottom-left > .arrow,[uib-popover-html-popup].popover.bottom-right > .arrow,[uib-popover-html-popup].popover.left-top > .arrow,[uib-popover-html-popup].popover.left-bottom > .arrow,[uib-popover-html-popup].popover.right-top > .arrow,[uib-popover-html-popup].popover.right-bottom > .arrow,[uib-popover-template-popup].popover.top-left > .arrow,[uib-popover-template-popup].popover.top-right > .arrow,[uib-popover-template-popup].popover.bottom-left > .arrow,[uib-popover-template-popup].popover.bottom-right > .arrow,[uib-popover-template-popup].popover.left-top > .arrow,[uib-popover-template-popup].popover.left-bottom > .arrow,[uib-popover-template-popup].popover.right-top > .arrow,[uib-popover-template-popup].popover.right-bottom > .arrow{top:auto;bottom:auto;left:auto;right:auto;margin:0;}[uib-popover-popup].popover,[uib-popover-html-popup].popover,[uib-popover-template-popup].popover{display:block !important;}</style>'), angular.$$uibTooltipCss = !0 }), angular.module("ui.bootstrap.timepicker").run(function () { !angular.$$csp().noInlineStyle && !angular.$$uibTimepickerCss && angular.element(document).find("head").prepend('<style type="text/css">.uib-time input{width:50px;}</style>'), angular.$$uibTimepickerCss = !0 }), angular.module("ui.bootstrap.typeahead").run(function () { !angular.$$csp().noInlineStyle && !angular.$$uibTypeaheadCss && angular.element(document).find("head").prepend('<style type="text/css">[uib-typeahead-popup].dropdown-menu{display:block;}</style>'), angular.$$uibTypeaheadCss = !0 });