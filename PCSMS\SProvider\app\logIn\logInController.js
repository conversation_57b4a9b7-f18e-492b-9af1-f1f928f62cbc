﻿PCSMSApp.controller("logInController", function ($scope, $rootScope, $state, blockUI, logInServices, $cookies, toastr, $base64, $window, $timeout) {
    //Load LogInObj From Cookie
    var LogInObjCookie = $cookies.get('LogInObj');
    //If <PERSON><PERSON> Exists
    if (LogInObjCookie != null) {
        var LogInObjTemp = JSON.parse($cookies.get('LogInObj'));
        $rootScope.LogInObj = {
            Username: LogInObjTemp.Username,
            Password: LogInObjTemp.Password
        }
        $scope.Remember = 1;
    }
        //Else Cookie not found
    else {
        $rootScope.LogInObj = {
            Username: null,
            Password: null
        }
        $scope.Remember = 0;
    }

    //Control Remember Me checkbox while formdata in invalid
    $scope.ControlRememberMe = function () {
        if ($scope.logInForm.$invalid == true) {
            $scope.Remember = 0;
        }
    };
    //LogIn Function
    $scope.LogIn = function () {

        if ($scope.Remember == 1) {
            $cookies.put('LogInObj', angular.toJson($rootScope.LogInObj), { 'path': '/', 'samesite': 'lax' });
        }
        else if ($scope.Remember == 0) {
            $cookies.remove('LogInObj', { 'path': '/', 'samesite': 'lax' });
        }


        if ($scope.logInForm.$invalid == false) {
            $scope.Submited = true;
            var key = $rootScope.LogInObj.Username + ':' + $rootScope.LogInObj.Password;

            //console.log($base64.encode(key));

            logInServices.GetAuthorizationToken($base64.encode(key)).then(function (response) {

                console.log(response.data);
                function redirectingToDashboard() {
                    return new Promise(function (done) {
                        $window.location.href = "/SProvider/#/home";
                        done();
                    });
                }
                function tasksAfterSuccessfulLogin() {
                    redirectingToDashboard()
                    .then(function () {
                        $timeout(function () {
                            if (response.data.SProviderUserDetails.FirstName != null || response.data.SProviderUserDetails.LastName != null) {
                                toastr.success("Successfully logged in ! </br>Welcome " + response.data.SProviderUserDetails.FirstName + " " + response.data.SProviderUserDetails.LastName, {
                                    timeOut: 2000
                                });
                            }
                            else {
                                toastr.success("Successfully logged in ! </br> " + $rootScope.SProviderUserDetails.AccessTypeName + "</br>", {
                                    timeOut: 2000
                                });
                            }
                        }, 3000);
                    });
                }


                if (response.data.Success && response.data.SProviderUserDetails.IsActive === "1") {
                    tasksAfterSuccessfulLogin();


                    //console.log(angular.toJson(response.data.Token));

                    //Setting a cookie
                    var date = new Date();
                    var expireTime = date.getTime() + 10800000; // 3 hours
                    date.setTime(expireTime);

                    $cookies.put('SProvider_Token_Codeaura_Demo', angular.toJson(response.data.Token), { 'expires': date, 'path': '/' });
                    $cookies.put('SProvider_Details_Codeaura_Demo', angular.toJson(response.data.SProviderUserDetails), { 'expires': date, 'path': '/' });
                } else {
                    //console.log("login is unsuccesfull !");
                    toastr.error("You have been blocked. Please contact system administrator.", 'Error!');
                }

            },
            function (reject) {
                
            });
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }
    };




    //Signing in through Enter key :
    //$("body").keypress(function (e) {
    //    var code = e.keyCode || e.which;
    //    if (code === 13) {
    //        $("#btnLogin").click();
    //    }
    //});

    if ($rootScope.spUnauthorizedRequestFound == true) {
        //$('.modal-backdrop').remove();
        toastr.error("Session expired, Please log in again !", {
            timeOut: 3000
        });
        $rootScope.spUnauthorizedRequestFound = false;
    }
    else if ($rootScope.spInternalServerErrorFound == true) {
        //$('.modal-backdrop').remove();
        toastr.error("Internal Server Error! Please log in again", {
            timeOut: 3000
        });
        $rootScope.spInternalServerErrorFound = false;
    }
    else if ($rootScope.spUsernameOrPasswordNotMatched == true) {
        //$('.modal-backdrop').remove();
        toastr.error("Invalid Username or Password !", {
            timeOut: 3000
        });
        $rootScope.spUsernameOrPasswordNotMatched = false;
        //Idle.unwatch();
    }
});