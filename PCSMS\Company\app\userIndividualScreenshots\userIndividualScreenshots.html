﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>

<!-- CONTENT -->
<!-- ========================================================= -->

<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-dashboard" aria-hidden="true"></i><a ui-sref="user">Individual User Screenschots History</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-4">
                <!--PANEL FOR ADJUST SETTINGS-->
                <div class="row">
                    <div class="panel-group" id="accordion">
                        <!--Adjust Settings-->
                        <div class="panel panel-default">
                            <div class="panel-header" style="height: inherit">
                                <script type="text/ng-template" id="customTemplate.html">
                                    <a style="padding:0px 20px; height:68px">
                                        <div class="webkitscroller-transparent">
                                            <table class="table table-borderless">
                                                <tbody>
                                                    <tr>
                                                        <td style="width:57px">
                                                            <img class="img-responsive img-circle animated fadeIn" style="border:1px solid #cccccc;box-shadow:inset 0 1px 20px 9px rgba(0, 0, 0, .075); object-fit:cover; height:41.40px; width:41.40px" src="{{match.model.Photo!=null?'../Company_Images/User_Images/'+match.model.Photo : match.model.Gender=='Male'? '../Company_Images/Default_Images/male.png':'../Company_Images/Default_Images/female.jpg'}}" />
                                                        </td>
                                                        <td>
                                                            <span class="k-state-default">
                                                                <span class="k-h3" ng-bind-html="match.model.FullName | uibTypeaheadHighlight:query"></span>
                                                                <br />
                                                                <span class="k-p" style="font-weight:bold; font-size:.7em" ng-bind-html="match.model.Designation | uibTypeaheadHighlight:query"></span>
                                                                <b>|</b>
                                                                <span class="k-p" style="font-weight:bold; font-size:.7em;" ng-bind-html="match.model.Mobile | uibTypeaheadHighlight:query"></span>
                                                                <b>|</b>
                                                                <span style="font-size:.7em; padding:2px 2px" class="{{match.model.Status=='Active'?'badge b-straight x-success':'badge b-straight x-danger'}}">
                                                                    {{match.model.Status=='Active'?'Active':'In-Active'}}
                                                                </span>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </a>
                                </script>



                                <div class="demo-section k-content">
                                    <h4>Search Your Employee</h4>
                                    
                                    <input type="text" id="SearchEmployee"
                                           ng-model="Tracking.SearchEmployee" placeholder="Type employee name here" class="form-control"
                                           ng-disabled="AccordionOpen==false || $root.PckgEnjoyed==false"
                                           ng-click="SearchEmployee_Click()"
                                           typeahead-on-select="typeaheadonselect($item, $model, $label, $event)"
                                           uib-typeahead="Employee as Employee.FullName for Employee in ActiveCP_EmployeeList | filter:$viewValue"
                                           typeahead-template-url="customTemplate.html"
                                           typeahead-show-hint="true"
                                           typeahead-min-length="0">
                                    <p class="demo-hint" ng-if="Tracking.SearchEmployee==null || Tracking.SearchEmployee==undefined || Tracking.SearchEmployee==''">
                                        <span class="text-info">Search employee by name, designation or username. e.g. "monir"</span>
                                    </p>
                                </div>
                            </div>
                            <div class="panel-collapse collapse in" id="_Adjust_Settings">
                                <div class="panel-content">
                                    <form name="AdjustLocationSettingsForm" novalidate>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <!--AGAIN CREATE A PANEL-->
                                                <div class="panel panel-default panel-bordered">
                                                    <div class="panel-header" style="height: 42px; border-bottom: 0;">
                                                        <h3 class="panel-title" style="font-size: 14px; margin: 0;">Choose Range</h3>
                                                        <div class="panel-actions">
                                                            <ul>
                                                                <li class="action" ng-class="{'disabled':AdjustLocationSettingsForm.$pristine}">
                                                                    <button id="a_Refresh_Session"
                                                                            ng-disabled="AdjustLocationSettingsForm.$pristine || Loader==true"
                                                                            tooltip-placement="left"
                                                                            uib-tooltip="Clear Ranges"
                                                                            ng-click="AdjustLocationSettingsOnlyReset()"
                                                                            ng-class="{'disabled':AdjustLocationSettingsForm.$pristine}">
                                                                        <span class="fa fa-refresh" style="font-size: 16px;"></span>
                                                                    </button>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="panel-content">
                                                        <div class="form-group-sm">
                                                            <div class="radio radio-custom radio-inline radio-primary">
                                                                <input type="radio" id="radioCustom1" name="SessionDate" value="Today"
                                                                       ng-model="Tracking.ShortDayPicker"
                                                                       ng-required="Tracking.DateAndTimeFormObj==undefined?true:Tracking.DateAndTimeFormObj==null?true:false"
                                                                       ng-disabled="Tracking.SearchEmployee.Id==undefined || Tracking.SearchEmployee.Id==null || Tracking.SearchEmployee==null || Tracking.DateAndTimeFormObj!=null">
                                                                <label for="radioCustom1">Today</label>
                                                            </div>
                                                            <div class="radio radio-custom radio-inline radio-primary">
                                                                <input type="radio" id="radioCustom2" name="SessionDate" value="Yesterday"
                                                                       ng-model="Tracking.ShortDayPicker"
                                                                       ng-required="Tracking.DateAndTimeFormObj==undefined?true:Tracking.DateAndTimeFormObj==null?true:false"
                                                                       ng-disabled="Tracking.SearchEmployee.Id==undefined || Tracking.SearchEmployee.Id==null || Tracking.SearchEmployee==null || Tracking.DateAndTimeFormObj!=null">
                                                                <label for="radioCustom2">Yesterday</label>
                                                            </div>
                                                            <div class="radio radio-custom radio-inline radio-primary">
                                                                <input type="radio" id="radioCustom3" name="SessionDate" value="OneWeek"
                                                                       ng-model="Tracking.ShortDayPicker"
                                                                       ng-required="Tracking.DateAndTimeFormObj==undefined?true:Tracking.DateAndTimeFormObj==null?true:false"
                                                                       ng-disabled="Tracking.SearchEmployee.Id==undefined || Tracking.SearchEmployee.Id==null || Tracking.SearchEmployee==null || Tracking.DateAndTimeFormObj!=null">
                                                                <label for="radioCustom3">For 1 Week</label>
                                                            </div>
                                                        </div>
                                                        <br/>
                                                        <div class="form-group-sm">
                                                            <label class="control-label">From<span class="required">*</span></label>
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" style="background-color: #fff; border-right: none" name="DateAndTimeFormObj"
                                                                       ng-required="Tracking.ShortDayPicker==null"
                                                                       ng-disabled="true"
                                                                       datetime-picker="medium"
                                                                       ng-model="Tracking.DateAndTimeFormObj"
                                                                       is-open="isOpenFrom"
                                                                       when-closed="TrackingDateAndTimeFormObjClosed(args)"
                                                                       datepicker-options="DateAndTimeFormObj.datepickerOptions"/>
                                                                <span class="input-group-btn">
                                                                    <button type="button" style="height: 30px; border: 1px solid #ccc; border-left: none" class="btn btn-default"
                                                                            ng-disabled="Tracking.ShortDayPicker!=null || Tracking.SearchEmployee.Id==undefined || Tracking.SearchEmployee.Id==null || Tracking.SearchEmployee==null"
                                                                            ng-click="openCalendarFrom($event, prop)">
                                                                        <i class="fa fa-calendar"></i>
                                                                    </button>
                                                                </span>
                                                            </div>
                                                            <div class="row custom-row">
                                                                <span class="errormessage pull-left"
                                                                      ng-show="AdjustLocationSettingsForm.DateAndTimeFormObj.$dirty && AdjustLocationSettingsForm.DateAndTimeFormObj.$invalid">
                                                                    <span ng-show="AdjustLocationSettingsForm.DateAndTimeFormObj.$error.required">*Required</span>
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <br/>
                                                        <div class="form-group-sm">
                                                            <label class="control-label">To<span class="required">*</span></label>
                                                            <div class="input-group">
                                                                <input type="text" class="form-control" style="background-color: #fff; border-right: none"
                                                                       ng-disabled="true"
                                                                       ng-required="Tracking.ShortDayPicker==null && Tracking.DateAndTimeFormObj!=null"
                                                                       datetime-picker="medium"
                                                                       ng-model="Tracking.DateAndTimeToObj"
                                                                       is-open="isOpenTo"
                                                                       datepicker-options="DateAndTimeToObj.datepickerOptions"/>
                                                                <span class="input-group-btn">
                                                                    <button type="button" style="height: 30px; border: 1px solid #ccc; border-left: none" class="btn btn-default"
                                                                            ng-disabled="Tracking.ShortDayPicker!=null || Tracking.SearchEmployee.Id==undefined || Tracking.SearchEmployee.Id==null || Tracking.SearchEmployee==null ||  Tracking.SearchEmployee==null || Tracking.DateAndTimeFormObj==null"
                                                                            ng-click="openCalendarTo($event, prop)">
                                                                        <i class="fa fa-calendar"></i>
                                                                    </button>
                                                                </span>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <button type="button"
                                        class="btn btn-primary pull-right"
                                        ng-disabled="AdjustLocationSettingsForm.$invalid || Loader==true"
                                        ng-click="AdjustLocationSettings()">
                                    <span ng-if="Loader==true">
                                        Please bear with us!  &nbsp;&nbsp;
                                        <i class="fa fa-spin fa-spinner"></i>
                                    </span>
                                    <span ng-if="Loader==false">
                                        Search Now &nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                    </span>

                                </button>
                                <button type="button"
                                        class="btn btn-default pull-right"
                                        ng-disabled="Tracking.SearchEmployee.Id==undefined  || Tracking.SearchEmployee.Id==null || Tracking.SearchEmployee==null || Loader==true"
                                        ng-click="AdjustLocationSettingsReset()"
                                        role="button">
                                    &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-refresh fa-1x"></i> Clear All Search
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-8">
                <div class="panel panel-default" ng-if="ScreenshotsList.length>0">
                    <div class="col-xs-12">
                        <div class="panel panel-default ">
                            <div class="panel-heading">
                                <h4 class="text-bold color-primary text-center">{{ScreenshotsList[0].DeviceName}}</h4>
                                <h4 class="text-bold color-primary text-center">{{ScreenshotsList[0].SearchSession}}</h4>
                            </div>

                            <div class="panel-body" ng-if="ScreenshotsList.length >0 ">
                                <!--<div class="col-xs-1">
                        <label class="left carousel-control btn-pointer arrow" ng-click="GetNew(item.DeviceUniqueId,ScreenshotsList[0].Id)"><i class="glyphicon glyphicon-chevron-left">Show Latest</i></label>
                    </div>-->
                                <div id="myId" class="col-xs-3 custom-height logEntry" ng-repeat="x in ScreenshotsList" ng-class="{'custom-greenish-border':x.MouseClick >0 || x.KeyStroke > 0, 'custom-red-border':x.MouseClick==0 && x.KeyStroke == 0}" style="margin: 30px;">
                                    <label class="color-primary">{{x.FirstName}} {{x.LastName}}</label><br />
                                    <label class="color-primary">{{x.CapturedOn | date : "MMM d, y h:mm:ss a"}}</label><br />
                                    <label class="color-primary"><a><img class="img-responsive" src="{{x.Url}}" width="200" ng-click="openFilePreviewModal(x)" style="border: 1px solid #cbc3c3" /></a></label><br>
                                    <div class="panel-body">
                                        <a ng-hide="x.Status=='Deleted'"><i class="fa fa-trash-o fa-2x" style="color: red" ng-click="DeleteScreenshot(x)"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <a><i class="fa fa-info-circle fa-2x" ng-click="openFilePreviewModal(x)"></i></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <a ng-hide="x.Status=='Deleted'"><i class="fa fa-download fa-2x " style="color: #1667ac" ng-click="DownloadScreenshot(x)"></i></a>
                                    </div>
                                </div>
                                <!--<div class="col-xs-1">
                        <label class="right carousel-control btn-pointer arrow" ng-click="GetPrevious(ScreenshotsList[0].DeviceUniqueId,ScreenshotsList[ScreenshotsList.length - 1].Id)" data-slide="next">Load More</label>
                    </div>-->
                                <!--<span class="pull-right"><button class="btn btn-primary btn-sm" ng-click="RefreshSS(item.DeviceUniqueId)">Update / Refresh</button></span>-->
                            </div>

                            <div class="panel-heading">
                                <button ng-click="LoadMoreScreenshots(ScreenshotsList[0].DeviceUniqueId,ScreenshotsList[ScreenshotsList.length - 1].Id)"
                                        style="margin-left: 40%"
                                        class="btn btn-info">
                                    Load More
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="modal fade" id="FilePreviewModal" tabindex="-1" role="dialog" aria-labelledby="modal-label" data-backdrop="static" data-keyboard="false" style="padding-top: 20px">
        <div class="{{file.Status==null?'modal-dialog modal-80':'modal-dialog  modal-50'}}" role="document">
            <div class="modal-content">
                <div class="modal-header" style="height: 65px">
                    <button type="button" class="custom-close" ng-click="cancelFilePreviewModal()" tooltip-placement="left" uib-tooltip="Close" style="margin-top: 0px;" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" style="margin-top: 1.6%">Screenshot Details</h4>
                </div>
                <div class="modal-body">
                    <div class="panel panel-primary panel-bordered">
                        <div class="panel-body">
                            <!--File Preview For Images-->
                            <img class="img-responsive img-rounded animated fadeIn" style="margin: auto auto" src="{{file.Url}}" ng-if="file.Status==null" />
                            <!--<img class="img-responsive img-rounded animated fadeIn" style="width: 200px" src="{{file.Url}}" ng-if="file.Status=='Deleted'"/>-->
                            <p class="text-bold support-color text-italic" style="padding-left: 15px;" ng-if="file.Status=='Deleted'">The screenshot has been deleted</p>
                            <br />
                            <div class="col-md-6">
                                <table class="table table-condensed">
                                    <tbody>
                                        <tr>
                                            <td>
                                                Captured On
                                            </td>
                                            <td>
                                                {{file.CapturedOn | date:"medium"}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Mouse Click
                                            </td>
                                            <td>
                                                {{file.MouseClick}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Key Stroke
                                            </td>
                                            <td>
                                                {{file.KeyStroke}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Device Name
                                            </td>
                                            <td>
                                                {{file.DeviceName}}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-condensed">
                                    <tbody>
                                        <tr>
                                            <td width="25%">
                                                User Full Name
                                            </td>
                                            <td width="35%">
                                                {{file.UserFullName}}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                User Photo
                                            </td>
                                            <td>
                                                <img class="img-responsive animated fadeIn" style="width: 21%; border: 1px solid #dbe0de; padding: 5px; border-radius: 15px" src="{{file.UserPhotoUrl}}">
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Designation
                                            </td>
                                            <td>
                                                {{file.Designation}}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="modal-footer">
                    <div class="row row-modal">
                        <button type="button" class="btn btn-wide btn-primary pull-right btn-right-border-radius"
                                ng-click="DownloadScreenshot(file)"
                                ng-if="file.Status == null">
                            Download &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-download fa-1x"></i>
                        </button>
                        <button type="button"
                                class="btn  btn-default btn-wide pull-right btn-left-border-radius"
                                ng-click="cancelFilePreviewModal()"
                                role="button">
                            Close
                        </button>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .custom-black-border {
        border: 1px solid black;
    }
    .custom-greenish-border {
        border: 1px solid #4ca492;
    }

    .custom-red-border {
        border: 1px solid red;
    }

    .custom-height {
        min-height: 250px;
    }

    .btn-pointer {
        cursor: pointer;
    }

    .arrow {
        color: #189279;
        margin-top: 85px !important;
    }

    .logEntry.ng-enter {
        -webkit-transition: 1s;
        transition: 2s;
        opacity: 0;
    }

    .logEntry.ng-enter-active {
        opacity: 1;
    }
</style>