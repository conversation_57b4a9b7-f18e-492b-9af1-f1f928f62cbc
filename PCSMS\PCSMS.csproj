﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.0\build\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.0\build\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.1.0.0\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.1.0.0\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{D6AFF56E-4D67-40A9-9A51-823E0198F26D}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>PCSMS</RootNamespace>
    <AssemblyName>PCSMS</AssemblyName>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <Use64BitIISExpress />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept, Version=2.0.6.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Agent.Intercept.2.0.6\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.2.0\lib\net45\Microsoft.AI.DependencyCollector.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.2.0\lib\net45\Microsoft.AI.PerfCounterCollector.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.2.0\lib\net45\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.Web, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Web.2.2.0\lib\net45\Microsoft.AI.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.2.2.0\lib\net45\Microsoft.AI.WindowsServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.2.0\lib\net46\Microsoft.ApplicationInsights.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Core, Version=2.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.Core.2.3.0\lib\net45\Microsoft.AspNet.SignalR.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.SystemWeb, Version=2.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.SystemWeb.2.3.0\lib\net45\Microsoft.AspNet.SignalR.SystemWeb.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.11.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.6\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.6.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.6\lib\net45\System.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.6\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.6\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.6\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.6\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="WebActivator, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivator.*******\lib\WebActivator.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.4.1.9004\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.AspNet.Identity.Core">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.1\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.1\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework">
      <HintPath>..\packages\Microsoft.AspNet.Identity.EntityFramework.2.2.1\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="Owin">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin">
      <HintPath>..\packages\Microsoft.Owin.3.0.1\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.3.0.1\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security">
      <HintPath>..\packages\Microsoft.Owin.Security.3.0.1\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Facebook">
      <HintPath>..\packages\Microsoft.Owin.Security.Facebook.3.0.1\lib\net45\Microsoft.Owin.Security.Facebook.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.3.0.1\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.3.0.1\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Google">
      <HintPath>..\packages\Microsoft.Owin.Security.Google.3.0.1\lib\net45\Microsoft.Owin.Security.Google.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Twitter">
      <HintPath>..\packages\Microsoft.Owin.Security.Twitter.3.0.1\lib\net45\Microsoft.Owin.Security.Twitter.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.MicrosoftAccount">
      <HintPath>..\packages\Microsoft.Owin.Security.MicrosoftAccount.3.0.1\lib\net45\Microsoft.Owin.Security.MicrosoftAccount.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Owin">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Owin.5.2.3\lib\net45\System.Web.Http.Owin.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\CorsHandler.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\IdentityConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\Startup.Auth.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Areas\HelpPage\ApiDescriptionExtensions.cs" />
    <Compile Include="Areas\HelpPage\App_Start\HelpPageConfig.cs" />
    <Compile Include="Areas\HelpPage\Controllers\HelpController.cs" />
    <Compile Include="Areas\HelpPage\HelpPageAreaRegistration.cs" />
    <Compile Include="Areas\HelpPage\HelpPageConfigurationExtensions.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\CollectionModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ComplexTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\DictionaryModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumValueDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\IModelDocumentationProvider.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\KeyValuePairModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescriptionGenerator.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameAttribute.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameHelper.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterAnnotation.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\SimpleTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\Models\HelpPageApiModel.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleKey.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ImageSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\InvalidSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ObjectGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\SampleDirection.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\TextSample.cs" />
    <Compile Include="Areas\HelpPage\XmlDocumentationProvider.cs" />
    <Compile Include="Controllers\AccessTypeController.cs" />
    <Compile Include="Controllers\AuthenticateController.cs" />
    <Compile Include="Controllers\CompanyTypeController.cs" />
    <Compile Include="Controllers\CP_DeviceController.cs" />
    <Compile Include="Controllers\CP_ProfileController.cs" />
    <Compile Include="Controllers\LogOutController.cs" />
    <Compile Include="Controllers\SP_ProfileController.cs" />
    <Compile Include="Controllers\SP_BranchController.cs" />
    <Compile Include="Controllers\SP_DesignationController.cs" />
    <Compile Include="Controllers\CP_UserController.cs" />
    <Compile Include="Controllers\SP_UserController.cs" />
    <Compile Include="Controllers\SubscriptionController.cs" />
    <Compile Include="Controllers\TaskSchedulerController.cs" />
    <Compile Include="Filters\AuthenticationFilter.cs" />
    <Compile Include="Filters\AuthorizationRequiredAttribute.cs" />
    <Compile Include="Filters\BasicIdentity.cs" />
    <Compile Include="Filters\GenericAuthenticationFilter.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Hubs\NotificationHub.cs" />
    <Compile Include="Models\AccountBindingModels.cs" />
    <Compile Include="Models\AccountViewModels.cs" />
    <Compile Include="Models\IdentityModels.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Providers\ApplicationOAuthProvider.cs" />
    <Compile Include="Results\ChallengeResult.cs" />
    <Compile Include="Startup.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Areas\HelpPage\HelpPage.css" />
    <Content Include="Company\app.js" />
    <Content Include="Company\appController.js" />
    <Content Include="Company\appServices.js" />
    <Content Include="Company\appTemplate\left-sidebar.html" />
    <Content Include="Company\appTemplate\page-header.html" />
    <Content Include="Company\app\home\home.html" />
    <Content Include="Company\app\home\homeController.js" />
    <Content Include="Company\app\home\homeServices.js" />
    <Content Include="Company\app\device\device.html" />
    <Content Include="Company\app\device\deviceController.js" />
    <Content Include="Company\app\device\deviceServices.js" />
    <Content Include="Company\app\license\license.html" />
    <Content Include="Company\app\license\licenseController.js" />
    <Content Include="Company\app\license\licenseServices.js" />
    <Content Include="Company\app\logIn\logIn.html" />
    <Content Include="Company\app\logIn\logInController.js" />
    <Content Include="Company\app\logIn\logInServices.js" />
    <Content Include="Company\app\profile\profile.html" />
    <Content Include="Company\app\profile\profileController.js" />
    <Content Include="Company\app\profile\profileServices.js" />
    <Content Include="Company\app\register\register.html" />
    <Content Include="Company\app\register\registerController.js" />
    <Content Include="Company\app\register\registerServices.js" />
    <Content Include="Company\app\schedule\default\defaultSchedule.html" />
    <Content Include="Company\app\schedule\default\defaultScheduleServices.js" />
    <Content Include="Company\app\schedule\default\defaultScheduleController.js" />
    <Content Include="Company\app\schedule\group\groupSchedule.html" />
    <Content Include="Company\app\schedule\group\groupScheduleController.js" />
    <Content Include="Company\app\schedule\group\groupScheduleServices.js" />
    <Content Include="Company\app\schedule\individual\individualSchedule.html" />
    <Content Include="Company\app\schedule\individual\individualScheduleController.js" />
    <Content Include="Company\app\schedule\individual\individualScheduleServices.js" />
    <Content Include="Company\app\screenshots\screenshots.html" />
    <Content Include="Company\app\screenshots\screenshotsController.js" />
    <Content Include="Company\app\screenshots\screenshotsServices.js" />
    <Content Include="Company\app\userIndividualScreenshots\userIndividualScreenshots.html" />
    <Content Include="Company\app\userIndividualScreenshots\userIndividualScreenshotsController.js" />
    <Content Include="Company\app\userIndividualScreenshots\userIndividualScreenshotsServices.js" />
    <Content Include="Company\app\user\user.html" />
    <Content Include="Company\app\user\userController.js" />
    <Content Include="Company\app\user\userServices.js" />
    <Content Include="Company\Assets_Company\ang-file-upload\ng-file-upload-shim.min.js" />
    <Content Include="Company\Assets_Company\ang-file-upload\ng-file-upload.min.js" />
    <Content Include="Company\Assets_Company\angular-720-picker\angular-datepicker.css" />
    <Content Include="Company\Assets_Company\angular-720-picker\angular-datepicker.js" />
    <Content Include="Company\Assets_Company\angular-animate\angular-animate.js" />
    <Content Include="Company\Assets_Company\angular-animate\angular-animate.min.js" />
    <Content Include="Company\Assets_Company\angular-base64\angular-base64.min.js" />
    <Content Include="Company\Assets_Company\angular-block-ui\angular-block-ui.css" />
    <Content Include="Company\Assets_Company\angular-block-ui\angular-block-ui.js" />
    <Content Include="Company\Assets_Company\angular-chart\angular-chart.js" />
    <Content Include="Company\Assets_Company\angular-chart\Chart.js" />
    <Content Include="Company\Assets_Company\angular-cookies\angular-cookies.min.js" />
    <Content Include="Company\Assets_Company\angular-datepicker\angular-datepicker.css" />
    <Content Include="Company\Assets_Company\angular-datepicker\angular-datepicker.js" />
    <Content Include="Company\Assets_Company\angular-message\angular-messages.js" />
    <Content Include="Company\Assets_Company\angular-message\angular-messages.min.js" />
    <Content Include="Company\Assets_Company\angular-sanitize\angular-sanitize.js" />
    <Content Include="Company\Assets_Company\angular-slick\angular-highlightjs.js" />
    <Content Include="Company\Assets_Company\angular-slick\fonts\ajax-loader.gif" />
    <Content Include="Company\Assets_Company\angular-slick\fonts\slick.svg" />
    <Content Include="Company\Assets_Company\angular-slick\slick.css" />
    <Content Include="Company\Assets_Company\angular-slick\slick.js" />
    <Content Include="Company\Assets_Company\angular-slick\slick.min.js" />
    <Content Include="Company\Assets_Company\angular-slick\solarized_light.min.css" />
    <Content Include="Company\Assets_Company\angular-tooltip\angular-tooltips.css" />
    <Content Include="Company\Assets_Company\angular-tooltip\angular-tooltips.js" />
    <Content Include="Company\Assets_Company\angular-touch\angular-touch.js" />
    <Content Include="Company\Assets_Company\angular-touch\angular-touch.min.js" />
    <Content Include="Company\Assets_Company\angular-ui-route\angular-ui-router.min.js" />
    <Content Include="Company\Assets_Company\angular-weekday\ngWeekdaySelector.css" />
    <Content Include="Company\Assets_Company\angular-weekday\ngWeekdaySelector.js" />
    <Content Include="Company\Assets_Company\angularjs-datatable\angular-datatables.js" />
    <Content Include="Company\Assets_Company\angularjs-datatable\dataTables.bootstrap.css" />
    <Content Include="Company\Assets_Company\angularjs-datatable\dataTables.bootstrap.min.js" />
    <Content Include="Company\Assets_Company\angularjs-datatable\jquery.dataTables.min.js" />
    <Content Include="Company\Assets_Company\angularjs-maps\ng-map.min.js" />
    <Content Include="Company\Assets_Company\angularjs\angular.js" />
    <Content Include="Company\Assets_Company\angularjs\angular.min.js" />
    <Content Include="Company\Assets_Company\bootstrap-timepicker\bootstrap-timepicker.js" />
    <Content Include="Company\Assets_Company\custom-css\custom.css" />
    <Content Include="Company\Assets_Company\custom-js\validator.js" />
    <Content Include="Company\Assets_Company\daterangepicker\angular-daterangepicker.js" />
    <Content Include="Company\Assets_Company\daterangepicker\daterangepicker.css" />
    <Content Include="Company\Assets_Company\daterangepicker\daterangepicker.js" />
    <Content Include="Company\Assets_Company\download-js\download.js" />
    <Content Include="Company\Assets_Company\dumpster driving\app %282%29.js" />
    <Content Include="Company\Assets_Company\dumpster driving\app.js" />
    <Content Include="Company\Assets_Company\dumpster driving\appServices.js" />
    <Content Include="Company\Assets_Company\dumpster driving\ComplyeeController.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\CP Findings.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\CP_Employee HTML Update Employee Another Row.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\current location.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\employee update.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\Garbage From manage_taskController.js" />
    <Content Include="Company\Assets_Company\dumpster driving\Huge ng if ternary operator on company package.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\Package History.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\package.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\Profile Html Garbage.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\profile.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\Raw HTML for CP.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\Reverse Geocoding.txt" />
    <Content Include="Company\Assets_Company\dumpster driving\TextFile1.txt" />
    <Content Include="Company\Assets_Company\favicon\android-icon-192x192.png" />
    <Content Include="Company\Assets_Company\favicon\apple-icon-120x120.png" />
    <Content Include="Company\Assets_Company\favicon\favicon-16x16.png" />
    <Content Include="Company\Assets_Company\favicon\favicon-32x32.png" />
    <Content Include="Company\Assets_Company\images\avatar\avatar_1.jpg" />
    <Content Include="Company\Assets_Company\images\avatar\avatar_2.jpg" />
    <Content Include="Company\Assets_Company\images\avatar\avatar_3.jpg" />
    <Content Include="Company\Assets_Company\images\avatar\avatar_4.jpg" />
    <Content Include="Company\Assets_Company\images\avatar\avatar_5.jpg" />
    <Content Include="Company\Assets_Company\images\avatar\avatar_user.jpg" />
    <Content Include="Company\Assets_Company\images\avatar\garbage.png" />
    <Content Include="Company\Assets_Company\images\avatar\info.png" />
    <Content Include="Company\Assets_Company\images\avatar\infoimage.png" />
    <Content Include="Company\Assets_Company\images\avatar\view.png" />
    <Content Include="Company\Assets_Company\images\b-cause.jpg" />
    <Content Include="Company\Assets_Company\images\bacbon-fav.png" />
    <Content Include="Company\Assets_Company\images\bacbon.png" />
    <Content Include="Company\Assets_Company\images\banner-blue.png" />
    <Content Include="Company\Assets_Company\images\banner.png" />
    <Content Include="Company\Assets_Company\images\bg\pattern.png" />
    <Content Include="Company\Assets_Company\images\company.png" />
    <Content Include="Company\Assets_Company\images\datatables\001-delete.png" />
    <Content Include="Company\Assets_Company\images\datatables\002-rubbish-bin-delete-button-1.png" />
    <Content Include="Company\Assets_Company\images\datatables\010-edit.png" />
    <Content Include="Company\Assets_Company\images\datatables\calendar.png" />
    <Content Include="Company\Assets_Company\images\datatables\clock.png" />
    <Content Include="Company\Assets_Company\images\datatables\cms.png" />
    <Content Include="Company\Assets_Company\images\datatables\color-adjustment.png" />
    <Content Include="Company\Assets_Company\images\datatables\computer.png" />
    <Content Include="Company\Assets_Company\images\datatables\delete.png" />
    <Content Include="Company\Assets_Company\images\datatables\design.png" />
    <Content Include="Company\Assets_Company\images\datatables\desktop-computer %281%29.png" />
    <Content Include="Company\Assets_Company\images\datatables\desktop %282%29.png" />
    <Content Include="Company\Assets_Company\images\datatables\desktop-computer.png" />
    <Content Include="Company\Assets_Company\images\datatables\desktop %281%29.png" />
    <Content Include="Company\Assets_Company\images\datatables\edit.png" />
    <Content Include="Company\Assets_Company\images\datatables\info.png" />
    <Content Include="Company\Assets_Company\images\datatables\monitor.png" />
    <Content Include="Company\Assets_Company\images\datatables\networking.png" />
    <Content Include="Company\Assets_Company\images\datatables\pc-search.png" />
    <Content Include="Company\Assets_Company\images\datatables\pc-settings.png" />
    <Content Include="Company\Assets_Company\images\datatables\pc-with-monitor %281%29.png" />
    <Content Include="Company\Assets_Company\images\datatables\pc-with-monitor.png" />
    <Content Include="Company\Assets_Company\images\datatables\renew.png" />
    <Content Include="Company\Assets_Company\images\datatables\screen-black.png" />
    <Content Include="Company\Assets_Company\images\datatables\screen-tia.png" />
    <Content Include="Company\Assets_Company\images\datatables\screen-yellow.png" />
    <Content Include="Company\Assets_Company\images\datatables\screen-blue.png" />
    <Content Include="Company\Assets_Company\images\datatables\screen.png" />
    <Content Include="Company\Assets_Company\images\datatables\settings.png" />
    <Content Include="Company\Assets_Company\images\datatables\shield.png" />
    <Content Include="Company\Assets_Company\images\datatables\sort_asc.png" />
    <Content Include="Company\Assets_Company\images\datatables\sort_asc_disabled.png" />
    <Content Include="Company\Assets_Company\images\datatables\sort_both.png" />
    <Content Include="Company\Assets_Company\images\datatables\sort_desc.png" />
    <Content Include="Company\Assets_Company\images\datatables\sort_desc_disabled.png" />
    <Content Include="Company\Assets_Company\images\datatables\stick-man.png" />
    <Content Include="Company\Assets_Company\images\datatables\tv-screen.png" />
    <Content Include="Company\Assets_Company\images\download-desktop-app.png" />
    <Content Include="Company\Assets_Company\images\falcon.png" />
    <Content Include="Company\Assets_Company\images\offline.png" />
    <Content Include="Company\Assets_Company\images\header-logo.png" />
    <Content Include="Company\Assets_Company\images\helsinki-lg.jpg" />
    <Content Include="Company\Assets_Company\images\helsinki.jpg" />
    <Content Include="Company\Assets_Company\images\logo-dark.png" />
    <Content Include="Company\Assets_Company\images\post\post_1.jpg" />
    <Content Include="Company\Assets_Company\images\post\post_2.jpg" />
    <Content Include="Company\Assets_Company\images\post\post_3.jpg" />
    <Content Include="Company\Assets_Company\images\post\post_4.jpg" />
    <Content Include="Company\Assets_Company\images\post\post_5.jpg" />
    <Content Include="Company\Assets_Company\images\post\post_6.jpg" />
    <Content Include="Company\Assets_Company\images\post\post_7.jpg" />
    <Content Include="Company\Assets_Company\images\register-now.png" />
    <Content Include="Company\Assets_Company\images\theme\dark_blue.png" />
    <Content Include="Company\Assets_Company\images\theme\dark_green.png" />
    <Content Include="Company\Assets_Company\images\theme\dark_white.png" />
    <Content Include="Company\Assets_Company\images\theme\white_blue.png" />
    <Content Include="Company\Assets_Company\images\theme\white_dark.png" />
    <Content Include="Company\Assets_Company\images\theme\white_green.png" />
    <Content Include="Company\Assets_Company\images\Wedges-1.5s-200px.gif" />
    <Content Include="Company\Assets_Company\jasny-bootstrap\css\jasny-bootstrap.css" />
    <Content Include="Company\Assets_Company\jasny-bootstrap\css\jasny-bootstrap.min.css" />
    <Content Include="Company\Assets_Company\jasny-bootstrap\js\jasny-bootstrap.js" />
    <Content Include="Company\Assets_Company\jasny-bootstrap\js\jasny-bootstrap.min.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\charts\chart-js.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\charts\morris.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\dashboard.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\forms\advanced.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\forms\validation.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\forms\wizard.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\tables\data-tables.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\ui-elements\alerts.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\ui-elements\buttons.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\ui-elements\lightbox.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\ui-elements\notifications-pnotify.js" />
    <Content Include="Company\Assets_Company\javascripts\examples\ui-elements\notifications-toastr.js" />
    <Content Include="Company\Assets_Company\javascripts\template-init.min.js" />
    <Content Include="Company\Assets_Company\javascripts\template-script.min.js" />
    <Content Include="Company\Assets_Company\kendo-ui\css\kendo.common-material.min.css" />
    <Content Include="Company\Assets_Company\kendo-ui\css\kendo.material.min.css" />
    <Content Include="Company\Assets_Company\kendo-ui\css\kendo.material.mobile.min.css" />
    <Content Include="Company\Assets_Company\kendo-ui\js\kendo.all.min.js" />
    <Content Include="Company\Assets_Company\moment-js\moment.js" />
    <Content Include="Company\Assets_Company\multiple datepicker\multipleDatePicker.css" />
    <Content Include="Company\Assets_Company\multiple datepicker\multipleDatePicker.js" />
    <Content Include="Company\Assets_Company\ng-autocomplete\ngAutocomplete.js" />
    <Content Include="Company\Assets_Company\ng-file-upload\ng-file-upload-all.js" />
    <Content Include="Company\Assets_Company\pdf-js\pdf.js" />
    <Content Include="Company\Assets_Company\pdf-js\pdf.worker.js" />
    <Content Include="Company\Assets_Company\stylesheets\css\style.css" />
    <Content Include="Company\Assets_Company\ui-bootstrap-datetime-picker\bootstrap-timepicker.min.css" />
    <Content Include="Company\Assets_Company\ui-bootstrap-datetime-picker\datetime-picker.js" />
    <Content Include="Company\Assets_Company\ui-bootstrap-datetime-picker\datetime-picker.tpls.js" />
    <Content Include="Company\Assets_Company\ui-bootstrap\ui-bootstrap-2.5.0.js" />
    <Content Include="Company\Assets_Company\ui-bootstrap\ui-bootstrap-tpls-2.5.0.js" />
    <Content Include="Company\Assets_Company\vendor\animate.css\animate.css" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\css\bootstrap-theme.css" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\css\bootstrap-theme.min.css" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\css\bootstrap.css" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\css\bootstrap.min.css" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.html" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\js\bootstrap.js" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\js\bootstrap.min.js" />
    <Content Include="Company\Assets_Company\vendor\bootstraptoggle\bootstrap2-toggle.css" />
    <Content Include="Company\Assets_Company\vendor\bootstraptoggle\bootstrap2-toggle.js" />
    <Content Include="Company\Assets_Company\vendor\bootstrap\css\bootstrap.css" />
    <Content Include="Company\Assets_Company\vendor\bootstrap\fonts\glyphicons-halflings-regular.html" />
    <Content Include="Company\Assets_Company\vendor\bootstrap\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Company\Assets_Company\vendor\bootstrap\js\bootstrap.js" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_color-picker\css\bootstrap-colorpicker.min.css" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_color-picker\img\bootstrap-colorpicker\alpha-horizontal.png" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_color-picker\img\bootstrap-colorpicker\alpha.png" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_color-picker\img\bootstrap-colorpicker\hue-horizontal.png" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_color-picker\img\bootstrap-colorpicker\hue.png" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_color-picker\img\bootstrap-colorpicker\saturation.png" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_color-picker\js\bootstrap-colorpicker.min.js" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_date-picker\css\bootstrap-datepicker3.min.css" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_date-picker\js\bootstrap-datepicker.min.js" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_max-lenght\bootstrap-maxlength.js" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_time-picker\css\timepicker.css" />
    <Content Include="Company\Assets_Company\vendor\bootstrap_time-picker\js\bootstrap-timepicker.js" />
    <Content Include="Company\Assets_Company\vendor\chart-js\chart.min.js" />
    <Content Include="Company\Assets_Company\vendor\data-table\extensions\Responsive\css\responsive.bootstrap.min.css" />
    <Content Include="Company\Assets_Company\vendor\data-table\extensions\Responsive\js\dataTables.responsive.min.js" />
    <Content Include="Company\Assets_Company\vendor\data-table\extensions\Responsive\js\responsive.bootstrap.min.js" />
    <Content Include="Company\Assets_Company\vendor\data-table\media\css\dataTables.bootstrap.min.css" />
    <Content Include="Company\Assets_Company\vendor\data-table\media\js\dataTables.bootstrap.min.js" />
    <Content Include="Company\Assets_Company\vendor\data-table\media\js\jquery.dataTables.min.js" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\css\font-awesome.css" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfont.svg" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfonte0a5.html" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfonte0a5.svg" />
    <Content Include="Company\Assets_Company\vendor\input-masked\inputmask.bundle.min.js" />
    <Content Include="Company\Assets_Company\vendor\input-masked\phone-codes\phone.js" />
    <Content Include="Company\Assets_Company\angular-slick\fonts\slick.eot" />
    <Content Include="Company\Assets_Company\angular-slick\fonts\slick.ttf" />
    <Content Include="Company\Assets_Company\angular-slick\fonts\slick.woff" />
    <None Include="Company\Assets_Company\vendor\jquery 1.9.1\jquery-1.9.1.intellisense.js" />
    <Content Include="Company\Assets_Company\vendor\jquery 1.9.1\jquery-1.9.1.js" />
    <Content Include="Company\Assets_Company\vendor\jquery 1.9.1\jquery-1.9.1.min.js" />
    <Content Include="Company\Assets_Company\vendor\jquery-validation\jquery.validate.min.js" />
    <Content Include="Company\Assets_Company\vendor\jquery-validation\JqueryValidator-AdditionalMethods.js" />
    <Content Include="Company\Assets_Company\vendor\jquery-validation\JqueryValidator.js" />
    <Content Include="Company\Assets_Company\vendor\jquery.appear\jquery.appear.min.js" />
    <Content Include="Company\Assets_Company\vendor\jquery\jquery-1.12.3.min.js" />
    <Content Include="Company\Assets_Company\vendor\magnific-popup\jquery.magnific-popup.min.js" />
    <Content Include="Company\Assets_Company\vendor\magnific-popup\magnific-popup.css" />
    <Content Include="Company\Assets_Company\vendor\morris-chart\morris.css" />
    <Content Include="Company\Assets_Company\vendor\morris-chart\morris.min.js" />
    <Content Include="Company\Assets_Company\vendor\morris-chart\raphael.min.js" />
    <Content Include="Company\Assets_Company\vendor\nano-scroller\nano-scroller.js" />
    <Content Include="Company\Assets_Company\vendor\owl-carousel\owl.carousel.min.css" />
    <Content Include="Company\Assets_Company\vendor\owl-carousel\owl.carousel.min.js" />
    <Content Include="Company\Assets_Company\vendor\owl-carousel\owl.video.play.html" />
    <Content Include="Company\Assets_Company\vendor\pace\pace-theme-minimal.css" />
    <Content Include="Company\Assets_Company\vendor\pace\pace.min.js" />
    <Content Include="Company\Assets_Company\vendor\peity-chart\jquery.peity.min.js" />
    <Content Include="Company\Assets_Company\vendor\pnotify\pnotify.custom.css" />
    <Content Include="Company\Assets_Company\vendor\pnotify\pnotify.custom.js" />
    <Content Include="Company\Assets_Company\vendor\select2\css\select2-bootstrap.min.css" />
    <Content Include="Company\Assets_Company\vendor\select2\css\select2.min.css" />
    <Content Include="Company\Assets_Company\vendor\select2\js\select2.min.js" />
    <Content Include="Company\Assets_Company\vendor\sweetalert\sweetalert.css" />
    <Content Include="Company\Assets_Company\vendor\sweetalert\sweetalert.min.js" />
    <Content Include="Company\Assets_Company\vendor\sweetalert\sweetalert\sweetalert.css" />
    <Content Include="Company\Assets_Company\vendor\sweetalert\sweetalert\sweetalert.js" />
    <Content Include="Company\Assets_Company\vendor\toastr\angular-toastr.css" />
    <Content Include="Company\Assets_Company\vendor\toastr\angular-toastr.tpls.min.js" />
    <Content Include="Company\Assets_Company\vendor\twitter-bootstrap-wizard\jquery.bootstrap.wizard.js" />
    <Content Include="Company\customConfigs_Company\customConfig.js" />
    <Content Include="Company\customDirectives_Company\customDirective.js" />
    <Content Include="Company\customFilters_Company\customFilter.js" />
    <Content Include="Company\customServices_Company\customServices.js" />
    <Content Include="Company\index.html" />
    <Content Include="Company_Images\Default_Images\doc-icon.png" />
    <Content Include="Company_Images\Default_Images\female.jpg" />
    <Content Include="Company_Images\Default_Images\Image-not-found.png" />
    <Content Include="Company_Images\Default_Images\male.png" />
    <Content Include="Company_Images\Default_Images\pdf-icon.png" />
    <Content Include="Company_Images\User_Images\test.png" />
    <Content Include="Company_Images\Screenshots\no-image.png" />
    <Content Include="Company_Images\Screenshots\test.png" />
    <Content Include="Company_Images\Profile_Images\test.png" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Download-app\sources.txt" />
    <Content Include="favicon.ico" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Global.asax" />
    <Content Include="Notification_Audio\light.mp3" />
    <Content Include="Notification_Audio\open-ended.mp3" />
    <Content Include="Notification_Audio\slow-spring-board.mp3" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Areas\HelpPage\Views\Web.config" />
    <Content Include="Areas\HelpPage\Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\ResourceModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Index.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\TextSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\SimpleTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Samples.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Parameters.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ModelDescriptionLink.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\KeyValuePairModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\InvalidSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ImageSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\HelpPageApiModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\EnumTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\DictionaryModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ComplexTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\CollectionModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ApiGroup.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Api.cshtml" />
    <Content Include="ApplicationInsights.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Company\Assets_Company\angular-animate\angular-animate.min.js.map" />
    <Content Include="Company\Assets_Company\angular-message\angular-messages.min.js.map" />
    <Content Include="Company\Assets_Company\angular-touch\angular-touch.min.js.map" />
    <Content Include="Company\Assets_Company\bootstrap-timepicker\timepicker.less" />
    <Content Include="Company\Assets_Company\jasny-bootstrap\css\jasny-bootstrap.css.map" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\css\bootstrap-theme.css.map" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\css\bootstrap-theme.min.css.map" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\css\bootstrap.css.map" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\css\bootstrap.min.css.map" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="Company\Assets_Company\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regulard41d.eot" />
    <Content Include="Company\Assets_Company\vendor\bootstrap\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Company\Assets_Company\vendor\bootstrap\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Company\Assets_Company\vendor\bootstrap\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="Company\Assets_Company\vendor\bootstrap\fonts\glyphicons-halflings-regulard41d.eot" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfont.eot" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfont.ttf" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfont.woff" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfont.woff2" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfontd41d.eot" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfonte0a5.eot" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfonte0a5.ttf" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\fontawesome-webfonte0a5.woff" />
    <Content Include="Company\Assets_Company\vendor\font-awesome\fonts\FontAwesome.otf" />
    <Content Include="Company\Assets_Company\vendor\jquery 1.9.1\jquery-1.9.1.min.map" />
    <None Include="Properties\PublishProfiles\PCSMS-Pusblish.pubxml" />
    <None Include="Properties\PublishProfiles\PCSMSPublish.pubxml" />
    <None Include="Properties\PublishProfiles\PCSMS_BANKPublish.pubxml" />
    <None Include="Scripts\jquery-1.10.2.intellisense.js" />
    <Content Include="Scripts\jquery-1.10.2.js" />
    <Content Include="Scripts\jquery-1.10.2.min.js" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.signalR-2.3.0.js" />
    <Content Include="Scripts\jquery.signalR-2.3.0.min.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\modernizr-2.6.2.js" />
    <Content Include="Scripts\respond.js" />
    <Content Include="Scripts\respond.min.js" />
    <Content Include="SProvider\app.js" />
    <Content Include="SProvider\appController.js" />
    <Content Include="SProvider\appServices.js" />
    <Content Include="SProvider\appTemplate\left-sidebar.html" />
    <Content Include="SProvider\appTemplate\page-header.html" />
    <Content Include="SProvider\app\database\database.html" />
    <Content Include="SProvider\app\database\databaseController.js" />
    <Content Include="SProvider\app\database\databaseServices.js" />
    <Content Include="SProvider\app\home\home.html" />
    <Content Include="SProvider\app\home\homeController.js" />
    <Content Include="SProvider\app\home\homeServices.js" />
    <Content Include="SProvider\app\license\license.html" />
    <Content Include="SProvider\app\license\licenseController.js" />
    <Content Include="SProvider\app\license\licenseServices.js" />
    <Content Include="SProvider\app\logIn\logIn.html" />
    <Content Include="SProvider\app\logIn\logInController.js" />
    <Content Include="SProvider\app\logIn\logInServices.js" />
    <Content Include="SProvider\app\managecompany\onlyregisteredcompany\onlyregisteredcompany.html" />
    <Content Include="SProvider\app\managecompany\onlyregisteredcompany\onlyregisteredcompanyController.js" />
    <Content Include="SProvider\app\managecompany\onlyregisteredcompany\onlyregisteredcompanyServices.js" />
    <Content Include="SProvider\app\managecompany\subscribedcompany\subscribedcompany.html" />
    <Content Include="SProvider\app\managecompany\subscribedcompany\subscribedcompanyController.js" />
    <Content Include="SProvider\app\managecompany\subscribedcompany\subscribedcompanyServices.js" />
    <Content Include="SProvider\app\profile\profile.html" />
    <Content Include="SProvider\app\profile\profileController.js" />
    <Content Include="SProvider\app\profile\profileServices.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\branch\branch.html" />
    <Content Include="SProvider\app\settings\Service Provider Settings\branch\branchController.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\branch\branchServices.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\designation\designation.html" />
    <Content Include="SProvider\app\settings\Service Provider Settings\designation\designationController.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\designation\designationService.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\user\user.html" />
    <Content Include="SProvider\app\settings\Service Provider Settings\user\userController.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\user\userService.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\user_profile\user profile.html" />
    <Content Include="SProvider\app\settings\Service Provider Settings\user_profile\user_profileController.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\user_profile\user_profileServices.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\access_type\access type.html" />
    <Content Include="SProvider\app\settings\Service Provider Settings\access_type\access_typeController.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\access_type\access_typeServices.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\company_type\company type.html" />
    <Content Include="SProvider\app\settings\Service Provider Settings\company_type\company_typeController.js" />
    <Content Include="SProvider\app\settings\Service Provider Settings\company_type\company_typeServices.js" />
    <Content Include="SProvider\Assets_SProvider\ang-file-upload\ng-file-upload-shim.min.js" />
    <Content Include="SProvider\Assets_SProvider\ang-file-upload\ng-file-upload.min.js" />
    <Content Include="SProvider\Assets_SProvider\angular-720-picker\angular-datepicker.css" />
    <Content Include="SProvider\Assets_SProvider\angular-720-picker\angular-datepicker.js" />
    <Content Include="SProvider\Assets_SProvider\angular-animate\angular-animate.js" />
    <Content Include="SProvider\Assets_SProvider\angular-animate\angular-animate.min.js" />
    <Content Include="SProvider\Assets_SProvider\angular-base64\angular-base64.min.js" />
    <Content Include="SProvider\Assets_SProvider\angular-block-ui\angular-block-ui.css" />
    <Content Include="SProvider\Assets_SProvider\angular-block-ui\angular-block-ui.js" />
    <Content Include="SProvider\Assets_SProvider\angular-chart\angular-chart.js" />
    <Content Include="SProvider\Assets_SProvider\angular-chart\Chart.js" />
    <Content Include="SProvider\Assets_SProvider\angular-cookies\angular-cookies.min.js" />
    <Content Include="SProvider\Assets_SProvider\angular-datepicker\angular-datepicker.css" />
    <Content Include="SProvider\Assets_SProvider\angular-datepicker\angular-datepicker.js" />
    <Content Include="SProvider\Assets_SProvider\angular-message\angular-messages.js" />
    <Content Include="SProvider\Assets_SProvider\angular-message\angular-messages.min.js" />
    <Content Include="SProvider\Assets_SProvider\angular-sanitize\angular-sanitize.js" />
    <Content Include="SProvider\Assets_SProvider\angular-tooltip\angular-tooltips.css" />
    <Content Include="SProvider\Assets_SProvider\angular-tooltip\angular-tooltips.js" />
    <Content Include="SProvider\Assets_SProvider\angular-touch\angular-touch.js" />
    <Content Include="SProvider\Assets_SProvider\angular-touch\angular-touch.min.js" />
    <Content Include="SProvider\Assets_SProvider\angular-ui-route\angular-ui-router.min.js" />
    <Content Include="SProvider\Assets_SProvider\angular-weekday\ngWeekdaySelector.css" />
    <Content Include="SProvider\Assets_SProvider\angular-weekday\ngWeekdaySelector.js" />
    <Content Include="SProvider\Assets_SProvider\angularjs-datatable\angular-datatables.js" />
    <Content Include="SProvider\Assets_SProvider\angularjs-datatable\dataTables.bootstrap.css" />
    <Content Include="SProvider\Assets_SProvider\angularjs-datatable\dataTables.bootstrap.min.js" />
    <Content Include="SProvider\Assets_SProvider\angularjs-datatable\jquery.dataTables.min.js" />
    <Content Include="SProvider\Assets_SProvider\angularjs-maps\ng-map.min.js" />
    <Content Include="SProvider\Assets_SProvider\angularjs\angular.js" />
    <Content Include="SProvider\Assets_SProvider\angularjs\angular.min.js" />
    <Content Include="SProvider\Assets_SProvider\bootstrap-timepicker\bootstrap-timepicker.js" />
    <Content Include="SProvider\Assets_SProvider\custom-css\custom.css" />
    <Content Include="SProvider\Assets_SProvider\custom-js\validator.js" />
    <Content Include="SProvider\Assets_SProvider\daterangepicker\angular-daterangepicker.js" />
    <Content Include="SProvider\Assets_SProvider\daterangepicker\daterangepicker.css" />
    <Content Include="SProvider\Assets_SProvider\daterangepicker\daterangepicker.js" />
    <Content Include="SProvider\Assets_SProvider\download-js\download.js" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\app %282%29.js" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\app.js" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\appServices.js" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\ComplyeeController.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\CP Findings.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\CP_Employee HTML Update Employee Another Row.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\current location.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\employee update.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\Garbage From manage_taskController.js" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\Huge ng if ternary operator on company package.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\Package History.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\package.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\Profile Html Garbage.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\profile.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\Raw HTML for CP.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\Reverse Geocoding.txt" />
    <Content Include="SProvider\Assets_SProvider\dumpster driving\TextFile1.txt" />
    <Content Include="SProvider\Assets_SProvider\favicon\android-icon-192x192.png" />
    <Content Include="SProvider\Assets_SProvider\favicon\apple-icon-120x120.png" />
    <Content Include="SProvider\Assets_SProvider\favicon\favicon-16x16.png" />
    <Content Include="SProvider\Assets_SProvider\favicon\favicon-32x32.png" />
    <Content Include="SProvider\Assets_SProvider\images\admin-logo.png" />
    <Content Include="SProvider\Assets_SProvider\images\arrow-download-icon.png" />
    <Content Include="SProvider\Assets_SProvider\images\avatar\avatar_1.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\avatar\avatar_2.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\avatar\avatar_3.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\avatar\avatar_4.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\avatar\avatar_5.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\avatar\avatar_user.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\avatar\garbage.png" />
    <Content Include="SProvider\Assets_SProvider\images\avatar\info.png" />
    <Content Include="SProvider\Assets_SProvider\images\avatar\infoimage.png" />
    <Content Include="SProvider\Assets_SProvider\images\avatar\view.png" />
    <Content Include="SProvider\Assets_SProvider\images\b-cause.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\bacbon-fav.png" />
    <Content Include="SProvider\Assets_SProvider\images\banner-blue.png" />
    <Content Include="SProvider\Assets_SProvider\images\banner.png" />
    <Content Include="SProvider\Assets_SProvider\images\bg\pattern.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\001-delete.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\002-rubbish-bin-delete-button-1.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\010-edit.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\delete.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\edit.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\info.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\renew.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\sort_asc.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\sort_asc_disabled.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\sort_both.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\sort_desc.png" />
    <Content Include="SProvider\Assets_SProvider\images\datatables\sort_desc_disabled.png" />
    <Content Include="SProvider\Assets_SProvider\images\falcon.png" />
    <Content Include="SProvider\Assets_SProvider\images\header-logo.png" />
    <Content Include="SProvider\Assets_SProvider\images\helsinki-lg.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\helsinki.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\logo-dark.png" />
    <Content Include="SProvider\Assets_SProvider\images\post\post_1.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\post\post_2.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\post\post_3.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\post\post_4.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\post\post_5.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\post\post_6.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\post\post_7.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\bacbon.png" />
    <Content Include="SProvider\Assets_SProvider\images\theme\dark_blue.png" />
    <Content Include="SProvider\Assets_SProvider\images\theme\dark_green.png" />
    <Content Include="SProvider\Assets_SProvider\images\theme\dark_white.png" />
    <Content Include="SProvider\Assets_SProvider\images\technology %283%29.jpg" />
    <Content Include="SProvider\Assets_SProvider\images\theme\white_blue.png" />
    <Content Include="SProvider\Assets_SProvider\images\theme\white_dark.png" />
    <Content Include="SProvider\Assets_SProvider\images\theme\white_green.png" />
    <Content Include="SProvider\Assets_SProvider\images\Wedges-1.5s-200px.gif" />
    <Content Include="SProvider\Assets_SProvider\jasny-bootstrap\css\jasny-bootstrap.css" />
    <Content Include="SProvider\Assets_SProvider\jasny-bootstrap\css\jasny-bootstrap.min.css" />
    <Content Include="SProvider\Assets_SProvider\jasny-bootstrap\js\jasny-bootstrap.js" />
    <Content Include="SProvider\Assets_SProvider\jasny-bootstrap\js\jasny-bootstrap.min.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\charts\chart-js.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\charts\morris.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\dashboard.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\forms\advanced.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\forms\validation.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\forms\wizard.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\tables\data-tables.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\ui-elements\alerts.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\ui-elements\buttons.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\ui-elements\lightbox.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\ui-elements\notifications-pnotify.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\examples\ui-elements\notifications-toastr.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\template-init.min.js" />
    <Content Include="SProvider\Assets_SProvider\javascripts\template-script.min.js" />
    <Content Include="SProvider\Assets_SProvider\kendo-ui\css\kendo.common-material.min.css" />
    <Content Include="SProvider\Assets_SProvider\kendo-ui\css\kendo.material.min.css" />
    <Content Include="SProvider\Assets_SProvider\kendo-ui\css\kendo.material.mobile.min.css" />
    <Content Include="SProvider\Assets_SProvider\kendo-ui\js\kendo.all.min.js" />
    <Content Include="SProvider\Assets_SProvider\moment-js\moment.js" />
    <Content Include="SProvider\Assets_SProvider\multiple datepicker\multipleDatePicker.css" />
    <Content Include="SProvider\Assets_SProvider\multiple datepicker\multipleDatePicker.js" />
    <Content Include="SProvider\Assets_SProvider\ng-autocomplete\ngAutocomplete.js" />
    <Content Include="SProvider\Assets_SProvider\ng-file-upload\ng-file-upload-all.js" />
    <Content Include="SProvider\Assets_SProvider\pdf-js\pdf.js" />
    <Content Include="SProvider\Assets_SProvider\pdf-js\pdf.worker.js" />
    <Content Include="SProvider\Assets_SProvider\stylesheets\css\style.css" />
    <Content Include="SProvider\Assets_SProvider\ui-bootstrap-datetime-picker\bootstrap-timepicker.min.css" />
    <Content Include="SProvider\Assets_SProvider\ui-bootstrap-datetime-picker\datetime-picker.js" />
    <Content Include="SProvider\Assets_SProvider\ui-bootstrap-datetime-picker\datetime-picker.tpls.js" />
    <Content Include="SProvider\Assets_SProvider\ui-bootstrap\ui-bootstrap-2.5.0.js" />
    <Content Include="SProvider\Assets_SProvider\ui-bootstrap\ui-bootstrap-tpls-2.5.0.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\animate.css\animate.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\css\bootstrap-theme.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\css\bootstrap-theme.min.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\css\bootstrap.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\css\bootstrap.min.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.html" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\js\bootstrap.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\js\bootstrap.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstraptoggle\bootstrap2-toggle.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstraptoggle\bootstrap2-toggle.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap\css\bootstrap.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap\fonts\glyphicons-halflings-regular.html" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap\js\bootstrap.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_color-picker\css\bootstrap-colorpicker.min.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_color-picker\img\bootstrap-colorpicker\alpha-horizontal.png" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_color-picker\img\bootstrap-colorpicker\alpha.png" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_color-picker\img\bootstrap-colorpicker\hue-horizontal.png" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_color-picker\img\bootstrap-colorpicker\hue.png" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_color-picker\img\bootstrap-colorpicker\saturation.png" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_color-picker\js\bootstrap-colorpicker.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_date-picker\css\bootstrap-datepicker3.min.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_date-picker\js\bootstrap-datepicker.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_max-lenght\bootstrap-maxlength.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_time-picker\css\timepicker.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap_time-picker\js\bootstrap-timepicker.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\chart-js\chart.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\data-table\extensions\Responsive\css\responsive.bootstrap.min.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\data-table\extensions\Responsive\js\dataTables.responsive.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\data-table\extensions\Responsive\js\responsive.bootstrap.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\data-table\media\css\dataTables.bootstrap.min.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\data-table\media\js\dataTables.bootstrap.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\data-table\media\js\jquery.dataTables.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\css\font-awesome.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfont.svg" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfonte0a5.html" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfonte0a5.svg" />
    <Content Include="SProvider\Assets_SProvider\vendor\input-masked\inputmask.bundle.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\input-masked\phone-codes\phone.js" />
    <None Include="SProvider\Assets_SProvider\vendor\jquery 1.9.1\jquery-1.9.1.intellisense.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\jquery 1.9.1\jquery-1.9.1.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\jquery 1.9.1\jquery-1.9.1.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\jquery-validation\jquery.validate.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\jquery-validation\JqueryValidator-AdditionalMethods.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\jquery-validation\JqueryValidator.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\jquery.appear\jquery.appear.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\jquery\jquery-1.12.3.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\magnific-popup\jquery.magnific-popup.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\magnific-popup\magnific-popup.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\morris-chart\morris.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\morris-chart\morris.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\morris-chart\raphael.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\nano-scroller\nano-scroller.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\owl-carousel\owl.carousel.min.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\owl-carousel\owl.carousel.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\owl-carousel\owl.video.play.html" />
    <Content Include="SProvider\Assets_SProvider\vendor\pace\pace-theme-minimal.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\pace\pace.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\peity-chart\jquery.peity.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\pnotify\pnotify.custom.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\pnotify\pnotify.custom.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\select2\css\select2-bootstrap.min.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\select2\css\select2.min.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\select2\js\select2.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\sweetalert\sweetalert.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\sweetalert\sweetalert.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\sweetalert\sweetalert\sweetalert.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\sweetalert\sweetalert\sweetalert.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\toastr\angular-toastr.css" />
    <Content Include="SProvider\Assets_SProvider\vendor\toastr\angular-toastr.tpls.min.js" />
    <Content Include="SProvider\Assets_SProvider\vendor\twitter-bootstrap-wizard\jquery.bootstrap.wizard.js" />
    <Content Include="SProvider\customConfigs_SProvider\customConfig.js" />
    <Content Include="SProvider\customDirectives_SProvider\customDirective.js" />
    <Content Include="SProvider\customFilters_SProvider\customFilter.js" />
    <Content Include="SProvider\customServices_SProvider\customServices.js" />
    <Content Include="SProvider\Assets_SProvider\images\home-sprovider.jpg" />
    <Content Include="SProvider\index.html" />
    <Content Include="SProvider_Images\Default_Images\app.png" />
    <Content Include="SProvider_Images\Default_Images\female.png" />
    <Content Include="SProvider_Images\Default_Images\Image-not-found.png" />
    <Content Include="SProvider_Images\Default_Images\male.png" />
    <Content Include="SProvider_Images\Default_Images\paid-seal.png" />
    <Content Include="SProvider_Images\Profile_Images\test.png" />
    <Content Include="SProvider_Images\User_Images\test.png" />
    <Content Include="terms-conditions.html" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Areas\HelpPage\Views\_ViewStart.cshtml" />
    <Content Include="Content\Site.css" />
    <Content Include="Scripts\_references.js" />
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="SProvider\Assets_SProvider\angular-animate\angular-animate.min.js.map" />
    <Content Include="SProvider\Assets_SProvider\angular-message\angular-messages.min.js.map" />
    <Content Include="SProvider\Assets_SProvider\angular-touch\angular-touch.min.js.map" />
    <Content Include="SProvider\Assets_SProvider\bootstrap-timepicker\timepicker.less" />
    <Content Include="SProvider\Assets_SProvider\jasny-bootstrap\css\jasny-bootstrap.css.map" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\css\bootstrap-theme.css.map" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\css\bootstrap-theme.min.css.map" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\css\bootstrap.css.map" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\css\bootstrap.min.css.map" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap 3.3.7\fonts\glyphicons-halflings-regulard41d.eot" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="SProvider\Assets_SProvider\vendor\bootstrap\fonts\glyphicons-halflings-regulard41d.eot" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfont.eot" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfont.ttf" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfont.woff" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfont.woff2" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfontd41d.eot" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfonte0a5.eot" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfonte0a5.ttf" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\fontawesome-webfonte0a5.woff" />
    <Content Include="SProvider\Assets_SProvider\vendor\font-awesome\fonts\FontAwesome.otf" />
    <Content Include="SProvider\Assets_SProvider\vendor\jquery 1.9.1\jquery-1.9.1.min.map" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Logs\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
    <Content Include="packages.config" />
    <None Include="Project_Readme.html" />
    <Content Include="Scripts\jquery-1.10.2.min.map" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\PCSMS.Common\PCSMS.Common.csproj">
      <Project>{22025331-a6d4-4af0-a096-6c8b75dec919}</Project>
      <Name>PCSMS.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\PCSMS.Models\PCSMS.Models.csproj">
      <Project>{f4753e7d-9dea-4c16-86d1-44b14aa4a942}</Project>
      <Name>PCSMS.Models</Name>
    </ProjectReference>
    <ProjectReference Include="..\PCSMS.Repository\PCSMS.Repository.csproj">
      <Project>{a0612edf-51f6-4d2d-8e15-0e980f4ea891}</Project>
      <Name>PCSMS.Repository</Name>
    </ProjectReference>
    <ProjectReference Include="..\PCSMS.Services\PCSMS.Services.csproj">
      <Project>{85271391-1782-4362-bb25-c6bd85c9a9a6}</Project>
      <Name>PCSMS.Services</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>1839</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:1839/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.1.0.0\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.1.0.0\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.0\build\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.1.0.0\build\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>