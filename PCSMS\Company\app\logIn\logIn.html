﻿<style>
    .page-body {
        padding-left: 10px;
        padding-right: 10px;
        padding-bottom: 50px;
    }

    .logo {
        max-width: 420px;
        margin: 20px auto 0px;
        margin-bottom: 40px;
    }

        .logo img {
            display: block;
            margin: auto;
            width: 50%;
            min-width: 140px;
        }

    .box {
        background: #ffffff;
        border-radius: 5px;
        padding: 15px;
        max-width: 420px;
        margin: 20px auto 0px;
    }
</style>


<div class="animated slideInDown">
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="row">
        <div class="col-md-offset-2 col-md-5">
            <div class="box">
                <!--SIGN IN FORM-->
                <div class="panel mb-none">
                    <div class="panel-content bg-scale-0">
                        <img alt="logo" src="Assets_Company/images/company.png"  style="width: 40%;border :2px dashed #67b6a5"/>
                        <form name="logInForm" novalidate>
                            <div class="form-group mt-md">
                                <span class="input-with-icon">
                                    <input id="Email" name="Email" class="form-control input-md" type="text" placeholder="E-mail"
                                           ng-model="LogInData.Email"
                                           ng-change="ControlRememberMe()"
                                           ng-required="true"
                                           ng-minlength="11"
                                           ng-maxlength="30"
                                           ng-disabled="Loader == true"
                                           ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/' />
                                    <i class="fa fa-envelope"></i>
                                </span>
                                <div class="row custom-row">
                                    <ul class="list-unstyled errormessage pull-left"
                                        ng-show="logInForm.Email.$dirty && logInForm.Email.$invalid">
                                        <li class="pull-left" ng-show="logInForm.Email.$error.pattern">Not a valid Email Address</li>
                                        <li><span class="pull-left" ng-show="!logInForm.Email.$error.pattern && logInForm.Email.$error.minlength">Minimum required length is 11</span></li>
                                        <li><span class="pull-left" ng-show="!logInForm.Email.$error.pattern && logInForm.Email.$error.maxlength">Maximum required length is 30</span></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="form-group">
                                <span class="input-with-icon">
                                    <input id="Password" name="Password" class="form-control input-md" type="password" placeholder="Password"
                                           ng-model="LogInData.Password"
                                           ng-change="ControlRememberMe()"
                                           ng-required="true"
                                           ng-minlength="6"
                                           ng-maxlength="50"
                                           ng-disabled="Loader == true"
                                           ng-pattern="/([a-zA-Z0-9 ])\w+/" />
                                    <i class="fa fa-key"></i>
                                </span>
                                <div class="row custom-row">
                                    <ul class="list-unstyled errormessage pull-left" ng-show="logInForm.Password.$dirty && logInForm.Password.$invalid">
                                        <li><span class="pull-left" ng-show="logInForm.Password.$error.required">*Required</span></li>
                                        <li><span class="pull-left" ng-show="logInForm.Password.$error.pattern">Not a valid Password</span></li>
                                        <li><span class="pull-left" ng-show="!logInForm.Password.$error.pattern && logInForm.Password.$error.minlength">Minimum required length is 6</span></li>
                                        <li><span class="pull-left" ng-show="!logInForm.Password.$error.pattern && logInForm.Password.$error.maxlength">Maximum required length is 50</span></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="checkbox-custom checkbox-primary">
                                    <input type="checkbox" id="remember-me"
                                           ng-disabled="logInForm.$invalid || Loader == true"
                                           ng-true-value="1"
                                           ng-false-value="0"
                                           ng-model="Remember" />
                                    <label class="check" for="remember-me">Remember me</label>
                                </div>
                            </div>
                            <div class="form-group" ng-if="Loader==false">
                                <button class="btn btn-primary btn-block"
                                        ng-click="LogIn();"
                                        ng-disabled="logInForm.$invalid || Loader == true">
                                    Sign in
                                </button>
                            </div>
                            <div class="form-group" ng-if="Loader==true">
                                <button class="btn btn-primary btn-block"
                                        ng-disabled="Loader == true">
                                    <i ng-if="Loader==true" class="fa fa-spinner fa-spin"></i>
                                </button>
                            </div>
                            <div class="form-group text-center">
                                <!--<a ui-sref="forgotPassword">Forgot password?</a>-->
                                <hr />
                                <span>Don't have an account?</span>
                                <a ui-sref="register" class="btn btn-block mt-sm" ng-disabled="Loader == true">Register</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-5">
            <div class="box">
                <div class="panel mb-none">
                    <div class="panel-content bg-scale-0">
                        <a><img alt="logo" src="Assets_Company/images/download-desktop-app.png" class="pull-right" ng-click="DownloadDesktopApp()" style="width: 100%" /></a>
                        <div class="form-group text-center">
                            Haven't downloaded app?, <a ui-sref="logIn" ng-click="DownloadDesktopApp()">Download Desktop App</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>