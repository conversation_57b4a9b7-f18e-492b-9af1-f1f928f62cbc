﻿using System;
using System.Configuration;
using System.Linq;
using System.Web.Script.Serialization;
using JWT;
using Newtonsoft.Json;
using PCSMS.Models;
using PCSMS.Models.Models_Anonymous;
using PCSMS.Models.Models_Company;
using PCSMS.Models.Models_SProvider;
using PCSMS.Models.Models_Temp;
using PCSMS.Services.Services_Company;
using PCSMS.Services.Services_SProvider;

namespace PCSMS.Services.Services_Shared
{
    public class TokenServices : ICP_TokenServices
    {
        #region Private member variables.
        private readonly IEntityService<CP_Token> _tokenCPRepository;
        private readonly IEntityService<SP_Token> _tokenTPRepository;
        private readonly PCSMSDbContext _context;
        private readonly ICP_ProfileServices _companyServices;
        private readonly ISP_UserServices _SProviderServices;
        #endregion


        public TokenServices()
        {
            _context = new PCSMSDbContext();
            _tokenCPRepository = new EntityService<CP_Token>(_context);
            _tokenTPRepository = new EntityService<SP_Token>(_context);
            _companyServices = new CP_ProfileServices();
            _SProviderServices = new SP_UserServices();
        }

        public CP_Token GenerateCompanyPCSMSToken(int companyId)
        {
            try
            {
                ////Add system.web.extensions
                ////Add system.configuration for ConfigurationManager
                var company = JsonConvert.DeserializeObject<CP_Profile>(new JavaScriptSerializer().Serialize(_companyServices.GetCompanyDetailsForPayload(companyId).Data));
                var issuedOn = DateTime.Now;
                var expiredOn = DateTime.Now.AddHours(Convert.ToDouble(ConfigurationManager.AppSettings["AuthTokenExpiry"]));

                var payload = new CP_PayLoad
                {
                    CompanyEmail = company.CompanyEmail

                };
                //Install-Package JWT -Version 1.3.0   through nuget package in the corresponded services (i.e. PCSMS.Services)
                var token = "company" + JsonWebToken.Encode(payload, EncryptionHelper.GetPrivateKey(),
                JwtHashAlgorithm.HS256);

                var tokenEntity = new CP_Token
                {
                    CompanyId = companyId,
                    AuthToken = token,
                    IssuedOn = issuedOn,
                    ExpiresOn = expiredOn
                };

                _tokenCPRepository.Save(tokenEntity);
                _tokenCPRepository.SaveChanges();
                return tokenEntity;
            }
            catch (Exception)
            {
                return new CP_Token();
            }
        }
        public SP_Token GenerateSProviderPCSMSToken(int userId)
        {
            try
            {
                var tpUser = JsonConvert.DeserializeObject<SP_User>(new JavaScriptSerializer().Serialize(_SProviderServices.GetSP_UserDetails(userId).Data));
                var issuedOn = DateTime.Now;
                var expiredOn = DateTime.Now.AddHours(Convert.ToDouble(ConfigurationManager.AppSettings["AuthTokenExpiry"]));

                var payload = new SP_PayLoad
                {
                    Username = tpUser.Username

                };
                //Install-Package JWT -Version 1.3.0   through nuget package in the corresponded services (i.e. PCSMS.Services)
                var token = "SProviderPCSMS" + JsonWebToken.Encode(payload, EncryptionHelper.GetPrivateKey(),
                JwtHashAlgorithm.HS256);

                var tokenEntity = new SP_Token
                {
                    UserId = userId,
                    AuthToken = token,
                    IssuedOn = issuedOn,
                    ExpiresOn = expiredOn
                };

                _tokenTPRepository.Save(tokenEntity);
                _tokenTPRepository.SaveChanges();
                return tokenEntity;
            }
            catch (Exception)
            {
                return new SP_Token();
            }
        }
        public bool ValidateToken(string longToken)
        {
            if (longToken.Contains("company"))
            {
                using (PCSMSDbContext a = new PCSMSDbContext())
                {
                    var token = a.CP_Token.FirstOrDefault(x => x.AuthToken == longToken);


                    if (token != null)
                    {
                        return true;

                    }
                    else
                    {
                        var companyId =
                            a.CP_Token.Where(x => x.AuthToken == longToken).Select(x => x.CompanyId).FirstOrDefault();
                        //DeleteCPTokenByCompanyId((int) companyId);
                        return false;
                    }
                }
            }
            else if (longToken.Contains("SProviderPCSMS"))
            {
                using (PCSMSDbContext a = new PCSMSDbContext())
                {
                    var token = a.SP_Token.FirstOrDefault(x => x.AuthToken == longToken);


                    if (token != null)
                    {
                        return true;

                    }
                    else
                    {
                        var userId =
                            a.SP_Token.Where(x => x.AuthToken == longToken).Select(x => x.UserId).FirstOrDefault();
                        //DeleteTPTokenByUserId((int) userId);
                        return false;
                    }                }
            }
            else
            {
                return false;
            }
            




        }
        public bool Kill(string tokenId)
        {
            var token = _context.CP_Token.FirstOrDefault(x => x.AuthToken == tokenId);
            _tokenCPRepository.Delete(token);
            _tokenCPRepository.SaveChanges();
            var isNotDeleted = _context.CP_Token.Any(x => x.AuthToken == tokenId);
            if (isNotDeleted)
            {
                return false;
            }
            return true;
        }
        public bool DeleteCPTokenByCompanyId(int companyId)
        {
            _context.CP_Token.RemoveRange(_context.CP_Token.Where(x => x.CompanyId == companyId));
            _tokenCPRepository.SaveChanges();
            var isNotDeleted = _context.CP_Token.Any(x => x.CompanyId == companyId);
            return !isNotDeleted;
        }
        public bool DeleteTPTokenByUserId(int userId)
        {
            _context.SP_Token.RemoveRange(_context.SP_Token.Where(x => x.UserId == userId));
            _tokenCPRepository.SaveChanges();
            var isNotDeleted = _context.SP_Token.Any(x => x.UserId == userId);
            return !isNotDeleted;
        }

    }

    public interface ICP_TokenServices
    {
        CP_Token GenerateCompanyPCSMSToken(int companyId);
        SP_Token GenerateSProviderPCSMSToken(int userId);
        bool ValidateToken(string longToken);
        bool Kill(string tokenId);
        bool DeleteCPTokenByCompanyId(int userId);
        bool DeleteTPTokenByUserId(int userId);
    }
}
