﻿!function(e,t){"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?module.exports=t():e.download=t()}(this,function(){return function e(t,n,o){var a,r,i=window,d="application/octet-stream",c=o||d,s=t,l=!n&&!o&&s,f=document.createElement("a"),u=function(e){return String(e)},p=i.Blob||i.MozBlob||i.WebKitBlob||u,m=n||"download";if(p=p.call?p.bind(i):Blob,"true"===String(this)&&(c=(s=[s,c])[0],s=s[1]),l&&l.length<2048&&(m=l.split("/").pop().split("?")[0],f.href=l,-1!==f.href.indexOf(l))){var b=new XMLHttpRequest;return b.open("GET",l,!0),b.responseType="blob",b.onload=function(t){e(t.target.response,m,d)},setTimeout(function(){b.send()},0),b}if(/^data:([\w+-]+\/[\w+.-]+)?[,;]/.test(s)){if(!(s.length>2096103.424&&p!==u))return navigator.msSaveBlob?navigator.msSaveBlob(g(s),m):y(s);s=g(s),c=s.type||d}else if(/([\x80-\xff])/.test(s)){for(var h=0,w=new Uint8Array(s.length),v=w.length;h<v;++h)w[h]=s.charCodeAt(h);s=new p([w],{type:c})}function g(e){for(var t=e.split(/[:;,]/),n=t[1],o=("base64"==t[e.indexOf("charset")>0?3:2]?atob:decodeURIComponent)(t.pop()),a=o.length,r=0,i=new Uint8Array(a);r<a;++r)i[r]=o.charCodeAt(r);return new p([i],{type:n})}function y(e,t){if("download"in f)return f.href=e,f.setAttribute("download",m),f.className="download-js-link",f.innerHTML="downloading...",f.style.display="none",f.addEventListener("click",function(e){e.stopPropagation(),this.removeEventListener("click",arguments.callee)}),document.body.appendChild(f),setTimeout(function(){f.click(),document.body.removeChild(f),!0===t&&setTimeout(function(){i.URL.revokeObjectURL(f.href)},250)},66),!0;if(/(Version)\/(\d+)\.(\d+)(?:\.(\d+))?.*Safari\//.test(navigator.userAgent))return/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,d)),window.open(e)||confirm("Displaying New Document\n\nUse Save As... to download, then click back to return to this page.")&&(location.href=e),!0;var n=document.createElement("iframe");document.body.appendChild(n),!t&&/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,d)),n.src=e,setTimeout(function(){document.body.removeChild(n)},333)}if(a=s instanceof p?s:new p([s],{type:c}),navigator.msSaveBlob)return navigator.msSaveBlob(a,m);if(i.URL)y(i.URL.createObjectURL(a),!0);else{if("string"==typeof a||a.constructor===u)try{return y("data:"+c+";base64,"+i.btoa(a))}catch(e){return y("data:"+c+","+encodeURIComponent(a))}(r=new FileReader).onload=function(e){y(this.result)},r.readAsDataURL(a)}return!0}});