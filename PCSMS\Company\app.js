﻿var PCSMSApp = angular.module("PCSMSApp", ['ui.router', 'ngMessages', 'ngAnimate', 'datatables', 'toastr', 'ngSanitize', 'ngFileUpload', '720kb.tooltips', '720kb.datepicker', 'ngAnimate', 'ui.bootstrap', 'ui.bootstrap.datetimepicker', "ngCookies", "base64", "ngFileUpload", "blockUI","daterangepicker"]);

PCSMSApp.service('authInterceptor', function ($q, $window, $rootScope, $timeout, $injector, $cookies) {
    var service = this;


    service.request = function (response) {
        $rootScope.response = true;
        return $q.resolve(response);
    };
    service.response = function (response) {
        //console.log(response);
        $rootScope.response = false;
        return $q.resolve(response);
    };
    service.responseError = function (response) {
        $rootScope.UsernameOrPasswordNotMatched = false;

        function goToLoginPageAndClearLocalStorage() {
            $window.location.href = "/Company/#/logIn";
            $cookies.remove('Company_Token_Codeaura_Demo', { path: '/' });
            $cookies.remove('Company_Details_Codeaura_Demo', { path: '/' });
        }

        var toastr = $injector.get('toastr');
        var $state = $injector.get('$state');


        //If token value expires or wrong token provided:
        if (response.status === 401) {
            goToLoginPageAndClearLocalStorage();
            $rootScope.UnauthorizedRequestFound = true;
        }
        else if (response.status === 500) {
            //goToLoginPageAndClearLocalStorage();
            toastr.error("Internal Server Error! Please try again !", {
                timeOut: 3000
            });
            $rootScope.InternalServerErrorFound = true;
        }

        else if (response.status === 403) {
            $state.reload();
            $rootScope.UsernameOrPasswordNotMatched = true;
        }
        return $q.reject(response);
    };

});
PCSMSApp.config(function ($stateProvider, $urlRouterProvider, $httpProvider, toastrConfig, blockUIConfig) {
    $httpProvider.interceptors.push('authInterceptor', function ($q, $cookies, $rootScope) {
        return {
            'request': function (config) {
                var companytoken;
                var companytokenFromCookies = $cookies.get('Company_Token_Codeaura_Demo');
                if (companytokenFromCookies) {
                    companytoken = JSON.parse(companytokenFromCookies);
                } else {
                    companytoken = null;
                }
                config.headers['Token'] = companytoken;
                return config;
            }
        };
    });
    angular.extend(toastrConfig, {
        autoDismiss: false,
        containerId: 'toast-container',
        maxOpened: 0,
        newestOnTop: true,
        positionClass: 'toast-top-right',
        preventDuplicates: false,
        preventOpenDuplicates: false,
        target: 'body',
        progressBar: true,
        allowHtml: true
    });

    blockUIConfig.message = 'Please Wait ...';

    $urlRouterProvider.otherwise('/logIn');
    $stateProvider
        //-------------------------------------------------------------LOGIN-----------------------------------------------------
        .state('logIn', {
            url: '/logIn',
            templateUrl: 'app/logIn/logIn.html',
            controller: 'logInController'
        })

        //-------------------------------------------------------------REGISTER-----------------------------------------------------
        .state('register', {
            url: '/register',
            templateUrl: 'app/register/register.html',
            controller: 'registerController'
        })

        //-------------------------------------------------------------HOME-----------------------------------------------------
        .state('home', {
            url: '/home',
            templateUrl: 'app/home/<USER>',
            controller: 'homeController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })

        //-------------------------------------------------------------PROFILE-----------------------------------------------------
        .state('profile', {
            url: '/profile',
            templateUrl: 'app/profile/profile.html',
            controller: 'profileController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })

        //-------------------------------------------------------------LICENSE-----------------------------------------------------
        .state('license', {
            url: '/license',
            templateUrl: 'app/license/license.html',
            controller: 'licenseController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })

        //-------------------------------------------------------------USER-----------------------------------------------------
        .state('user', {
            url: '/user',
            templateUrl: 'app/user/user.html',
            controller: 'userController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
       //-------------------------------------------------------------DEVICE-----------------------------------------------------
        .state('device', {
            url: '/device',
            templateUrl: 'app/device/device.html',
            controller: 'deviceController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        //-------------------------------------------------------------SCHEDULE-----------------------------------------------------
        .state('defaultSchedule', {
            url: '/defaultSchedule',
            templateUrl: 'app/schedule/default/defaultSchedule.html',
            controller: 'defaultScheduleController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        .state('individualSchedule', {
            url: '/individualSchedule',
            templateUrl: 'app/schedule/individual/individualSchedule.html',
            controller: 'individualScheduleController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        .state('groupSchedule', {
            url: '/groupSchedule',
            templateUrl: 'app/schedule/group/groupSchedule.html',
            controller: 'groupScheduleController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        //-------------------------------------------------------------SCREENSHOTS-----------------------------------------------------
        .state('screenshots', {
            url: '/screenshots',
            templateUrl: 'app/screenshots/screenshots.html',
            controller: 'screenshotsController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })
        .state('userIndividualScreenshots', {
            url: '/userIndividualScreenshots',
            templateUrl: 'app/userIndividualScreenshots/userIndividualScreenshots.html',
            controller: 'userIndividualScreenshotsController',
            resolve: {
                isCompanyAuthenticated: checkAuthentication
            }
        })


});

function checkAuthentication($q, customServices, $state, $timeout, $window, toastr, $rootScope, $cookies) {
    if (customServices.isLoggedIn()) {
        // Resolve the promise successfully
        return $q.when();
    }
    else {
        $timeout(function () {
            //This code runs after the authentication promise has been rejected.
            //Reason of using $timeout below: Let's assume, unauthenticated user is in state A. They click a link to go to protected state 
            //B but you want to redirect them to logInPage. If there's no $timeout, ui-router will simply halt all state transitions, 
            //so the user would be stuck in state A. The $timeout allows ui-router to first prevent the initial transition to protected state 
            //B because the resolve was rejected and after that's done, it redirects to logInPage
            if ($rootScope.CompanyDetails) {
                toastr.error("Session expired, Please login again !", {
                    timeOut: 2000
                });
                $window.location.href = "/Company/#/logIn";
                $cookies.remove('CompanyPckgPrivileges', { path: '/' });
                $cookies.remove('Company_Details_Codeaura_Demo', { path: '/' });
            } else {
                toastr.error("You seem to be unauthorized, Please login !", {
                    timeOut: 2000
                });
                $window.location.href = "/Company/#/logIn";
            }


        });
        // Reject the authentication promise to prevent the state from loading
        return $q.reject();
    }
}
PCSMSApp.run(function ($rootScope, $http, $q, $state, toastr, $timeout, appServices, customServices, $cookies, $window) {
    $rootScope.divId = "3";
    //========On $stateChangeStart Event========
    $rootScope.$on('$stateChangeStart', function (event, toState, toParams, fromState, fromParams) {
        //Getting Token and Other objects :
        $rootScope.CompanyTokenFromCookies = $cookies.get('Company_Token_Codeaura_Demo');
        if ($rootScope.CompanyTokenFromCookies) {
            $rootScope.CompanyToken = JSON.parse($rootScope.CompanyTokenFromCookies);
        }

        $rootScope.CompanyPckgPrivilegesFromCookies = $cookies.get('CompanyPckgPrivileges');

        $rootScope.CompanyDetailsFromCookies = $cookies.get('Company_Details_Codeaura_Demo');
        if ($rootScope.CompanyDetailsFromCookies) {
            $rootScope.CompanyDetails = JSON.parse($rootScope.CompanyDetailsFromCookies);
            $rootScope.CompanyId = $rootScope.CompanyDetails.Id;
            $rootScope.CompanyName = $rootScope.CompanyDetails.CompanyName;
            $rootScope.CompanyCode = $rootScope.CompanyDetails.CompanyCode;
        }

        //Assume, user is logged-in and closes his browser and now he reopens the browser
        //three phrases are found:
        //1. he starts his journey from unknown state
        //2. our app will commence journey from logIn state
        //3. he could be logged in

        var userIsLoggedIn = customServices.isLoggedIn();


        var unKnownState = "^";
        if (fromState.url === unKnownState && toState.url === "/logIn" && userIsLoggedIn) {
            $window.location.href = "/Company/#/home";

        }


            //If user is logged-in and wants to go to logIn page again, To restrict him:
        else if (toState.url === "/logIn" && userIsLoggedIn) {
            event.preventDefault();
            toastr.error("You are already logged in, hence you can not go to login page !", {
                timeOut: 3000
            });
            $window.location.href = "/Company/#" + fromState.url;
        }

        //Updating cookies :
        if ($rootScope.CompanyTokenFromCookies) {
            var date = new Date();
            var expireTime = date.getTime() + 10800000; // 3 hours
            date.setTime(expireTime);
            $cookies.put('Company_Token_Codeaura_Demo', $cookies.get('Company_Token_Codeaura_Demo'), { 'expires': date, 'path': '/' });
            $cookies.put('Company_Details_Codeaura_Demo', $cookies.get('Company_Details_Codeaura_Demo'), { 'expires': date, 'path': '/' });
            $cookies.put('HTMLCollapseStatus', "fixed left-sidebar-top", { 'expires': date, 'path': '/' });
        }
    });


    //========On $stateChangeSuccess Event========
    $rootScope.$on("$stateChangeSuccess", function (event, toState, toParams, fromState, fromParams) {
        if ($rootScope.HTMLCollapseStatus == null || $rootScope.HTMLCollapseStatus == undefined) {
            $rootScope.HTMLCollapseStatus = "fixed left-sidebar-top";
        }
    });
});





