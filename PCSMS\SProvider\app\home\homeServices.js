﻿PCSMSApp.factory("homeServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetCountOfActiveAndBlockedSP_Users: function () {
            return $http({
                url: "/api/SP_User/GetCountOfActiveAndBlockedSP_Users",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCountOfRegisteredCompanies: function () {
            return $http({
                url: "/api/Subscription/GetCountOfRegisteredCompanies",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCountOfSubscribedCompanies: function () {
            return $http({
                url: "/api/Subscription/GetCountOfSubscribedCompanies",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCountOfActiveLicenses: function () {
            return $http({
                url: "/api/Subscription/GetCountOfActiveLicenses",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCountOfExpiredLicenses: function () {
            return $http({
                url: "/api/Subscription/GetCountOfExpiredLicenses",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        TerminateSubscriptionByTaskScheduler: function () {
            return $http({
                url: "/api/TaskScheduler/TerminateSubscriptionByTaskScheduler",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },


    };

}]);