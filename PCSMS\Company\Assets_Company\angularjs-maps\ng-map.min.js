﻿!function (e, t) { "object" == typeof exports ? module.exports = t(require("angular")) : "function" == typeof define && define.amd ? define(["angular"], t) : t(e.angular) }(this, function (angular) {/**
 * AngularJS Google Maps Ver. 1.18.4
 *
 * The MIT License (MIT)
 * 
 * Copyright (c) 2014, 2015, 1016 <PERSON>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
    return angular.module("ngMap", []), function () { "use strict"; var e, t = function (t, n, o, r, a, i, s, p, c) { e = i; var u = this, l = a.startSymbol(), g = a.endSymbol(); u.mapOptions, u.mapEvents, u.eventListeners, u.addObject = function (e, t) { if (u.map) { u.map[e] = u.map[e] || {}; var n = Object.keys(u.map[e]).length; u.map[e][t.id || n] = t, u.map instanceof google.maps.Map && ("infoWindows" != e && t.setMap && t.setMap && t.setMap(u.map), t.centered && t.position && u.map.setCenter(t.position), "markers" == e && u.objectChanged("markers"), "customMarkers" == e && u.objectChanged("customMarkers")) } }, u.deleteObject = function (e, t) { if (t.map) { var n = t.map[e]; for (var o in n) n[o] === t && (google.maps.event.clearInstanceListeners(t), delete n[o]); t.map && t.setMap && t.setMap(null), "markers" == e && u.objectChanged("markers"), "customMarkers" == e && u.objectChanged("customMarkers") } }, u.observeAttrSetObj = function (t, n, o) { if (n.noWatcher) return !1; for (var r = e.getAttrsToObserve(t), a = 0; a < r.length; a++) { var i = r[a]; n.$observe(i, s.observeAndSet(i, o)) } }, u.zoomToIncludeMarkers = function () { if (null != u.map.markers && Object.keys(u.map.markers).length > 0 || null != u.map.customMarkers && Object.keys(u.map.customMarkers).length > 0) { var e = new google.maps.LatLngBounds; for (var t in u.map.markers) e.extend(u.map.markers[t].getPosition()); for (var n in u.map.customMarkers) e.extend(u.map.customMarkers[n].getPosition()); u.mapOptions.maximumZoom && (u.enableMaximumZoomCheck = !0), u.map.fitBounds(e) } }, u.objectChanged = function (e) { !u.map || "markers" != e && "customMarkers" != e || "auto" != u.map.zoomToIncludeMarkers || u.zoomToIncludeMarkers() }, u.initializeMap = function () { var a = u.mapOptions, i = u.mapEvents, m = u.map; if (u.map = p.getMapInstance(n[0]), s.setStyle(n[0]), m) { var f = e.filter(o), v = e.getOptions(f), y = e.getControlOptions(f); a = angular.extend(v, y); for (var h in m) { var b = m[h]; if ("object" == typeof b) for (var M in b) u.addObject(h, b[M]) } u.map.showInfoWindow = u.showInfoWindow, u.map.hideInfoWindow = u.hideInfoWindow } a.zoom = a.zoom && !isNaN(a.zoom) ? +a.zoom : 15; var O = a.center, w = new RegExp(c(l) + ".*" + c(g)); if (!a.center || "string" == typeof O && O.match(w)) a.center = new google.maps.LatLng(0, 0); else if ("string" == typeof O && O.match(/^[0-9.-]*,[0-9.-]*$/)) { var L = parseFloat(O.split(",")[0]), k = parseFloat(O.split(",")[1]); a.center = new google.maps.LatLng(L, k) } else if (!(O instanceof google.maps.LatLng)) { var $ = a.center; delete a.center, s.getGeoLocation($, a.geoLocationOptions).then(function (e) { u.map.setCenter(e); var n = a.geoCallback; n && r(n)(t) }, function () { a.geoFallbackCenter && u.map.setCenter(a.geoFallbackCenter) }) } u.map.setOptions(a); for (var C in i) { var j = i[C], A = google.maps.event.addListener(u.map, C, j); u.eventListeners[C] = A } u.observeAttrSetObj(d, o, u.map), u.singleInfoWindow = a.singleInfoWindow, google.maps.event.trigger(u.map, "resize"), google.maps.event.addListenerOnce(u.map, "idle", function () { s.addMap(u), a.zoomToIncludeMarkers && u.zoomToIncludeMarkers(), t.map = u.map, t.$emit("mapInitialized", u.map), o.mapInitialized && r(o.mapInitialized)(t, { map: u.map }) }), a.zoomToIncludeMarkers && a.maximumZoom && google.maps.event.addListener(u.map, "zoom_changed", function () { 1 == u.enableMaximumZoomCheck && (u.enableMaximumZoomCheck = !1, google.maps.event.addListenerOnce(u.map, "bounds_changed", function () { u.map.setZoom(Math.min(a.maximumZoom, u.map.getZoom())) })) }) }, t.google = google; var d = e.orgAttributes(n), m = e.filter(o), f = e.getOptions(m, { scope: t }), v = e.getControlOptions(m), y = angular.extend(f, v), h = e.getEvents(t, m); if (Object.keys(h).length && void 0, u.mapOptions = y, u.mapEvents = h, u.eventListeners = {}, f.lazyInit) { if (o.id && 0 === o.id.indexOf(l, 0) && -1 !== o.id.indexOf(g, o.id.length - g.length)) var b = o.id.slice(2, -2), M = r(b)(t); else var M = o.id; u.map = { id: M }, s.addMap(u) } else u.initializeMap(); f.triggerResize && google.maps.event.trigger(u.map, "resize"), n.bind("$destroy", function () { p.returnMapInstance(u.map), s.deleteMap(u) }) }; t.$inject = ["$scope", "$element", "$attrs", "$parse", "$interpolate", "Attr2MapOptions", "NgMap", "NgMapPool", "escapeRegexpFilter"], angular.module("ngMap").controller("__MapController", t) }(), function () { "use strict"; var e, t = function (t, o, r, a) { a = a[0] || a[1]; var i = e.orgAttributes(o), s = e.filter(r), p = e.getOptions(s, { scope: t }), c = e.getEvents(t, s), u = n(p, c); a.addObject("bicyclingLayers", u), a.observeAttrSetObj(i, r, u), o.bind("$destroy", function () { a.deleteObject("bicyclingLayers", u) }) }, n = function (e, t) { var n = new google.maps.BicyclingLayer(e); for (var o in t) google.maps.event.addListener(n, o, t[o]); return n }, o = function (n) { return e = n, { restrict: "E", require: ["?^map", "?^ngMap"], link: t } }; o.$inject = ["Attr2MapOptions"], angular.module("ngMap").directive("bicyclingLayer", o) }(), function () { "use strict"; var e, t, n = function (t, n, o, r, a) { r = r[0] || r[1]; { var i = e.filter(o), s = e.getOptions(i, { scope: t }), p = e.getEvents(t, i), c = t.$new(), u = n[0].parentElement.removeChild(n[0]); a(c, function (e) { n.empty(), n.append(e), n.on("$destroy", function () { c.$destroy() }) }) } for (var l in p) google.maps.event.addDomListener(u, l, p[l]); r.addObject("customControls", u); var g = s.position; r.map.controls[google.maps.ControlPosition[g]].push(u), n.bind("$destroy", function () { r.deleteObject("customControls", u) }) }, o = function (o, r) { return e = o, t = r, { restrict: "E", require: ["?^map", "?^ngMap"], link: n, transclude: !0 } }; o.$inject = ["Attr2MapOptions", "NgMap"], angular.module("ngMap").directive("customControl", o) }(), function () { "use strict"; var e, t, n, o, r = function () { for (var e = "transform WebkitTransform MozTransform OTransform msTransform".split(" "), t = document.createElement("div"), n = 0; n < e.length; n++) if (t && void 0 !== t.style[e[n]]) return e[n]; return !1 }(), a = function (e) { e = e || {}, this.el = document.createElement("div"), this.el.style.display = "block", this.el.style.visibility = "hidden", this.visible = !0; for (var t in e) this[t] = e[t] }, i = function () { a.prototype = new google.maps.OverlayView, a.prototype.setContent = function (e, t) { this.el.innerHTML = e, this.el.style.position = "absolute", this.el.style.top = 0, this.el.style.left = 0, t && n(angular.element(this.el).contents())(t) }, a.prototype.getDraggable = function () { return this.draggable }, a.prototype.setDraggable = function (e) { this.draggable = e }, a.prototype.getPosition = function () { return this.position }, a.prototype.setPosition = function (e) { e && (this.position = e); var n = this; if (this.getProjection() && "function" == typeof this.position.lng) { var o = function () { if (n.getProjection()) { var e = n.getProjection().fromLatLngToDivPixel(n.position), t = Math.round(e.x - n.el.offsetWidth / 2), o = Math.round(e.y - n.el.offsetHeight - 10); r ? n.el.style[r] = "translate(" + t + "px, " + o + "px)" : (n.el.style.left = t + "px", n.el.style.top = o + "px"), n.el.style.visibility = "visible" } }; n.el.offsetWidth && n.el.offsetHeight ? o() : t(o, 300) } }, a.prototype.setZIndex = function (e) { void 0 !== e && (this.zIndex !== e && (this.zIndex = e), this.el.style.zIndex !== this.zIndex && (this.el.style.zIndex = this.zIndex)) }, a.prototype.getVisible = function () { return this.visible }, a.prototype.setVisible = function (e) { "none" === this.el.style.display && e ? this.el.style.display = "block" : "none" === this.el.style.display || e || (this.el.style.display = "none"), this.visible = e }, a.prototype.addClass = function (e) { var t = this.el.className.trim().split(" "); -1 == t.indexOf(e) && t.push(e), this.el.className = t.join(" ") }, a.prototype.removeClass = function (e) { var t = this.el.className.split(" "), n = t.indexOf(e); n > -1 && t.splice(n, 1), this.el.className = t.join(" ") }, a.prototype.onAdd = function () { this.getPanes().overlayMouseTarget.appendChild(this.el) }, a.prototype.draw = function () { this.setPosition(), this.setZIndex(this.zIndex), this.setVisible(this.visible) }, a.prototype.onRemove = function () { this.el.parentNode.removeChild(this.el) } }, s = function (t, n) { return function (r, i, s, p) { p = p[0] || p[1]; var c = e.orgAttributes(i), u = e.filter(s), l = e.getOptions(u, { scope: r }), g = e.getEvents(r, u); i[0].style.display = "none"; var d = new a(l); setTimeout(function () { r.$watch("[" + n.join(",") + "]", function () { d.setContent(t, r) }, !0), d.setContent(i[0].innerHTML, r); var e = i[0].firstElementChild && (i[0].firstElementChild.className || ""); d.class && (e += " " + d.class), d.addClass("custom-marker"), e && d.addClass(e), l.position instanceof google.maps.LatLng || o.getGeoLocation(l.position).then(function (e) { d.setPosition(e) }) }); for (var m in g) google.maps.event.addDomListener(d.el, m, g[m]); p.addObject("customMarkers", d), p.observeAttrSetObj(c, s, d), i.bind("$destroy", function () { p.deleteObject("customMarkers", d) }) } }, p = function (r, a, p, c, u, l) { e = c, t = r, n = a, o = u; var g = p.startSymbol(), d = p.endSymbol(), m = new RegExp(l(g) + "([^" + d.substring(0, 1) + "]+)" + l(d), "g"); return { restrict: "E", require: ["?^map", "?^ngMap"], compile: function (e) { i(), e[0].style.display = "none"; var t = e.html(), n = t.match(m), o = []; return (n || []).forEach(function (e) { var t = e.replace(g, "").replace(d, ""); -1 == e.indexOf("::") && -1 == e.indexOf("this.") && -1 == o.indexOf(t) && o.push(e.replace(g, "").replace(d, "")) }), s(t, o) } } }; p.$inject = ["$timeout", "$compile", "$interpolate", "Attr2MapOptions", "NgMap", "escapeRegexpFilter"], angular.module("ngMap").directive("customMarker", p) }(), function () { "use strict"; var e, t, n, o, r, a = 20, i = function (e, t) { e.panel && (e.panel = document.getElementById(e.panel) || document.querySelector(e.panel)); var n = new google.maps.DirectionsRenderer(e); for (var o in t) google.maps.event.addListener(n, o, t[o]); return n }, s = function (e, i) { var s = new google.maps.DirectionsService, p = i; p.travelMode = p.travelMode || "DRIVING"; var c = ["origin", "destination", "travelMode", "transitOptions", "unitSystem", "durationInTraffic", "waypoints", "optimizeWaypoints", "provideRouteAlternatives", "avoidHighways", "avoidTolls", "region"]; if (p) for (var u in p) p.hasOwnProperty(u) && -1 === c.indexOf(u) && delete p[u]; p.waypoints && (Array.isArray(p.waypoints) || delete p.waypoints); var l = function (n) { if (o && n) if (r) for (var i in n) n.hasOwnProperty(i) && (r[i] = n[i]); else r = n; else o = t(function () { r || (r = n), s.route(r, function (t, n) { n == google.maps.DirectionsStatus.OK && (e.setDirections(t), r = void 0) }), t.cancel(o), o = void 0 }, a) }; p && p.origin && p.destination && ("current-location" == p.origin ? n.getCurrentPosition().then(function (e) { p.origin = new google.maps.LatLng(e.coords.latitude, e.coords.longitude), l(p) }) : "current-location" == p.destination ? n.getCurrentPosition().then(function (e) { p.destination = new google.maps.LatLng(e.coords.latitude, e.coords.longitude), l(p) }) : l(p)) }, p = function (o, r, a, p) { var c = o; e = p, t = r, n = a; var u = function (n, o, r, a) { a = a[0] || a[1]; var p = c.orgAttributes(o), u = c.filter(r), l = c.getOptions(u, { scope: n }), g = c.getEvents(n, u), d = c.getAttrsToObserve(p), d = []; u.noWatcher || (d = c.getAttrsToObserve(p)); var m = i(l, g); a.addObject("directionsRenderers", m), d.forEach(function (e) { !function (e) { r.$observe(e, function (n) { if ("panel" == e) t(function () { var e = document.getElementById(n) || document.querySelector(n); e && m.setPanel(e) }); else if (l[e] !== n) { var o = c.toOptionValue(n, { key: e }); l[e] = o, s(m, l) } }) }(e) }), e.getMap().then(function () { s(m, l) }), o.bind("$destroy", function () { a.deleteObject("directionsRenderers", m) }) }; return { restrict: "E", require: ["?^map", "?^ngMap"], link: u } }; p.$inject = ["Attr2MapOptions", "$timeout", "NavigatorGeolocation", "NgMap"], angular.module("ngMap").directive("directions", p) }(), function () { "use strict"; angular.module("ngMap").directive("drawingManager", ["Attr2MapOptions", function (e) { var t = e; return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (e, n, o, r) { r = r[0] || r[1]; var a = t.filter(o), i = t.getOptions(a, { scope: e }), s = t.getControlOptions(a), p = t.getEvents(e, a), c = new google.maps.drawing.DrawingManager({ drawingMode: i.drawingmode, drawingControl: i.drawingcontrol, drawingControlOptions: s.drawingControlOptions, circleOptions: i.circleoptions, markerOptions: i.markeroptions, polygonOptions: i.polygonoptions, polylineOptions: i.polylineoptions, rectangleOptions: i.rectangleoptions }); o.$observe("drawingControlOptions", function (e) { c.drawingControlOptions = t.getControlOptions({ drawingControlOptions: e }).drawingControlOptions, c.setDrawingMode(null), c.setMap(r.map) }); for (var u in p) google.maps.event.addListener(c, u, p[u]); r.addObject("mapDrawingManager", c), n.bind("$destroy", function () { r.deleteObject("mapDrawingManager", c) }) } } }]) }(), function () { "use strict"; angular.module("ngMap").directive("dynamicMapsEngineLayer", ["Attr2MapOptions", function (e) { var t = e, n = function (e, t) { var n = new google.maps.visualization.DynamicMapsEngineLayer(e); for (var o in t) google.maps.event.addListener(n, o, t[o]); return n }; return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (e, o, r, a) { a = a[0] || a[1]; var i = t.filter(r), s = t.getOptions(i, { scope: e }), p = t.getEvents(e, i, p), c = n(s, p); a.addObject("mapsEngineLayers", c) } } }]) }(), function () { "use strict"; angular.module("ngMap").directive("fusionTablesLayer", ["Attr2MapOptions", function (e) { var t = e, n = function (e, t) { var n = new google.maps.FusionTablesLayer(e); for (var o in t) google.maps.event.addListener(n, o, t[o]); return n }; return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (e, o, r, a) { a = a[0] || a[1]; var i = t.filter(r), s = t.getOptions(i, { scope: e }), p = t.getEvents(e, i, p), c = n(s, p); a.addObject("fusionTablesLayers", c), o.bind("$destroy", function () { a.deleteObject("fusionTablesLayers", c) }) } } }]) }(), function () { "use strict"; angular.module("ngMap").directive("heatmapLayer", ["Attr2MapOptions", "$window", function (e, t) { var n = e; return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (e, o, r, a) { function i(e, t) { return e.split(".").reduce(function (e, t) { return e[t] }, t || this) } a = a[0] || a[1]; var s = n.filter(r), p = n.getOptions(s, { scope: e }); if (p.data = t[r.data] || i(r.data, e), !(p.data instanceof Array)) throw "invalid heatmap data"; p.data = new google.maps.MVCArray(p.data); { var c = new google.maps.visualization.HeatmapLayer(p); n.getEvents(e, s) } a.addObject("heatmapLayers", c) } } }]) }(), function () { "use strict"; var e = function (e, t, n, o, r, a, i) { var s = e, p = function (e, a, i) { var s; !e.position || e.position instanceof google.maps.LatLng || delete e.position, s = new google.maps.InfoWindow(e); for (var p in a) p && google.maps.event.addListener(s, p, a[p]); var c = n(function (e) { angular.isString(i) ? o(i).then(function (t) { e(angular.element(t).wrap("<div>").parent()) }, function (e) { throw "info-window template request failed: " + e }) : e(i) }).then(function (e) { var t = e.html().trim(); if (1 != angular.element(t).length) throw "info-window working as a template must have a container"; s.__template = t.replace(/\s?ng-non-bindable[='"]+/, "") }); return s.__open = function (e, n, o) { c.then(function () { r(function () { o && (n.anchor = o); var r = t(s.__template)(n); s.setContent(r[0]), n.$apply(), o && o.getPosition ? s.open(e, o) : o && o instanceof google.maps.LatLng ? (s.open(e), s.setPosition(o)) : s.open(e); var a = s.content.parentElement.parentElement.parentElement; a.className = "ng-map-info-window" }) }) }, s }, c = function (e, t, n, o) { o = o[0] || o[1], t.css("display", "none"); var r, c = s.orgAttributes(t), u = s.filter(n), l = s.getOptions(u, { scope: e }), g = s.getEvents(e, u), d = p(l, g, l.template || t); !l.position || l.position instanceof google.maps.LatLng || (r = l.position), r && i.getGeoLocation(r).then(function (t) { d.setPosition(t), d.__open(o.map, e, t); var r = n.geoCallback; r && a(r)(e) }), o.addObject("infoWindows", d), o.observeAttrSetObj(c, n, d), o.showInfoWindow = o.map.showInfoWindow = o.showInfoWindow || function (t, n, r) { var a = "string" == typeof t ? t : n, i = "string" == typeof t ? n : r; if ("string" == typeof i) if ("undefined" != typeof o.map.markers && "undefined" != typeof o.map.markers[i]) i = o.map.markers[i]; else { if ("undefined" == typeof o.map.customMarkers || "undefined" == typeof o.map.customMarkers[i]) throw new Error("Cant open info window for id " + i + ". Marker or CustomMarker is not defined"); i = o.map.customMarkers[i] } var s = o.map.infoWindows[a], p = i ? i : this.getPosition ? this : null; s.__open(o.map, e, p), o.singleInfoWindow && (o.lastInfoWindow && e.hideInfoWindow(o.lastInfoWindow), o.lastInfoWindow = a) }, o.hideInfoWindow = o.map.hideInfoWindow = o.hideInfoWindow || function (e, t) { var n = "string" == typeof e ? e : t, r = o.map.infoWindows[n]; r.close() }, e.showInfoWindow = o.map.showInfoWindow, e.hideInfoWindow = o.map.hideInfoWindow; var m = d.mapId ? { id: d.mapId } : 0; i.getMap(m).then(function (t) { if (d.visible && d.__open(t, e), d.visibleOnMarker) { var n = d.visibleOnMarker; d.__open(t, e, t.markers[n]) } }) }; return { restrict: "E", require: ["?^map", "?^ngMap"], link: c } }; e.$inject = ["Attr2MapOptions", "$compile", "$q", "$templateRequest", "$timeout", "$parse", "NgMap"], angular.module("ngMap").directive("infoWindow", e) }(), function () { "use strict"; angular.module("ngMap").directive("kmlLayer", ["Attr2MapOptions", function (e) { var t = e, n = function (e, t) { var n = new google.maps.KmlLayer(e); for (var o in t) google.maps.event.addListener(n, o, t[o]); return n }; return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (e, o, r, a) { a = a[0] || a[1]; var i = t.orgAttributes(o), s = t.filter(r), p = t.getOptions(s, { scope: e }), c = t.getEvents(e, s), u = n(p, c); a.addObject("kmlLayers", u), a.observeAttrSetObj(i, r, u), o.bind("$destroy", function () { a.deleteObject("kmlLayers", u) }) } } }]) }(), function () { "use strict"; angular.module("ngMap").directive("mapData", ["Attr2MapOptions", "NgMap", function (e, t) { var n = e; return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (e, o, r, a) { a = a[0] || a[1]; var i = n.filter(r), s = n.getOptions(i, { scope: e }), p = n.getEvents(e, i, p); t.getMap(a.map.id).then(function (t) { for (var n in s) { var o = s[n]; "function" == typeof e[o] ? t.data[n](e[o]) : t.data[n](o) } for (var r in p) t.data.addListener(r, p[r]) }) } } }]) }(), function () { "use strict"; var e, t, n, o = [], r = [], a = function (n, a, i) { var s = i.mapLazyLoadParams || i.mapLazyLoad; if (void 0 === window.google || void 0 === window.google.maps) { r.push({ scope: n, element: a, savedHtml: o[r.length] }), window.lazyLoadCallback = function () { e(function () { r.forEach(function (e) { e.element.html(e.savedHtml), t(e.element.contents())(e.scope) }) }, 100) }; var p = document.createElement("script"); p.src = s + (s.indexOf("?") > -1 ? "&" : "?") + "callback=lazyLoadCallback", document.querySelector('script[src="' + p.src + '"]') || document.body.appendChild(p) } else a.html(o), t(a.contents())(n) }, i = function (e, t) { return !t.mapLazyLoad && void 0, o.push(e.html()), n = t.mapLazyLoad, void 0 !== window.google && void 0 !== window.google.maps ? !1 : (e.html(""), { pre: a }) }, s = function (n, o) { return t = n, e = o, { compile: i } }; s.$inject = ["$compile", "$timeout"], angular.module("ngMap").directive("mapLazyLoad", s) }(), function () { "use strict"; angular.module("ngMap").directive("mapType", ["$parse", "NgMap", function (e, t) { return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (n, o, r, a) { a = a[0] || a[1]; var i, s = r.name; if (!s) throw "invalid map-type name"; if (i = e(r.object)(n), !i) throw "invalid map-type object"; t.getMap().then(function (e) { e.mapTypes.set(s, i) }), a.addObject("mapTypes", i) } } }]) }(), function () { "use strict"; var e = function () { return { restrict: "AE", controller: "__MapController", controllerAs: "ngmap" } }; angular.module("ngMap").directive("map", [e]), angular.module("ngMap").directive("ngMap", [e]) }(), function () { "use strict"; angular.module("ngMap").directive("mapsEngineLayer", ["Attr2MapOptions", function (e) { var t = e, n = function (e, t) { var n = new google.maps.visualization.MapsEngineLayer(e); for (var o in t) google.maps.event.addListener(n, o, t[o]); return n }; return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (e, o, r, a) { a = a[0] || a[1]; var i = t.filter(r), s = t.getOptions(i, { scope: e }), p = t.getEvents(e, i, p), c = n(s, p); a.addObject("mapsEngineLayers", c) } } }]) }(), function () { "use strict"; var e, t, n, o = function (e, t) { var o; if (n.defaultOptions.marker) for (var r in n.defaultOptions.marker) "undefined" == typeof e[r] && (e[r] = n.defaultOptions.marker[r]); e.position instanceof google.maps.LatLng || (e.position = new google.maps.LatLng(0, 0)), o = new google.maps.Marker(e), Object.keys(t).length > 0; for (var a in t) a && google.maps.event.addListener(o, a, t[a]); return o }, r = function (r, a, i, s) { s = s[0] || s[1]; var p, c = e.orgAttributes(a), u = e.filter(i), l = e.getOptions(u, r, { scope: r }), g = e.getEvents(r, u); l.position instanceof google.maps.LatLng || (p = l.position); var d = o(l, g); s.addObject("markers", d), p && n.getGeoLocation(p).then(function (e) { d.setPosition(e), l.centered && d.map.setCenter(e); var n = i.geoCallback; n && t(n)(r) }), s.observeAttrSetObj(c, i, d), a.bind("$destroy", function () { s.deleteObject("markers", d) }) }, a = function (o, a, i) { return e = o, t = a, n = i, { restrict: "E", require: ["^?map", "?^ngMap"], link: r } }; a.$inject = ["Attr2MapOptions", "$parse", "NgMap"], angular.module("ngMap").directive("marker", a) }(), function () { "use strict"; angular.module("ngMap").directive("overlayMapType", ["NgMap", function (e) { return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (t, n, o, r) { r = r[0] || r[1]; var a = o.initMethod || "insertAt", i = t[o.object]; e.getMap().then(function (e) { if ("insertAt" == a) { var t = parseInt(o.index, 10); e.overlayMapTypes.insertAt(t, i) } else "push" == a && e.overlayMapTypes.push(i) }), r.addObject("overlayMapTypes", i) } } }]) }(), function () { "use strict"; var e = function (e, t) { var n = e, o = function (e, o, r, a) { if ("false" === r.placesAutoComplete) return !1; var i = n.filter(r), s = n.getOptions(i, { scope: e }), p = n.getEvents(e, i), c = new google.maps.places.Autocomplete(o[0], s); c.setOptions({ strictBounds: s.strictBounds === !0 }); for (var u in p) google.maps.event.addListener(c, u, p[u]); var l = function () { t(function () { a && a.$setViewValue(o.val()) }, 100) }; google.maps.event.addListener(c, "place_changed", l), o[0].addEventListener("change", l), r.$observe("rectBounds", function (e) { if (e) { var t = n.toOptionValue(e, { key: "rectBounds" }); c.setBounds(new google.maps.LatLngBounds(new google.maps.LatLng(t.south_west.lat, t.south_west.lng), new google.maps.LatLng(t.north_east.lat, t.north_east.lng))) } }), r.$observe("circleBounds", function (e) { if (e) { var t = n.toOptionValue(e, { key: "circleBounds" }), o = new google.maps.Circle(t); c.setBounds(o.getBounds()) } }), r.$observe("types", function (e) { if (e) { var t = n.toOptionValue(e, { key: "types" }); c.setTypes(t) } }), r.$observe("componentRestrictions", function (t) { t && c.setComponentRestrictions(e.$eval(t)) }) }; return { restrict: "A", require: "?ngModel", link: o } }; e.$inject = ["Attr2MapOptions", "$timeout"], angular.module("ngMap").directive("placesAutoComplete", e) }(), function () { "use strict"; var e = function (e, t) { var n, o = e.name; switch (delete e.name, o) { case "circle": e.center instanceof google.maps.LatLng || (e.center = new google.maps.LatLng(0, 0)), n = new google.maps.Circle(e); break; case "polygon": n = new google.maps.Polygon(e); break; case "polyline": n = new google.maps.Polyline(e); break; case "rectangle": n = new google.maps.Rectangle(e); break; case "groundOverlay": case "image": var r = e.url, a = { opacity: e.opacity, clickable: e.clickable, id: e.id }; n = new google.maps.GroundOverlay(r, e.bounds, a) } for (var i in t) t[i] && google.maps.event.addListener(n, i, t[i]); return n }, t = function (t, n, o) { var r = t, a = function (t, a, i, s) { s = s[0] || s[1]; var p, c, u = r.orgAttributes(a), l = r.filter(i), g = r.getOptions(l, { scope: t }), d = r.getEvents(t, l); c = g.name, g.center instanceof google.maps.LatLng || (p = g.center); var m = e(g, d); s.addObject("shapes", m), p && "circle" == c && o.getGeoLocation(p).then(function (e) { m.setCenter(e), m.centered && m.map.setCenter(e); var o = i.geoCallback; o && n(o)(t) }), s.observeAttrSetObj(u, i, m), a.bind("$destroy", function () { s.deleteObject("shapes", m) }) }; return { restrict: "E", require: ["?^map", "?^ngMap"], link: a } }; t.$inject = ["Attr2MapOptions", "$parse", "NgMap"], angular.module("ngMap").directive("shape", t) }(), function () { "use strict"; var e = function (e, t) { var n = e, o = function (e, t, n) { var o, r; t.container && (r = document.getElementById(t.container), r = r || document.querySelector(t.container)), r ? o = new google.maps.StreetViewPanorama(r, t) : (o = e.getStreetView(), o.setOptions(t)); for (var a in n) a && google.maps.event.addListener(o, a, n[a]); return o }, r = function (e, r, a) { var i = n.filter(a), s = n.getOptions(i, { scope: e }), p = n.getControlOptions(i), c = angular.extend(s, p), u = n.getEvents(e, i); t.getMap().then(function (e) { var t = o(e, c, u); e.setStreetView(t), !t.getPosition() && t.setPosition(e.getCenter()), google.maps.event.addListener(t, "position_changed", function () { t.getPosition() !== e.getCenter() && e.setCenter(t.getPosition()) }); var n = google.maps.event.addListener(e, "center_changed", function () { t.setPosition(e.getCenter()), google.maps.event.removeListener(n) }) }) }; return { restrict: "E", require: ["?^map", "?^ngMap"], link: r } }; e.$inject = ["Attr2MapOptions", "NgMap"], angular.module("ngMap").directive("streetViewPanorama", e) }(), function () { "use strict"; angular.module("ngMap").directive("trafficLayer", ["Attr2MapOptions", function (e) { var t = e, n = function (e, t) { var n = new google.maps.TrafficLayer(e); for (var o in t) google.maps.event.addListener(n, o, t[o]); return n }; return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (e, o, r, a) { a = a[0] || a[1]; var i = t.orgAttributes(o), s = t.filter(r), p = t.getOptions(s, { scope: e }), c = t.getEvents(e, s), u = n(p, c); a.addObject("trafficLayers", u), a.observeAttrSetObj(i, r, u), o.bind("$destroy", function () { a.deleteObject("trafficLayers", u) }) } } }]) }(), function () { "use strict"; angular.module("ngMap").directive("transitLayer", ["Attr2MapOptions", function (e) { var t = e, n = function (e, t) { var n = new google.maps.TransitLayer(e); for (var o in t) google.maps.event.addListener(n, o, t[o]); return n }; return { restrict: "E", require: ["?^map", "?^ngMap"], link: function (e, o, r, a) { a = a[0] || a[1]; var i = t.orgAttributes(o), s = t.filter(r), p = t.getOptions(s, { scope: e }), c = t.getEvents(e, s), u = n(p, c); a.addObject("transitLayers", u), a.observeAttrSetObj(i, r, u), o.bind("$destroy", function () { a.deleteObject("transitLayers", u) }) } } }]) }(), function () { "use strict"; var e = /([\:\-\_]+(.))/g, t = /^moz([A-Z])/, n = function () { return function (n) { return n.replace(e, function (e, t, n, o) { return o ? n.toUpperCase() : n }).replace(t, "Moz$1") } }; angular.module("ngMap").filter("camelCase", n) }(), function () { "use strict"; var e = function () { return function (e) { return e.replace(/[.*+?^${}()|[\]\\]/g, "\\$&") } }; angular.module("ngMap").filter("escapeRegexp", e) }(), function () { "use strict"; var e = function () { return function (e) { try { return JSON.parse(e), e } catch (t) { return e.replace(/([\$\w]+)\s*:/g, function (e, t) { return '"' + t + '":' }).replace(/'([^']+)'/g, function (e, t) { return '"' + t + '"' }).replace(/''/g, '""') } } }; angular.module("ngMap").filter("jsonize", e) }(), function () { "use strict"; var isoDateRE = /^(\d{4}\-\d\d\-\d\d([tT][\d:\.]*)?)([zZ]|([+\-])(\d\d):?(\d\d))?$/, Attr2MapOptions = function ($parse, $timeout, $log, $interpolate, NavigatorGeolocation, GeoCoder, camelCaseFilter, jsonizeFilter, escapeRegExp) { var exprStartSymbol = $interpolate.startSymbol(), exprEndSymbol = $interpolate.endSymbol(), orgAttributes = function (e) { e.length > 0 && (e = e[0]); for (var t = {}, n = 0; n < e.attributes.length; n++) { var o = e.attributes[n]; t[o.name] = o.value } return t }, getJSON = function (e) { var t = /^[\+\-]?[0-9\.]+,[ ]*\ ?[\+\-]?[0-9\.]+$/; return e.match(t) && (e = "[" + e + "]"), JSON.parse(jsonizeFilter(e)) }, getLatLng = function (e) { var t = e; if (e[0].constructor == Array) if (e[0][0].constructor == Array && 2 == e[0][0].length || e[0][0].constructor == Object) { for (var n, o = [], r = 0; r < e.length; r++) n = e[r].map(function (e) { return new google.maps.LatLng(e[0], e[1]) }), o.push(n); t = o } else t = e.map(function (e) { return new google.maps.LatLng(e[0], e[1]) }); else !isNaN(parseFloat(e[0])) && isFinite(e[0]) && (t = new google.maps.LatLng(t[0], t[1])); return t }, toOptionValue = function (input, options) { var output; try { output = getNumber(input) } catch (err) { try { var output = getJSON(input); if (output instanceof Array) output = output[0].constructor == Object ? output : output[0] instanceof Array ? output[0][0].constructor == Object ? output : getLatLng(output) : getLatLng(output); else if (output === Object(output)) { var newOptions = options; newOptions.doNotConverStringToNumber = !0, output = getOptions(output, newOptions) } } catch (err2) { if (input.match(/^[A-Z][a-zA-Z0-9]+\(.*\)$/)) try { var exp = "new google.maps." + input; output = eval(exp) } catch (e) { output = input } else if (input.match(/^([A-Z][a-zA-Z0-9]+)\.([A-Z]+)$/)) try { var matches = input.match(/^([A-Z][a-zA-Z0-9]+)\.([A-Z]+)$/); output = google.maps[matches[1]][matches[2]] } catch (e) { output = input } else if (input.match(/^[A-Z]+$/)) try { var capitalizedKey = options.key.charAt(0).toUpperCase() + options.key.slice(1); options.key.match(/temperatureUnit|windSpeedUnit|labelColor/) ? (capitalizedKey = capitalizedKey.replace(/s$/, ""), output = google.maps.weather[capitalizedKey][input]) : output = google.maps[capitalizedKey][input] } catch (e) { output = input } else if (input.match(isoDateRE)) try { output = new Date(input) } catch (e) { output = input } else if (input.match(new RegExp("^" + escapeRegExp(exprStartSymbol))) && options.scope) try { var expr = input.replace(new RegExp(escapeRegExp(exprStartSymbol)), "").replace(new RegExp(escapeRegExp(exprEndSymbol), "g"), ""); output = options.scope.$eval(expr) } catch (err) { output = input } else output = input } } if (("center" == options.key || "position" == options.key) && output instanceof Array && (output = new google.maps.LatLng(output[0], output[1])), "bounds" == options.key && output instanceof Array && (output = new google.maps.LatLngBounds(output[0], output[1])), "icons" == options.key && output instanceof Array) for (var i = 0; i < output.length; i++) { var el = output[i]; el.icon.path.match(/^[A-Z_]+$/) && (el.icon.path = google.maps.SymbolPath[el.icon.path]) } if ("icon" == options.key && output instanceof Object) { ("" + output.path).match(/^[A-Z_]+$/) && (output.path = google.maps.SymbolPath[output.path]); for (var key in output) { var arr = output[key]; "anchor" == key || "origin" == key || "labelOrigin" == key ? output[key] = new google.maps.Point(arr[0], arr[1]) : ("size" == key || "scaledSize" == key) && (output[key] = new google.maps.Size(arr[0], arr[1])) } } return output }, getAttrsToObserve = function (e) { var t = [], n = new RegExp(escapeRegExp(exprStartSymbol) + ".*" + escapeRegExp(exprEndSymbol), "g"); if (!e.noWatcher) for (var o in e) { var r = e[o]; r && r.match(n) && t.push(camelCaseFilter(o)) } return t }, filter = function (e) { var t = {}; for (var n in e) n.match(/^\$/) || n.match(/^ng[A-Z]/) || (t[n] = e[n]); return t }, getOptions = function (e, t) { t = t || {}; var n = {}; for (var o in e) if (e[o] || 0 === e[o]) { if (o.match(/^on[A-Z]/)) continue; if (o.match(/ControlOptions$/)) continue; n[o] = "string" != typeof e[o] ? e[o] : t.doNotConverStringToNumber && e[o].match(/^[0-9]+$/) ? e[o] : toOptionValue(e[o], { key: o, scope: t.scope }) } return n }, getEvents = function (e, t) { var n = {}, o = function (e) { return "_" + e.toLowerCase() }, r = function (t) { var n = t.match(/([^\(]+)\(([^\)]*)\)/), o = n[1], r = n[2].replace(/event[ ,]*/, ""), a = $parse("[" + r + "]"); return function (t) { function n(e, t) { return e[t] } var r = a(e), i = o.split(".").reduce(n, e); i && i.apply(this, [t].concat(r)), $timeout(function () { e.$apply() }) } }; for (var a in t) if (t[a]) { if (!a.match(/^on[A-Z]/)) continue; var i = a.replace(/^on/, ""); i = i.charAt(0).toLowerCase() + i.slice(1), i = i.replace(/([A-Z])/g, o); var s = t[a]; n[i] = new r(s) } return n }, getControlOptions = function (e) { var t = {}; if ("object" != typeof e) return !1; for (var n in e) if (e[n]) { if (!n.match(/(.*)ControlOptions$/)) continue; var o = e[n], r = o.replace(/'/g, '"'); r = r.replace(/([^"]+)|("[^"]+")/g, function (e, t, n) { return t ? t.replace(/([a-zA-Z0-9]+?):/g, '"$1":') : n }); try { var a = JSON.parse(r); for (var i in a) if (a[i]) { var s = a[i]; if ("string" == typeof s ? s = s.toUpperCase() : "mapTypeIds" === i && (s = s.map(function (e) { return e.match(/^[A-Z]+$/) ? google.maps.MapTypeId[e.toUpperCase()] : e })), "style" === i) { var p = n.charAt(0).toUpperCase() + n.slice(1), c = p.replace(/Options$/, "") + "Style"; a[i] = google.maps[c][s] } else a[i] = "position" === i ? google.maps.ControlPosition[s] : s } t[n] = a } catch (u) { } } return t }; return { filter: filter, getOptions: getOptions, getEvents: getEvents, getControlOptions: getControlOptions, toOptionValue: toOptionValue, getAttrsToObserve: getAttrsToObserve, orgAttributes: orgAttributes } }; Attr2MapOptions.$inject = ["$parse", "$timeout", "$log", "$interpolate", "NavigatorGeolocation", "GeoCoder", "camelCaseFilter", "jsonizeFilter", "escapeRegexpFilter"], angular.module("ngMap").service("Attr2MapOptions", Attr2MapOptions) }(), function () { "use strict"; var e, t = function (t) { var n = e.defer(), o = new google.maps.Geocoder; return o.geocode(t, function (e, t) { t == google.maps.GeocoderStatus.OK ? n.resolve(e) : n.reject(t) }), n.promise }, n = function (n) { return e = n, { geocode: t } }; n.$inject = ["$q"], angular.module("ngMap").service("GeoCoder", n) }(), function () { "use strict"; var e, t, n = function (n, o) { return e = n, t = o, { load: function (n) { var o = e.defer(); if (void 0 === window.google || void 0 === window.google.maps) { window.lazyLoadCallback = function () { t(function () { o.resolve(window.google) }, 100) }; var r = document.createElement("script"); r.src = n + (n.indexOf("?") > -1 ? "&" : "?") + "callback=lazyLoadCallback", document.querySelector('script[src="' + r.src + '"]') || document.body.appendChild(r) } else o.resolve(window.google); return o.promise } } }; n.$inject = ["$q", "$timeout"], angular.module("ngMap").service("GoogleMapsApi", n) }(), function () { "use strict"; var e, t = function (t) { var n = e.defer(); return navigator.geolocation ? (void 0 === t ? t = { timeout: 5e3 } : void 0 === t.timeout && (t.timeout = 5e3), navigator.geolocation.getCurrentPosition(function (e) { n.resolve(e) }, function (e) { n.reject(e) }, t)) : n.reject("Browser Geolocation service failed."), n.promise }, n = function (n) { return e = n, { getCurrentPosition: t } }; n.$inject = ["$q"], angular.module("ngMap").service("NavigatorGeolocation", n) }(), function () {
        "use strict";
        var e, t, n, o = [], r = function (n) { var r = t.createElement("div"); r.style.width = "100%", r.style.height = "100%", n.appendChild(r); var a = new e.google.maps.Map(r, {}); return o.push(a), a }, a = function (e, t) { for (var n, r = 0; r < o.length; r++) { var a = o[r]; if (a.id == t && !a.inUse) { var i = a.getDiv(); e.appendChild(i), n = a; break } } return n }, i = function (e) { for (var t, n = 0; n < o.length; n++) { var r = o[n]; if (!r.id && !r.inUse) { var a = r.getDiv(); e.appendChild(a), t = r; break } } return t }, s = function (e) { var t = a(e, e.id) || i(e); return t ? n(function () { google.maps.event.trigger(t, "idle") }, 100) : t = r(e), t.inUse = !0, t }, p = function (e) { e.inUse = !1 }, c = function () { for (var e = 0; e < o.length; e++) o[e] = null; o = [] }, u = function (e) { for (var t = 0; t < o.length; t++) null !== o[t] && o[t].id == e && (o[t] = null, o.splice(t, 1)) }, l = function (r, a, i) { return t = r[0], e = a, n = i, { mapInstances: o, resetMapInstances: c, getMapInstance: s, returnMapInstance: p, deleteMapInstance: u } }; l.$inject = ["$document", "$window", "$timeout"], angular.module("ngMap").factory("NgMapPool", l)
    }(), function () { "use strict"; var e, t, n, o, r, a, i, s, p = {}, c = function (n, o) { var r; return n.currentStyle ? r = n.currentStyle[o] : e.getComputedStyle && (r = t.defaultView.getComputedStyle(n, null).getPropertyValue(o)), r }, u = function (e) { var t = p[e || 0]; return t.map instanceof google.maps.Map ? void 0 : (t.initializeMap(), t.map) }, l = function (t, o) { function r(n) { var o = Object.keys(p), s = p[o[0]]; t && p[t] ? a.resolve(p[t].map) : !t && s && s.map ? a.resolve(s.map) : n > i ? a.reject("could not find map") : e.setTimeout(function () { r(n + 100) }, 100) } o = o || {}, t = "object" == typeof t ? t.id : t; var a = n.defer(), i = o.timeout || 1e4; return r(0), a.promise }, g = function (e) { if (e.map) { var t = Object.keys(p).length; p[e.map.id || t] = e } }, d = function (e) { var t = Object.keys(p).length - 1, n = e.map.id || t; if (e.map) { for (var o in e.eventListeners) { var r = e.eventListeners[o]; google.maps.event.removeListener(r) } e.map.controls && e.map.controls.forEach(function (e) { e.clear() }) } e.map.heatmapLayers && Object.keys(e.map.heatmapLayers).forEach(function (t) { e.deleteObject("heatmapLayers", e.map.heatmapLayers[t]) }), s.deleteMapInstance(n), delete p[n] }, m = function (e, t) { var r = n.defer(); return !e || e.match(/^current/i) ? o.getCurrentPosition(t).then(function (e) { var t = e.coords.latitude, n = e.coords.longitude, o = new google.maps.LatLng(t, n); r.resolve(o) }, function (e) { r.reject(e) }) : a.geocode({ address: e }).then(function (e) { r.resolve(e[0].geometry.location) }, function (e) { r.reject(e) }), r.promise }, f = function (e, t) { return function (n) { if (n) { var o = i("set-" + e), a = r.toOptionValue(n, { key: e }); t[o] && (e.match(/center|position/) && "string" == typeof a ? m(a).then(function (e) { t[o](e) }) : t[o](a)) } } }, v = function (e) { var t = e.getAttribute("default-style"); "true" == t ? (e.style.display = "block", e.style.height = "300px") : ("block" != c(e, "display") && (e.style.display = "block"), c(e, "height").match(/^(0|auto)/) && (e.style.height = "300px")) }; angular.module("ngMap").provider("NgMap", function () { var p = {}; this.setDefaultOptions = function (e) { p = e }; var c = function (c, y, h, b, M, O, w, L) { return e = c, t = y[0], n = h, o = b, r = M, a = O, i = w, s = L, { defaultOptions: p, addMap: g, deleteMap: d, getMap: l, initMap: u, setStyle: v, getGeoLocation: m, observeAndSet: f } }; c.$inject = ["$window", "$document", "$q", "NavigatorGeolocation", "Attr2MapOptions", "GeoCoder", "camelCaseFilter", "NgMapPool"], this.$get = c }) }(), function () { "use strict"; var e, t = function (t, n) { n = n || t.getCenter(); var o = e.defer(), r = new google.maps.StreetViewService; return r.getPanoramaByLocation(n || t.getCenter, 100, function (e, t) { t === google.maps.StreetViewStatus.OK ? o.resolve(e.location.pano) : o.resolve(!1) }), o.promise }, n = function (e, t) { var n = new google.maps.StreetViewPanorama(e.getDiv(), { enableCloseButton: !0 }); n.setPano(t) }, o = function (o) { return e = o, { getPanorama: t, setPanorama: n } }; o.$inject = ["$q"], angular.module("ngMap").service("StreetView", o) }(), "ngMap"
});