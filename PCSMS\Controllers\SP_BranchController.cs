﻿using System.Web.Http;
using PCSMS.Filters;
using PCSMS.Models.Models_SProvider;
using PCSMS.Services.Services_SProvider;

namespace PCSMS.Controllers
{
    [AuthorizationRequired]
    [RoutePrefix("Api/SP_Branch")]
    public class SP_BranchController : ApiController
    {
        private readonly ISP_BranchServices _services;
        public SP_BranchController()
        {
            _services = new SP_BranchServices();
        }

        [Route("SaveSP_Branch")]
        [HttpPost]
        public IHttpActionResult SaveSP_Branch(SP_Branch SP_Branch)
        {
            return Ok(_services.SaveSP_Branch(SP_Branch).Data);
        }

        [Route("UpdateSP_Branch")]
        [HttpPost]
        public IHttpActionResult UpdateSP_Branch(SP_Branch SP_Branch)
        {
            return Ok(_services.UpdateSP_Branch(SP_Branch).Data);
        }

        [Route("DeleteSP_Branch/{branchId:int}")]
        [HttpPost]
        public IHttpActionResult DeleteSP_Branch(long branchId)
        {
            return Ok(_services.DeleteSP_Branch(branchId).Data);
        }

        [Route("GetSP_BranchList")]
        [HttpGet]
        public IHttpActionResult GetSP_BranchList()
        {

            return Ok(_services.GetSP_BranchList().Data);
        }

        [Route("GetSP_BranchDetails/{id:int}")]
        [HttpGet]
        public IHttpActionResult GetSP_BranchDetails(int id)
        {
            return Ok(_services.GetSP_BranchDetails(x => x.Id == id).Data);
        }


    }
}
