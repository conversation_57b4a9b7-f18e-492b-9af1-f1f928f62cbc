﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web.Mvc;
using PCSMS.Common;
using PCSMS.Models;
using PCSMS.Models.Models_SProvider;

namespace PCSMS.Services.Services_SProvider
{
    public class SP_BranchServices: ISP_BranchServices
    {
        private readonly PCSMSDbContext _context;
        private readonly IEntityService<SP_Branch> _services;
        public SP_BranchServices()
        {
            _context = new PCSMSDbContext();
            _services = new EntityService<SP_Branch>(_context);
        }

        //Private Services :
        private bool BranchExist(SP_Branch obj)
        {
            var result =_context.SP_Branch.FirstOrDefault(x => x.BranchName == obj.BranchName && x.Id != obj.Id);

            if (result == null)
            {
                return false;
            }
            return true;
        }

        
        //Public Services :
        public JsonResult SaveSP_Branch(SP_Branch obj)
        {
            //Profile Id is gotten from backend and assigned to obj.SP_ProfileId below:
            var profileId = _context.SP_Profile.Select(x => x.Id).FirstOrDefault();
            obj.SP_ProfileId = profileId;

            string message;
            try
            {
                if (profileId<1)
                {
                    Generator.IsReport = "ProfileNotCreated";
                    message = "There is no profile created for you your company. Create a profile first !";
                }
                else if (_services.DoesExist(x => x.BranchName == obj.BranchName))
                {
                    Generator.IsReport = "BranchExists";
                    message = "This Branch already exists !";
                }
                else
                {
                   _services.Save(obj);
                   _services.SaveChanges();

                    Generator.IsReport = "Ok";
                    message = "Branch created successfully !";
                }
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult UpdateSP_Branch(SP_Branch obj)
        {
            
            List<object> avoidProperties = new List<object>();
            avoidProperties.Add(obj.SP_ProfileId);
            string message;

            if (BranchExist(obj))
            {
                Generator.IsReport = "BranchExists";
                message = "Branch : " + "\"" + obj.BranchName + "\"" + " already exists !";
                
            }
            else
            {
                try
                {
                    _services.Update(obj, avoidProperties);
                    _services.SaveChanges();
                    Generator.IsReport = "Ok";
                    message = "Branch updated successfully !";
                }
                catch (Exception ex)
                {
                    message = ex.Message;
                    Generator.IsReport = "NotOk";
                }
            }
            
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
            
        }

        public JsonResult DeleteSP_Branch(long branchId)
        {
            string message;
            try
            {
                _context.Database.ExecuteSqlCommand($"Delete [SP_Branch] WHERE Id = {branchId}");
                _context.SaveChanges();
                Generator.IsReport = "Ok";
                message = "Branch deleted successfully !";
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult GetSP_BranchList()
        {
            return new JsonResult
            {
                Data = _context.SP_Branch
                .Join(_context.SP_Profile,
                x=> x.SP_ProfileId,
                y=> y.Id,
                (x,y)=> new {Branch = x, Profile = y})
                .Select(x => new
                {
                    x.Branch.Id,
                    x.Branch.BranchName,
                    x.Branch.SP_ProfileId,
                    x.Profile.OrganizationName,
                }).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult GetSP_BranchDetails(Expression<Func<SP_Branch, bool>> expression)
        {
            return new JsonResult
            {
                Data = _context.SP_Branch.Where(expression)
                .Join(_context.SP_Profile,
                x => x.SP_ProfileId,
                y => y.Id,
                (x, y) => new { Branch = x, Profile = y })
                .Select(x => new
                {
                    x.Branch.Id,
                    x.Branch.BranchName,
                    x.Branch.SP_ProfileId,
                    x.Profile.OrganizationName,
                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
    }

    public interface ISP_BranchServices
    {
        JsonResult DeleteSP_Branch(long branchId);
        JsonResult GetSP_BranchDetails(Expression<Func<SP_Branch, bool>> expression);
        JsonResult SaveSP_Branch(SP_Branch obj);
        JsonResult UpdateSP_Branch(SP_Branch obj);
        JsonResult GetSP_BranchList();
    }
}
