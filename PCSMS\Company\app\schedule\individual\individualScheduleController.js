﻿/// <reference path="app.js" />
PCSMSApp.controller('individualScheduleController', function ($scope, deviceServices, individualScheduleServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    $scope.ScheduleMenuOpen = true;
    $scope.NonGroupDeviceList = [];

    $scope.TimingList = [
        {"Timing": 1, "Id":1},
        {"Timing": 5, "Id":5},
        {"Timing": 10, "Id":10},
        {"Timing": 15, "Id":15},
        {"Timing": 20, "Id":20},
        {"Timing": 30, "Id":30},
        {"Timing": 60, "Id":60},
        {"Timing": "Random", "Id":"Random"},
    ]

    $scope.hstep = 1;
    $scope.mstep = 1;
    $scope.Schedule = {};
    $scope.ismeridian = true;
    $scope.Schedule.Sat = "N";
    $scope.Schedule.Sun = "N";
    $scope.Schedule.Mon = "N";
    $scope.Schedule.Tues = "N";
    $scope.Schedule.Wed = "N";
    $scope.Schedule.Thurs = "N";
    $scope.Schedule.Fri = "N";
    $scope.Loading = false;//For Capturing and identyfying loader;

    $scope.IsAtLeastOneDaySelected = function() {
        if ($scope.Schedule.Sat == "N" &&
            $scope.Schedule.Sun == "N" &&
            $scope.Schedule.Mon == "N" &&
            $scope.Schedule.Tues == "N" &&
            $scope.Schedule.Wed == "N" &&
            $scope.Schedule.Thurs == "N" &&
            $scope.Schedule.Fri == "N") {
            return false;
        }
        return true;
    }
    individualScheduleServices.GetNonGroupedDeviceListByCompanyId($rootScope.CompanyId).then(function (response) {
        $scope.NonGroupDeviceList = response.data;
        //console.log(response.data);
    });


    $scope.GetDeviceDetailsForScheduleUpdate = function (deviceId) {
        individualScheduleServices.GetDeviceDetails(deviceId).then(function(response) {
        $scope.Schedule = response.data;
        console.log($scope.Schedule);
        $scope.Schedule.StartTime = moment($scope.Schedule.StartTime, "HH:mm:ss")._d;
        $scope.Schedule.EndTime = moment($scope.Schedule.EndTime, "HH:mm:ss")._d;
            if ($scope.Schedule.IsRandom == "Y") {
                $scope.Schedule.Interval = "Random";

            }

        }).then(function() {
            $('#DeviceDetailsModal').modal('show');
        });
    }

    $scope.UpdateIndividualSchedule = function () {
        if ($scope.Schedule.Interval == "Random") {
            $scope.Schedule.Interval = null;
            $scope.Schedule.IsRandom = "Y";

        } else {
            $scope.Schedule.IsRandom = "N";
        }


            $scope.Schedule.StartTime = moment($scope.Schedule.StartTime).format("HH:mm:ss");
            $scope.Schedule.EndTime = moment($scope.Schedule.EndTime).format("HH:mm:ss");

         individualScheduleServices.UpdateSingularDeviceScheduling($scope).then(function (response) {
            if (response.data.IsReport == "Ok") {
                toastr.success(response.data.Message, "Success!");
            }
                else if (response.data.IsReport == "NotOk") {
                toastr.error(response.data.Message, "Error!");
            }
            $('#DeviceDetailsModal').modal("hide");
            })
            .then(function () {
                 $timeout(function() {

                 
                     $state.reload();
                 }, 300);
             });
     }

    $scope.cancelDeviceDetailsModal = function () {
        $('#DeviceDetailsModal').modal("hide");
        $timeout(function () {
            $scope.DeviceDetails = {};
        }, 200);
    }
});