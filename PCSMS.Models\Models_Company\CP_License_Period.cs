﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PCSMS.Models.Common;
using System.ComponentModel.DataAnnotations.Schema;

namespace PCSMS.Models.Models_Company
{
    public class CP_License_Period : Entity<int>
    {
        [ForeignKey("LicenseId")]
        public virtual CP_License CP_License { get; set; }
        public int? LicenseId { get; set; }

        
        public DateTime? ExpiryDate { get; set; }

        [MaxLength(10)]
        public string Status { get; set; }
    }
}
