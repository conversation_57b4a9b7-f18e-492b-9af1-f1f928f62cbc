﻿PCSMSApp.factory("BranchServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        SaveSP_Branch: function (param) {
            return $http({
                url: "/Api/SP_Branch/SaveSP_Branch",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        UpdateSP_Branch: function (param) {
            return $http({
                url: "/Api/SP_Branch/UpdateSP_Branch",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        GetSP_BranchList: function () {
            return $http({
                url: "/Api/SP_Branch/GetSP_BranchList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetSP_BranchDetails: function (id) {
            return $http({
                url: "/Api/SP_Branch/GetSP_BranchDetails/" + id,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DeleteSP_Branch: function (id) {
            return $http({
                url: "/Api/SP_Branch/DeleteSP_Branch/" + id,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        }
    };
}]);