﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!-- main content -->

<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-universal-access" aria-hidden="true"></i><a ui-sref="accesstype">Access Type</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-header">
                        <!--<button type="button" class="btn btn-default btn-o pull-right btn-addon btn-o-addon" id="btnAddAccessType" data-toggle="modal" data-target="#AccessTypeModal">
                            <i class="fa fa-plus" aria-hidden="true"></i>
                            Add Access Type
                        </button>-->
                    </div>
                    <div class="panel-content">
                        <div class="table-responsive">
                            <!-- DataTable -->
                            <table class="data-table table table-striped nowrap table-bordered table-condensed" id="basic-table" datatable="ng" dt-options="vm.dtOptions" dt-instance="vm.dtInstance" dt-column-defs="vm.dtColumnDefs" cellspacing="0">
                                <thead>
                                    <tr class="animated fadeIn">
                                        <th>Access Type</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="animated fadeIn" ng-repeat="x in AccessTypeList">
                                        <td>{{x.AccessTypeName}}</td>
                                        <td>
                                            <button class="btn btn-default btn-Line-Height btn-datatables" tooltip-placement="top" uib-tooltip="Edit" ng-click="openAccessTypeModal(x.Id)"  style="border:1px solid #02715a; background-color:#ffffff"><img src="Assets_SProvider/images/datatables/info.png" width="20" /></button>
                                            <!--<button class="btn btn-default btn-Line-Height btn-datatables" tooltip-placement="top" uib-tooltip="Edit" ng-click="openAccessTypeModal(x.Id)"  style="border:1px solid #02715a; background-color:#ffffff"><img src="Assets_SProvider/images/datatables/edit.png" width="20" /></button>-->
                                            <!--<button class="btn btn-default btn-Line-Height btn-datatables" ng-click="deleteAccessTypeAlert(x.Id, x.AccessTypeName)" style="border-color: #abaaaa"><img src="Assets_SProvider/img/datatables/delete.png" width="20" /></button>-->
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>        
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>

<form novalidate name="AccessTypeForm">
    <div class="modal fade" id="AccessTypeModal" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog  modal-dialog-at">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="custom-close" data-dismiss="modal" id="AccessType-btn-top-close" ng-click="cancelAccessTypeListModal()" tooltip-placement="left" uib-tooltip="Close">&times;</button>
                    <h4 ng-if="AccessType.Id === undefined">Create New Access Type</h4>
                    <h4 ng-if="AccessType.Id >=1">Update Access Type</h4>
                    <div class="form-sep">
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-10 col-md-offset-2">
                            <div class="col-md-3">
                                <label class="control-label label-for-textbox-center-direction">Access Type</label>
                            </div>
                            <div class="col-md-9">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Enter Access Type" name="AccessTypeName"
                                           ng-required="true"
                                           ng-model="AccessType.AccessTypeName"
                                           ng-change="LiveValidation(AccessType.AccessTypeName)"
                                           ng-minlength="4"
                                           ng-maxlength="30"
                                           ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" />
                                    <div class="row custom-row">
                                        <ul class="list-unstyled text-danger pull-left" ng-show="LiveValidation(AccessType.AccessTypeName)==false || AccessTypeForm.AccessTypeName.$dirty && AccessTypeForm.AccessTypeName.$invalid">
                                            <li><span class="pull-left" ng-show="LiveValidation(Designation.AccessTypeName)==false">{{LiveValidationError}}</span></li>
                                            <li><span class="pull-left" ng-show="AccessTypeForm.AccessTypeName.$error.required">*Required</span></li>
                                            <li><span class="pull-left" ng-show="AccessTypeForm.AccessTypeName.$error.pattern">Start with capital letter, don't use special character.</span></li>
                                            <li><span class="pull-left" ng-show="!AccessTypeForm.AccessTypeName.$error.pattern && AccessTypeForm.AccessTypeName.$error.minlength">Minimum length is 4</span></li>
                                            <li><span class="pull-left" ng-show="!AccessTypeForm.AccessTypeName.$error.pattern && AccessTypeForm.AccessTypeName.$error.maxlength">Maximum length is 30</span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="col-md-5 col-md-offset-7">
                        <div class="btn-group btn-group-justified" role="group" aria-label="group button" ng-if="AccessType.Id === undefined">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-default pull-right" ng-click="cancelAccessTypeListModal()" role="button">Close</button>
                            </div>
                            <div class="btn-group" role="group">
                                <button ng-disabled="AccessTypeForm.$invalid || LiveValidation(AccessType.AccessTypeName)==false"
                                        type="button"
                                        ng-click="SaveOrUpdateAccessType()" class="btn btn-sm btn-primary">
                                    Save &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                </button>
                            </div>
                        </div>
                        <!--<div class="btn-group btn-group-justified" role="group" aria-label="group button" ng-if="AccessType.Id >=1">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-default pull-right" ng-click="cancelAccessTypeListModal()" role="button">Close</button>
                            </div>
                            <div class="btn-group" role="group">
                                <button ng-disabled="AccessTypeForm.$invalid || LiveValidation(AccessType.AccessTypeName)==false"
                                        type="button"
                                        ng-click="SaveOrUpdateAccessType()" class="btn btn-sm btn-primary">
                                    Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                </button>
                            </div>
                        </div>-->
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

