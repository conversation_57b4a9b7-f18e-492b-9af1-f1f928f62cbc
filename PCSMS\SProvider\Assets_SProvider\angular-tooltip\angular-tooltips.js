﻿!function (t, o) { "use strict"; var e, i, r, l, n, p = "tooltips", a = (i = [], r = 0, l = function (t) { t - r >= 15 ? (i.forEach(function (t) { t() }), r = t) : o.console.log("Skipped!") }, n = function () { o.clearTimeout(e), e = o.setTimeout(function () { o.requestAnimationFrame(l) }, 500) }, { add: function (t) { var e; i.length || o.addEventListener("resize", n), (e = t) && i.push(e) }, remove: function () { i.length || (o.clearTimeout(e), o.removeEventListener("resize", n)) } }), s = function (t) { return o.getComputedStyle ? o.getComputedStyle(t, "") : t.currentStyle ? t.currentStyle : void 0 }, d = function (e) { for (var i, r, l = o.document.querySelectorAll("._exradicated-tooltip"), n = 0, p = l.length; n < p; n += 1) if ((i = l.item(n)) && (r = t.element(i)).data("_tooltip-parent") && r.data("_tooltip-parent") === e) return r }, c = function (t) { var o = d(t); o && o.remove() }, u = function (t) { if (t) { var e = t[0].getBoundingClientRect(); return (e.top < 0 || e.top > o.document.body.offsetHeight || e.left < 0 || e.left > o.document.body.offsetWidth || e.bottom < 0 || e.bottom > o.document.body.offsetHeight || e.right < 0 || e.right > o.document.body.offsetWidth) && (t.css({ top: "", left: "", bottom: "", right: "" }), !0) } throw new Error("You must provide a position") }, m = function (t) { return t.split(" ").map(function (t) { return "_" + t }).join(" ") }, f = ["_top", "_top _left", "_left", "_bottom _left", "_bottom", "_bottom _right", "_right", "_top _right"], g = ["$log", "$http", "$compile", "$timeout", "$controller", "$injector", "tooltipsConf", "$templateCache", "$q", function (e, i, r, l, n, g, h, v, C) { return { restrict: "A", transclude: "element", priority: 1, terminal: !0, link: function (g, b, w, y, _) { if (w.tooltipTemplate && w.tooltipTemplateUrl) throw new Error("You can not define tooltip-template and tooltip-template-url together"); if (!w.tooltipTemplateUrl && !w.tooltipTemplate && w.tooltipController) throw new Error("You can not have a controller without a template or templateUrl defined"); var T, S = m(h.side), $ = h.showTrigger, A = h.hideTrigger, B = h.size, z = "_" + h.speed; w.tooltipSide = w.tooltipSide || h.side, w.tooltipShowTrigger = w.tooltipShowTrigger || h.showTrigger, w.tooltipHideTrigger = w.tooltipHideTrigger || h.hideTrigger, w.tooltipShow = w.tooltipShow || h.show, w.tooltipClass = w.tooltipClass || h.class, w.tooltipSmart = "true" === w.tooltipSmart || h.smart, w.tooltipCloseButton = w.tooltipCloseButton || h.closeButton.toString(), w.tooltipSize = w.tooltipSize || h.size, w.tooltipSpeed = w.tooltipSpeed || h.speed, w.tooltipAppendToBody = "true" === w.tooltipAppendToBody, _(g, function (g, h) { var y, _, E = (_ = {}, (y = g).removeAttr(p), void 0 !== y.attr("tooltip-template") && (_["tooltip-template"] = y.attr("tooltip-template"), y.removeAttr("tooltip-template")), void 0 !== y.attr("tooltip-template-url") && (_["tooltip-template-url"] = y.attr("tooltip-template-url"), y.removeAttr("tooltip-template-url")), void 0 !== y.attr("tooltip-template-url-cache") && (_["tooltip-template-url-cache"] = y.attr("tooltip-template-url-cache"), y.removeAttr("tooltip-template-url-cache")), void 0 !== y.attr("tooltip-controller") && (_["tooltip-controller"] = y.attr("tooltip-controller"), y.removeAttr("tooltip-controller")), void 0 !== y.attr("tooltip-side") && (_["tooltip-side"] = y.attr("tooltip-side"), y.removeAttr("tooltip-side")), void 0 !== y.attr("tooltip-show-trigger") && (_["tooltip-show-trigger"] = y.attr("tooltip-show-trigger"), y.removeAttr("tooltip-show-trigger")), void 0 !== y.attr("tooltip-hide-trigger") && (_["tooltip-hide-trigger"] = y.attr("tooltip-hide-trigger"), y.removeAttr("tooltip-hide-trigger")), void 0 !== y.attr("tooltip-smart") && (_["tooltip-smart"] = y.attr("tooltip-smart"), y.removeAttr("tooltip-smart")), void 0 !== y.attr("tooltip-class") && (_["tooltip-class"] = y.attr("tooltip-class"), y.removeAttr("tooltip-class")), void 0 !== y.attr("tooltip-show") && (_["tooltip-show"] = y.attr("tooltip-show"), y.removeAttr("tooltip-show")), void 0 !== y.attr("tooltip-close-button") && (_["tooltip-close-button"] = y.attr("tooltip-close-button"), y.removeAttr("tooltip-close-button")), void 0 !== y.attr("tooltip-size") && (_["tooltip-size"] = y.attr("tooltip-size"), y.removeAttr("tooltip-size")), void 0 !== y.attr("tooltip-speed") && (_["tooltip-speed"] = y.attr("tooltip-speed"), y.removeAttr("tooltip-speed")), _), P = t.element(o.document.createElement("tooltip")), x = t.element(o.document.createElement("tip-cont")), H = t.element(o.document.createElement("tip")), V = t.element(o.document.createElement("tip-tip")), U = t.element(o.document.createElement("span")), k = t.element(o.document.createElement("tip-arrow")), I = function (t) { void 0 !== t && x[0].getClientRects().length > 1 ? P.addClass("_multiline") : P.removeClass("_multiline") }, O = function (e) { if (e && !P.hasClass("active") && e.stopImmediatePropagation(), H.addClass("_hidden"), w.tooltipSmart) switch (w.tooltipSide) { case "top": case "left": case "bottom": case "right": case "top left": case "top right": case "bottom left": case "bottom right": !function (t, o, e) { for (var i = f.indexOf(m(e)), r = f.length, l = 0; l < r && u(t) ; l += 1) (i += 1) >= f.length && (i = 0), o.removeClass("_top _left _bottom _right"), o.addClass(f[i]) }(H, P, w.tooltipSide); break; default: throw new Error("Position not supported") } if (w.tooltipAppendToBody) { var i, r, l, n, p, a = s(V[0]), d = s(k[0]), g = s(H[0]), h = H[0].getBoundingClientRect(), v = t.copy(H), C = 0, b = a.length, y = 0, _ = d.length, T = 0, S = g.length, $ = {}, A = {}, B = {}; for (H.removeClass("_hidden"), v.removeClass("_hidden"), v.data("_tooltip-parent", P), c(P) ; C < b; C += 1) (i = a[C]) && a.getPropertyValue(i) && ($[i] = a.getPropertyValue(i)); for (; y < _; y += 1) (i = d[y]) && d.getPropertyValue(i) && (B[i] = d.getPropertyValue(i)); for (; T < S; T += 1) (i = g[T]) && "position" !== i && "display" !== i && "opacity" !== i && "z-index" !== i && "bottom" !== i && "height" !== i && "left" !== i && "right" !== i && "top" !== i && "width" !== i && g.getPropertyValue(i) && (A[i] = g.getPropertyValue(i)); r = o.parseInt(g.getPropertyValue("padding-top"), 10), l = o.parseInt(g.getPropertyValue("padding-bottom"), 10), n = o.parseInt(g.getPropertyValue("padding-left"), 10), p = o.parseInt(g.getPropertyValue("padding-right"), 10), A.top = h.top + o.pageYOffset + "px", A.left = h.left + o.pageXOffset + "px", A.height = h.height - (r + l) + "px", A.width = h.width - (n + p) + "px", v.css(A), v.children().css($), v.children().next().css(B), e && "true" !== w.tooltipHidden && (v.addClass("_exradicated-tooltip"), t.element(o.document.body).append(v)) } else H.removeClass("_hidden"), e && "true" !== w.tooltipHidden && P.addClass("active") }, R = function (t) { t && P.hasClass("active") && t.stopImmediatePropagation(), w.tooltipAppendToBody ? c(P) : P.removeClass("active") }, W = function t(o) { var e, i = o.parent(); o[0] && (o[0].scrollHeight > o[0].clientHeight || o[0].scrollWidth > o[0].clientWidth) && o.on("scroll", function () { var t = this; e && l.cancel(e), e = l(function () { var o = d(P), e = P[0].getBoundingClientRect(), i = t.getBoundingClientRect(); e.top < i.top || e.bottom > i.bottom || e.left < i.left || e.right > i.right ? c(P) : o && O(!0) }) }), i && i.length && t(i) }, Y = function (t) { P.removeClass("_force-hidden"), V.empty(), V.append(U), V.append(t), l(function () { O() }) }, j = function () { V.empty(), P.addClass("_force-hidden") }, q = function (t) { var o = v.get(t); return void 0 !== o ? C.resolve(o) : i.get(t).then(function (o) { return v.put(t, o.data), o.data }) }, L = w.$observe("tooltipTemplate", function (t) { t ? Y(t) : j() }), F = w.$observe("tooltipTemplateUrl", function (t) { t && !w.tooltipTemplateUrlCache ? q(t).then(function (t) { Y(r(t)(h)) }).catch(function (t) { e.error(t) }) : j() }), X = w.$observe("tooltipTemplateUrlCache", function (t) { t && w.tooltipTemplateUrl ? q(w.tooltipTemplateUrl).then(function (t) { Y(r(t)(h)) }).catch(function (t) { e.error(t) }) : j() }), D = w.$observe("tooltipSide", function (t) { t && (S && P.removeClass(S), P.addClass(m(t)), S = t) }), G = w.$observe("tooltipShowTrigger", function (t) { t && ($ && P.off($), P.on(t, O), $ = t) }), J = w.$observe("tooltipHideTrigger", function (t) { t && (A && P.off(A), P.on(t, R), A = t) }), K = w.$observe("tooltipShow", function (t) { "true" === t ? P.addClass("active") : P.removeClass("active") }), M = w.$observe("tooltipClass", function (t) { t && (T && H.removeClass(T), H.addClass(t), T = t) }), N = w.$observe("tooltipSmart", function () { "boolean" != typeof w.tooltipSmart && (w.tooltipSmart = "true" === w.tooltipSmart) }), Q = w.$observe("tooltipCloseButton", function (t) { "true" === t ? (U.on("click", R), U.css("display", "block")) : (U.off("click"), U.css("display", "none")) }), Z = w.$observe("tooltipController", function (o) { if (o) { var e = n(o, { $scope: h }), i = h.$new(!1, h), l = o.indexOf("as"); l >= 0 ? i[o.substr(l + 3)] = e : t.extend(i, e), V.replaceWith(r(V)(i)), Z() } }), tt = w.$observe("tooltipSize", function (t) { t && (B && V.removeClass("_" + B), V.addClass("_" + t), B = t) }), ot = w.$observe("tooltipSpeed", function (t) { t && (z && P.removeClass("_" + z), P.addClass("_" + t), z = t) }), et = h.$watch(function () { return x.html() }, I); U.addClass("close-button"), U.html("&times;"), H.addClass("_hidden"), V.append(U), V.append(w.tooltipTemplate), H.append(V), H.append(k), x.append(g), P.attr(E), P.addClass("tooltips"), P.append(x), P.append(H), b.after(P), w.tooltipAppendToBody && (a.add(function () { W(P) }), W(P)), a.add(function () { I(), O() }), l(function () { O(), H.removeClass("_hidden"), P.addClass("_ready") }), h.$on("$destroy", function () { L(), F(), X(), D(), G(), J(), K(), M(), N(), Q(), tt(), ot(), et(), a.remove(), g.off(w.tooltipShowTrigger + " " + w.tooltipHideTrigger) }) }) } } }]; t.module("720kb.tooltips", []).provider(p + "Conf", function () { var t = { side: "top", showTrigger: "mouseenter", hideTrigger: "mouseleave", class: "", smart: !1, closeButton: !1, size: "", speed: "steady", tooltipTemplateUrlCache: !1, show: null }; return { configure: function (o) { var e, i = Object.keys(t), r = 0; if (o) for (; r < i.length; r += 1) (e = i[r]) && o[e] && (t[e] = o[e]) }, $get: function () { return t } } }).directive(p, g) }(angular, window);