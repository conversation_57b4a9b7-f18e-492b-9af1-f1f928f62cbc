﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using PCSMS.Models.Common;
using PCSMS.Models.Models_Company;
using PCSMS.Models.Models_SProvider;

namespace PCSMS.Models.Models_Shared
{
    public class CompanyType : Entity<int>
    {
        [MaxLength(70)]
        public string CompanyTypeName { get; set; }

        public virtual ICollection<SP_Profile> SP_Profiles { get; set; }
        public virtual ICollection<CP_Profile> CP_Profiles { get; set; }
    }
}
