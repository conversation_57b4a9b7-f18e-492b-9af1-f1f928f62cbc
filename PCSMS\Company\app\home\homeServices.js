﻿PCSMSApp.factory("homeServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetLoggedInUserList: function (companyId) {
            return $http({
                url: "/api/CP_User/GetLoggedInUserList/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCountOfActiveAndBlockedUsersByCompanyId: function (companyId) {
            return $http({
                url: "/api/CP_User/GetCountOfActiveAndBlockedUsersByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCountOfRegisteredDevicesByCompanyId: function (companyId) {
            return $http({
                url: "/api/CP_User/GetCountOfRegisteredDevicesByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCountOfLicensesByCompanyId: function (companyId) {
            return $http({
                url: "/api/CP_User/GetCountOfLicensesByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },

        
    };



}]);