﻿using System;
using System.Collections.Generic;
using System.Web.Http;
using PCSMS.Filters;
using PCSMS.Models.Models_Company;
using PCSMS.Services.Services_Shared;


namespace PCSMS.Controllers
{
    [AuthorizationRequired]
    [RoutePrefix("api/Subscription")]
    public class SubscriptionController : ApiController
    {
        private readonly ISubscriptionServices _services;
        public SubscriptionController()
        {
            _services = new SubscriptionServices();
        }


        
        //For Company

        [Route("RequestLicense/{companyId:int}")]
        [HttpPost]
        public IHttpActionResult RequestLicense(int companyId)
        {
            return Ok(_services.RequestLicense(companyId).Data);
        }

        [Route("GetLicenseListByCompanyId/{companyId:int}")]
        [HttpGet]
        public IHttpActionResult GetLicenseListByCompanyId(int companyId)
        {
            return Ok(_services.GetLicenseListByCompanyId(companyId).Data);
        }

        [Route("RequestForLicenseRenewal/{licenseId:int}")]
        [HttpPost]
        public IHttpActionResult RequestForLicenseRenewal(int licenseId)
        {
            return Ok(_services.RequestForLicenseRenewal(licenseId).Data);
        }

        [Route("GetLicenseDetailsByLicenseId/{licenseId:int}")]
        [HttpGet]
        public IHttpActionResult GetLicenseDetailsByLicenseId(int licenseId)
        {
            return Ok(_services.GetLicenseDetailsByLicenseId(licenseId).Data);
        }


        [Route("GetCountOfActiveLicenses")]
        [HttpGet]
        public IHttpActionResult GetCountOfActiveLicenses()
        {
            return Ok(_services.GetCountOfActiveLicenses().Data);
        }


        [Route("GetCountOfExpiredLicenses")]
        [HttpGet]
        public IHttpActionResult GetCountOfExpiredLicenses()
        {
            return Ok(_services.GetCountOfExpiredLicenses().Data);
        }

        //For SProvider:

        [Route("GetAllLicenseList")]
        [HttpGet]
        public IHttpActionResult GetAllLicenseList()
        {
            return Ok(_services.GetAllLicenseList().Data);
        }

        [Route("GetRegisteredCompanyList")]
        [HttpGet]
        public IHttpActionResult GetRegisteredCompanyList()
        {

            return Ok(_services.GetRegisteredCompanyList().Data);
        }


        [Route("GetSubscribedCompanyList")]
        [HttpGet]
        public IHttpActionResult GetSubscribedCompanyList()
        {

            return Ok(_services.GetSubscribedCompanyList().Data);
        }

        [Route("GetCountOfRegisteredCompanies")]
        [HttpGet]
        public IHttpActionResult GetCountOfRegisteredCompanies()
        {

            return Ok(_services.GetCountOfRegisteredCompanies().Data);
        }


        [Route("GetCountOfSubscribedCompanies")]
        [HttpGet]
        public IHttpActionResult GetCountOfSubscribedCompanies()
        {

            return Ok(_services.GetCountOfSubscribedCompanies().Data);
        }




        [Route("ApproveLicense/{licenseId:int}/{deviceLimit:int}/{expiryDate:datetime}")]
        [HttpPost]
        public IHttpActionResult ApproveLicense(int licenseId, int deviceLimit, DateTime expiryDate)
        {
            return Ok(_services.ApproveLicense(licenseId, deviceLimit, expiryDate).Data);
        }

        [Route("ApproveLicenseRenewal/{expiryDate:datetime}/{licenseId:int}")]
        [HttpPost]
        public IHttpActionResult ApproveLicenseRenewal(DateTime expiryDate, int licenseId)
        {
            return Ok(_services.ApproveLicenseRenewal(expiryDate, licenseId).Data);
        }

        //TaskScheduler Services:

        [Route("TerminateSubscriptionByTaskScheduler")]
        [HttpGet]
        public IHttpActionResult TerminateSubscriptionByTaskScheduler()
        {
            return Ok(_services.TerminateSubscriptionByTaskScheduler().Data);
        }
    }
}
