﻿/*
 * angular-tooltips
 * 1.2.2
 * 
 * Angular.js tooltips module.
 * http://720kb.github.io/angular-tooltips
 * 
 * MIT license
 * Tue Jun 20 2017
 */
@-webkit-keyframes animate-tooltip {
  0% {
    opacity: 0; }
  50% {
    opacity: .5; }
  60% {
    opacity: .8; }
  70% {
    opacity: .9; }
  90% {
    opacity: 1; } }

@-moz-keyframes animate-tooltip {
  0% {
    opacity: 0; }
  50% {
    opacity: .5; }
  60% {
    opacity: .8; }
  70% {
    opacity: .9; }
  90% {
    opacity: 1; } }

@-ms-keyframes animate-tooltip {
  0% {
    opacity: 0; }
  50% {
    opacity: .5; }
  60% {
    opacity: .8; }
  70% {
    opacity: .9; }
  90% {
    opacity: 1; } }

@keyframes animate-tooltip {
  0% {
    opacity: 0; }
  50% {
    opacity: .5; }
  60% {
    opacity: .8; }
  70% {
    opacity: .9; }
  90% {
    opacity: 1; } }

._exradicated-tooltip {
  display: block;
  opacity: 1;
  position: absolute;
  z-index: 999; }

tooltip {
  display: inline-block;
  position: relative; }
  tooltip._multiline {
    display: block; }
  tooltip._slow._ready tip {
    animation: animate-tooltip 0.65s; }
  tooltip._fast._ready tip {
    animation: animate-tooltip 0.15s; }
  tooltip._steady._ready tip {
    animation: animate-tooltip 0.35s; }
  tooltip tip {
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.85);
    color: #fff;
    display: none;
    line-height: normal;
    max-width: 500px;
    min-width: 100px;
    opacity: 0;
    padding: 8px 16px;
    position: absolute;
    text-align: center;
    width: auto;
    will-change: top, left, bottom, right; }
    tooltip tip._hidden {
      display: block;
      visibility: hidden; }
  tooltip.active:not(._force-hidden) tip {
    display: block;
    opacity: 1;
    z-index: 999; }
  tooltip tip-tip {
    font-size: 0.95em; }
    tooltip tip-tip._large {
      font-size: 1.1em; }
    tooltip tip-tip._small {
      font-size: 0.8em; }
  tooltip._top:not(._left):not(._right) tip {
    left: 50%;
    top: -9px;
    -webkit-transform: translateX(-50%) translateY(-100%);
    transform: translateX(-50%) translateY(-100%); }
    tooltip._top:not(._left):not(._right) tip tip-arrow {
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid rgba(0, 0, 0, 0.85);
      content: '';
      height: 0;
      left: 50%;
      margin-left: -6px;
      position: absolute;
      top: 100%;
      width: 0; }
  tooltip._bottom:not(._left):not(._right) tip {
    right: 50%;
    top: 100%;
    -webkit-transform: translateX(50%) translateY(9px);
    transform: translateX(50%) translateY(9px); }
    tooltip._bottom:not(._left):not(._right) tip tip-arrow {
      border-bottom: 6px solid rgba(0, 0, 0, 0.85);
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      bottom: 100%;
      content: '';
      height: 0;
      left: 50%;
      margin-left: -6px;
      position: absolute;
      width: 0; }
  tooltip._right:not(._top):not(._bottom) tip {
    left: 100%;
    top: 50%;
    -webkit-transform: translateX(9px) translateY(-50%);
    transform: translateX(9px) translateY(-50%); }
    tooltip._right:not(._top):not(._bottom) tip tip-arrow {
      border-bottom: 6px solid transparent;
      border-right: 6px solid rgba(0, 0, 0, 0.85);
      border-top: 6px solid transparent;
      content: '';
      height: 0;
      margin-top: -6px;
      position: absolute;
      right: 100%;
      top: 50%;
      width: 0; }
  tooltip._left:not(._top):not(._bottom) tip {
    left: -9px;
    top: 50%;
    -webkit-transform: translateX(-100%) translateY(-50%);
    transform: translateX(-100%) translateY(-50%); }
    tooltip._left:not(._top):not(._bottom) tip tip-arrow {
      border-bottom: 6px solid transparent;
      border-left: 6px solid rgba(0, 0, 0, 0.85);
      border-top: 6px solid transparent;
      content: '';
      height: 0;
      left: 100%;
      margin-top: -6px;
      position: absolute;
      top: 50%;
      width: 0; }
  tooltip._top._left tip {
    left: -9px;
    top: -9px;
    -webkit-transform: translateX(-100%) translateY(-100%);
    transform: translateX(-100%) translateY(-100%); }
    tooltip._top._left tip tip-arrow {
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid rgba(0, 0, 0, 0.85);
      content: '';
      height: 0;
      left: 90%;
      margin-left: -6px;
      position: absolute;
      top: 100%;
      width: 0; }
  tooltip._top._right tip {
    left: 100%;
    top: -9px;
    -webkit-transform: translateX(9px) translateY(-100%);
    transform: translateX(9px) translateY(-100%); }
    tooltip._top._right tip tip-arrow {
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid rgba(0, 0, 0, 0.85);
      content: '';
      height: 0;
      left: 10%;
      margin-left: -6px;
      position: absolute;
      top: 100%;
      width: 0; }
  tooltip._bottom._left tip {
    left: -9px;
    top: 100%;
    -webkit-transform: translateX(-100%) translateY(9px);
    transform: translateX(-100%) translateY(9px); }
    tooltip._bottom._left tip tip-arrow {
      border-bottom: 6px solid rgba(0, 0, 0, 0.85);
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      bottom: 100%;
      content: '';
      height: 0;
      left: 90%;
      margin-left: -6px;
      position: absolute;
      width: 0; }
  tooltip._bottom._right tip {
    left: 100%;
    top: 100%;
    -webkit-transform: translateX(9px) translateY(9px);
    transform: translateX(9px) translateY(9px); }
    tooltip._bottom._right tip tip-arrow {
      border-bottom: 6px solid rgba(0, 0, 0, 0.85);
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      bottom: 100%;
      content: '';
      height: 0;
      left: 10%;
      margin-left: -6px;
      position: absolute;
      width: 0; }

tip-tip .close-button {
  cursor: pointer;
  float: right;
  left: 8%;
  margin-top: -7%;
  padding: 3px;
  position: relative; }
