$(function(){"use strict";function e(e){var n=e.attr("data-target"),a=e.attr("data-toggle-class");e.on("click.toggleClass.fireEvent",function(e){e.preventDefault(),$(n).toggleClass(a)})}function n(e){e.children("ul.child-nav").slideDown(500,function(){$(this).css("display","")}),e.addClass("open-item").removeClass("close-item")}function a(e){e.children("ul.child-nav").slideUp(300,function(){$(this).css("display",""),e.addClass("close-item").removeClass("open-item")})}function t(e){e.children(".dropdown-box").slideDown(400)}function s(e){e.children(".dropdown-box").slideUp(200)}function i(e){e.siblings(".notice.open").each(function(){s($(this)),$(this).removeClass("open")})}function o(){r.addClass("fixed").removeClass("scroll"),f.prop("checked",!0)}function l(){r.addClass("scroll").addClass("left-sidebar-scroll").addClass("content-header-scroll").removeClass("fixed"),f.removeAttr("checked")}$(document).on("click",function(e){$("#notice-headerbox").find(e.target).length||$("#notice-headerbox .notice.open").removeClass("open").children(".dropdown-box").slideUp(200),$("#user-headerbox").find(e.target).length||$("#user-headerbox.open").removeClass("open").children(".user-options").slideUp(400),$("#search-headerbox").find(e.target).length||$("#search").is(":visible")&&$("#search").slideToggle()}),$("[data-toggle-class][data-target]").each(function(){e($(this))});var c=$("#main-nav");c.on("click","li.close-item > a",function(){var e=$(this).parent("li");n(e),e.siblings("li.open-item").each(function(){a($(this))})}),c.on("click","li.open-item > a",function(){var e=$(this).parent("li");a(e)}),$("#notice-headerbox .notice i").on("click",function(){var e=$(this).parent();e.toggleClass("open"),e.hasClass("open")?(i(e),t(e)):s(e)}),$("#user-headerbox").on("click",function(){var e=$(this).children(".user-options");$(this).toggleClass("open"),$(this).hasClass("open")?e.slideDown(400):e.slideUp(400)}),$("#search-icon").on("click",function(){$("#search").slideToggle()}),$(".panel").on("click",".toggle-panel.panel-expand",function(){var e=$(this).closest(".panel");e.children(".panel-content, .panel-footer").slideUp(400),$(this).addClass("panel-collapse").removeClass("panel-expand")}).on("click",".toggle-panel.panel-collapse",function(){var e=$(this).closest(".panel");e.children(".panel-content, .panel-footer").slideDown(400),$(this).addClass("panel-expand").removeClass("panel-collapse")}).on("click",".remove-panel",function(){var e=$(this).closest(".panel"),n=e.parent();n.is('[class*="col-"]')&&1==n.children().length?n.fadeOut(500,function(){n.remove()}):e.fadeOut(300,function(){e.remove()})});var d=$(".scroll-to-top");d.on("click",function(){return $("html, body").animate({scrollTop:0},600),!1}),$(window).on("scroll",function(){$(this).scrollTop()>100?d.fadeIn():d.fadeOut()});var r=$("html"),f=$("#header-fixed"),h=$("#content-header-fixed"),p=$("#left-sidebar-top"),u=$("#left-sidebar-fixed"),g=$("#left-sidebar-collapsed"),v=$("#left-sidebar-over"),m=$("#left-sidebar-left-lines");f.on("change",function(){f.is(":checked")?o():(l(),u.removeAttr("checked"),h.removeAttr("checked"))}),h.on("change",function(){h.is(":checked")?(o(),r.removeClass("content-header-scroll")):r.addClass("content-header-scroll")}),p.on("change",function(){p.is(":checked")?r.addClass("left-sidebar-top"):r.removeClass("left-sidebar-top")}),u.on("change",function(){u.is(":checked")?(o(),r.removeClass("left-sidebar-scroll")):r.addClass("left-sidebar-scroll")}),g.on("change",function(){g.is(":checked")?r.addClass("left-sidebar-collapsed"):r.removeClass("left-sidebar-collapsed")}),v.on("change",function(){v.is(":checked")?r.addClass("left-sidebar-over"):r.removeClass("left-sidebar-over")}),m.on("change",function(){m.is(":checked")?$("#main-nav").addClass("nav-left-lines"):$("#main-nav").removeClass("nav-left-lines")}),$(".left-sidebar-toggle").on("click",function(){g.is(":checked")?g.removeAttr("checked"):g.prop("checked",!0)})}),function(e){"use strict";e.fn.pluginAnimate=function(n){var a=e(this);a.addClass("animated unshown");var t=e.extend({},e.fn.pluginAnimate.defaults,n);return a.appear(function(){var e=a.data("animation-delay")?a.data("animation-delay"):1;e>1&&a.css("animation-delay",e+"ms"),a.addClass(a.data("animation-name")),setTimeout(function(){a.addClass("shown").removeClass("unshown")},e)},{accX:t.accX,accY:t.accY}),this},e.fn.pluginAnimate.defaults={accX:0,accY:-50}}(jQuery),function(e){"use strict";e.fn.loadingButton=function(n){var a=e(this),t=e.extend({},e.fn.loadingButton.defaults,n);if("start"===t.action){if(a.prop("disabled"))return this;a.data("loading-text")&&(t.text=a.data("loading-text")),a.data("loading-icon")&&(t.icon=a.data("loading-icon")),a.prop("disabled",!0).addClass("btn-spinning").attr("data-button-text",a.text()),a.html('<i class="'+t.icon+' fa-spin" aria-hidden="true"></i> '+t.text)}return"stop"===t.action&&a.hasClass("btn-spinning")&&a.prop("disabled",!1).removeClass("btn-spinning").html(a.attr("data-button-text")),this},e.fn.loadingButton.defaults={action:"start",text:"Loading",icon:"fa fa-spinner"}}(jQuery);