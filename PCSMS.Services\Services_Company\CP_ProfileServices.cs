﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Net.Mail;
using System.Text.RegularExpressions;
using System.Web.Mvc;
using PCSMS.Common;
using PCSMS.Models;
using PCSMS.Models.Models_Company;
using PCSMS.Models.Models_SProvider;
using PCSMS.Services.Services_Shared;

namespace PCSMS.Services.Services_Company
{
    public class CP_ProfileServices: ICP_ProfileServices
    {
        private readonly PCSMSDbContext _context;
        private readonly IEntityService<CP_Profile> _services;
        private readonly IEntityService<CP_Device_Schedule> _scheduleServices;
        public CP_ProfileServices()
        {
            _context = new PCSMSDbContext();
            _services = new EntityService<CP_Profile>(_context);
            _scheduleServices = new EntityService<CP_Device_Schedule>(_context);
        }



        //Private Services :
        private bool ExistingPasswordMatches(int companyId, string extPassword)
        {
            var result = _context.CP_Profile.FirstOrDefault(x => x.CompanyPassword == extPassword && x.Id == companyId);

            if (result == null)
            {
                return false;
            }
            return true;
        }
        private void SendMailToCompanyAfterSignup(string companyName, string companyEmail, string password, string receiverEmailAddress)
        {
            string senderEmailAddress = "<EMAIL>";
            string senderDetails = "PCSMS - Company Login Credentials";

            MailMessage mail = new MailMessage();
            mail.From = new MailAddress(senderEmailAddress, senderDetails);
            mail.To.Add(new MailAddress(receiverEmailAddress));

            mail.Subject = "Credentials to log into dashboard";
            string Body = "Hello, " + companyName + " !<br/>" +
            "Thank you for signing up, we are thrilled to have you on board !<br/><br/> " +
            "Here are your login credentials :<br /><br/> " +
            "Username : " + companyEmail + " <br/> " +
            "Password : " + password;
            mail.Body = Body;
            mail.IsBodyHtml = true;
            mail.BodyEncoding = System.Text.Encoding.UTF8;
            mail.SubjectEncoding = System.Text.Encoding.Default;

            SmtpClient client = new SmtpClient();
            client.Host = "smtp.gmail.com";
            client.Port = 587;
            client.EnableSsl = true;
            client.Timeout = 10000;
            client.UseDefaultCredentials = false;
            client.Credentials = new NetworkCredential("<EMAIL>", "Infinity@207");

            try
            {
                client.Send(mail);
                Generator.IsReport = "Ok";
            }
            catch (Exception ex)
            {
                var message = ex.Message;
                Generator.IsReport = "NotOk";
            }
        }

        
        
        
        //Public Services :
        public JsonResult SignUp(CP_Profile obj)
        {

            string message;
            obj.RegistrationDate = DateTime.UtcNow;
            try
            {
                if (_services.DoesExist(x => x.CompanyEmail == obj.CompanyEmail))
                {
                    Generator.IsReport = "NotOk";
                    message = "This e-mail is already in use, try another email !";
                }
                else
                {
                    obj.CompanyCode = GenericServices.GenerateCompanyCode();
                    _services.Save(obj);
                    _services.SaveChanges();


                    


                    #region step 02 : Sending mail to company with its credentials:
                    Generator.IsReport = "Ok";
                    message = "Registration successful ! Check your e-mail for login credentials.";

                    SendMailToCompanyAfterSignup(obj.CompanyName, obj.CompanyEmail, obj.CompanyPassword, obj.CompanyEmail);
                    #endregion

                    if (Generator.IsReport == "NotOk")
                    {
                        Generator.IsReport = "NotOk";
                        message = "Registration successful ! However, mail sending failed with login credentials.";
                    }
                }
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;

            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult ChangeCompanyEmailForLogin(int companyId, string newEmail)
        {
            string message;
            try
            {
                var result = _context.CP_Profile.FirstOrDefault(x => x.CompanyEmail == newEmail && x.Id != companyId);

                if (result == null)
                {
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Profile] SET CompanyEmail='{newEmail}' WHERE Id = {companyId}");
                    _context.SaveChanges();


                    Generator.IsReport = "Ok";
                    message = "E-mail for login has been updated successfully !";
                }
                else
                {
                    Generator.IsReport = "CompanyEmailExists";
                    message = "The e-mail your are trying to set is being used by another company !";
                }
            }
            catch (Exception ex)
            {

                Generator.IsReport = "NotOk";
                message = ex.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult ChangeCompanyPasswordForLogin(int companyId, string extPassword, string newPassword)
        {
            string message;
            try
            {
                if (extPassword == newPassword)
                {
                    Generator.IsReport = "ExistingAndNewPasswordSame";
                    message = "Apparently, your existing password and new password are same, Password NOT updated !";
                }
                else if (ExistingPasswordMatches(companyId, extPassword))
                {
                    //Update:
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Profile] SET CompanyPassword ='{newPassword}' WHERE Id = {companyId}");
                    _context.SaveChanges();
                    Generator.IsReport = "Ok";
                    message = "Password for login has been updated successfully !";
                }
                else
                {
                    Generator.IsReport = "ExistingPasswordDoesNotMatch";
                    message = "Your existing password is incorrect, Password NOT updated !";
                }
            }
            catch (Exception ex)
            {

                Generator.IsReport = "NotOk";
                message = ex.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UpdateCompanyInfo(CP_Profile obj)
        {
            
            string message;

            
            try
            {
                _context.Database.ExecuteSqlCommand($"UPDATE [CP_Profile] SET CompanyTypeId = {obj.CompanyTypeId}, CompanyPhone = '{obj.CompanyPhone}', CompanyMobile = '{obj.CompanyMobile}', CompanyWebsite = '{obj.CompanyWebsite}', CompanyAddress = '{obj.CompanyAddress}', CompanyBillingAddress = '{obj.CompanyBillingAddress}',  CompanyLat = '{obj.CompanyLat}', CompanyLong = '{obj.CompanyLong}' WHERE Id = {obj.Id}");
                _context.SaveChanges();
                Generator.IsReport = "Ok";
                message = "Company info updated successfully !";


            }
            catch (Exception ex)
            {
                Generator.IsReport = "NotOk";
                message = ex.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UpdateContactPersonInfo(CP_Profile obj)
        {

            string message;
            try
            {
                _context.Database.ExecuteSqlCommand($"UPDATE [CP_Profile] SET ContactPerson = '{obj.ContactPerson}', ContactPersonPhone = '{obj.ContactPersonPhone}', ContactPersonMobile = '{obj.ContactPersonMobile}', ContactPersonEmail = '{obj.ContactPersonEmail}', ContactPersonDesignation = '{obj.ContactPersonDesignation}' WHERE Id = {obj.Id}");
                _context.SaveChanges();

                Generator.IsReport = "Ok";
                message = "Contact person info updated successfully !";

            }
            catch (Exception ex)
            {
                Generator.IsReport = "NotOk";
                message = ex.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetCompanyProfileDetails(Expression<Func<CP_Profile, bool>> expression)
        {
            return new JsonResult
            {
                Data = _context.CP_Profile.Where(expression)
                .GroupJoin(_context.CompanyType,
                x=> x.CompanyTypeId,
                y=> y.Id,
                (x,y)=> new {CP_Profile = x, CompanyType = y})
                .SelectMany(x => x.CompanyType.DefaultIfEmpty(),
                (x, y) => new { x.CP_Profile, CompanyType = y })
                .Select(x => new
                {
                    x.CP_Profile.Id,
                    x.CP_Profile.CompanyCode,
                    x.CP_Profile.CompanyTypeId,
                    x.CP_Profile.CompanyName,
                    x.CP_Profile.CompanyAddress,
                    x.CP_Profile.CompanyBillingAddress,
                    x.CP_Profile.CompanyPhone,
                    x.CP_Profile.CompanyMobile,
                    x.CP_Profile.CompanyWebsite,
                    x.CP_Profile.CompanyEmail,
                    x.CP_Profile.ContactPerson,
                    x.CP_Profile.ContactPersonPhone,
                    x.CP_Profile.ContactPersonMobile,
                    x.CP_Profile.ContactPersonEmail,
                    x.CP_Profile.ContactPersonDesignation,
                    x.CP_Profile.CompanyLogo,
                    x.CP_Profile.RegistrationDate,
                    x.CP_Profile.CompanyLat,
                    x.CP_Profile.CompanyLong,

                    x.CompanyType.CompanyTypeName,

                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UploadCP_Logo(int profileId)
        {
            //Add System.Web to Services References

            string message = "";
            int iUploadedCnt = 0;

            
            string myPath = "";
            myPath = System.Web.Hosting.HostingEnvironment.MapPath("~/Company_Images/Profile_Images/");

            var obj = _context.CP_Profile.Where(x => x.Id == profileId).Select(x => new
            {
                x.CompanyLogo,
                x.CompanyName
            }).FirstOrDefault();

            var getOldImageName = obj.CompanyLogo;
            string trimmedFirstName = Regex.Replace(obj.CompanyName, " ", "");

            string randNum = GenericServices.CreateRandomNumberWithLetter();


            System.Web.HttpFileCollection httpFileCollection = System.Web.HttpContext.Current.Request.Files;
            if (httpFileCollection.Count > 0)
            {

                try
                {
                    for (int iCnt = 0; iCnt <= httpFileCollection.Count - 1; iCnt++)
                    {
                        System.Web.HttpPostedFile hpf = httpFileCollection[iCnt];

                        var imaginaryNameWithoutExtension = trimmedFirstName + "_" + profileId + "_" + randNum; ;
                        var extension = Path.GetExtension(hpf.FileName);
                        var newImageName = imaginaryNameWithoutExtension + extension;

                        if (extension != ".pdf")
                        {
                            if (getOldImageName != null)
                            {
                                File.Delete(myPath + getOldImageName);
                            }

                            //now moving new image to folder:
                            hpf.SaveAs(myPath + newImageName);
                            _context.Database.ExecuteSqlCommand($"UPDATE [CP_Profile] SET CompanyLogo = '{newImageName}' WHERE Id = {profileId}");
                            _context.SaveChanges();


                            iUploadedCnt = iUploadedCnt + 1;


                            

                            Generator.IsReport = "Ok";
                            message = "Image(s) Uploaded Successfully";

                        }
                        else
                        {
                            message = "This is not an image type.";
                            Generator.IsReport = "NotOk";
                        }

                    }
                }
                catch (Exception ex)
                {
                    message = ex.Message;
                    Generator.IsReport = "NotOk";
                }
            }
            else
            {
                Generator.IsReport = "NotOk";
                message = "There is no image to be saved";
            }

            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetCompanyListWhoHasScreenshots()
        {
            int?[] idsOfAllCompanies = _context.CP_ScreenCapture.Select(x => x.CompanyId).Distinct().ToArray();

               var result = _context.CP_Profile.Where(x=> idsOfAllCompanies.Contains(x.Id)).Select(x => new
               {
                   x.Id,
                   x.CompanyCode,
                   x.CompanyName,
                   x.CompanyEmail,
                   x.CompanyAddress,
                   x.CompanyBillingAddress,
               }).ToList();
                return new JsonResult
                {
                    Data = result,
                    JsonRequestBehavior = JsonRequestBehavior.AllowGet
                };
        }
        public JsonResult GetCompanyDetailsForPayload(int profileId)
        {
            var result = _context.CP_Profile.Where(x => x.Id == profileId).Select(x => new
            {
                x.Id,
                x.CompanyCode,
                x.CompanyName,
                x.CompanyEmail,
                x.CompanyAddress,
                x.CompanyBillingAddress,
            }).FirstOrDefault();
            return new JsonResult
            {
                //This dataset saves on TokenEntity Table with expiration-time and issued-time:
                Data = result,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetCompanyDetailsForCookies(int profileId)
        {
            return new JsonResult
            {
                Data = _context.CP_Profile.Where(x=> x.Id == profileId)
                .GroupJoin(_context.CompanyType,
                x => x.CompanyTypeId,
                y => y.Id,
                (x, y) => new { CP_Profile = x, CompanyType = y })
                .SelectMany(x => x.CompanyType.DefaultIfEmpty(),
                (x, y) => new { x.CP_Profile, CompanyType = y })
                
                //.GroupJoin(_context.CompanyPackageLog,
                //x => x.CP_Profile.PackageId,
                //y => y.Id,
                //(x, y) => new { x.CP_Profile, x.CompanyType, x.Area, x.Zone, x.Division, x.Country, CompanyPackage = y })
                //.SelectMany(x => x.CompanyPackage.DefaultIfEmpty(),
                //(x, y) => new { x.CP_Profile, x.CompanyType, x.Area, x.Zone, x.Division, x.Country, CompanyPackage = y })
                .Select(x => new
                {
                    x.CP_Profile.Id,
                    x.CP_Profile.CompanyCode,
                    x.CP_Profile.CompanyTypeId,
                    x.CP_Profile.CompanyName,
                    x.CP_Profile.CompanyAddress,
                    x.CP_Profile.CompanyBillingAddress,
                    x.CP_Profile.CompanyPhone,
                    x.CP_Profile.CompanyMobile,
                    x.CP_Profile.CompanyWebsite,
                    x.CP_Profile.CompanyEmail,
                    x.CP_Profile.ContactPerson,
                    x.CP_Profile.ContactPersonPhone,
                    x.CP_Profile.ContactPersonMobile,
                    x.CP_Profile.ContactPersonEmail,
                    x.CP_Profile.ContactPersonDesignation,
                    x.CP_Profile.CompanyLogo,
                    x.CP_Profile.RegistrationDate,
                    x.CP_Profile.CompanyLat,
                    x.CP_Profile.CompanyLong,

                    x.CompanyType.CompanyTypeName,

                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public int CP_Authenticate(string companyEmail, string password)
        {
            var company = _context.CP_Profile.FirstOrDefault(u => u.CompanyEmail == companyEmail && u.CompanyPassword == password);
            if (company != null && company.Id > 0)
            {
                return company.Id;
            }
            return 0;
        }


    }

    public interface ICP_ProfileServices
    {
        JsonResult SignUp(CP_Profile obj);
        JsonResult ChangeCompanyEmailForLogin(int companyId, string newEmail);
        JsonResult ChangeCompanyPasswordForLogin(int companyId, string extPassword, string newPassword);
        JsonResult UpdateCompanyInfo(CP_Profile obj);
        JsonResult UpdateContactPersonInfo(CP_Profile obj);
        JsonResult UploadCP_Logo(int profileId);
        JsonResult GetCompanyListWhoHasScreenshots();
        JsonResult GetCompanyProfileDetails(Expression<Func<CP_Profile, bool>> expression);
        JsonResult GetCompanyDetailsForPayload(int profileId);
        JsonResult GetCompanyDetailsForCookies(int profileId);
        int CP_Authenticate(string username, string password);


    }
}
