﻿PCSMSApp.factory("deviceServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetDeviceListByLicenseId: function (licenseId) {
            return $http({
                url: "/api/Device/GetDeviceListByLicenseId/" + licenseId ,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        ChangeDeviceName: function (deviceUniqueId, deviceName) {
            return $http({
                url: "/api/Device/ChangeDeviceName/" + deviceUniqueId + "/" +  deviceName,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        
    };
}]);