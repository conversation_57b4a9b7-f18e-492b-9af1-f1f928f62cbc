﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!-- main content -->

<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-cube" aria-hidden="true"></i><a ui-sref="companytype">Company Type</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-header">
                        <button type="button" class="btn btn-default btn-o pull-right btn-addon btn-o-addon" id="btnAddCompanyType" data-toggle="modal" data-target="#CompanyTypeModal">
                            <i class="fa fa-plus" aria-hidden="true"></i>
                            Add Company Type
                        </button>
                    </div>
                    <div class="panel-content">
                        <div class="table-responsive">
                            <!-- DataTable -->
                            <table class="data-table table table-striped nowrap table-bordered table-condensed" id="basic-table" datatable="ng" dt-options="vm.dtOptions" dt-instance="vm.dtInstance" dt-column-defs="vm.dtColumnDefs" cellspacing="0">
                                <thead>
                                    <tr class="animated fadeIn">
                                        <th>Company Type</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="animated fadeIn" ng-repeat="x in CompanyTypeList">
                                        <td>{{x.CompanyTypeName}}</td>
                                        <td>
                                            <button class="btn btn-default btn-Line-Height btn-datatables" tooltip-placement="top" uib-tooltip="Edit" ng-click="openCompanyTypeModal(x.Id)" style="border:1px solid #02715a; background-color:#ffffff"><img src="Assets_SProvider/images/datatables/edit.png" width="20" /></button>
                                            <!--<button class="btn btn-default btn-Line-Height btn-datatables" ng-click="deleteCompanyTypeAlert(x.Id, x.CompanyTypeName)" style="border-color: #abaaaa"><img src="Assets_SProvider/img/datatables/delete.png" width="20" /></button>-->
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>

<form novalidate name="CompanyTypeForm">
    <div class="modal fade" id="CompanyTypeModal" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog  modal-dialog-at">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="custom-close" data-dismiss="modal" id="CompanyType-btn-top-close" ng-click="cancelCompanyTypeListModal()" tooltip-placement="left" uib-tooltip="Close">&times;</button>
                    <h4 ng-if="CompanyType.Id === undefined">Create New Company Type</h4>
                    <h4 ng-if="CompanyType.Id >=1">Update Company Type</h4>
                    <div class="form-sep">
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 ">
                            <div class="col-md-3">
                                <label class="control-label label-for-textbox-center-direction">Company Type</label>
                            </div>
                            <div class="col-md-9">
                                <div class="form-group">
                                    <input type="text" class="form-control" placeholder="Enter Company Type" name="CompanyTypeName"
                                           ng-required="true"
                                           ng-model="CompanyType.CompanyTypeName"
                                           ng-change="LiveValidation(CompanyType.CompanyTypeName)"
                                           ng-minlength="4"
                                           ng-maxlength="60"
                                           ng-pattern="/^[A-Z][a-zA-Z0-9\.\/\-_ ]*$/" />
                                    <div class="row custom-row">
                                        <ul class="list-unstyled text-danger pull-left" ng-show="LiveValidation(Designation.CompanyTypeName)==false || CompanyTypeForm.CompanyTypeName.$dirty && CompanyTypeForm.CompanyTypeName.$invalid">
                                            <li><span class="pull-left" ng-show="LiveValidation(Designation.CompanyTypeName)==false">{{LiveValidationError}}</span></li>
                                            <li><span class="pull-left" ng-show="CompanyTypeForm.CompanyTypeName.$error.required">*Required</span></li>
                                            <li><span class="pull-left" ng-show="CompanyTypeForm.CompanyTypeName.$error.pattern">Start with capital letter, don't use special character.</span></li>
                                            <li><span class="pull-left" ng-show="!CompanyTypeForm.CompanyTypeName.$error.pattern && CompanyTypeForm.CompanyTypeName.$error.minlength">Minimum length is 4</span></li>
                                            <li><span class="pull-left" ng-show="!CompanyTypeForm.CompanyTypeName.$error.pattern && CompanyTypeForm.CompanyTypeName.$error.maxlength">Maximum length is 60</span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="col-md-5 col-md-offset-7">
                        <div class="btn-group btn-group-justified" role="group" aria-label="group button" ng-if="CompanyType.Id === undefined">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-default pull-right" ng-click="cancelCompanyTypeListModal()" role="button">Close</button>
                            </div>
                            <div class="btn-group" role="group">
                                <button ng-disabled="CompanyTypeForm.$invalid || LiveValidation(CompanyType.CompanyTypeName)==false"
                                        type="button"
                                        ng-click="SaveOrUpdateCompanyType()" class="btn btn-sm btn-primary">
                                    Save &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                </button>
                            </div>
                        </div>
                        <div class="btn-group btn-group-justified" role="group" aria-label="group button" ng-if="CompanyType.Id >=1">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-default pull-right" ng-click="cancelCompanyTypeListModal()" role="button">Close</button>
                            </div>
                            <div class="btn-group" role="group">
                                <button ng-disabled="CompanyTypeForm.$invalid || LiveValidation(CompanyType.CompanyTypeName)==false"
                                        type="button"
                                        ng-click="SaveOrUpdateCompanyType()" class="btn btn-sm btn-primary">
                                    Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

