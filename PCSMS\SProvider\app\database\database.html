﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>


<!-- CONTENT -->
<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-database" aria-hidden="true"></i><a ui-sref="home">Manage Database</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="row animated fadeInUp">
        <div class="row">
            <div class="col-md-12">
                <div class="col-sm-12 col-md-5">
                    <div class="panel b-primary bt-md" style="height: 540px">
                        <div class="panel-content">
                            <p class="text-justify"  style="padding: 20px; border: 1px solid #e6dddd;">
                                <span class="text-bold color-primary">Tips !</span>
                                <br/>
                                <br/>
                                You can delete any company's old screenshots that had been saved in the database. Select 'Company' and choose date from calendar in
                                order to delete <span class="text-bold color-primary">screenshots</span> from database. Besides, there is an option to delete all companies old
                                screenshots at one go.

                                <br/>
                                <br/>
                                However, you won't be able to delete last 14 days screenshots of any company. You can only 
                                delete <span class="text-bold color-primary">two-weeks</span> old data.If calendar date seems disable, then scroll
                                left to get previous dates.
                            </p>
                            <p class="text-justify" style="padding: 20px; border: 1px solid #e6dddd;"  ng-if="CompanyList.length == 0">
                                <span class="text-bold text-danger">Warning !</span>
                                <br />
                                No screenshots found in database for any company !
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-7">
                    <div class="panel b-primary bt-md" style="height: 540px">
                        <div class="panel-content">
                            <form name="DatabaseForm" novalidate  style="padding: 20px; border: 1px solid #e6dddd;">
                                <div class="form-group-sm">
                                    <label class="col-lg-4 control-label">Select Company<span class="required">*</span></label>
                                    <div class="col-lg-7">
                                        <select class="form-control" name="Company"
                                                ng-model="Database.CompanyId"
                                                ng-required="true"
                                                ng-disabled="AllCompaniesChkBoxSelected()==true || CompanyList.length == 0"
                                                ng-options="Company.Id as Company.CompanyName for Company in CompanyList">
                                            <option value="">Select</option>
                                            <option ng-repeat="e in CompanyList" ng-selected="Database.CompanyId==e.Id" value="{{e.Id}}">{{e.CompanyName}}</option>
                                        </select>
                                    </div>
                                    <div class="row custom-row">
                                        <span class="text-danger pull-left"
                                              ng-show="DatabaseForm.Company.$dirty && DatabaseForm.Company.$invalid">
                                            <span ng-show="DatabaseForm.Company.$error.required">*Required</span>
                                        </span>
                                    </div>
                                </div>
                                <br />
                                <div class="form-group-sm">
                                    <label class="col-lg-3 control-label">Delete For All Companies</label>
                                    <div class="col-lg-4">
                                        <div class="checkbox" style="padding-left: 53px"  ng-class="{'my-disable':CompanyList.length == 0}">
                                            <label>
                                                <input type="checkbox" value="S" ng-model="Database.AllCompanies" ng-true-value="'Y'" ng-false-value="'N'"  ng-class="{'my-disable':CompanyList.length == 0}"> 
                                                <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                Yes
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <br />
                                <br />
                                <br />
                                <br />
                                <div class="form-group-sm">
                                    <label class="col-lg-4 control-label">Delete Up To<span class="required">*</span></label>
                                    <div class="col-lg-7">
                                        <datepicker date-format="yyyy-MM-dd" selector="form-control" date-max-limit="{{DisableUpTo | date}}">
                                            <div class="input-group">
                                                <input class="form-control" placeholder="Choose a date" ng-model="Database.DateUpTo" ng-disabled="CompanyList.length == 0"/>
                                                <span class="input-group-addon" style="cursor: pointer">
                                                    <i class="fa fa-lg fa-calendar"></i>
                                                </span>
                                            </div>
                                        </datepicker>
                                    </div>
                                    <div class="row custom-row">
                                        <span class="errormessage pull-left"
                                              ng-show="DatabaseForm.DateUpTo.$dirty && DatabaseForm.DateUpTo.$invalid">
                                            <span ng-show="DatabaseForm.DateUpTo.$error.required">*Required</span>
                                        </span>
                                    </div>
                                </div>
                                <br />
                                <hr class="custom-hr2"/>
                                <br />
                                <br />
                                <br />
                                <br />
                                <div class="form-group-sm">
                                    <div class="col-lg-offset-4 col-lg-7">
                                        <button type="button"
                                                class="btn  btn-primary btn-wide pull-right btn-left-border-radius"
                                                ng-click="DeleteScreenshots()"
                                                ng-disabled="IsFormValid()==false || CompanyList.length == 0"
                                                role="button">
                                            <span >Delete Screenshot (s)</span>
                                            &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-trash-o fa-1x"></i>
                                        </button>
                                    </div>
                                </div>
                                <br />
                                <br />
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>

