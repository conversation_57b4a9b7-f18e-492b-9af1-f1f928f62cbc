﻿using PCSMS.Models.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PCSMS.Models.Models_Company
{
    public class CP_Device_License : Entity<int>
    {
        [ForeignKey("DeviceId")]
        public virtual CP_Device CP_Device { get; set; }
        public int? DeviceId { get; set; }


        [ForeignKey("LicenseId")]
        public virtual CP_License CP_License { get; set; }
        public int? LicenseId { get; set; }

        
        [MaxLength(10)]
        public string Status { get; set; } //still not sure
    }
}
