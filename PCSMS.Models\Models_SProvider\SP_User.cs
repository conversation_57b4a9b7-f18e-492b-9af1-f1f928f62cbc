﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PCSMS.Models.Common;
using PCSMS.Models.Models_Shared;

namespace PCSMS.Models.Models_SProvider
{
    public class SP_User:Entity<int>
    {

        [ForeignKey("SP_ProfileId")]
        public virtual SP_Profile SP_Profile { get; set; }
        public int? SP_ProfileId { get; set; }

        [ForeignKey("SP_BranchId")]
        public virtual SP_Branch SP_Branch { get; set; }
        public int? SP_BranchId { get; set; }

        [ForeignKey("AccessTypeId")]
        public virtual AccessType AccessType { get; set; }
        public int? AccessTypeId { get; set; }

        [ForeignKey("SP_DesignationId")]
        public virtual SP_Designation SP_Designation { get; set; }
        public int? SP_DesignationId { get; set; }


        [MaxLength(20)]
        public string Username { get; set; }
        public string Password { get; set; }
        [MaxLength(75)]
        public string FirstName { get; set; }
        [MaxLength(75)]
        public string LastName { get; set; }
        [MaxLength(500)]
        public string Address { get; set; }
        [MaxLength(50)]
        public string BirthDate { get; set; }
        [MaxLength(50)]
        public string Gender { get; set; }
        [MaxLength(50)]
        public string Mobile { get; set; }
        [MaxLength(50)]
        public string Email { get; set; }
        [MaxLength(150)]
        public string Photo { get; set; }
        public string IsActive { get; set; }
        //0
        //1
        //I know it is weird but keep 0 and 1 for the time being, 

    }
}
