﻿/// <reference path="app.js" />
PCSMSApp.controller('onlyregisteredcompanyController', function ($scope, onlyregisteredcompanyServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    //====================================================================Declaration=================================================================================
    $rootScope.managecompanyOpen = true;
    $scope.CompanyDetails = {};



    //====================================================================Element Processing==========================================================================
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [DTColumnDefBuilder.newColumnDef(2).notSortable()
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
        .withOption('paging', true)
        .withOption('searching', true)
        .withOption('info', true);




    //====================================================================DB Operation================================================================================
    onlyregisteredcompanyServices.GetRegisteredCompanyList().then(function (response) {
        $scope.RegisteredCompanyList = response.data;
        angular.forEach(response.data, function (value, key) {
            if (value.RegistrationDate != null) {
                var regDateUtc = moment.utc(value.RegistrationDate);
                value.RegistrationDate = regDateUtc.local().format("DD-MM-YYYY hh:mm:ss a").toString();
            }
        });
    });

    $scope.GetCompanyProfileDetails = function (companyId) {
        onlyregisteredcompanyServices.GetCompanyProfileDetails(companyId).then(function(response) {
                $scope.CompanyDetails = response.data;
                if ($scope.CompanyDetails.RegistrationDate != null) {
                    var expDateUtc = moment.utc($scope.CompanyDetails.RegistrationDate);
                    $scope.CompanyDetails.RegistrationDate = expDateUtc.local().format("DD-MM-YYYY hh:mm:ss a").toString();
                }
            })
            .then(function() {
                $('#CompanyProfileDetailsModal').modal('show');
            });
    };



    //====================================================================Modal Operation=============================================================================
    $scope.cancelCompanyProfileDetailsModal = function () {
        $('#CompanyProfileDetailsModal').modal('hide');
        $timeout(function() {
            $scope.CompanyDetails = {};
        }, 200);
    };


});