﻿using System;
using PCSMS.Models;
using PCSMS.Common;
using PCSMS.Services.Services_Shared;
using PCSMS.Models.Models_Company;
using PCSMS.Models.Models_SProvider;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Cryptography.X509Certificates;
using System.Web.Mvc;
using Microsoft.AspNet.SignalR;

namespace PCSMS.Services.Services_Shared
{
    public class SubscriptionServices : ISubscriptionServices
    {
        private readonly PCSMSDbContext _context;
        private readonly IEntityService<CP_License> _licenseServices;
        private readonly IEntityService<CP_License_Period> _licensePeriodServices;
        private readonly IHubContext _notificationHubContext;
        public SubscriptionServices()
        {
            _context = new PCSMSDbContext();
            _licenseServices = new EntityService<CP_License>(_context);
            _licensePeriodServices = new EntityService<CP_License_Period>(_context);
            _notificationHubContext = GlobalHost.ConnectionManager.GetHubContext("notificationHub");
        }


        //STATUS 
        //Requested
        //Active
        //In-Active
        //Expired

        //SecondaryStatus
        //Renewal Requested   (n.b. space available)
        //Private Services:

        //For Company:
        public JsonResult RequestLicense(int companyId)
        {
            var message = "";

            var companyIdExists = _context.CP_Profile.Any(x => x.Id == companyId);

            if (companyIdExists)
            {
                var requestedForChargeableLicense = _context.CP_License.Where(x => x.CompanyId == companyId).Any(x => x.LicenseType == "Chargeable" && x.Status == "Requested");
                if (requestedForChargeableLicense)
                {
                    message = "A chargeable license has recently been requested . Please wait for that to be approved.";
                    Generator.IsReport = "NotOk";
                }
                else
                {
                    try
                    {
                        //saving licenseobj in CP_License Table
                        CP_License licenseObj = new CP_License();
                        licenseObj.CompanyId = companyId;
                        licenseObj.LicenseType = "Chargeable";
                        licenseObj.RequestedOn = DateTime.UtcNow;
                        licenseObj.LCode = GenericServices.GenerateLCode(companyId);
                        licenseObj.Status = "Requested";
                        _licenseServices.Save(licenseObj);
                        _licenseServices.SaveChanges();


                        //add a row in CP_License_Period table
                        CP_License_Period cpl = new CP_License_Period();
                        cpl.LicenseId = licenseObj.Id;
                        cpl.Status = "Current";

                        _licensePeriodServices.Save(cpl);
                        _licensePeriodServices.SaveChanges();


                        Generator.IsReport = "Ok";
                        message = "Request sent successfully !";

                        //Now broadcasting this to SProvider that comapny has requested a license:
                        _notificationHubContext.Clients.All.onLicenseRequested();
                    }
                    catch (Exception ex)
                    {
                        Generator.IsReport = "NotOk";
                        message = ex.Message;
                    }
                }

            }
            else
            {
                message = "Invalid Company requests ! Please contact system administrator.";
                Generator.IsReport = "NotOk";
            }


            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult RequestForLicenseRenewal(int licenseId)
        {
            var message = "";
            var licenseObj = _context.CP_License.Where(x => x.Id == licenseId).Select(x => new
            {
                x.Id,
                x.Status,
                x.LicenseType,
                x.CompanyId,
                x.LCode,
                x.SecondaryStatus
            }).FirstOrDefault();
            if (licenseObj ==  null)
            {
                Generator.IsReport = "NotOk";
                message = "Invalid license Id !";
            }
            else if (licenseObj.LicenseType == "Trial")
            {
                Generator.IsReport = "NotOk";
                message = "You can not renew a Trial version of license.";
            }
            else if ((licenseObj.Status == "Expired" || licenseObj.Status == "Active") && licenseObj.SecondaryStatus == null)
            {
                //updating status in CP_License Table
                string secondaryStatus = "Renewal Requested";
                _context.Database.ExecuteSqlCommand($"UPDATE [CP_License] SET SecondaryStatus = '{secondaryStatus}' WHERE Id = {licenseId}");

                Generator.IsReport = "Ok";
                message = "Your renewal request sent successfully !";

                //Now broadcasting to sprovider that a renewal is requested
                _notificationHubContext.Clients.All.onLicenseRenewalRequested(licenseObj.LCode);
            }
            else if (licenseObj.Status == null)
            {
                Generator.IsReport = "NotOk";
                message = "License Id is not valid. Contact System administrator.";
            }
            else
            {
                Generator.IsReport = "NotOk";
                message = "License can not be renewed because it is in '" + licenseObj.SecondaryStatus + "' status.";
            }
            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetLicenseListByCompanyId(int companyId)
        {
            return new JsonResult
            {
                Data = _context.CP_License.Where(x => x.CompanyId == companyId)
                .Join(_context.CP_License_Period.Where(x => x.Status == "Current"),
                x => x.Id,
                y => y.LicenseId,
                (x, y) => new { License = x, LicensePeriod = y })
                .Join(_context.CP_Profile,
                x => x.License.CompanyId,
                y => y.Id,
                (x, y) => new { x.License, x.LicensePeriod, Company = y })
                .Select(x => new
                {
                    //Preoject License Details
                    x.License.Id,
                    x.License.LCode,
                    x.License.LicenseKey,
                    x.License.LicenseType,
                    x.License.RequestedOn,
                    x.License.DeviceLimit,
                    x.License.Status,
                    x.License.SecondaryStatus,

                    //Project License_Period
                    x.License.ActivatedOn,
                    x.LicensePeriod.ExpiryDate,

                    //Company Deatails
                    x.Company.CompanyName

                }).OrderBy(x => x.LCode).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetLicenseDetailsByLicenseId(int licenseId)
        {
            return new JsonResult
            {
                Data = _context.CP_License.Where(x => x.Id == licenseId)
                .Join(_context.CP_License_Period.Where(x=> x.Status == "Current"), 
                x => x.Id, 
                y => y.LicenseId, 
                (x, y) => new{License = x, LicensePeriod = y})
                .Join(_context.CP_Profile,
                x=> x.License.CompanyId,
                y=> y.Id,
                (x,y)=> new { x.License, x.LicensePeriod, Company = y })
                .Select(x => new
                {
                    //Preoject License Details
                    x.License.Id,
                    x.License.LCode,
                    x.License.LicenseKey,
                    x.License.LicenseType,
                    x.License.RequestedOn,
                    x.License.DeviceLimit,
                    x.License.Status,
                    x.License.SecondaryStatus,

                    //Project License_Period
                    x.License.ActivatedOn,
                    x.LicensePeriod.ExpiryDate,

                    //Company Deatails
                    x.Company.CompanyName

                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetAllLicenseList()
        {
            return new JsonResult
            {
                Data = _context.CP_License
                    .Join(_context.CP_License_Period.Where(x => x.Status == "Current"),
                        x => x.Id,
                        y => y.LicenseId,
                        (x, y) => new { License = x, LicensePeriod = y })
                    .Join(_context.CP_Profile,
                        x => x.License.CompanyId,
                        y => y.Id,
                        (x, y) => new { x.License, x.LicensePeriod, Company = y })
                    .Select(x => new
                    {
                        //Preoject License Details
                        x.License.Id,
                        x.License.LCode,
                        x.License.LicenseKey,
                        x.License.LicenseType,
                        x.License.RequestedOn,
                        x.License.DeviceLimit,
                        x.License.Status,
                        x.License.SecondaryStatus,

                        //Project License_Period
                        x.License.ActivatedOn,
                        x.LicensePeriod.ExpiryDate,

                        //Company Deatails
                        x.Company.CompanyName

                    }).OrderBy(x => x.LCode).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetCountOfActiveLicenses()
        {
            return new JsonResult
            {
                Data = _context.CP_License.Count(x => x.Status == "Active"),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetCountOfExpiredLicenses()
        {
            return new JsonResult
            {
                Data = _context.CP_License.Count(x => x.Status == "Expired"),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }


        //For SProvider:
        public JsonResult GetRegisteredCompanyList()
        {
            int?[] companiesWhoHasPackage = _context.CP_License.Select(x => x.CompanyId).Distinct().ToArray();
            return new JsonResult
            {
                Data = _context.CP_Profile.Where(x => !companiesWhoHasPackage.Contains(x.Id))
                .GroupJoin(_context.CompanyType,
                x => x.CompanyTypeId,
                y => y.Id,
                (x, y) => new { Company = x, CompanyType = y })
                .SelectMany(x => x.CompanyType.DefaultIfEmpty(),
                (x, y) => new { x.Company, CompanyType = y })
                .Select(x => new
                {
                    x.Company.Id,
                    x.Company.CompanyName,
                    x.Company.CompanyEmail,
                    x.Company.CompanyPhone,
                    x.Company.CompanyMobile,
                    x.Company.RegistrationDate,
                    x.Company.CompanyAddress,
                    x.Company.CompanyBillingAddress,
                    x.Company.ContactPersonMobile,
                    x.Company.ContactPersonEmail,
                    x.Company.ContactPersonDesignation,

                    CompanyType = x.CompanyType.CompanyTypeName,
                }).OrderByDescending(x => x.RegistrationDate).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,

            };
        }
        public JsonResult GetSubscribedCompanyList()
        {
            int?[] companiesWhoHasPackage = _context.CP_License.Select(x => x.CompanyId).Distinct().ToArray();
            return new JsonResult
            {
                Data = _context.CP_Profile.Where(x => companiesWhoHasPackage.Contains(x.Id))
                .GroupJoin(_context.CompanyType,
                x => x.CompanyTypeId,
                y => y.Id,
                (x, y) => new { Company = x, CompanyType = y })
                .SelectMany(x => x.CompanyType.DefaultIfEmpty(),
                (x, y) => new { x.Company, CompanyType = y })
                .Select(x => new
                {
                    x.Company.Id,
                    x.Company.CompanyName,
                    x.Company.CompanyEmail,
                    x.Company.CompanyPhone,
                    x.Company.CompanyMobile,
                    x.Company.RegistrationDate,
                    x.Company.CompanyAddress,
                    x.Company.CompanyBillingAddress,
                    x.Company.ContactPersonMobile,
                    x.Company.ContactPersonEmail,
                    x.Company.ContactPersonDesignation,

                    CompanyType = x.CompanyType.CompanyTypeName,
                }).OrderByDescending(x => x.RegistrationDate).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,

            };
        }
        public JsonResult GetCountOfRegisteredCompanies()
        {
            int?[] companiesWhoHasPackage = _context.CP_License.Select(x => x.CompanyId).Distinct().ToArray();
            var countOfRegisteredCompanies = _context.CP_Profile.Count(x => !companiesWhoHasPackage.Contains(x.Id));
            return new JsonResult
            {
                Data = countOfRegisteredCompanies,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetCountOfSubscribedCompanies()
        {
            int?[] companiesWhoHasPackage = _context.CP_License.Select(x => x.CompanyId).Distinct().ToArray();
            var countOfSubscribedCompanies = _context.CP_Profile.Count(x => companiesWhoHasPackage.Contains(x.Id));
            return new JsonResult
            {
                Data = countOfSubscribedCompanies,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }




        public JsonResult ApproveLicense(int licenseId, int deviceLimit, DateTime expiryDate)
        {
            
            expiryDate = expiryDate.Date.AddHours(17).AddMinutes(59).AddSeconds(59).AddTicks(1);
            //expiryDate = expiryDate.ToUniversalTime();

            //expiryDate = expiryDate.AddDays(1).AddTicks(-1);
            expiryDate = expiryDate.Date + new TimeSpan(17, 59, 59);
            var message = "";
            
            try
            {
                var licenseObj = _context.CP_License.Where(x => x.Id == licenseId).Select(x => new
                {
                    x.Status,
                    x.LicenseType,
                    x.CompanyId,
                    x.LCode
                }).FirstOrDefault();

                if (licenseObj == null)
                {
                    Generator.IsReport = "NotOk";
                    message = "Invalid license Id !";
                }

                else if (licenseObj.LicenseType == "Trial")
                {
                    Generator.IsReport = "NotOk";
                    message = "Free license can not be approved.";
                }
                else if (expiryDate <= DateTime.UtcNow)
                {
                    Generator.IsReport = "NotOk";
                    message = "License expiry date less or equal than current time. Please extend expiry date and try again.";
                }
                else if (licenseObj.Status == "Requested")
                {
                    //first check for any Trial license exists, if yes then terminate that first
                    //1.Getting companyId
                    var companyId = _context.CP_License.Where(x => x.Id == licenseId).Select(x => x.CompanyId).FirstOrDefault();
                    var trialLicenseId = _context.CP_License.Where(x => x.CompanyId == companyId && x.LicenseType == "Trial" && x.Status == "Active").Select(x => x.Id).FirstOrDefault();
                    if (trialLicenseId > 0)
                    {
                        var trialLicenseStatus = "In-Active";
                        _context.Database.ExecuteSqlCommand($"UPDATE [CP_License] SET Status = '{trialLicenseStatus}' WHERE Id = {trialLicenseId}");

                        //updating CP_Device_License table
                        var device_license_status = "In-Active";
                        _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device_License] SET Status = '{device_license_status}' WHERE LicenseId = {trialLicenseId}");

                        _notificationHubContext.Clients.All.onLicenseStatusTransition(trialLicenseId);
                    }

                    //updating CP_License table 
                    string status = "Active";
                    DateTime activatedOn = DateTime.UtcNow;
                    string licenseKey = GenericServices.CreateRandomNumber1() + "-" + GenericServices.CreateRandomNumber2() + "-" + GenericServices.CreateRandomNumber3() + "-" + GenericServices.CreateRandomNumber4() + "-" + GenericServices.CreateRandomNumber5();
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_License] SET Status = '{status}',  LicenseKey = '{licenseKey}',  DeviceLimit = '{deviceLimit}', ActivatedOn = '{activatedOn}' WHERE Id = {licenseId}");

                    //updating CP_License_Period table
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_License_Period] SET ExpiryDate = '{expiryDate}'  WHERE LicenseId = {licenseId}");


                    //Now, broadcasting to client browser that license has been approved
                    _notificationHubContext.Clients.All.onLicenseApproval(licenseObj.CompanyId, licenseObj.LCode);


                    Generator.IsReport = "Ok";
                    message = "License approved successfully !";

                }
                else if (licenseObj.Status == null)
                {
                    Generator.IsReport = "NotOk";
                    message = "License Id is not valid. Contact System administrator.";
                }
                else
                {
                    Generator.IsReport = "NotOk";
                    message = "License can not be approved because it is in '" + licenseObj.Status + "' status.";
                }

            }
            catch (Exception ex)
            {
                Generator.IsReport = "NotOk";
                message = ex.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult ApproveLicenseRenewal(DateTime expiryDate, int licenseId)
        {
            expiryDate = expiryDate.Date.AddHours(23).AddMinutes(59).AddSeconds(59).AddTicks(1);

            var message = "";
            try
            {
                var licenseObj = _context.CP_License.Where(x => x.Id == licenseId).Select(x => new
                {
                    x.Id,
                    x.LCode,
                    x.LicenseKey,
                    x.LicenseType,
                    x.Status,
                    x.SecondaryStatus,
                    x.CompanyId,

                }).FirstOrDefault();

                
                if (licenseObj == null)
                {
                    Generator.IsReport = "NotOk";
                    message = "License Id seems to be invalid !";
                }
                else if (licenseObj.LicenseType == "Trial")
                {
                    Generator.IsReport = "NotOk";
                    message = "You can not renew a Trial version of license.";
                }
                else if (expiryDate.ToUniversalTime() <= DateTime.UtcNow) //<-----I need to work here
                { 
                    Generator.IsReport = "NotOk";
                    message = "License expiry date less or equal than current time. Please extend expiry date and try again.";
                }
                else if (licenseObj.SecondaryStatus == "Renewal Requested")
                {
                    //updating CP_License table
                    string status = "Active";
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_License] SET Status = '{status}', SecondaryStatus = NULL WHERE Id = {licenseId}");
                    var device_license_status = "Active";
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device_License] SET Status = '{device_license_status}' WHERE LicenseId = {licenseId}");
                    //update last License_Period table all status of expiry date to null
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_License_Period] SET Status = NULL WHERE LicenseId = {licenseId}");

                    //add a row in CP_License_Period table
                    CP_License_Period cpl = new CP_License_Period();
                    cpl.LicenseId = licenseId;
                    cpl.Status = "Current";
                    cpl.ExpiryDate = expiryDate.ToUniversalTime();

                    _licensePeriodServices.Save(cpl);
                    _licensePeriodServices.SaveChanges();

                    Generator.IsReport = "Ok";
                    message = "License renewed successfully !";

                    //after that, now it is time to broadcast that company's license has been renewed
                    _notificationHubContext.Clients.All.onLicenseStatusTransition(licenseId);

                    //Now broadcasting for browser
                    _notificationHubContext.Clients.All.onLicenseRenewal(licenseObj.CompanyId, licenseObj.LCode);



                }
                else if (licenseObj.Status == null)
                {
                    Generator.IsReport = "NotOk";
                    message = "License Id is not valid. Contact System administrator.";
                }
                else
                {
                    Generator.IsReport = "NotOk";
                    message = "License can not be approved because it is in '" + licenseObj.Status + "' status.";
                }

            }
            catch (Exception ex)
            {
                Generator.IsReport = "NotOk";
                message = ex.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        //TaskScheduler Services:
        public JsonResult TerminateSubscriptionByTaskScheduler()
        {
            var message = "";

            int?[] distinctIdsOfLicenses = _context.CP_License_Period
                .Join(_context.CP_License.Where(x => x.Status == "Active"),
                x=>x.LicenseId,
                y=> y.Id,
                (x,y)=> new {LicensePeriod = x, License = y})
                .Select(x => x.LicensePeriod.LicenseId).Distinct().ToArray();
            int count = 0;

            if (distinctIdsOfLicenses.Length < 1)
            {
                message = "There is no license to terminate";
            }
            {
                foreach (var licenseId in distinctIdsOfLicenses)
                {

                    var periodObj = _context.CP_License_Period.Where(x => x.LicenseId == licenseId).Select(x => new
                    {
                        x.ExpiryDate,
                        x.Id,
                    }).OrderByDescending(x => x.Id).FirstOrDefault();

                    var currentDateTime = DateTime.UtcNow;
                    var expiryDate = periodObj.ExpiryDate.Value.ToUniversalTime();
                    if (currentDateTime >= expiryDate)
                    {
                        #region stage 01 : Updating CP_License Table
                        var license_status = "Expired";
                        var device_license_status = "In-Active";
                        _context.Database.ExecuteSqlCommand($"UPDATE [CP_License] SET Status = '{license_status}' WHERE Id = {licenseId}");

                        _context.Database.ExecuteSqlCommand($"UPDATE [CP_Device_License] SET Status = '{device_license_status}' WHERE LicenseId = {licenseId}");

                        #endregion

                        count = count + 1;

                        message = count + " license(s) been terminated successfully by task-scheduler.";
                       
                        var licenseObj = _context.CP_License.Where(x => x.Id == licenseId)
                            .Join(_context.CP_Profile,
                            x => x.CompanyId,
                            y => y.Id,
                            (x, y) => new { License = x, Company = y })
                            .Select(x => new
                            {
                                x.Company.CompanyName,
                                x.License.CompanyId,
                                x.License.LCode
                            }).FirstOrDefault();


                        //after that, now it is time to broadcast to desktop app that company's license has expired
                        _notificationHubContext.Clients.All.onLicenseStatusTransition(licenseId);

                        //broadcasting to sprovider that company's license has expired
                        _notificationHubContext.Clients.All.broadcastSProdiverThatCompanyLicenseExpired(licenseObj.CompanyName, licenseObj.LCode);

                        //broadcasting to company that company's license has expired
                        _notificationHubContext.Clients.All.onLicenseExpiration(licenseObj.CompanyId, licenseObj.LCode);


                    }
                    if (count == 0)
                    {
                        message = "There is no 'terminable' License.";
                    }

                }

            }

            return new JsonResult
            {
                Data = message,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }


    }


    public interface ISubscriptionServices
    {
        //For Company
        JsonResult RequestLicense(int companyId);
        JsonResult GetLicenseListByCompanyId(int companyId);
        JsonResult RequestForLicenseRenewal(int licenseId);
        JsonResult GetLicenseDetailsByLicenseId(int licenseId);
        JsonResult GetCountOfActiveLicenses();
        JsonResult GetCountOfExpiredLicenses();

        //For SProvider
        JsonResult GetRegisteredCompanyList();
        JsonResult GetSubscribedCompanyList();
        JsonResult ApproveLicense(int licenseId, int deviceLimit, DateTime expiryDate);
        JsonResult ApproveLicenseRenewal(DateTime expiryDate, int licenseId);
        JsonResult GetAllLicenseList();

        //TaskScheduler
        JsonResult TerminateSubscriptionByTaskScheduler();
        JsonResult GetCountOfRegisteredCompanies();
        JsonResult GetCountOfSubscribedCompanies();
    }
}
