﻿/// <reference path="app.js" />
PCSMSApp.controller('subscribedcompanyController', function ($scope, subscribedcompanyServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    //====================================================================Declaration=================================================================================
    $rootScope.managecompanyOpen = true;
    $scope.CompanyDetails = {};



    //====================================================================Element Processing==========================================================================
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [DTColumnDefBuilder.newColumnDef(2).notSortable()
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
        .withOption('paging', true)
        .withOption('searching', true)
        .withOption('info', true);





    //====================================================================DB Operation================================================================================
    subscribedcompanyServices.GetSubscribedCompanyList().then(function (response) {
        $scope.SubscribedCompanyList = response.data;
        angular.forEach(response.data, function (value, key) {
            if (value.RegistrationDate != null) {
                var regDateUtc = moment.utc(value.RegistrationDate);
                value.RegistrationDate = regDateUtc.local().format("DD-MM-YYYY hh:mm:ss a").toString();
            }
        });
    });

    $scope.GetCompanyProfileDetails = function (companyId) {
        subscribedcompanyServices.GetCompanyProfileDetails(companyId).then(function(response) {
                $scope.CompanyDetails = response.data;
                if ($scope.CompanyDetails.RegistrationDate != null) {
                    var expDateUtc = moment.utc($scope.CompanyDetails.RegistrationDate);
                    $scope.CompanyDetails.RegistrationDate = expDateUtc.local().format("DD-MM-YYYY hh:mm:ss a").toString();
                }
            })
            .then(function() {
                $('#CompanyProfileDetailsModal').modal('show');
            });
    };





    //====================================================================Modal Operation=============================================================================
    $scope.cancelCompanyProfileDetailsModal = function () {
        $('#CompanyProfileDetailsModal').modal('hide');
        $timeout(function() {
            $scope.CompanyDetails = {};
        }, 200);
    };



});