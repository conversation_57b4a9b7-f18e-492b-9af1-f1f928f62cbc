﻿

1] Create a project 
2] Package Manager Console> install-package Microsoft.AspNet.SignalR

3] Create a folder called Hubs (any name can be given)
4] Inside Hubs folder, create a SignalR Hub Class (v2) (while creating class look for signalR Hub class, easy peasy)
   Name the class MyHub, It will be automatically be inherited from Hub class.
   Now, main things to understand:
5] You will see the following inside "MyHub" class:

	    public class MyHub : Hub
		{
			public void Hello()
			{
				Clients.All.hello();
			}
		}

6] Change It to the following:

		[HubName("myHub")]
		public class MyHub : Hub
		{
			public void TellMe()
			{
				Clients.All.iWillAnnouncethisToEverybody();
			}
		}

Note :  a) [HubName("myHub")] is needed because, when we reference MyHub class in the angular/client side, only camel case is accepted;
		b) When "TellMe()" method will be invoked by client side, the server side will fire "iWillAnnouncethisToEverybody()" method. 

7] Look for "Startup" class, if you have then ok, if not then you can add Startup class in two ways
   i)In Solution Explorer, right-click the project, then click Add | OWIN Startup Class. Name the new class Startup and click OK.
   ii)Or Create a simple class called "Startup" and replace your code with following code:

		using Microsoft.Owin;
		using Owin;
		[assembly: OwinStartup(typeof(TestSignalR.Startup))]         //<------Your namespace can be different of course
		namespace TestSignalR.Hubs                                   //<------Your namespace can be different of course
		{
			public class Startup
			{
				public void Configuration(IAppBuilder app)
				{
					// Any connection or hub wire up and configuration should go here
					app.MapSignalR();
				}
			}
		}
		
Note: a)If you have "Startup" class already, then you will find "ConfigureAuth(app);" inside. Don't worry , keep it and go ahead.


8] Now, add these references in html file:
			
			<script src="Scripts/jquery-3.1.1.min.js" ></script>                //drap down the jQuery that got downloaded along with signalR, otherwise it might give you hard time 
			
			<script src="Scripts/jquery.signalR-2.2.1.min.js"></script>         //drag down your signalR version from script file
			
			<script src="../signalr/hubs"></script>                             //don't forget the ../ before signalR, because it refers domain, or use your current localhost directly(e.g. http://localhost:xxxxx//signalr/hubssignalr/hubs)
		

9] Now, it's time to work on controller of client side:

			
			a)step 1:
			-----------
			//the following is giving me current number of divisions:
			DivisionServices.GetCountOfDivision().then(function (response) {
				$scope.CountOfDivisionCreated = response.data;
			});


			// Declare a proxy to reference the hub.
			var x = $.connection.myHub;                                                //did you see myHub ??


			// Create a function that the hub can call to broadcast messages.
			x.client.iWillAnnouncethisToEverybody = function (msg) {                   //did you see iWillAnnouncethisToEverybody() ??
				
				DivisionServices.GetCountOfDivision().then(function (response) {
					$scope.CountOfDivisionCreated = response.data;
				});
			};
    
			$.connection.hub.start().done(function () {
				console.log("connection established");
			});

			a)step 2:
			-----------
			$scope.SaveOrUpdateDivision = function () {
			
			DivisionServices.SaveDivision($scope.Division).then(function (response) {
                    
                    if (response.data.IsReport === "Ok") {
                                           
						// Declare a proxy to reference the hub.
                        x.server.tellMe("a-new-row-created");                           //did you see tellMe() ??
                        
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "DivisionNameExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                })
			
			}

NOTE: Question is how this works. A story can explain this complexity. 
Imagine, I have some divisions created (let's say 10). Now, we can show it on html page by calling a method GetCountOfDivision() method and keep it to a variable
named "$scope.CountOfDivisionCreated" and then show it to html page. right?

Now, if any new division is created or deleted by different window/browser, i want the division number to be changed real-time. How can i achieve that?
Right after creating a division (through SaveDivision() method), we can call TellMe() method, In above case, it needs an string argument. As soon as TellMe() method is 
called it will call iWillAnnouncethisToEverybody() method. Inside iWillAnnouncethisToEverybody(), cleverly i am calling GetCountOfDivision() method which will update the 
variable "$scope.CountOfDivisionCreated". This is how i achieve my goal.


NOTE:
any client method starting with "on" (e.g. onLicenseChnage etc) needs to add this after:
$.connection.hub.start().done(function () {
				console.log("connection established");
			});


check user: halter73 said that
https://stackoverflow.com/questions/16064651/the-on-event-on-the-signalr-client-hub-does-not-get-called

you can also check:
check this following link when signalR not firing in client side
https://docs.microsoft.com/en-us/aspnet/signalr/overview/testing-and-debugging/troubleshooting#calling-methods-between-the-client-and-server-silently-fails
