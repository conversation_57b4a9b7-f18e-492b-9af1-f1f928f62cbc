﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web.Mvc;
using PCSMS.Common;
using PCSMS.Models;
using PCSMS.Models.Models_Shared;
using PCSMS.Models.Models_SProvider;

namespace PCSMS.Services.Services_Shared
{
    public class AccessTypeServices: IAccessTypeServices
    {
        private readonly PCSMSDbContext _context;
        private readonly IEntityService<AccessType> _services;
        public AccessTypeServices()
        {
            _context = new PCSMSDbContext();
            _services = new EntityService<AccessType>(_context);
        }

        //Private Services :
        private bool AccessTypeExist(AccessType obj)
        {
            var result = _context.AccessType.FirstOrDefault(x => x.AccessTypeName == obj.AccessTypeName && x.Id != obj.Id);

            if (result == null)
            {
                return false;
            }
            return true;
        }


        //Public Services :
        public JsonResult SaveAccessType(AccessType obj)
        {
            string message;
            try
            {
                if (_services.DoesExist(x => x.AccessTypeName == obj.AccessTypeName))
                {
                    Generator.IsReport = "AccessTypeNameExists";
                    message = "This Access-Type already exists !";
                }
                else
                {
                    _services.Save(obj);
                    _services.SaveChanges();

                    Generator.IsReport = "Ok";
                    message = "Access-Type created successfully !";
                }
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult UpdateAccessType(AccessType obj)
        {
            List<object> avoidProperties = new List<object>();
            string message;

            if (AccessTypeExist(obj))
            {
                Generator.IsReport = "AccessTypeNameExists";
                message = "Access-Type : " + "\"" + obj.AccessTypeName + "\"" + " already exists !";

            }
            else
            {
                try
                {
                    _services.Update(obj, avoidProperties);
                    _services.SaveChanges();
                    Generator.IsReport = "Ok";
                    message = "Access-Type updated successfully !";
                }
                catch (Exception ex)
                {
                    message = ex.Message;
                    Generator.IsReport = "NotOk";
                }
            }

            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult DeleteAccessType(int accessTypeId)
        {
            string message;
            try
            {
                _context.Database.ExecuteSqlCommand($"Delete [AccessType] WHERE Id = {accessTypeId}");
                _context.SaveChanges();
                Generator.IsReport = "Ok";
                message = "Access-Type deleted successfully !";
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult GetAccessTypeList()
        {
            return new JsonResult
            {
                Data = _context.AccessType.Select(x => new
                {
                    x.Id,
                    x.AccessTypeName
                }).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult GetAccessTypeDetails(Expression<Func<AccessType, bool>> expression)
        {
            return new JsonResult
            {
                Data = _context.AccessType.Where(expression)
                .Select(x => new
                {
                    x.Id,
                    x.AccessTypeName
                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
    }

    public interface IAccessTypeServices
    {
        JsonResult DeleteAccessType(int accessTypeId);
        JsonResult GetAccessTypeDetails(Expression<Func<AccessType, bool>> expression);
        JsonResult GetAccessTypeList();
        JsonResult SaveAccessType(AccessType obj);
        JsonResult UpdateAccessType(AccessType obj);
    }
}
