﻿PCSMSApp.factory("CompanyTypeServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        SaveCompanyType: function (param) {
            return $http({
                url: "/api/CompanyType/SaveCompanyType",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        UpdateCompanyType: function (param) {
            return $http({
                url: "/api/CompanyType/UpdateCompanyType",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        GetCompanyTypeList: function () {
            return $http({
                url: "/api/CompanyType/GetCompanyTypeList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCompanyTypeDetails: function (id) {
            return $http({
                url: "/api/CompanyType/GetCompanyTypeDetails/" + id,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DeleteCompanyType: function (id) {
            return $http({
                url: "/api/CompanyType/DeleteCompanyType/" + id,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        }
    };
}]);