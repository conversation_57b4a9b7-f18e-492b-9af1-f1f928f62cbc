﻿PCSMSApp.factory("groupScheduleServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        AddGroupScheduleSetting: function ($scope) {
            return $http({
                url: "/api/Device/AddGroupScheduleSetting/",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: {
                    groupObj: $scope.Schedule,
                    deviceObjs : $scope.SelectedDeviceArray
                },
                async: false
            });
        },
        UpdateGroupScheduleSetting: function ($scope) {
            return $http({
                url: "/api/Device/UpdateGroupScheduleSetting/",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: {
                    groupObj: $scope.Schedule,
                    groupedDeviceObjs: $scope.SelectedDeviceArray
                },
                async: false
            });
        },
        GetGroupListByCompanyId: function (companyId) {
            return $http({
                url: "/Api/Device/GetGroupListByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetGroupDetailsByGroupId: function (groupId) {
            return $http({
                url: "/Api/Device/GetGroupDetailsByGroupId/" + groupId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        }, 
        DeleteGroup: function (groupId) {
            return $http({
                url: "/Api/Device/DeleteGroup/" + groupId,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetNonGroupedDeviceListByLicenseId: function (licenseId) {
            return $http({
                url: "/Api/Device/GetNonGroupedDeviceListByLicenseId/" + licenseId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
    };
}]);