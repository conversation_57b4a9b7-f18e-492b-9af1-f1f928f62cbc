﻿/// <reference path="app.js" />
PCSMSApp.controller('userController', function ($scope, userServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    //====================================================================Declaration=================================================================================
    $scope.UserList = [];
    $scope.User = {};
    var currentDate = new Date();
    $scope.MaxDate = currentDate.setDate(currentDate.getDate() - 1);
    $scope.MinDate = currentDate.setYear(currentDate.getFullYear() - 90);
    $scope.HasImage = false;




    //====================================================================Element Processing==========================================================================
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [DTColumnDefBuilder.newColumnDef(3).notSortable()
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
        .withOption('paging', true)
        .withOption('searching', true)
        .withOption('info', true);
    //.withOption('order', [1, 'desc']);




    //====================================================================Object Processing===========================================================================
    var formdata = new FormData();
    $scope.getTheFiles = function ($files) {
        formdata.append(0, $files[0]);
        $scope.HasImage = true;
    };




    //====================================================================Modal Operation=============================================================================
    $scope.openInviteNewUserModal = function () {
        $('#InviteNewUserModal').modal('show');
    };
    $scope.cancelInviteNewUserModal = function () {
        $('#InviteNewUserModal').modal('hide');
        $timeout(function () {
            $scope.User = {};
            $scope.HasImage = false;
            $('#PhotoRemover').trigger('click');
        }, 200);
        
        $scope.InviteNewUserForm.$setPristine();
    }




    //====================================================================DB Operation================================================================================
    userServices.GetCP_UserListByCompanyId($rootScope.CompanyId).then(function (response) {
        $scope.UserList = response.data;
    });

    $scope.InviteNewUser = function () {
        $scope.User.CompanyId = $rootScope.CompanyId;
        userServices.InviteNewUser($scope).then(function (response) {
            if (response.data.IsReport == "Ok") {
                toastr.success(response.data.Message, "Success!")
            }
            else if (response.data.IsReport == "NotOk") {
                toastr.error(response.data.Message, "Error!")
            }
        })
        .then(function () {
            $('#InviteNewUserModal').modal('hide');
        })
        .then(function () {
            $timeout(function () {
                $state.reload();
            }, 300)
        })
    };

    $scope.GetCP_UserDetails = function (userId) {
        userServices.GetCP_UserDetails(userId).then(function (response) {
            $scope.User = response.data;
        })
        .then(function () {
            console.log($scope.User)
            $('#UserDetailsModal').modal('show');
        })
    };

    $scope.cancelUserDetailsModal = function () {
        $('#UserDetailsModal').modal('hide');
        $timeout(function () {
            $scope.User = {};
            $scope.HasImage = false;
            $('#PhotoRemover').trigger('click');
        }, 200)
    }

    //Deleting:
    $scope.deleteUserAlert = function (user) {
        var gText1 = "his";
        var gText2 = "him";
        if (user.Gender == "Female") {
            gText1 = "her";
            gText2 = "her";
        }
        swal({
            title: "Are you sure?",
            text: "You are going to delete '" + user.FirstName + " " + user.LastName + "' and all " + gText1 + " associated data.",
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn-danger",
            confirmButtonText: "Yes, delete " + gText2 ,
            closeOnConfirm: true
        },
        function () {
            //console.log(UserId + ' ' + UserName);
            userServices.DeleteCP_User(user.Id, $rootScope.CompanyId).then(function (response) {
                //console.log(response.data);
                if (response.data.IsReport === "Ok") {
                    toastr.success(response.data.Message, 'Successful');
                }
                else if (response.data.IsReport === "NotOk") {
                    toastr.error(response.data.Message, 'Failed');
                }
                $state.reload();
            });

        });
    };

    //Opening a modal:
    $scope.openUserModal = function (id) {

        if (id != null) {
            //blockUI.start();
            //blockUI.message("Loading User Details ...");
            userServices.GetCP_UserDetails(id).then(function (response) {
                $scope.User = response.data;
                $scope.TempUsername = response.data.Username;
                if ($scope.User.Photo != null) {
                    $('#imageHolder').prepend('<img id="imageTag" src="" />');
                    $('#imageTag').attr("src", "../SProvider_Images/User_Images/" + $scope.User.Photo);
                    $scope.src = "/SProvider_Images/User_Images/" + $scope.User.Photo;
                    $scope.HasImage = true;
                    //imageName = response.data.CompanyLogo ;
                }
                else if ($scope.User.Photo == null) {
                    $scope.src = "/SProvider_Images/Default_Images/Image-not-found.png";
                }
            })
                .then(function () {
                    //blockUI.stop();
                    $('#InviteNewUserModal').modal('show');
                });
        }
    };

    $scope.UpdateUser = function() {
        userServices.UpdateCP_UserProfile($scope).then(function (response) {
            //console.log(response.data);
            if (response.data.IsReport === "Ok") {
                toastr.success(response.data.Message, 'Successful');
            }
            else if (response.data.IsReport === "NotOk") {
                toastr.error(response.data.Message, 'Failed');
            }
            
        })
            .then(function () {
                $('#InviteNewUserModal').modal('hide');
                $timeout(function() {
                    $state.reload();
                },200)
            });;
    }


    //====================================================================Garbage Code================================================================================






});