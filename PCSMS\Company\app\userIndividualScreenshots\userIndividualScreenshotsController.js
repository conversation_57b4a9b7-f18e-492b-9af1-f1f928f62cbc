﻿
PCSMSApp.controller('userIndividualScreenshotsController', function ($scope, userIndividualScreenshotsServices, screenshotsServices, userServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    //====================================================================Declaration=================================================================================
  
    $scope.Tracking = {};
    $scope.Loader = false;

    
    //Getting userlist for searchable dropdown
    userIndividualScreenshotsServices.GetCP_UserListByCompanyId($rootScope.CompanyId).then(function (response) {
        $scope.ActiveCP_EmployeeList = response.data;
        console.log(response.data);
    });

    //Main Search
    $scope.AdjustLocationSettings = function () {

        //By Today/Yesterday/Week
        if ($scope.Tracking.ShortDayPicker != undefined || $scope.Tracking.ShortDayPicker != null) {
            $scope.Loader = true;
            userIndividualScreenshotsServices
                .GetScreenshotsListForSpecificSessionByUserId($scope.Tracking.SearchEmployee.Id, $scope.Tracking.ShortDayPicker).then(
                    function (response) {
                        //first, retrieving result from db:
                        $scope.ScreenshotsList = response.data;
                        console.log(response.data);
                        //now converting datetime to local:
                        convertDatabaseUtcDatetimeToLocalPcTime(response.data);

                        //now checking if there are any screenshots available to download:
                        $scope.ScreenshotDownloadable = isAnyScreenshotDownloadable(response.data);
                        if (response.data.length < 1) {
                            toastr.info("No screenshot is found !", "Info !");
                        }
                    })
                .then(function () {
                    $scope.Loader = false;
                });

        }
        //By CustomDateRange
        if (($scope.Tracking.DateAndTimeFormObj != null || $scope.Tracking.DateAndTimeFormObj != undefined) &&
            ($scope.Tracking.DateAndTimeToObj != null || $scope.Tracking.DateAndTimeToObj != undefined)) {
            $scope.Loader = true;
            userIndividualScreenshotsServices.GetScreenshotsListByCustomRangeByUserId($scope.Tracking.SearchEmployee.Id,
                    moment($scope.Tracking.DateAndTimeFormObj).format("YYYY-MM-DD HH:mm:ss.sss"),
                    moment($scope.Tracking.DateAndTimeToObj).format("YYYY-MM-DD HH:mm:ss.sss")).then(function (response) {

                        //first, retrieving result from db:
                        $scope.ScreenshotsList = response.data;
                        console.log(response.data);
                        //now converting datetime to local:
                        convertDatabaseUtcDatetimeToLocalPcTime(response.data);

                        //now checking if there are any screenshots available to download:
                        $scope.ScreenshotDownloadable = isAnyScreenshotDownloadable(response.data);
                        if (response.data.length < 1) {
                            toastr.info("No screenshot is found !", "Info !");
                        }
                    })
                .then(function () {
                    $scope.Loader = false;
                });

        }

        if (($scope.Tracking.ShortDayPicker == undefined || $scope.Tracking.ShortDayPicker == null) &&
            ($scope.Tracking.DateAndTimeFormObj == null || $scope.Tracking.DateAndTimeFormObj == undefined)) {
            toastr.error("Please select at least one session type. (e.g.:'Today')", "Error!");
        }
    };
    
    //Load More Button Search
    $scope.LoadMoreScreenshots = function (deviceUniqueId, lastId) {
        
        
        //By Today/Yesterday/Week
        if ($scope.Tracking.ShortDayPicker != undefined || $scope.Tracking.ShortDayPicker != null) {
            $scope.Loader = true;
            userIndividualScreenshotsServices
                .GetScreenshotsListForSpecificSessionByUserIdAndLastId($scope.Tracking.SearchEmployee.Id, $scope.Tracking.ShortDayPicker, lastId).then(
                    function (response) {
                        $scope.NewSShots = response.data;
                        console.log($scope.NewSShots);
                        if ($scope.NewSShots == null || $scope.NewSShots.length < 1) {
                            toastr.info("No more screenshots found within this time range !", "Info !");
                        } else {
                            //now converting datetime to local:
                            convertDatabaseUtcDatetimeToLocalPcTime(response.data);

                            //now checking if there are any screenshots available to download:
                            $scope.ScreenshotDownloadable = isAnyScreenshotDownloadable(response.data);

                            angular.forEach($scope.NewSShots, function (val, key) {
                                //pushing into array
                                $scope.ScreenshotsList.push(val);
                            });
                        }
                    })
                .then(function () {
                    $scope.Loader = false;
                });

        }


        //By CustomDateRange
        if (($scope.Tracking.DateAndTimeFormObj != null || $scope.Tracking.DateAndTimeFormObj != undefined) &&
            ($scope.Tracking.DateAndTimeToObj != null || $scope.Tracking.DateAndTimeToObj != undefined)) {
            $scope.Loader = true;
            userIndividualScreenshotsServices.GetScreenshotsListByCustomRangeByUserIdAndLastId($scope.Tracking.SearchEmployee.Id,
                    moment($scope.Tracking.DateAndTimeFormObj).format("YYYY-MM-DD HH:mm:ss.sss"),
                    moment($scope.Tracking.DateAndTimeToObj).format("YYYY-MM-DD HH:mm:ss.sss"), lastId).then(function (response) {

                        $scope.NewSShots = response.data;
                        console.log($scope.NewSShots);
                        if ($scope.NewSShots == null || $scope.NewSShots.length < 1) {
                            toastr.info("No more screenshots found within this time range !", "Info !");
                        } else {
                            //now converting datetime to local:
                            convertDatabaseUtcDatetimeToLocalPcTime(response.data);

                            //now checking if there are any screenshots available to download:
                            $scope.ScreenshotDownloadable = isAnyScreenshotDownloadable(response.data);
                            
                     
                            angular.forEach($scope.NewSShots, function (val, key) {
                                //pushing into array
                                $scope.ScreenshotsList.push(val);
                            });
                        }
                    })
                .then(function () {
                    $scope.Loader = false;
                });

        }

        if (($scope.Tracking.ShortDayPicker == undefined || $scope.Tracking.ShortDayPicker == null) &&
            ($scope.Tracking.DateAndTimeFormObj == null || $scope.Tracking.DateAndTimeFormObj == undefined)) {
            toastr.error("Please select at least one session type. (e.g.:'Today')", "Error!");
        }

    }
    
    //Resetting
    $scope.AdjustLocationSettingsOnlyReset = function () {
        $scope.Tracking.ShortDayPicker = null;
        $scope.Tracking.DateAndTimeForm = null;
        $scope.Tracking.DateAndTimeFormObj = null;
        $scope.Tracking.DateAndTimeTo = null;
        $scope.Tracking.DateAndTimeToObj = null;
        $scope.AdjustLocationSettingsForm.$setPristine();
        //toastr.info("Refreshed From Adjust Session!", "Message");
    };
    $scope.AdjustLocationSettingsReset = function () {
        userIndividualScreenshotsServices.GetCP_UserListByCompanyId($rootScope.CompanyId).then(function (response) {
            $scope.ActiveCP_EmployeeList = response.data;
            console.log(response.data);
        })
        .then(function () {
            $scope.Tracking.SearchEmployee = undefined;
            $scope.Tracking.ShortDayPicker = null;
            $scope.Tracking.DateAndTimeForm = null;
            $scope.Tracking.DateAndTimeFormObj = null;
            $scope.Tracking.DateAndTimeTo = null;
            $scope.Tracking.DateAndTimeToObj = null;
            $scope.AdjustLocationSettingsForm.$setPristine();
        });
    };
    
    //Modal for each screenshot
    $scope.openFilePreviewModal = function (obj) {

        //console.log(obj.Url);
        $timeout(function () {
            //if (obj.Photo != null) {
            //    $scope.imgSrc = "../Company_Images/User_Images/" + obj.Photo;
            //} else {
            //    if (obj.Gender == 'Male') {
            //        $scope.imgSrc = "../Company_Images/Default_Images/male.png";
            //    }
            //    else if (obj.Gender == 'Female') {
            //        $scope.imgSrc = "../Company_Images/Default_Images/female.jpg";
            //    }
            //}


            $scope.file = {
                Url: obj.Url,
                CapturedOn: obj.CapturedOn,
                MouseClick: obj.MouseClick,
                KeyStroke: obj.KeyStroke,
                ScreenShot: obj.ScreenShot,
                UserFullName: obj.FirstName + " " + obj.LastName,
                Designation: obj.Designation,
                UserPhotoUrl: obj.UserPhotoUrl,
                Gender: obj.Gender,
                DeviceUniqueId: obj.DeviceUniqueId,
                DeviceName: obj.DeviceName,
                Status: obj.Status,
            }
        },
            200);

        $('#FilePreviewModal').modal('show');
    };
    $scope.cancelFilePreviewModal = function () {
        $('#FilePreviewModal').modal('hide');
        $timeout(function () {
            $scope.file = null;
        },
            200);
    };
    
    //Downloading Screenshots:
    $scope.ScreenshotsDownloadable = false;
    $scope.DownloadScreenshot = function (obj) {
        if (obj) {
            var link = document.createElement('a');
            link.href = obj.Url; // use realtive Url 
            link.download = obj.ScreenShot;
            document.body.appendChild(link);
            link.click();
        } else {
            toastr.error("No screenshot found for download !", "Error!");
        }

    }
    $scope.DownloadAllScreenshot = function (ScreenshotsList) {

        //console.log(ScreenshotsList);

        //Now i don't want deleted image to be downloaded; so i am creating a variable named obj and 
        //pushing only real screenshots to it
        $scope.downloadableScreenshotsArray = [];
        angular.forEach(ScreenshotsList,
            function (value, key) {
                if (value.Status != "Deleted") {
                    $scope.downloadableScreenshotsArray.push(value);
                }
            });
        console.log($scope.downloadableScreenshotsArray);
        screenshotsServices.DownloadAllScreenShots($scope.downloadableScreenshotsArray).then(function (response) {

            console.log(response.data);
        })
            .then(function () {
                $scope.Loader = false;
            });

    }


    //Deleting Screenshot:
    $scope.DeleteScreenshot = function (obj) {
        if (obj) {
            swal({
                title: "Are You Sure ?",
                text: "You can not undo this action once the screenshot gets deleted !",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn btn-success",
                confirmButtonText: "Yes",
                cancelButtonClass: "btn btn-default",
                cancelButtonText: "No",
                closeOnConfirm: true
            },
                function (isConfirm) {
                    if (isConfirm) {
                        screenshotsServices.DeleteScreenshot(obj.Id).then(function (response) {
                            if (response.data.IsReport == "Ok") {
                                toastr.success(response.data.Message, "Success");
                                $scope.AdjustLocationSettings();

                            } else if (response.data.IsReport == "NotOk") {
                                toastr.error(response.data.Message, "Error!");
                            }
                        })
                            .then(function () {
                                var result = $scope.GetScreenShotsForDevice();
                                $scope.ScreenshotDownloadable = isAnyScreenshotDownloadable(result);
                            });
                    }
                });

        } else {
            toastr.error("No screenshot found for deletation !", "Error!");
        }
    }


    //=========================================DATE TIME RELATED===================================
    //Prevent Date and Time to be taken

    $('#ShortDayPickerForm').on('keypress', function (e) {
        e.preventDefault();
    });

    $('#ShortDayPickerTo').on('keypress', function (e) {
        e.preventDefault();
    });

    $('#SearchEmployee').on('keypress', function (e) {
        if ($scope.AccordionOpen == false) {
            e.preventDefault();
        }
    });

    //Datetime Picker Composing
    $scope.isOpenFrom = false;
    $scope.isOpenTo = false;

    $scope.openCalendarFrom = function (e) {
        e.preventDefault();
        e.stopPropagation();

        $scope.isOpenFrom = true;
        $scope.isOpenTo = false;
    };
    $scope.openCalendarTo = function (e) {
        e.preventDefault();
        e.stopPropagation();

        $scope.isOpenTo = true;
        $scope.isOpenFrom = false;
    };

    $scope.DateAndTimeFormObj = {
        date: new Date(),
        datepickerOptions: {
            showWeeks: false,
            maxDate: new Date()
        }
    }
    $scope.DateAndTimeToObj = {
        date: new Date(),
        datepickerOptions: {
            showWeeks: false,
            maxDate: new Date()
        }
    }

    $scope.TrackingDateAndTimeFormObjClosed = function (args) {
        $scope.DateAndTimeToObj = {
            date: new Date(),
            datepickerOptions: {
                showWeeks: false,
                minDate: args.closeDate,
                maxDate: new Date()
            }
        }
        console.log(args.closeDate);
    }
    
    //=========================================UTILITY FUNCTIONS===================================
    function isAnyScreenshotDownloadable(obj) {
        var keepGoing = true;
        var result = false;
        var count = 0;
        angular.forEach(obj,
            function (value, key) {
                if (keepGoing) {
                    if (value.Status == null) {
                        count = count + 1;
                        if (count > 1) {

                            keepGoing = false;
                            result = true;
                        }
                    }
                }
            });
        return result;
    }
    function convertDatabaseUtcDatetimeToLocalPcTime(obj) {
        angular.forEach(obj,
            function (value, key) {
                if (value.CapturedOn != null) {
                    var capDateUtc = moment.utc(value.CapturedOn);
                    value.CapturedOn = capDateUtc.local().format("DD-MM-YYYY hh:mm:ss a").toString();
                }

            });
    }
});