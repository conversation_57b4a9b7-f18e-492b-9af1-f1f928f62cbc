{"version": 3, "file": "angular-touch.min.js", "lineCount": 13, "mappings": "A;;;;;aAKC,SAAQ,CAACA,CAAD,CAASC,CAAT,CAAkB,CAyC3BC,QAASA,EAAc,CAACC,CAAD,CAAWC,CAAX,CAA6B,CAoBlD,IAAIC,EAAyB,CAAA,CAA7B,CACIC,EAAwB,CAAA,CAC5B,KAAAD,uBAAA,CAA8BE,QAAQ,CAACC,CAAD,CAAU,CAC9C,MAAIP,EAAAQ,UAAA,CAAkBD,CAAlB,CAAJ,EAEMA,CA6BG,EA7BSF,CAAAA,CA6BT,GA5BLA,CAMA,CANwB,CAAA,CAMxB,CAHAI,CAAAC,aAGA,CAH4C,SAG5C,CAFAP,CAAAQ,UAAA,CAA2B,SAA3B,CAAsCF,CAAtC,CAEA,CAAAP,CAAAU,UAAA,CAAmB,kBAAnB,CAAuC,CAAC,WAAD,CAAc,QAAQ,CAACC,CAAD,CAAY,CACvE,GAAIT,CAAJ,CAEES,CAAAC,MAAA,EAFF,KAOE,KADA,IAAIC,EAAIF,CAAAG,OAAJD,CAAuB,CAC3B,CAAY,CAAZ,EAAOA,CAAP,CAAA,CAAe,CACb,GAAkC,SAAlC,GAAIF,CAAA,CAAUE,CAAV,CAAAL,aAAJ,CAA6C,CAC3CG,CAAAI,OAAA,CAAiBF,CAAjB,CAAoB,CAApB,CACA,MAF2C,CAI7CA,CAAA,EALa,CASjB,MAAOF,EAjBgE,CAAlC,CAAvC,CAsBK,EADPT,CACO,CADkBG,CAClB,CAAA,IA/BT,EAkCOH,CAnCuC,CA+ChD,KAAAc,KAAA,CAAYC,QAAQ,EAAG,CACrB,MAAO,CAULf,uBAAwBA,QAAQ,EAAG,CACjC,MAAOA,EAD0B,CAV9B,CADc,CArE2B,CAgnBpDgB,QAASA,EAAkB,CAACC,CAAD,CAAgBC,CAAhB,CAA2BC,CAA3B,CAAsC,CAC/DC,CAAAb,UAAA,CAAkBU,CAAlB,CAAiC,CAAC,QAAD,CAAW,QAAX,CAAqB,QAAQ,CAACI,CAAD;AAASC,CAAT,CAAiB,CAQ7E,MAAO,SAAQ,CAACC,CAAD,CAAQC,CAAR,CAAiBC,CAAjB,CAAuB,CAKpCC,QAASA,EAAU,CAACC,CAAD,CAAS,CAS1B,GAAKC,CAAAA,CAAL,CAAkB,MAAO,CAAA,CACzB,KAAIC,EAASC,IAAAC,IAAA,CAASJ,CAAAK,EAAT,CAAoBJ,CAAAI,EAApB,CACTC,EAAAA,EAAUN,CAAAO,EAAVD,CAAqBL,CAAAM,EAArBD,EAAsCf,CAC1C,OAAOiB,EAAP,EAvBwBC,EAuBxB,CACIP,CADJ,EAEa,CAFb,CAEII,CAFJ,EAnB0BI,EAmB1B,CAGIJ,CAHJ,EArBqBK,EAqBrB,CAIIT,CAJJ,CAIaI,CAhBa,CAJ5B,IAAIM,EAAelB,CAAA,CAAOI,CAAA,CAAKR,CAAL,CAAP,CAAnB,CAEIW,CAFJ,CAEiBO,CAFjB,CAuBIK,EAAe,CAAC,OAAD,CACd5C,EAAAQ,UAAA,CAAkBqB,CAAA,oBAAlB,CAAL,EACEe,CAAAC,KAAA,CAAkB,OAAlB,CAEFnB,EAAAoB,KAAA,CAAYlB,CAAZ,CAAqB,CACnB,MAASmB,QAAQ,CAAChB,CAAD,CAASiB,CAAT,CAAgB,CAC/BhB,CAAA,CAAcD,CACdQ,EAAA,CAAQ,CAAA,CAFuB,CADd,CAKnB,OAAUU,QAAQ,CAACD,CAAD,CAAQ,CACxBT,CAAA,CAAQ,CAAA,CADgB,CALP,CAQnB,IAAOW,QAAQ,CAACnB,CAAD,CAASiB,CAAT,CAAgB,CACzBlB,CAAA,CAAWC,CAAX,CAAJ,EACEJ,CAAAwB,OAAA,CAAa,QAAQ,EAAG,CACtBvB,CAAAwB,eAAA,CAAuB7B,CAAvB,CACAoB,EAAA,CAAahB,CAAb,CAAoB,CAAC0B,OAAQL,CAAT,CAApB,CAFsB,CAAxB,CAF2B,CARZ,CAArB,CAgBGJ,CAhBH,CA5BoC,CARuC,CAA9C,CAAjC,CAD+D,CAhoBjE,IAAIpB,EAAUxB,CAAAsD,OAAA,CAAe,SAAf,CAA0B,EAA1B,CAEd9B,EAAA+B,SAAA,CAAiB,QAAjB,CAA2BtD,CAA3B,CAaAA,EAAAuD,QAAA,CAAyB,CAAC,UAAD,CAAa,kBAAb,CA6GzBhC,EAAAiC,QAAA,CAAgB,QAAhB,CAA0B,CAAC,QAAQ,EAAG,CAwBpCC,QAASA,EAAc,CAACV,CAAD,CAAQ,CACzBW,CAAAA;AAAgBX,CAAAW,cAAhBA,EAAuCX,CAC3C,KAAIY,EAAUD,CAAAC,QAAA,EAAyBD,CAAAC,QAAA5C,OAAzB,CAAwD2C,CAAAC,QAAxD,CAAgF,CAACD,CAAD,CAC1FE,EAAAA,CAAKF,CAAAG,eAALD,EAAqCF,CAAAG,eAAA,CAA6B,CAA7B,CAArCD,EAAyED,CAAA,CAAQ,CAAR,CAE7E,OAAO,CACLtB,EAAGuB,CAAAE,QADE,CAEL3B,EAAGyB,CAAAG,QAFE,CALsB,CAW/BC,QAASA,EAAS,CAACrB,CAAD,CAAesB,CAAf,CAA0B,CAC1C,IAAIC,EAAM,EACVnE,EAAAoE,QAAA,CAAgBxB,CAAhB,CAA8B,QAAQ,CAACyB,CAAD,CAAc,CAElD,CADI9C,CACJ,CADgB+C,CAAA,CAAeD,CAAf,CAAA,CAA4BH,CAA5B,CAChB,GACEC,CAAAtB,KAAA,CAAStB,CAAT,CAHgD,CAApD,CAMA,OAAO4C,EAAAI,KAAA,CAAS,GAAT,CARmC,CA/B5C,IAAID,EAAiB,CACnB,MAAS,CACPvB,MAAO,WADA,CAEPyB,KAAM,WAFC,CAGPtB,IAAK,SAHE,CADU,CAMnB,MAAS,CACPH,MAAO,YADA,CAEPyB,KAAM,WAFC,CAGPtB,IAAK,UAHE,CAIPD,OAAQ,aAJD,CANU,CAYnB,QAAW,CACTF,MAAO,aADE,CAETyB,KAAM,aAFG,CAGTtB,IAAK,WAHI,CAITD,OAAQ,eAJC,CAZQ,CA0CrB,OAAO,CAkCLH,KAAMA,QAAQ,CAAClB,CAAD,CAAU6C,CAAV,CAAyB7B,CAAzB,CAAuC,CAAA,IAE/C8B,CAF+C;AAEvCC,CAFuC,CAI/C3C,CAJ+C,CAM/C4C,CAN+C,CAQ/CC,EAAS,CAAA,CAEbjC,EAAA,CAAeA,CAAf,EAA+B,CAAC,OAAD,CAAU,OAAV,CAAmB,SAAnB,CAC/BhB,EAAAkD,GAAA,CAAWb,CAAA,CAAUrB,CAAV,CAAwB,OAAxB,CAAX,CAA6C,QAAQ,CAACI,CAAD,CAAQ,CAC3DhB,CAAA,CAAc0B,CAAA,CAAeV,CAAf,CACd6B,EAAA,CAAS,CAAA,CAETF,EAAA,CADAD,CACA,CADS,CAETE,EAAA,CAAU5C,CACVyC,EAAA,MAAA,EAA0BA,CAAA,MAAA,CAAuBzC,CAAvB,CAAoCgB,CAApC,CANiC,CAA7D,CAQA,KAAI+B,EAASd,CAAA,CAAUrB,CAAV,CAAwB,QAAxB,CACb,IAAImC,CAAJ,CACEnD,CAAAkD,GAAA,CAAWC,CAAX,CAAmB,QAAQ,CAAC/B,CAAD,CAAQ,CACjC6B,CAAA,CAAS,CAAA,CACTJ,EAAA,OAAA,EAA2BA,CAAA,OAAA,CAAwBzB,CAAxB,CAFM,CAAnC,CAMFpB,EAAAkD,GAAA,CAAWb,CAAA,CAAUrB,CAAV,CAAwB,MAAxB,CAAX,CAA4C,QAAQ,CAACI,CAAD,CAAQ,CAC1D,GAAK6B,CAAL,EAQK7C,CARL,CAQA,CACA,IAAID,EAAS2B,CAAA,CAAeV,CAAf,CAEb0B,EAAA,EAAUxC,IAAAC,IAAA,CAASJ,CAAAO,EAAT,CAAoBsC,CAAAtC,EAApB,CACVqC,EAAA,EAAUzC,IAAAC,IAAA,CAASJ,CAAAK,EAAT,CAAoBwC,CAAAxC,EAApB,CAEVwC,EAAA,CAAU7C,CAxHSiD,GA0HnB,CAAIN,CAAJ,EA1HmBM,EA0HnB,CAAmCL,CAAnC,GAKIA,CAAJ,CAAaD,CAAb,EAEEG,CACA,CADS,CAAA,CACT,CAAAJ,CAAA,OAAA,EAA2BA,CAAA,OAAA,CAAwBzB,CAAxB,CAH7B,GAOEA,CAAAiC,eAAA,EACA,CAAAR,CAAA,KAAA,EAAyBA,CAAA,KAAA,CAAsB1C,CAAtB,CAA8BiB,CAA9B,CAR3B,CALA,CARA,CAT0D,CAA5D,CAkCApB,EAAAkD,GAAA,CAAWb,CAAA,CAAUrB,CAAV,CAAwB,KAAxB,CAAX,CAA2C,QAAQ,CAACI,CAAD,CAAQ,CACpD6B,CAAL,GACAA,CACA,CADS,CAAA,CACT,CAAAJ,CAAA,IAAA,EAAwBA,CAAA,IAAA,CAAqBf,CAAA,CAAeV,CAAf,CAArB,CAA4CA,CAA5C,CAFxB,CADyD,CAA3D,CA7DmD,CAlChD,CA9C6B,CAAZ,CAA1B,CAuMA,KAAIvC,EAA+B,CAAC,QAAD,CAAW,UAAX,CAAuB,cAAvB,CAC/B,QAAQ,CAACgB,CAAD,CAASyD,CAAT,CAAmBC,CAAnB,CAAiC,CA2D3CC,QAASA,EAAqB,CAACC,CAAD;AAAmB/C,CAAnB,CAAsBF,CAAtB,CAAyB,CACrD,IAAS,IAAArB,EAAI,CAAb,CAAgBA,CAAhB,CAAoBsE,CAAArE,OAApB,CAA6CD,CAA7C,EAAkD,CAAlD,CAAqD,CACtB,IAAA,EAAAsE,CAAA,CAAiBtE,CAAjB,CAAqB,CAArB,CAAA,CAA4BqB,EAAAA,CAAzD,IAzDwBkD,EAyDxB,CARKpD,IAAAC,IAAA,CAQGkD,CAAAE,CAAiBxE,CAAjBwE,CARH,CAQiDjD,CARjD,CAQL,EAzDwBgD,EAyDxB,CARkDpD,IAAAC,IAAA,CAASqD,CAAT,CAAcC,CAAd,CAQlD,CAEE,MADAJ,EAAApE,OAAA,CAAwBF,CAAxB,CAA2BA,CAA3B,CAA+B,CAA/B,CACO,CAAA,CAAA,CAH0C,CAMrD,MAAO,CAAA,CAP8C,CAYvD2E,QAASA,EAAO,CAAC1C,CAAD,CAAQ,CACtB,GAAI,EArEiB2C,IAqEjB,CAAAC,IAAAC,IAAA,EAAA,CAAaC,CAAb,CAAJ,CAAA,CAIA,IAAIlC,EAAUZ,CAAAY,QAAA,EAAiBZ,CAAAY,QAAA5C,OAAjB,CAAwCgC,CAAAY,QAAxC,CAAwD,CAACZ,CAAD,CAAtE,CACIV,EAAIsB,CAAA,CAAQ,CAAR,CAAAG,QADR,CAEI3B,EAAIwB,CAAA,CAAQ,CAAR,CAAAI,QAKR,IAAI,EAAI,CAAJ,CAAA1B,CAAA,EAAa,CAAb,CAASF,CAAT,EAGA2D,CAHA,EAIAA,CAAA,CAA0B,CAA1B,CAJA,GAIiCzD,CAJjC,EAIsCyD,CAAA,CAA0B,CAA1B,CAJtC,GAIuE3D,CAJvE,CAAJ,CAGA,CAKI2D,CAAJ,GACEA,CADF,CAC8B,IAD9B,CAIcC,KAAAA,EAAAhD,CAAAgD,OAAkB,QAAhC,GA9ZKhG,CAAAiG,UAAA,CAAkBrE,CAAAsE,SAAlB,EAAuCtE,CAAA,CAAQ,CAAR,CAAvC,EAAqDA,CAAA,CAAQ,CAAR,CAAAsE,SAArD,CA8ZL,GACEH,CADF,CAC8B,CAACzD,CAAD,CAAIF,CAAJ,CAD9B,CAOIgD,EAAA,CAAsBC,CAAtB,CAAwC/C,CAAxC,CAA2CF,CAA3C,CAAJ,GAKAY,CAAAmD,gBAAA,EAIA,CAHAnD,CAAAiC,eAAA,EAGA,CAAAjC,CAAAgD,OAAA,EAAgBhD,CAAAgD,OAAAI,KAAhB,EAAqCpD,CAAAgD,OAAAI,KAAA,EATrC,CAhBA,CAdA,CADsB,CA8CxBC,QAASA,EAAY,CAACrD,CAAD,CAAQ,CACvBY,CAAAA,CAAUZ,CAAAY,QAAA,EAAiBZ,CAAAY,QAAA5C,OAAjB;AAAwCgC,CAAAY,QAAxC,CAAwD,CAACZ,CAAD,CACtE,KAAIV,EAAIsB,CAAA,CAAQ,CAAR,CAAAG,QAAR,CACI3B,EAAIwB,CAAA,CAAQ,CAAR,CAAAI,QACRqB,EAAAxC,KAAA,CAAsBP,CAAtB,CAAyBF,CAAzB,CAEA8C,EAAA,CAAS,QAAQ,EAAG,CAElB,IAAS,IAAAnE,EAAI,CAAb,CAAgBA,CAAhB,CAAoBsE,CAAArE,OAApB,CAA6CD,CAA7C,EAAkD,CAAlD,CACE,GAAIsE,CAAA,CAAiBtE,CAAjB,CAAJ,EAA2BuB,CAA3B,EAAgC+C,CAAA,CAAiBtE,CAAjB,CAAqB,CAArB,CAAhC,EAA2DqB,CAA3D,CAA8D,CAC5DiD,CAAApE,OAAA,CAAwBF,CAAxB,CAA2BA,CAA3B,CAA+B,CAA/B,CACA,MAF4D,CAH9C,CAApB,CAxHqB4E,IAwHrB,CAQqB,CAAA,CARrB,CAN2B,CA9G7B,IAAIG,CAAJ,CACIT,CADJ,CAEIU,CA4IJ,OAAO,SAAQ,CAACpE,CAAD,CAAQC,CAAR,CAAiBC,CAAjB,CAAuB,CAAA,IAChCyE,EAAe7E,CAAA,CAAOI,CAAA0E,QAAP,CADiB,CAEhCC,EAAU,CAAA,CAFsB,CAGhCC,CAHgC,CAIhCC,CAJgC,CAKhCC,CALgC,CAMhCC,CAOJhF,EAAAkD,GAAA,CAAW,YAAX,CAAyB,QAAQ,CAAC9B,CAAD,CAAQ,CACvCwD,CAAA,CAAU,CAAA,CACVC,EAAA,CAAazD,CAAAgD,OAAA,CAAehD,CAAAgD,OAAf,CAA8BhD,CAAA6D,WAEhB,EAA3B,EAAIJ,CAAAK,SAAJ,GACEL,CADF,CACeA,CAAAM,WADf,CAIAnF,EAAAoF,SAAA,CApKoBC,iBAoKpB,CAEAP,EAAA,CAAYd,IAAAC,IAAA,EAGRlC,EAAAA,CAAgBX,CAAAW,cAAhBA,EAAuCX,CAEvCa,EAAAA,CAAI,CADMF,CAAAC,QAAAA,EAAyBD,CAAAC,QAAA5C,OAAzB4C,CAAwDD,CAAAC,QAAxDA,CAAgF,CAACD,CAAD,CACtF,EAAQ,CAAR,CACRgD,EAAA,CAAc9C,CAAAE,QACd6C,EAAA,CAAc/C,CAAAG,QAjByB,CAAzC,CAoBApC,EAAAkD,GAAA,CAAW,aAAX,CAA0B,QAAQ,CAAC9B,CAAD,CAAQ,CAxBxCwD,CAAA,CAAU,CAAA,CACV5E,EAAAsF,YAAA,CAzJoBD,iBAyJpB,CAuBwC,CAA1C,CAIArF;CAAAkD,GAAA,CAAW,UAAX,CAAuB,QAAQ,CAAC9B,CAAD,CAAQ,CACrC,IAAImE,EAAOvB,IAAAC,IAAA,EAAPsB,CAAoBT,CAAxB,CAGI/C,EAAgBX,CAAAW,cAAhBA,EAAuCX,CAH3C,CAOIa,EAAI,CAHOF,CAAAG,eAADF,EAAiCD,CAAAG,eAAA9C,OAAjC4C,CACVD,CAAAG,eADUF,CAERD,CAAAC,QAAD,EAA0BD,CAAAC,QAAA5C,OAA1B,CAA0D2C,CAAAC,QAA1D,CAAkF,CAACD,CAAD,CAC/E,EAAQ,CAAR,CAPR,CAQIrB,EAAIuB,CAAAE,QARR,CASI3B,EAAIyB,CAAAG,QATR,CAUIoD,EAAOlF,IAAAmF,KAAA,CAAUnF,IAAAoF,IAAA,CAAShF,CAAT,CAAaqE,CAAb,CAA0B,CAA1B,CAAV,CAAyCzE,IAAAoF,IAAA,CAASlF,CAAT,CAAawE,CAAb,CAA0B,CAA1B,CAAzC,CAEPJ,EAAJ,EAtMee,GAsMf,CAAeJ,CAAf,EArMiBK,EAqMjB,CAAsCJ,CAAtC,GA9DG/B,CAyED,GAxEFF,CAAA,CAAa,CAAb,CAAAsC,iBAAA,CAAiC,OAAjC,CAA0C/B,CAA1C,CAAmD,CAAA,CAAnD,CAEA,CADAP,CAAA,CAAa,CAAb,CAAAsC,iBAAA,CAAiC,YAAjC,CAA+CpB,CAA/C,CAA6D,CAAA,CAA7D,CACA,CAAAhB,CAAA,CAAmB,EAsEjB,EAnEJS,CAmEI,CAnEgBF,IAAAC,IAAA,EAmEhB,CAjEJT,CAAA,CAAsBC,CAAtB,CAwDsB/C,CAxDtB,CAwDyBF,CAxDzB,CAiEI,CAJIqE,CAIJ,EAHEA,CAAAL,KAAA,EAGF,CAAKpG,CAAAQ,UAAA,CAAkBqB,CAAA6F,SAAlB,CAAL,EAA2D,CAAA,CAA3D,GAAyC7F,CAAA6F,SAAzC,EACE9F,CAAAwB,eAAA,CAAuB,OAAvB,CAAgC,CAACJ,CAAD,CAAhC,CAZJ,CAzCAwD,EAAA,CAAU,CAAA,CACV5E,EAAAsF,YAAA,CAzJoBD,iBAyJpB,CA2BqC,CAAvC,CAkCArF,EAAA+F,QAAA;AAAkBC,QAAQ,CAAC5E,CAAD,CAAQ,EAQlCpB,EAAAkD,GAAA,CAAW,OAAX,CAAoB,QAAQ,CAAC9B,CAAD,CAAQ6E,CAAR,CAAkB,CAC5ClG,CAAAwB,OAAA,CAAa,QAAQ,EAAG,CACtBmD,CAAA,CAAa3E,CAAb,CAAoB,CAAC0B,OAASwE,CAATxE,EAAqBL,CAAtB,CAApB,CADsB,CAAxB,CAD4C,CAA9C,CAMApB,EAAAkD,GAAA,CAAW,WAAX,CAAwB,QAAQ,CAAC9B,CAAD,CAAQ,CACtCpB,CAAAoF,SAAA,CArOoBC,iBAqOpB,CADsC,CAAxC,CAIArF,EAAAkD,GAAA,CAAW,mBAAX,CAAgC,QAAQ,CAAC9B,CAAD,CAAQ,CAC9CpB,CAAAsF,YAAA,CAzOoBD,iBAyOpB,CAD8C,CAAhD,CAzFoC,CArJK,CADV,CAwXnC7F,EAAA,CAAmB,aAAnB,CAAmC,EAAnC,CAAsC,WAAtC,CACAA,EAAA,CAAmB,cAAnB,CAAmC,CAAnC,CAAsC,YAAtC,CArtB2B,CAA1B,CAAD,CAytBGrB,MAztBH,CAytBWA,MAAAC,QAztBX;", "sources": ["angular-touch.js"], "names": ["window", "angular", "$TouchProvider", "$provide", "$compileProvider", "ngClickOverrideEnabled", "ngClickDirectiveAdded", "this.ngClickOverrideEnabled", "enabled", "isDefined", "ngTouchClickDirectiveFactory", "$$moduleName", "directive", "decorator", "$delegate", "shift", "i", "length", "splice", "$get", "this.$get", "makeSwipeDirective", "directiveName", "direction", "eventName", "ngTouch", "$parse", "$swipe", "scope", "element", "attr", "validSwipe", "coords", "startCoords", "deltaY", "Math", "abs", "y", "deltaX", "x", "valid", "MAX_VERTICAL_DISTANCE", "MIN_HORIZONTAL_DISTANCE", "MAX_VERTICAL_RATIO", "swi<PERSON><PERSON><PERSON><PERSON>", "pointerTypes", "push", "bind", "start", "event", "cancel", "end", "$apply", "<PERSON><PERSON><PERSON><PERSON>", "$event", "module", "provider", "$inject", "factory", "getCoordinates", "originalEvent", "touches", "e", "changedTouches", "clientX", "clientY", "getEvents", "eventType", "res", "for<PERSON>ach", "pointerType", "POINTER_EVENTS", "join", "move", "eventHandlers", "totalX", "totalY", "lastPos", "active", "on", "events", "MOVE_BUFFER_RADIUS", "preventDefault", "$timeout", "$rootElement", "checkAllowableRegions", "touchCoordinates", "CLICKBUSTER_THRESHOLD", "x1", "y1", "y2", "onClick", "PREVENT_DURATION", "Date", "now", "lastPreventedTime", "lastLabelClickCoordinates", "target", "lowercase", "nodeName", "stopPropagation", "blur", "onTouchStart", "clickHandler", "ngClick", "tapping", "tapElement", "startTime", "touchStartX", "touchStartY", "srcElement", "nodeType", "parentNode", "addClass", "ACTIVE_CLASS_NAME", "removeClass", "diff", "dist", "sqrt", "pow", "TAP_DURATION", "MOVE_TOLERANCE", "addEventListener", "disabled", "onclick", "element.onclick", "touchend"]}