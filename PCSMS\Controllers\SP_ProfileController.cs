﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using PCSMS.Filters;
using PCSMS.Models.Models_SProvider;
using PCSMS.Services.Services_SProvider;

namespace PCSMS.Controllers
{
    [AuthorizationRequired]
    [RoutePrefix("Api/SP_Profile")]
    public class SP_ProfileController : ApiController
    {
        private readonly ISP_ProfileServices _services;
        public SP_ProfileController()
        {
            _services = new SP_ProfileServices();
        }

        [Route("SaveSP_Profile")]
        [HttpPost]
        public IHttpActionResult SaveSP_Profile(SP_Profile obj)
        {
            return Ok(_services.SaveSP_Profile(obj).Data);
        }


        [Route("UpdateSP_Profile")]
        [HttpPost]
        public IHttpActionResult UpdateSP_Profile(SP_Profile SP_Profile)
        {
            return Ok(_services.UpdateSP_Profile(SP_Profile).Data);
        }


        [Route("GetSP_ProfileDetails")]
        [HttpGet]
        public IHttpActionResult GetSP_ProfileDetails()
        {
            return Ok(_services.GetSP_ProfileDetails().Data);
        }


        [Route("UploadSP_Logo")]
        [HttpPost]
        public IHttpActionResult UploadSP_Logo()
        {
            return Ok(_services.UploadSP_Logo().Data);
        }


    }
}
