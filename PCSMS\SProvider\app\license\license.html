﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>


<!-- CONTENT -->
<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-key" aria-hidden="true"></i><a ui-sref="license">License</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-content">
                        <div class="panel-header">
                        </div>
                        <div class="panel-content">
                            <div class="table-responsive">
                                <!-- DataTable -->
                                <table class="data-table table table-striped nowrap table-bordered table-condensed" id="basic-table" datatable="ng" dt-options="vm.dtOptions" dt-instance="vm.dtInstance" dt-column-defs="vm.dtColumnDefs" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>License Code</th>
                                            <th>Company Name</th>
                                            <th>Requested On</th>
                                            <th>Expiry Date</th>
                                            <th>License Type</th>
                                            <th>Status</th>
                                            <th class="custom-datatabel-action-th">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="x in LicenseList">
                                            <td>{{x.LCode}}</td>
                                            <td>{{x.CompanyName}}</td>
                                            <td>{{ x.RequestedOn}}</td>
                                            <td>{{ x.ExpiryDate}}</td>
                                            <td>{{x.LicenseType}}</td>
                                            <td>
                                                <span class="{{x.Status=='Requested'?'badge x-warning ':x.Status=='Active'?'badge x-success ':x.Status=='In-Active'?'badge ':x.Status=='Expired'?'badge x-danger ':'badge '}}">{{x.Status}}</span>
                                                <span class="badge x-primary">{{x.SecondaryStatus}}</span>
                                            </td>
                                            <td>
                                                <button style="border: 1px solid #02715a; background-color: #ffffff"
                                                        ng-if="x.Status=='Requested' || x.SecondaryStatus=='Renewal Requested' && x.LicenseType!='Trial'"
                                                        type="button" tooltip-placement="top"
                                                        class="btn btn-default"
                                                        ng-click="openApproveOrRejectLicenseModal(x.Id, x.Status, x.SecondaryStatus, x.ExpiryDate, x.LCode);">
                                                    <img src="Assets_SProvider/images/datatables/renew.png" width="20"/>
                                                </button>
                                                <button style="border: 1px solid #02715a; background-color: #ffffff"
                                                        type="button" tooltip-placement="top"
                                                        uib-tooltip="View Details" class="btn btn-default"
                                                        ng-if ="x.Status == 'Active' || x.Status == 'In-Active' || x.Status == 'Expired'"
                                                        ng-click="GetLicenseDetailsByLicenseId(x.Id);">
                                                    <img src="Assets_SProvider/images/datatables/info.png" width="20"/>
                                                </button>
                                             </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>

<form name="ApproveOrRejectLicenseForm" novalidate>
    <div class="modal fade" id="ApproveOrRejectLicenseModal" tabindex="-1" role="dialog" aria-labelledby="modal-label" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" role="document" style="width:30%">
            <div class="modal-content">
                <div class="modal-header" style="height:65px">
                    <button type="button" class="custom-close" ng-click="cancelApproveOrRejectLicenseModal()" style="margin-top:0px;" aria-label="Close" tooltip-placement="left" uib-tooltip="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" style="margin-top:1.6%">Adjust License</h4>
                </div>
                <div class="modal-body">
                    <div class="panel panel-primary panel-bordered">
                        <div class="panel-body">
                            <div class="row">
                                <!--Task Description-->
                                <div class="col-lg-12">
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <label class="col-lg-4 control-label">License Code</label>
                                            <div class="col-lg-7">
                                                <input type="text"  class="form-control" value="{{License.LCode}}" disabled/>
                                            </div>
                                    </div>
                                        <div class="form-horizontal">
                                            <div class="form-group" ng-if="License.Status=='Requested'">
                                                <label class="col-lg-4 control-label">Device Limit<span class="required">*</span></label>
                                                <div class="col-lg-7">
                                                    <div class="input-group">
                                                        <input type="number" min="1" max="300" class="form-control" name="LicenseDeviceLimit"
                                                               ng-required="true"
                                                               ng-model="License.DeviceLimit"
                                                               ng-minlength="1"
                                                               ng-pattern="/^[1-9]\d*$/">
                                                        <span class="input-group-addon">
                                                            Device(s)
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="row custom-row">
                                                    <ul class="list-unstyled errormessage pull-left"
                                                        ng-show="ApproveOrRejectLicenseForm.LicenseDeviceLimit.$dirty && ApproveOrRejectLicenseForm.LicenseDeviceLimit.$invalid">
                                                        <li class="pull-left" ng-show="ApproveOrRejectLicenseForm.LicenseDeviceLimit.$error.required">*Required</li>
                                                        <li class="pull-left" ng-show="ApproveOrRejectLicenseForm.LicenseDeviceLimit.$error.pattern">Not a valid Device Limit</li>
                                                        <li class="pull-left" ng-show="ApproveOrRejectLicenseForm.LicenseDeviceLimit.$error.min">Minimum limit is 1</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div><br />
                                    <div class="form-horizontal">
                                        <div class="form-group">
                                            <label class="col-lg-4 control-label">Expiry Date<span class="required">*</span></label>
                                            <div class="col-lg-7">
                                                <datepicker date-format="yyyy-MM-dd" selector="form-control" date-min-limit="{{DisableUpTo | date}}">
                                                    <div class="input-group">
                                                        <input class="form-control" placeholder="Choose a date" ng-model="License.ExpiryDate" />
                                                        <span class="input-group-addon" style="cursor: pointer">
                                                            <i class="fa fa-lg fa-calendar"></i>
                                                        </span>
                                                    </div>
                                                </datepicker>
                                            </div>
                                            <div class="row custom-row">
                                                <span class="errormessage pull-left"
                                                      ng-show="ApproveOrRejectLicenseForm.ExpiryDate.$dirty && ApproveOrRejectLicenseForm.ExpiryDate.$invalid">
                                                    <span ng-show="ApproveOrRejectLicenseForm.ExpiryDate.$error.required">*Required</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button"
                            class="btn  btn-primary btn-wide pull-right btn-left-border-radius"
                            ng-click="ApproveOrRejectLicense()"
                            ng-disabled="ApproveOrRejectLicenseForm.$invalid || IsDateTimeSelected()==false"
                            role="button">
                        <span ng-if="License.Status=='Requested'">Approve</span><span ng-if="License.SecondaryStatus=='Renewal Requested'">Renew</span> &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    </div>
</form>

<div class="modal fade" id="LicenseDetailsModal" tabindex="-1" role="dialog" aria-labelledby="modal-label" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="height:65px">
                <button type="button" class="custom-close" ng-click="cancelLicenseDetailsModal()" style="margin-top:0px;" aria-label="Close" tooltip-placement="left" uib-tooltip="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" style="margin-top:1.6%">License Details</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-primary panel-bordered">
                    <div class="panel-body">
                        <div class="row">
                            <!--Task Description-->
                            <div class="col-lg-12">
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered table-condensed">
                                        <tbody>
                                            <tr>
                                                <th>Company Name</th>
                                                <td><label class="control-label">{{License.CompanyName}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>License Code</th>
                                                <td><label class="control-label">{{License.LCode}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>License Key</th>
                                                <td><label class="control-label">{{License.LicenseKey}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Status</th>
                                                <td>
                                                    <span class="{{License.Status=='Requested'?'badge x-warning ':License.Status=='Active'?'badge x-success ':License.Status=='In-Active'?'badge ':License.Status=='Expired'?'badge x-danger ':'badge '}}">{{License.Status}}</span>
                                                    <span class="badge x-primary">{{License.SecondaryStatus}}</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Requested On</th>
                                                <td><label class="control-label">{{License.RequestedOn}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Activated On</th>
                                                <td><label class="control-label">{{License.ActivatedOn}}</label></td>
                                            </tr>
                                            <tr ng-hide="License.Status == 'In-Active'">
                                                <th>Expiry Date</th>
                                                <td><label class="control-label">{{License.ExpiryDate}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>License Type</th>
                                                <td><label class="control-label">{{License.LicenseType}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Device Limit</th>
                                                <td><label class="control-label">{{License.DeviceLimit}}</label></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button"
                        class="btn  btn-default btn-wide pull-right btn-left-border-radius"
                        ng-click="cancelLicenseDetailsModal()"
                        role="button">
                    Close &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                </button>
            </div>
        </div>
    </div>
</div>
