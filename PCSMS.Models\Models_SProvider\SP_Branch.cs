﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PCSMS.Models.Common;
using PCSMS.Models.Models_SProvider;

namespace PCSMS.Models.Models_SProvider
{
    public class SP_Branch : Entity<int>
    {
        [ForeignKey("SP_ProfileId")]
        public virtual SP_Profile SP_Profile { get; set; }
        public int? SP_ProfileId { get; set; }

        [MaxLength(50)]
        [Required]
        public string BranchName { get; set; }

        public virtual ICollection<SP_User> SP_Users { get; set; }
    }
}
