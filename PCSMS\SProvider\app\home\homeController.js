﻿/// <reference path="app.js" />
PCSMSApp.controller('homeController', function ($scope, homeServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {

    homeServices.GetCountOfActiveAndBlockedSP_Users().then(function (response) {
        $scope.CountOfActiveUsers = response.data.CountOfActiveUsers;
        $scope.CountOfBlockedUsers = response.data.CountOfBlockedUsers;

    });
    homeServices.GetCountOfRegisteredCompanies().then(function (response) {
        $scope.countOfRegisteredCompanies = response.data;
    });
    homeServices.GetCountOfSubscribedCompanies().then(function (response) {
        $scope.countOfSubscribedCompanies = response.data;
    });


    homeServices.GetCountOfActiveLicenses().then(function (response) {
        $scope.countOfActiveLicenses = response.data;
    });
    homeServices.GetCountOfExpiredLicenses().then(function (response) {
        $scope.countOfExpiredLicenses = response.data;
    });


    //Here it it being checked if any company's license is getting expired or not through following:

    homeServices.TerminateSubscriptionByTaskScheduler().then(function (response) {
        console.log(response.data);
    });

});