﻿using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using PCSMS.Filters;
using PCSMS.Models.Models_Company;
using PCSMS.Models.Models_SProvider;
using PCSMS.Services.Services_Company;
using PCSMS.Services.Services_Shared;
using PCSMS.Services.Services_SProvider;

namespace PCSMS.Controllers
{
    [AuthenticationFilter]
    [RoutePrefix("api/Authenticate")]
    public class AuthenticateController : ApiController
    {
        private readonly ICP_TokenServices _tokenServices;
        private readonly ICP_ProfileServices _companyServices;
        private readonly ISP_UserServices _SProviderServices;

        public AuthenticateController()
        {
            _tokenServices = new TokenServices();
            _companyServices = new CP_ProfileServices();
            _SProviderServices = new SP_UserServices();
        }

        [Route("LogIn")]
        [HttpPost]
        public HttpResponseMessage LogIn()
        {
            if (System.Threading.Thread.CurrentPrincipal != null && System.Threading.Thread.CurrentPrincipal.Identity.IsAuthenticated)
            {
                var basicAuthenticationIdentity = System.Threading.Thread.CurrentPrincipal.Identity as BasicIdentity;
                if (basicAuthenticationIdentity != null)
                {
                    var companyId = basicAuthenticationIdentity.CompanyId;
                    var SProviderUserId = basicAuthenticationIdentity.SProviderUserId;
                    
                    return GetAuthToken(companyId, SProviderUserId);
                }
            }
            return null;
        }


        private HttpResponseMessage GetAuthToken(int companyId, int SProviderUserId)
        {
            CP_Token tokenCP = new CP_Token();
            SP_Token tokenSProvider = new SP_Token();

            var response = Request.CreateResponse();
            if (SProviderUserId > 0)
            {
                tokenSProvider = _tokenServices.GenerateSProviderPCSMSToken(SProviderUserId);
                response = Request.CreateResponse(HttpStatusCode.OK, new
                {
                    Success = true,
                    Token = tokenSProvider.AuthToken,
                    SProviderUserDetails = _SProviderServices.GetSP_UserDetails(SProviderUserId).Data,
                });
                return response;
            }
            else if (companyId > 0)
            {
                tokenCP = _tokenServices.GenerateCompanyPCSMSToken(companyId);
                response = Request.CreateResponse(HttpStatusCode.OK, new
                {
                    Success = true,
                    Token = tokenCP.AuthToken,
                    CompanyDetails = _companyServices.GetCompanyDetailsForCookies(companyId).Data,
                });
                return response;
            }
            else
            {
                response = Request.CreateResponse(HttpStatusCode.OK, new
                {
                    Success = false,
                    Token = "",
                    UserInfo = ""
                });
            }

            return response;
        }
    }
}

