﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics.Eventing.Reader;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Net.Mail;
using System.Text.RegularExpressions;
using System.Web.Mvc;
using PCSMS.Common;
using PCSMS.Models;
using PCSMS.Models.Models_Company;
using PCSMS.Models.Models_SProvider;
using PCSMS.Services.Services_Shared;

namespace PCSMS.Services.Services_Company
{
    public class CP_UserServices : ICP_UserServices
    {
        private readonly PCSMSDbContext _context;
        private readonly IEntityService<CP_User> _userServices;

        private readonly IEntityService<CP_User_LSession> _cp_User_LSessionServices;
        public CP_UserServices()
        {
            _context = new PCSMSDbContext();
            _userServices = new EntityService<CP_User>(_context);
            _cp_User_LSessionServices = new EntityService<CP_User_LSession>(_context);
        }


        //Private methods

        private bool ExistingPasswordMatches(int userId, string extPassword)
        {
            var result = _context.CP_User.FirstOrDefault(x => x.Password == extPassword && x.Id == userId);

            if (result == null)
            {
                return false;
            }
            return true;
        }
        private bool InvitedUserEmailExist(string email, int companyId)
        {
            var result = _context.CP_User.FirstOrDefault(x => x.Email == email && x.CompanyId == companyId);

            if (result == null)
            {
                return false;
            }
            return true;
        }
        private string SendMailToNewUserWithLoginCredentials(string empName, string email, string password, string companyCode)
        {
            var message = "";

            string senderEmailAddress = "<EMAIL>";
            string senderDetails = "PCSMS - User Login Credentials";

            MailMessage mail = new MailMessage();
            mail.From = new MailAddress(senderEmailAddress, senderDetails);
            mail.To.Add(new MailAddress(email));

            mail.Subject = "Credentials to log into PCSMS app.";
            string Body = "Hello, " + empName + " !<br/><br/>" +
                          "You have been registered for PCSMS (PC Screen Monitoring System)<br/><br/> " +
                          "Please, click the link below and download the desktop app. In case, the link is not clickable, then copy the link and paste it on any browser. It will download automatically<br/><br/> " +
                          "Link:  <br/> " +
                          ConfigurationManager.AppSettings["downloadUrl"] + " <br/><br/> " +
                          "Here are your login credentials :<br/><br/> " +
                          "Username : " + email + " <br/> " +
                          "Password : " + password + " <br/>" +
                          "Company Code : " + companyCode + " <br/><br/><br/> " +
                          "NOTE: After installing the desktop app, you will be asked this above company code. <br/> " +
                          "You may want to change your credentials after first login. <br/><br/><br/><br/> " +
                          "Thank you <br/> " +
                          "PCSMS (PC Screen Monitoring System) TEAM <br/> ";

            mail.Body = Body;
            mail.IsBodyHtml = true;
            mail.BodyEncoding = System.Text.Encoding.UTF8;
            mail.SubjectEncoding = System.Text.Encoding.Default;

            SmtpClient client = new SmtpClient();
            client.Host = "smtp.gmail.com";
            client.Port = 587;
            client.EnableSsl = true;
            client.Timeout = 10000;
            client.UseDefaultCredentials = false;
            client.Credentials = new NetworkCredential("<EMAIL>", "Infinity@207");

            try
            {
                client.Send(mail);
                message = "Mail sending Successful !";
                Generator.IsReport = "Ok";
            }
            catch (Exception ex)
            {
                message = ex.Message;
                Generator.IsReport = "NotOk";
            }

            return Generator.IsReport;
        }
        private void SendMailForForgotPassword(string empName, string email, string password)
        {
            string senderEmailAddress = "<EMAIL>";
            string senderDetails = "PCSMS - Forgot Password";

            MailMessage mail = new MailMessage();
            mail.From = new MailAddress(senderEmailAddress, senderDetails);
            mail.To.Add(new MailAddress(email));

            mail.Subject = "Forgotten Password Or Username";
            string Body = "Hello, " + empName + " !<br/><br/>" +
            "You have requested for log in credentials ! <br/><br/> " +
            "Here are your login credentials :<br /><br/> " +
            "User Id : " + email + " <br/> " +
            "Password : " + password;
            mail.Body = Body;
            mail.IsBodyHtml = true;
            mail.BodyEncoding = System.Text.Encoding.UTF8;
            mail.SubjectEncoding = System.Text.Encoding.Default;

            SmtpClient client = new SmtpClient();
            client.Host = "smtp.gmail.com";
            client.Port = 587;
            client.EnableSsl = true;
            client.Timeout = 10000;
            client.UseDefaultCredentials = false;
            client.Credentials = new NetworkCredential("<EMAIL>", "Infinity@207");

            try
            {
                client.Send(mail);
                Generator.IsReport = "Ok";
            }
            catch
            {
                Generator.IsReport = "NotOk";
            }
        }


        //Company Dashboard methods
        public JsonResult InviteNewUser(CP_User userObj)
        {
            var message = "";

            if (userObj.FirstName == null || userObj.LastName == null || userObj.Email == null || userObj.Designation == null)
            {
                Generator.IsReport = "NotOk";
                message = "Firstname , Lastname , Email , Designation can not be empty !";
            }
            else
            {
                if (InvitedUserEmailExist(userObj.Email, (int)userObj.CompanyId))
                {
                    Generator.IsReport = "NotOk";
                    message = "An user with this email has already been invited !";
                }
                else
                {
                    try
                    {
                        #region step 01: Saving CP_User
                        //Now, saving obj:
                        userObj.Password = "123456";
                        _userServices.Save(userObj);
                        _userServices.SaveChanges();
                        #endregion

                        #region step 04 : Sending mail to user with his/her credentials:

                        var empName = userObj.FirstName + " " + userObj.LastName;
                        var companyCode =
                            _context.CP_Profile.Where(x => x.Id == userObj.CompanyId)
                                .Select(x => x.CompanyCode)
                                .FirstOrDefault();
                        string r = SendMailToNewUserWithLoginCredentials(empName, userObj.Email, userObj.Password, companyCode);
                        #endregion

                        if (r == "Ok")
                        {
                            Generator.IsReport = "Ok";
                            message = "User has been invited successfully ! Shortly, " + userObj.FirstName + " " + userObj.LastName + " will receive an e-mail with login credentials.";
                        }
                        else if (r == "NotOk")
                        {
                            Generator.IsReport = "Ok";
                            message = "User has been invited successfully ! However, e-mail sending to your user failed !";
                        }
                    }
                    catch (Exception ex)
                    {
                        Generator.IsReport = "NotOk";
                        message = ex.Message;
                    }

                }
            }

            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport,
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
            };
        }
        public JsonResult GetCP_UserListByCompanyId(int companyId)
        {
            return new JsonResult
            {
                Data = _context.CP_User.Where(x => x.CompanyId == companyId)
                .Join(_context.CP_Profile,
                x => x.CompanyId,
                y => y.Id,
                (x, y) => new { User = x, Company = y })
                .Select(x => new
                {
                    x.User.Id,
                    x.User.Email,
                    x.User.Password,
                    x.User.FirstName,
                    x.User.LastName,
                    x.User.Address,
                    x.User.BirthDate,
                    x.User.Gender,
                    x.User.Mobile,
                    x.User.Photo,
                    x.User.Designation,
                    x.User.Status,
                    //===============
                    x.Company.CompanyName,
                    FullName = x.User.FirstName + " " + x.User.LastName,

                }).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult GetCP_UserDetails(int userId)
        {
            return new JsonResult
            {
                Data = _context.CP_User.Where(x => x.Id == userId)
                .Join(_context.CP_Profile,
                x => x.CompanyId,
                y => y.Id,
                (x, y) => new { User = x, Company = y })
                .Select(x => new
                {
                    x.User.Id,
                    x.User.Email,
                    x.User.Password,
                    x.User.FirstName,
                    x.User.LastName,
                    x.User.Address,
                    x.User.BirthDate,
                    x.User.Gender,
                    x.User.Mobile,
                    x.User.Photo,
                    x.User.Designation,
                    x.User.Status,
                    //===============
                    x.Company.CompanyName,

                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        

        //home services:
        public JsonResult GetLoggedInUserList(int companyId)
        {
            int[] arrayOfIds = _context.CP_User.Where(x => x.CompanyId == companyId && x.Status == "Active")
                .Select(x => x.Id).ToArray();
            return new JsonResult
            {
                Data = _context.CP_User_LSession.Where(x => arrayOfIds.Contains((int)x.UserId))
                    .Join(_context.CP_User,
                        x => x.UserId,
                        y => y.Id,
                        (x, y) => new { UserLSession = x, User = y })
                    .Join(_context.CP_Device,
                        x => x.UserLSession.DeviceId,
                        y => y.Id,
                        (x, y) => new { x.UserLSession, x.User, Device = y })
                    .Select(x => new
                    {
                        x.User.FirstName,
                        x.User.LastName,
                        UserFullName = x.User.FirstName + " "+x.User.LastName,
                        x.User.Photo,
                        x.User.Designation,
                        x.User.Gender,
                        UserPhotoUrl = (x.User.Photo == null && x.User.Gender == "Male")
                            ? "/Company_Images/Default_Images/male.png"
                            : (x.User.Photo == null && x.User.Gender == "Female")
                                ? "/Company_Images/Default_Images/female.jpg"
                                : "/Company_Images/User_Images/" + x.User.Photo,

                        x.Device.DeviceName,
                        x.Device.DeviceUniqueId,
                    }).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet

            };
        }
        public JsonResult GetCountOfActiveAndBlockedUsersByCompanyId(int companyId)
        {
            var CountOfActiveUsers = _context.CP_User.Count(x => x.CompanyId == companyId && x.Status == "Active");
            var CountOfBlockedUsers = _context.CP_User.Count(x => x.CompanyId == companyId && x.Status == "In-Active");
            return new JsonResult
            {
                Data = new
                {
                    CountOfActiveUsers,
                    CountOfBlockedUsers
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
            };
        }
        public JsonResult GetCountOfRegisteredDevicesByCompanyId(int companyId)
        {
            return new JsonResult
            {
                Data = _context.CP_Device.Where(x => x.CompanyId == companyId).Select(x => x.DeviceName).Distinct().Count(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
            };
        }
        public JsonResult GetCountOfLicensesByCompanyId(int companyId)
        {
            return new JsonResult
            {
                Data = _context.CP_License.Where(x => x.CompanyId == companyId).Select(x => x.Id).Count(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
            };
        }


        //FOR DEVICE USAGE:
        public JsonResult LogIn(string email, string password, string companyCode, string deviceUniqueId)
        {
            var message = "";

            #region companyObj
            //One person can use same email for differnet companies (e.g. Mr X is working for two companies and he wants to use same email for both companies)
            //That's why i need companyCode to find out which company he wants to login for..
            //here, i am getting company Id from provided companyCode to check x.Email == email && x.Password == password && x.CompanyId == companyObj.Id
            var companyObj = _context.CP_Profile.Where(x => x.CompanyCode == companyCode).Select(x => new { x.Id }).FirstOrDefault();
            #endregion



            if (companyObj == null)
            {
                message = "Invalid company-code ! Failed to log in !";
                Generator.IsReport = "NotOk";
            }
            else
            {
                #region deviceObj

                //Now i am getting additional deviceInfo
                var deviceObj = _context.CP_Device.Where(x => x.DeviceUniqueId == deviceUniqueId && x.CompanyId == companyObj.Id)
                    .GroupJoin(_context.CP_Device_Schedule,
                        d => d.DefaultScheduleId,
                        e => e.Id,
                        (d, e) => new { Device = d, DefaultSchedule = e })
                    .SelectMany(d => d.DefaultSchedule.DefaultIfEmpty(),
                        (d, e) => new { d.Device, DefaultSchedule = e })
                    .GroupJoin(_context.CP_Device_Group,
                        d => d.Device.GroupScheduleId,
                        e => e.Id,
                        (d, e) => new { d.Device, d.DefaultSchedule, GroupSchedule = e })
                    .SelectMany(d => d.GroupSchedule.DefaultIfEmpty(),
                        (d, e) => new { d.Device, d.DefaultSchedule, GroupSchedule = e })
                    .Select(d => new
                    {
                        d.Device.Id,
                        d.Device.DeviceName,


                        Settings = d.Device.GroupScheduleId != null ? "Group settings applied" : d.Device.DefaultScheduleId != null ? "Default settings applied" : "Individual settings applied",

                        Interval = d.Device.GroupScheduleId != null ? d.GroupSchedule.Interval : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Interval : d.Device.Interval,
                        IsRandom = d.Device.GroupScheduleId != null ? d.GroupSchedule.IsRandom : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.IsRandom : d.Device.IsRandom,
                        StartTime = d.Device.GroupScheduleId != null ? d.GroupSchedule.StartTime : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.StartTime : d.Device.StartTime,
                        EndTime = d.Device.GroupScheduleId != null ? d.GroupSchedule.EndTime : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.EndTime : d.Device.EndTime,
                        Sat = d.Device.GroupScheduleId != null ? d.GroupSchedule.Sat : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Sat : d.Device.Sat,
                        Sun = d.Device.GroupScheduleId != null ? d.GroupSchedule.Sun : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Sun : d.Device.Sun,
                        Mon = d.Device.GroupScheduleId != null ? d.GroupSchedule.Mon : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Mon : d.Device.Mon,
                        Tues = d.Device.GroupScheduleId != null ? d.GroupSchedule.Tues : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Tues : d.Device.Tues,
                        Wed = d.Device.GroupScheduleId != null ? d.GroupSchedule.Wed : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Wed : d.Device.Wed,
                        Thurs = d.Device.GroupScheduleId != null ? d.GroupSchedule.Thurs : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Thurs : d.Device.Thurs,
                        Fri = d.Device.GroupScheduleId != null ? d.GroupSchedule.Fri : d.Device.DefaultScheduleId != null ? d.DefaultSchedule.Fri : d.Device.Fri,

                    }).FirstOrDefault();
                #endregion

                if (deviceObj != null)
                {
                    #region licenseStatus and Id
                    var licenseStatus = _context.CP_Device_License.Where(x => x.DeviceId == deviceObj.Id).OrderByDescending(x => x.Id).Select(x => x.Status).FirstOrDefault();
                    var licenseId = _context.CP_Device_License.Where(x => x.DeviceId == deviceObj.Id).OrderByDescending(x => x.Id).Select(x => x.LicenseId).FirstOrDefault();
                    #endregion

                    if (licenseStatus == "Active")
                    {
                        #region userObj
                        var userObj = _context.CP_User.Where(x => x.Email == email && x.Password == password && x.CompanyId == companyObj.Id)
                                    .Join(_context.CP_Profile,
                                        x => x.CompanyId,
                                        y => y.Id,
                                        (x, y) => new { User=x, Company = y })
                                    //select new RoleHasRightInfo() { Right = obj, Rolename = role.roleName }
                                    .Select(x => new
                                    {
                                        x.User.Id,
                                        x.User.Email,
                                        x.User.FirstName,
                                        x.User.LastName,
                                        x.User.Address,
                                        x.User.BirthDate,
                                        x.User.Gender,
                                        x.User.Mobile,
                                        x.User.Photo,
                                        x.User.Designation,
                                        x.User.Status,
                                        //===============
                                        x.Company.CompanyName,

                                    }).FirstOrDefault();
                        #endregion

                        if (userObj == null)
                        {
                            //Invalid username or password
                            message = "Invalid username , password or company-code !";
                            Generator.IsReport = "NotOk";
                        }
                        else if (userObj.Status == "In-Active")
                        {
                            //user is in-active/blocked
                            message = "You have been blocked by your company !";
                            Generator.IsReport = "NotOk";
                        }
                        else
                        {
                            #region isUserLoggedIntoAnotherDevice || isDeviceEngaged 
                            //bool isUserLoggedIntoAnotherDevice = _context.CP_User_LSession.Any(x => x.UserId == userObj.Id);
                            //bool isDeviceEngaged = _context.CP_User_LSession.Any(x => x.DeviceUniqueId == deviceUniqueId);
                            #endregion

                            #region isUserLoggedIntoAnotherDevice
                            //if (isUserLoggedIntoAnotherDevice)
                            //{
                            //    // currently logged in some device
                            //    message = "You are currently logged in some device. Log out and try again !";
                            //    Generator.IsReport = "NotOk";
                            //}
                            #endregion

                            #region isDeviceEngaged
                            //if (isDeviceEngaged)
                            //{
                            //    // another user logged in ; so you won't be able to log in
                            //    message = "Another user logged in ; so you won't be able to log in same device/ PC!";
                            //    Generator.IsReport = "NotOk";
                            //}
                            #endregion

                            #region If everthing goes ok / success !
                            //Now, creating a LSession for this user
                            CP_User_LSession lObj = new CP_User_LSession
                            {
                                UserId = userObj.Id,
                                DeviceUniqueId = deviceUniqueId,
                                DeviceId = deviceObj.Id
                            };
                            _cp_User_LSessionServices.Save(lObj);
                            _cp_User_LSessionServices.SaveChanges();


                            message = "You have successfully logged in !";
                            Generator.IsReport = "Ok";


                            //now getting licenseObj:
                            var licenseObj = _context.CP_License.Where(x => x.Id == licenseId)
                                .Join(_context.CP_License_Period.Where(x => x.Status == "Current"),
                                    x => x.Id,
                                    y => y.LicenseId,
                                    (x, y) => new { License = x, CP_License_Period = y })
                                .Select(x => new
                                {
                                    x.License.Id,
                                    x.License.LicenseKey,
                                    x.License.Status,
                                    x.License.ActivatedOn,
                                    x.CP_License_Period.ExpiryDate,
                                }).FirstOrDefault();

                            return new JsonResult
                            {
                                Data = new
                                {
                                    Generator.IsReport,
                                    Message = message,
                                    Content = new
                                    {
                                        userObj,
                                        deviceObj,
                                        licenseObj
                                    }
                                },
                                JsonRequestBehavior = JsonRequestBehavior.AllowGet
                            };
                            #endregion
                        }
                    }
                    else if (licenseStatus == "In-Active" || licenseStatus == "Expired")
                    {
                        // license key is inactive or expired
                        message = "License key is inactive or expired !";
                        Generator.IsReport = "NotOk";
                    }
                }
                else
                {
                    //invalid device Id
                    message = "This device is not registered under any sort of license (Trial/Paid).";
                    Generator.IsReport = "NotOk";
                }
            }




            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message,
                    Content = (string)null
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult LogOut(int userId)
        {
            string message;

            bool isUserLoggedIn = _context.CP_User_LSession.Any(x => x.UserId == userId);
            if (isUserLoggedIn)
            {
                _context.Database.ExecuteSqlCommand($"Delete [CP_User_LSession] WHERE UserId = {userId}");
                _context.SaveChanges();
                Generator.IsReport = "Ok";
                message = "You have successfully logged out !";
            }
            else
            {
                Generator.IsReport = "Ok";
                message = "Invalid userid ! You are not logged in so can not be logged out!";

            }




            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message,
                    Content = (string)null,
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UpdateCP_UserProfile(CP_User obj)
        {
            string message;
            object userObj = new object();

            if (obj.FirstName == null)
            {
                Generator.IsReport = "NotOk";
                message = "You did not provide your first name. Your profile has NOT been updated !";
            }
            else if (obj.LastName == null)
            {
                Generator.IsReport = "NotOk";
                message = "You did not provide your last name. Your profile has NOT been updated !";
            }
            else if (obj.Gender == null)
            {
                Generator.IsReport = "NotOk";
                message = "You did not provide your gender. Your profile has NOT been updated !";
            }
            else
            {
                try
                {
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_User] SET FirstName = '{obj.FirstName}' , LastName = '{obj.LastName}', Mobile = '{obj.Mobile}', BirthDate = '{obj.BirthDate}', Gender = '{obj.Gender}', Address = '{obj.Address}', Status = '{obj.Status}' WHERE Id = {obj.Id}");
                    _context.SaveChanges();
                    Generator.IsReport = "Ok";
                    message = "Your profile has been updated successfully !";

                    //Getting newly updated user details:
                    userObj = _context.CP_User.Where(x => x.Id == obj.Id)
                            .GroupJoin(_context.CP_Profile,
                                x => x.CompanyId,
                                y => y.Id,
                                (x, y) => new { User = x, CP_Profile = y })
                            .SelectMany(x => x.CP_Profile.DefaultIfEmpty(),
                                (x, y) => new { x.User, Company = y })
                            .Select(x => new
                            {
                                x.User.Id,
                                x.User.Email,
                                x.User.FirstName,
                                x.User.LastName,
                                x.User.Address,
                                x.User.BirthDate,
                                x.User.Gender,
                                x.User.Mobile,
                                x.User.Photo,
                                x.User.Designation,
                                x.User.Status,
                                //===============
                                x.Company.CompanyName,

                            }).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    message = ex.Message;
                    Generator.IsReport = "NotOk";
                }
            }

            return new JsonResult
            {

                Data = new
                {
                    Generator.IsReport,
                    Message = message,
                    Content = userObj,
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult ChangeCP_UserPassword(int userId, string extPassword, string newPassword)
        {
            string message;


            try
            {

                if (extPassword == newPassword)
                {
                    Generator.IsReport = "NotOk";
                    message = "Apparently, your existing password and new password are same, Password NOT updated !";
                }
                else if (ExistingPasswordMatches(userId, extPassword))
                {
                    _context.Database.ExecuteSqlCommand($"UPDATE [CP_User] SET Password ='{newPassword}' WHERE Id = {userId}");
                    _context.SaveChanges();
                    Generator.IsReport = "Ok";
                    message = "Your password has been updated successfully !";
                }
                else
                {
                    Generator.IsReport = "NotOk";
                    message = "Your existing password is incorrect, Password NOT updated !";
                }
            }
            catch (Exception ex)
            {

                Generator.IsReport = "NotOk";
                message = ex.Message;
            }
            string content = null;
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message,
                    Content = content
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult UploadCP_UserPhoto(int userId)
        {
            //Add System.Web to Services References

            string message = "";
            string content = "";
            int iUploadedCnt = 0;


            string myPath = "";
            myPath = System.Web.Hosting.HostingEnvironment.MapPath("~/Company_Images/User_Images/");

            var obj = _context.CP_User.Where(x => x.Id == userId).Select(x => new
            {
                x.Photo,
                x.FirstName
            }).FirstOrDefault();

            var getOldImageName = obj.Photo;
            string trimmedFirstName = Regex.Replace(obj.FirstName, " ", "");

            string randNum = GenericServices.CreateRandomNumberWithLetter();


            System.Web.HttpFileCollection httpFileCollection = System.Web.HttpContext.Current.Request.Files;
            if (httpFileCollection.Count > 0)
            {

                try
                {
                    for (int iCnt = 0; iCnt <= httpFileCollection.Count - 1; iCnt++)
                    {
                        System.Web.HttpPostedFile hpf = httpFileCollection[iCnt];

                        var imaginaryNameWithoutExtension = trimmedFirstName + "_" + userId + "_" + randNum; ;
                        var extension = Path.GetExtension(hpf.FileName);
                        var newImageName = imaginaryNameWithoutExtension + extension;

                        if (extension != ".pdf")
                        {
                            if (getOldImageName != null)
                            {
                                File.Delete(myPath + getOldImageName);
                            }

                            //From mobile sometimes no extension given, so we add up an extension :
                            if (extension.Length < 1)
                            {
                                newImageName = newImageName + ".jpg";
                            }


                            //now moving new image to folder:
                            hpf.SaveAs(myPath + newImageName);
                            _context.Database.ExecuteSqlCommand($"UPDATE [CP_User] SET Photo = '{newImageName}' WHERE Id = {userId}");
                            _context.SaveChanges();


                            iUploadedCnt = iUploadedCnt + 1;


                            content = newImageName;
                            Generator.IsReport = "Ok";
                            message = "Your profile picture uploaded successfully !";

                        }
                        else
                        {
                            content = newImageName;
                            message = "This is not an image type.";
                            Generator.IsReport = "NotOk";
                        }

                    }
                }
                catch (Exception ex)
                {
                    content = null;
                    message = ex.Message;
                    Generator.IsReport = "NotOk";
                }
            }
            else
            {
                content = null;
                Generator.IsReport = "NotOk";
                message = "There is no image to be saved";
            }

            return new JsonResult
            {
                Data = new
                {
                    Message = message,
                    Generator.IsReport,
                    Content = content,
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult ForgotPassword(string email, string companyCode)
        {
            string message;
            try
            {

                //One person can use same email for differnet companies (e.g. Mr X is working for two companies and he wants to use same email for both companies)
                //That's why i need companyCode to find out which company he wants to login for..
                //here, i am getting company Id from provided companyCode to check x.Email == email && x.CompanyId == companyObj.Id
                var companyObj = _context.CP_Profile.Where(x => x.CompanyCode == companyCode).Select(x => new { x.Id }).FirstOrDefault();

                #region step 01 : Getting user's current password by provided e-mail:
                var UserObj = _context.CP_User.Where(x => x.Email == email && x.CompanyId == companyObj.Id)
                    .Join(_context.CP_Profile,
                    x => x.CompanyId,
                    y => y.Id,
                    (x, y) => new { CP_User = x, CP_Profile = y })
                .Select(x => new
                {
                    x.CP_User.FirstName,
                    x.CP_User.LastName,
                    x.CP_User.Email,
                    x.CP_User.Password,

                    x.CP_Profile.CompanyName,
                }).FirstOrDefault();
                #endregion


                if (companyObj == null)
                {
                    message = "Invalid company-code !";
                    Generator.IsReport = "NotOk";
                }
                else if (UserObj == null)
                {
                    message = "The e-mail that you provided does not exist !";
                    Generator.IsReport = "NotOk";
                }
                else
                {


                    Generator.IsReport = "Ok";
                    message = "Shortly, you will receive an e-mail with login credentials. Please check your e-mail.";
                    var empName = UserObj.FirstName + " " + UserObj.LastName;
                    SendMailForForgotPassword(empName, UserObj.Email, UserObj.Password);





                    if (Generator.IsReport == "NotOk")
                    {
                        Generator.IsReport = "NotOk";
                        message = "Mail sending failed with your login credentials. Contact system administrator.";
                    }
                }



            }
            catch (Exception ex)
            {
                Generator.IsReport = "NotOk";
                message = ex.Message;
            }
            string content = null;
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message,
                    Content = content
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
        public JsonResult DeleteCP_User(int userId, int companyId)
        {
            string message;
            try
            {
                int[] primaryIdsOfScreenshots = _context.CP_ScreenCapture.Where(x => x.CompanyId == companyId && x.UserId == userId).Select(x => x.Id).ToArray();

                if (primaryIdsOfScreenshots.Length > 0)
                {
                    #region Step 01: deleting image from folder by primaryKey and CompanyId combination

                    var myPath = System.Web.Hosting.HostingEnvironment.MapPath("~/Company_Images/Screenshots/");

                    foreach (var primaryId in primaryIdsOfScreenshots)
                    {
                        string deleteImageWithThis = primaryId + "_" + companyId + "_";
                        string[] imageNames = Directory.GetFiles(myPath);

                        foreach (string imageName in imageNames)
                        {
                            if (imageName.Contains(deleteImageWithThis))
                            {
                                File.Delete(imageName);
                            }
                        }
                    }

                    #endregion

                    #region Step 2: deleting row from db permanently

                    foreach (var primaryId in primaryIdsOfScreenshots)
                    {
                        _context.Database.ExecuteSqlCommand($"Delete [CP_ScreenCapture] WHERE Id = {primaryId}");
                    }

                    #endregion

                }

                //Clearing CP_LSession
                _context.Database.ExecuteSqlCommand($"Delete [CP_User_LSession] WHERE UserId = {userId}");
                //finally, deleting user from CP_User table:
                _context.Database.ExecuteSqlCommand($"Delete [CP_User] WHERE Id = {userId}");
                _context.SaveChanges();
                Generator.IsReport = "Ok";
                message = "User deleted successfully !";
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

    }

    public interface ICP_UserServices
    {
        //Company Dashboard methods
        JsonResult InviteNewUser(CP_User userObj);
        JsonResult GetCP_UserListByCompanyId(int companyId);
        JsonResult GetCP_UserDetails(int userId);



        //home services:
        JsonResult GetLoggedInUserList(int companyId);
        JsonResult GetCountOfActiveAndBlockedUsersByCompanyId(int companyId);
        JsonResult GetCountOfRegisteredDevicesByCompanyId(int companyId);
        JsonResult GetCountOfLicensesByCompanyId(int companyId);


        //FOR DEVICE USAGE:
        
        JsonResult LogIn(string email, string password, string companyCode, string deviceUniqueId);
        JsonResult LogOut(int userId);
        JsonResult UpdateCP_UserProfile(CP_User obj);
        JsonResult ChangeCP_UserPassword(int userId, string extPassword, string newPassword);
        JsonResult UploadCP_UserPhoto(int userId);
        JsonResult ForgotPassword(string email, string companyCode);
        JsonResult DeleteCP_User(int userId, int companyId);
    }


}
