﻿/// <reference path="app.js" />
PCSMSApp.controller('homeController', function ($scope, homeServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    
    
    homeServices.GetLoggedInUserList($rootScope.CompanyId).then(function (response) {
        $scope.LoggedInUserList = response.data;
    });
    homeServices.GetCountOfActiveAndBlockedUsersByCompanyId($rootScope.CompanyId).then(function (response) {
        $scope.CountOfActiveUsers = response.data.CountOfActiveUsers;
        $scope.CountOfBlockedUsers = response.data.CountOfBlockedUsers;

    });
    homeServices.GetCountOfRegisteredDevicesByCompanyId($rootScope.CompanyId).then(function (response) {
        $scope.GetCountOfRegisteredDevices = response.data;
    });
    homeServices.GetCountOfLicensesByCompanyId($rootScope.CompanyId).then(function (response) {
        $scope.GetCountOfLicenses = response.data;
    });

});