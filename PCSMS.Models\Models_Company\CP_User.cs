﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PCSMS.Models.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PCSMS.Models.Models_Company
{
    public class CP_User : Entity<int>
    {
        [ForeignKey("CompanyId")]
        public virtual CP_Profile CP_Profile { get; set; }
        public int? CompanyId { get; set; }


        //[ForeignKey("LicenseId")]
        //public virtual CP_License CP_License { get; set; }
        //public int? LicenseId { get; set; }



        //[ForeignKey("DeviceId")]
        //public virtual CP_Device CP_Device { get; set; }
        //public int? DeviceId { get; set; }


        //Username is not included; email will be taken as username
        [MaxLength(50)]
        public string Email { get; set; }

        [MaxLength(150)]
        public string Password { get; set; }
        [MaxLength(75)]
        public string FirstName { get; set; }
        [MaxLength(75)]
        public string LastName { get; set; }
        [MaxLength(75)]
        public string Designation { get; set; }
        [MaxLength(500)]
        public string Address { get; set; }
        [MaxLength(50)]
        public string BirthDate { get; set; }
        [MaxLength(50)]
        public string Gender { get; set; }
        [MaxLength(50)]
        public string Mobile { get; set; }
        
        [MaxLength(150)]
        public string Photo { get; set; }

        [MaxLength(10)]
        public string Status { get; set; } 
        //Active 
        //In-Active


    }
}
