﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!--<div class="container">
    What is Lorem Ipsum?
    Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's
     standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make
    a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining
    essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages,
    and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
</div>-->
<!-- CONTENT -->
<!-- ========================================================= -->

<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-dashboard" aria-hidden="true"></i><a ui-sref="user">Manage User</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-content">
                        <div class="panel-header">
                            <button type="button" class="btn btn-default btn-o pull-right btn-addon btn-o-addon" ng-click="openInviteNewUserModal()">
                                <i class="fa fa-user-plus" aria-hidden="true"></i>
                                Invite New User
                            </button>
                        </div>
                        <div class="panel-content">
                            <div class="table-responsive">
                                <!-- DataTable -->
                                <table class="data-table table table-striped nowrap table-bordered table-condensed" id="basic-table" datatable="ng" dt-options="vm.dtOptions" dt-instance="vm.dtInstance" dt-column-defs="vm.dtColumnDefs" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Email</th>
                                            <th>Name</th>
                                            <th>Status</th>
                                            <th class="custom-datatabel-action-th">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr ng-repeat="x in UserList">
                                            <td>{{x.Email}}</td>
                                            <td>{{x.FirstName}} {{x.LastName}}</td>
                                            <!--<td>{{ x.PaymentEntryDate | date: "fullDate"}} {{ x.PaymentEntryDate | date: "shortTime"}}</td>-->
                                            <td><span class="{{x.Status=='Active'?'badge x-success ':'badge '}}">{{x.Status}}</span></td>
                                            <td>
                                                <button style="border:1px solid #02715a; background-color:#ffffff" type="button" tooltip-placement="top" uib-tooltip="View Details" class="btn btn-default" ng-click="GetCP_UserDetails(x.Id);"><img src="Assets_Company/images/datatables/info.png" width="20" /></button>
                                                <!--<button class="btn btn-default btn-Line-Height btn-datatables" tooltip-placement="top" uib-tooltip="Edit" ng-click="openUserModal(x.Id)" style="border:1px solid #02715a; background-color:#ffffff"><img src="Assets_Company/images/datatables/edit.png" width="20" /></button>
                                                <button style="border:1px solid #02715a; background-color:#ffffff" type="button" class="btn btn-default" ng-click="deleteUserAlert(x);"><img src="Assets_Company/images/datatables/delete.png" width="20"/></button>-->
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>


<form name="InviteNewUserForm" novalidate>
    <div class="modal fade" id="InviteNewUserModal" tabindex="-1" role="dialog" aria-labelledby="modal-label" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header" style="height:65px">
                    <button type="button" class="custom-close" ng-click="cancelInviteNewUserModal()" style="margin-top:0px;" aria-label="Close" tooltip-placement="left" uib-tooltip="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ng-if="User.Id == null" style="margin-top:1.6%">User Details</h4>
                    <h4 class="modal-title" ng-if="User.Id != null" style="margin-top:1.6%">Update User</h4>
                </div>
                <div class="modal-body">
                    <div class="panel panel-primary panel-bordered">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="custom-panel custom-panel-theme">
                                        <div class="custom-panel-body" style="min-height:201px;">
                                            <div class="row">
                                                <div class="col-lg-6 separator">
                                                    <!--Email-->
                                                    <div class="form-group-sm">
                                                        <label class="req control-label">Email</label>
                                                        <input type="text" name="Email" class="form-control" placeholder="Enter Email"
                                                               ng-model="User.Email"
                                                               ng-required="true"
                                                               ng-minlength="11"
                                                               ng-maxlength="40"
                                                               ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/' />
                                                        <div class="row custom-row">
                                                            <ul class="list-unstyled text-danger pull-left" ng-show="InviteNewUserForm.Email.$dirty && InviteNewUserForm.Email.$invalid">
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Email.$error.required">*Required</span></li>
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Email.$error.pattern">Not a valid Email.</span></li>
                                                                <li><span class="pull-left" ng-show="!InviteNewUserForm.Email.$error.pattern && InviteNewUserForm.Email.$error.minlength">Minimum length is 11</span></li>
                                                                <li><span class="pull-left" ng-show="!InviteNewUserForm.Email.$error.pattern && InviteNewUserForm.Email.$error.maxlength">Maximum length is 40</span></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <!--First Name-->
                                                    <div class="form-group-sm">
                                                        <label class="req control-label">First Name</label>
                                                        <input type="text" name="FirstName" class="form-control" placeholder="Enter First Name"
                                                               ng-model="User.FirstName"
                                                               ng-required="true"
                                                               ng-minlength="2"
                                                               ng-maxlength="50"
                                                               ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" />
                                                        <div class="row custom-row">
                                                            <ul class="list-unstyled text-danger pull-left" ng-show="InviteNewUserForm.FirstName.$dirty && InviteNewUserForm.FirstName.$invalid">
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.FirstName.$error.required">*Required</span></li>
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.FirstName.$error.pattern">Start with capital letter, don't use special characters.</span></li>
                                                                <li><span class="pull-left" ng-show="!InviteNewUserForm.FirstName.$error.pattern && InviteNewUserForm.FirstName.$error.minlength">Minimum length is 2</span></li>
                                                                <li><span class="pull-left" ng-show="!InviteNewUserForm.FirstName.$error.pattern && InviteNewUserForm.FirstName.$error.maxlength">Maximum length is 50</span></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <!--Last Name-->
                                                    <div class="form-group-sm">
                                                        <label class="req control-label">Last Name</label>
                                                        <input type="text" name="LastName" class="form-control" placeholder="Enter Last Name"
                                                               ng-model="User.LastName"
                                                               ng-required="true"
                                                               ng-minlength="2"
                                                               ng-maxlength="50"
                                                               ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" />
                                                        <div class="row custom-row">
                                                            <ul class="list-unstyled text-danger pull-left" ng-show="InviteNewUserForm.LastName.$dirty && InviteNewUserForm.LastName.$invalid">
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.LastName.$error.required">*Required</span></li>
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.LastName.$error.pattern">Start with capital letter, don't use special characters.</span></li>
                                                                <li><span class="pull-left" ng-show="!InviteNewUserForm.LastName.$error.pattern && InviteNewUserForm.LastName.$error.minlength">Minimum length is 2</span></li>
                                                                <li><span class="pull-left" ng-show="!InviteNewUserForm.LastName.$error.pattern && InviteNewUserForm.LastName.$error.maxlength">Maximum length is 50</span></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <!--Designation-->
                                                    <div class="form-group-sm">
                                                        <label class="req control-label">Designation</label>
                                                        <input type="text" name="Designation" class="form-control" placeholder="Enter Designation"
                                                               ng-model="User.Designation"
                                                               ng-required="true"
                                                               ng-minlength="4"
                                                               ng-maxlength="40"
                                                               ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" />
                                                        <div class="row custom-row">
                                                            <ul class="list-unstyled text-danger pull-left" ng-show="InviteNewUserForm.Designation.$dirty && InviteNewUserForm.Designation.$invalid">
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Designation.$error.required">*Required</span></li>
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Designation.$error.pattern">Start with capital letter, don't use special characters.</span></li>
                                                                <li><span class="pull-left" ng-show="!InviteNewUserForm.Designation.$error.pattern && InviteNewUserForm.Designation.$error.minlength">Minimum length is 4</span></li>
                                                                <li><span class="pull-left" ng-show="!InviteNewUserForm.Designation.$error.pattern && InviteNewUserForm.Designation.$error.maxlength">Maximum length is 40</span></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <!--Gender-->
                                                    <div class="form-group-sm">
                                                        <label>Gender</label>
                                                        <select class="form-control" name="Gender" required
                                                                ng-model="User.Gender">
                                                            <option value="">Select</option>
                                                            <option value="Male">Male</option>
                                                            <option value="Female">Female</option>
                                                        </select>
                                                        <div class="row custom-row">
                                                            <ul class="list-unstyled text-danger pull-left" ng-show="InviteNewUserForm.Gender.$dirty && InviteNewUserForm.Gender.$invalid">
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Gender.$error.required">*Required</span></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <!--Status-->
                                                    <div class="form-group-sm">
                                                        <label class="req">Status</label>
                                                        <div class="form-group-sm">
                                                            <div class="radio radio-custom radio-inline radio-primary">
                                                                <input type="radio" name="Status" id="StatusActive" value="Active"
                                                                       ng-required="true"
                                                                       ng-model="User.Status" />
                                                                <label for="StatusActive">Active</label>
                                                            </div>
                                                            <div class="radio radio-custom radio-inline">
                                                                <input type="radio" name="Status" id="StatusInActive" value="In-Active"
                                                                       ng-required="true"
                                                                       ng-model="User.Status" />
                                                                <label for="StatusInActive">In-Active</label>
                                                            </div>
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="InviteNewUserForm.Status.$dirty && InviteNewUserForm.Status.$invalid">
                                                                    <li><span class="pull-left" ng-show="InviteNewUserForm.Status.$error.required">*Required</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 separator">
                                                    <!--Birth Date-->
                                                    <div class="form-group-sm">
                                                        <label class="control-label">Birth Date</label>
                                                        <datepicker date-format="dd/MM/yyyy" date-max-limit="{{MaxDate | date}}" date-min-limit="{{MinDate | date}}">
                                                            <input class="form-control custom-form-control" style="cursor:pointer" placeholder="Select Birth Date" name="BirthDate" type="text"
                                                                   ng-model="User.BirthDate"
                                                                   ng-readonly="true" />
                                                        </datepicker>
                                                        <div class="row custom-row">
                                                            <ul class="list-unstyled text-danger pull-left" ng-show="InviteNewUserForm.BirthDate.$dirty && InviteNewUserForm.BirthDate.$invalid">
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.BirthDate.$error.pattern">This is not a valid date. Use datepicker to pick a date</span></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <!--Mobile-->
                                                    <div class="form-group-sm">
                                                        <label class="req control-label">Mobile</label>
                                                        <input type="text" class="form-control" placeholder="Enter Mobile Number" name="Mobile"
                                                               ng-model="User.Mobile"
                                                               ng-minlength="11"
                                                               ng-maxlength="20" />
                                                        <div class="row custom-row">
                                                            <ul class="list-unstyled text-danger pull-left" ng-show="InviteNewUserForm.Mobile.$dirty && InviteNewUserForm.Mobile.$invalid">
                                                                <li class="pull-left" ng-show="InviteNewUserForm.Mobile.$error.pattern">Not a valid Mobile Number</li>
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Mobile.$error.minlength">Minimum length is 11</span></li>
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Mobile.$error.maxlength">Maximum length is 20</span></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <!--Address-->
                                                    <div class="form-group-sm">
                                                        <label class="control-label">Address</label>
                                                        <textarea class="form-control no-resize" name="Address" placeholder="Enter Address" style="resize: none; height: 80px"
                                                                  ng-model="User.Address"
                                                                  ng-maxlength="200"
                                                                  ng-minlength="3">   
                                                        </textarea>
                                                        <div class="row custom-row">
                                                            <ul class="list-unstyled text-danger pull-left"
                                                                ng-show="InviteNewUserForm.Address.$dirty && InviteNewUserForm.Address.$invalid">
                                                                <li class="pull-left" ng-show="InviteNewUserForm.Address.$error.pattern">Not a valid address</li>
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Address.$error.minlength">Minimum length is 3</span></li>
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Address.$error.maxlength">Maximum length is 200</span></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <!--Password-->
                                                    <!--<div class="form-group-sm" ng-if="User.Id==null">
                                                        <label class="req">Password</label>
                                                        <input type="password" name="Password" class="form-control" placeholder="Enter Password"
                                                               ng-required="true"
                                                               ng-model="User.Password"
                                                               ng-minlength="6"
                                                               ng-maxlength="50"
                                                               ng-pattern="/([a-zA-Z0-9 ])\w+/" />
                                                        <div class="row custom-row">
                                                            <ul class="list-unstyled text-danger pull-left" ng-show="InviteNewUserForm.Password.$dirty && InviteNewUserForm.Password.$invalid">
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Password.$error.required">*Required</span></li>
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.Password.$error.pattern">Not a valid password</span></li>
                                                                <li><span class="pull-left" ng-show="!InviteNewUserForm.Password.$error.pattern && InviteNewUserForm.Password.$error.minlength">Minimum length is 6</span></li>
                                                                <li><span class="pull-left" ng-show="!InviteNewUserForm.Password.$error.pattern && InviteNewUserForm.Password.$error.maxlength">Maximum length is 50</span></li>
                                                            </ul>
                                                        </div>
                                                    </div>-->
                                                    <!--Confirm Password-->
                                                    <!--<div class="form-group-sm" ng-if="User.Id==null">
                                                        <label class="req">Confirm Password</label>
                                                        <input type="password" name="ConfirmPassword" class="form-control" placeholder="Re-Type Passsword"
                                                               ng-required="true"
                                                               ng-model="ConfirmPassword"
                                                               ng-pattern="User.Password"/>
                                                        <div class="row custom-row">
                                                            <ul class="list-unstyled text-danger pull-left" ng-show="InviteNewUserForm.ConfirmPassword.$dirty && InviteNewUserForm.ConfirmPassword.$invalid">
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.ConfirmPassword.$error.required">*Required</span></li>
                                                                <li><span class="pull-left" ng-show="InviteNewUserForm.ConfirmPassword.$error.pattern">Password is not matched</span></li>
                                                            </ul>
                                                        </div>
                                                    </div>-->
                                                    <br/>
                                                    <br/>
                                                    <br/>
                                                    <div>
                                                        <span>NOTE: The password will be generated automatically for the user and credentials will be sent to his/her mail.</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button"
                            class="btn  btn-primary btn-wide pull-right btn-left-border-radius"
                            ng-click="InviteNewUser()"
                            ng-disabled="InviteNewUserForm.$invalid"
                            ng-if="User.Id==null"
                            role="button">
                        Invite User &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-mail-forward fa-1x"></i>
                    </button>
                    <button type="button"
                            class="btn  btn-primary btn-wide pull-right btn-left-border-radius"
                            ng-click="UpdateUser()"
                            ng-disabled="InviteNewUserForm.$invalid"
                            ng-if="User.Id !=null"
                            role="button">
                        Update User &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-tick fa-1x"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
<div class="modal fade" id="UserDetailsModal" tabindex="-1" role="dialog" aria-labelledby="modal-label" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="height:65px">
                <button type="button" class="custom-close" ng-click="cancelUserDetailsModal()" style="margin-top:0px;" aria-label="Close" tooltip-placement="left" uib-tooltip="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" style="margin-top:1.6%">User Details</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-primary panel-bordered">
                    <div class="panel-body">
                        <div class="row">
                            <!--Task Description-->
                            <div class="col-lg-12">
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered table-condensed">
                                        <tbody>
                                            <tr>
                                                <th>Name</th>
                                                <td><label class="control-label">{{User.FirstName}} {{User.LastName}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Email</th>
                                                <td><label class="control-label">{{User.Email}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Address</th>
                                                <td><label class="control-label">{{User.Address}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Birthdate</th>
                                                <td><label class="control-label">{{User.BirthDate}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Designation</th>
                                                <td><label class="control-label">{{User.Designation}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Mobile</th>
                                                <td><label class="control-label">{{User.Mobile}}</label></td>
                                            </tr>
                                            <tr>
                                                <th>Status</th>
                                                <td><span class="{{User.Status=='Active'?'badge x-success ':'badge '}}">{{User.Status}}</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button"
                        class="btn  btn-default btn-wide pull-right btn-left-border-radius"
                        ng-click="cancelUserDetailsModal()"
                        role="button">
                    Close &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                </button>
            </div>
        </div>
    </div>
</div>
<style>
</style>