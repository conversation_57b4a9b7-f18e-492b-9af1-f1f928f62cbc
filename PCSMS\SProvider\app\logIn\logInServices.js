﻿PCSMSApp.factory("logInServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetAuthorizationToken: function (param) {
            return $http({
                url: "/Api/Authenticate/LogIn",
                method: "POST",
                headers: {
                    "authorization": "Basic " + param,
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: {
	                "Type":"SProviderUser"
                },
                async: false
            });
        },

    };
}]);
