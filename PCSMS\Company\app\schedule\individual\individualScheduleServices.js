﻿PCSMSApp.factory("individualScheduleServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetNonGroupedDeviceListByCompanyId: function (companyId) {
            return $http({
                url: "/Api/Device/GetNonGroupedDeviceListByCompanyId/" + companyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetDeviceDetails: function (deviceId) {
            return $http({
                url: "/Api/Device/GetDeviceDetails/" + deviceId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        UpdateSingularDeviceScheduling: function ($scope) {
            return $http({
                url: "/Api/Device/UpdateSingularDeviceScheduling",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: {
                    deviceObj: $scope.Schedule
                },
                async: false
            });
        },
        
    };
}]);