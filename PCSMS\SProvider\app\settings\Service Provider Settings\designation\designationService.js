﻿/// <reference path="../../../Assets_SProvider/angularjs/angular.js" />
/// <reference path="../../../app.js" />
PCSMSApp.factory("designationServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    var baseUrl = '/Api/SP_Designation';
    return {
        SaveSP_Designation: function (param) {
            return $http({
                url: baseUrl + "/SaveSP_Designation",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        UpdateSP_Designation: function (param) {
            return $http({
                url: baseUrl + "/UpdateSP_Designation",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        GetSP_DesignationList: function () {
            return $http({
                url: baseUrl + "/GetSP_DesignationList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DeleteSP_Designation: function (id) {
            return $http({
                url: baseUrl + '/DeleteSP_Designation/' + id,
                method: 'POST',
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetSP_DesignationDetails: function (id) {
            return $http({
                url: baseUrl + "/GetSP_DesignationDetails/" + id,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
    };
}]);