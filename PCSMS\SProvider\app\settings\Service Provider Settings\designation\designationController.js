﻿/// <reference path="../../../app.js" />
PCSMSApp.controller('designationController', function ($scope, $rootScope, designationServices, toastr, $q, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $compile, $state, blockUI) {
    $scope.Designation = {};
    $rootScope.settingsOpen = true;
    //Retrieving data from db:
    $scope.DesignationList = designationServices.GetSP_DesignationList().then(function (response) {
        $scope.DesignationList = response.data;
        //console.log("----List of Designation----");
        //console.log($scope.Designation);
        return response.data;
    });


    //Datatable
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [
        DTColumnDefBuilder.newColumnDef(1).withOption('width', '1%').notSortable()
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
        .withOption('paging', true)
        .withOption('searching', true)
        .withOption('info', true);

    
    //Open Modal to EDIT as well as Update

    $scope.openDesignationModal = function (id) {

        if (id != null) {
            //blockUI.start();
            //blockUI.message("Loading Designation Details ...");
            designationServices.GetSP_DesignationDetails(id).then(function (response) {
                $scope.Designation = response.data;
                $scope.TempDesignationName = response.data.DesignationName;
            })
                .then(function () {
                    //blockUI.stop();
                    $('#DesignationModal').modal('show');
                });
        }
    };
    $scope.SaveOrUpdateDesignation = function () {
        if ($scope.DesignationForm.$invalid == false && $scope.LiveValidation($scope.Designation.DesignationName) == true) {
            //console.log("===Outside===");
            //console.log($scope.Designation);
            if ($scope.Designation.Id == null) {
                //console.log("===Inside===");
                //console.log($scope.Designation);
                //blockUI.start();
                //blockUI.message("Saving Designation ...");
                designationServices.SaveSP_Designation($scope.Designation).then(function (response) {
                    $('#DesignationModal').modal('hide');
                    //console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "DesignationExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                })
                    .then(function () {
                        $scope.cancelDesignationListModal();
                        $scope.DesignationForm.$setPristine();
                        $scope.DesignationForm.$setUntouched();
                    })
                    .then(function () {
                        $timeout(function () {
                            //blockUI.message("Loading Designation List ...");
                            designationServices.GetSP_DesignationList().then(function (response) {
                                $scope.DesignationList = response.data;
                                //console.log("----List of Designation----");
                                //console.log($scope.Designation);
                                return response.data;
                            })
                        }, 100)
                    });
            } else {
                //blockUI.start();
                //blockUI.message("Updating Designation ...");
                designationServices.UpdateSP_Designation($scope.Designation).then(function (response) {
                    $('#DesignationModal').modal('hide');
                    //console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "DesignationExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                })
                    .then(function () {
                        $scope.cancelDesignationListModal();
                        $scope.DesignationForm.$setPristine();
                        $scope.DesignationForm.$setUntouched();
                    })
                    .then(function () {
                        $timeout(function () {
                            //blockUI.message("Loading Designation List ...");
                            designationServices.GetSP_DesignationList().then(function (response) {
                                $scope.DesignationList = response.data;
                                //console.log("----List of Designation----");
                                //console.log($scope.Designation);
                                return response.data;
                            })
                        }, 100)
                    });
            }
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }

    };



    //Deleting:
    $scope.deleteDesignationAlert = function (designationId, DesignationName) {
        swal({
            title: "Are you sure?",
            text: "You are going to delete this designation",
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn-danger",
            confirmButtonText: "Yes, delete it!",
            closeOnConfirm: true
        },
            function () {
                //console.log(designationId + '' + DesignationName);
                //blockUI.start();
                //blockUI.message("Deleting Designation ...");
                designationServices.DeleteSP_Designation(designationId).then(function (response) {
                    //console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                })
                    .then(function () {
                        //blockUI.message("Loading Designation List ...");
                        designationServices.GetSP_DesignationList().then(function (response) {
                            $scope.DesignationList = response.data;
                            //console.log("----List of Designation----");
                            //console.log($scope.Designation);
                            return response.data;
                        })
                            .then(function () {
                                //blockUI.stop();
                            })
                    })

            });
    };

    $scope.cancelDesignationListModal = function () {
        $('#DesignationModal').modal('hide');
        $timeout(function () {
            //$scope.Designation = {};
            //$scope.TempDesignationName = null;
            //blockUI.stop();
             $state.reload();
        }, 500);
        ////console.log($scope.Designation);
        //$scope.DesignationForm.$setPristine();
        //$scope.DesignationForm.$setUntouched();
    };

    //Live Validation
    $scope.LiveValidation = function (x) {
        //console.log("---Live Validation Started---");
        var valid = false;
        if ($scope.DesignationList.length > 0) {
            for (var i = 0; i < $scope.DesignationList.length; i++) {
                ////console.log($scope.DesignationList[i].DesignationName);
                if ($scope.DesignationList[i].DesignationName == x) {
                    //console.log($scope.DesignationList[i].DesignationName);
                    if ($scope.DesignationList[i].DesignationName == $scope.TempDesignationName) {
                        valid = true;
                    }
                    else {
                        valid = false;
                        $scope.LiveValidationError = "Designation already exists";
                        break;
                    }
                }
                else {
                    valid = true;
                }
            }
        }
        else if ($scope.DesignationList.length == 0) {
            valid = true;
        }
        return valid;
    };
})