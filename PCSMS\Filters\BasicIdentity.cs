﻿using System.Security.Principal;

namespace PCSMS.Filters
{
    public class BasicIdentity : GenericIdentity
    {
        
        public string Password { get; set; }
        public string Username { get; set; }
        public int CompanyId { get; set; }
        public int SProviderUserId { get; set; }

        
        public BasicIdentity(string username, string password): base(username, "Basic")
        {
            Password = password;
            Username = username;
        }
    }
}