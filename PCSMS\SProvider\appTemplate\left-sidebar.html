﻿<div class="left-sidebar" ng-controller="appController">
    <!-- left sidebar HEADER -->
    <div class="left-sidebar-header">
        <!--<div class="left-sidebar-title"><span class="menu-title"><img src="Assets_SProvider/images/PCSMS.png"  width="50"/></span></div>-->
        <div class="left-sidebar-title"><span class="menu-title">PCSMS</span></div>
        <div class="left-sidebar-toggle c-hamburger c-hamburger--htla hidden-xs" ng-click="HTMLCollapser()">
            <span></span>
        </div>
    </div>
    <!-- MENU -->
    <div id="left-nav" class="nano"   ng-controller="appController">
        <div class="nano-content">

            <nav >
                <ul class="nav nav-left-lines" id="main-nav">
                    <!--Dahboard-->
                    <li ui-sref-active="active-item"><a ui-sref="home" class="dashboard-color"><i class="fa fa-dashboard dashboard-color" aria-hidden="true"></i><span>Dashboard</span></a></li>
                    <!--Profile-->
                    <li ui-sref-active="active-item">
                        <a ui-sref="profile"><i class="fa fa-home myaccount-color" aria-hidden="true"></i><span>Company Profile</span></a>
                    </li>
                    <!--Settings-->
                    <!--MENU LEVELS-->
                    <li class="has-child-item close-item" ng-class="{'has-child-item open-item':settingsOpen==true}">
                        <a>
                            <i class="fa fa-cog" aria-hidden="true"></i>
                            <span>Settings</span>
                        </a>
                        <ul class="nav child-nav level-1">
                            <li ui-sref-active="active-item"><a ui-sref="user">User</a></li>
                            <li ui-sref-active="active-item"><a ui-sref="designation">Designation</a></li>
                            <li ui-sref-active="active-item"><a ui-sref="accesstype">Access Type</a></li>
                            <li ui-sref-active="active-item"><a ui-sref="companytype">Company Type</a></li>
                            <li ui-sref-active="active-item"><a ui-sref="branch">Branch</a></li>
                        </ul>
                    </li>
                    <!--Manage License-->
                    <li ui-sref-active="active-item">
                        <a ui-sref="license"><i class="fa fa-key bill-color" aria-hidden="true"></i><span>Manage License</span></a>
                    </li>
                    <!--Manage Company (s)-->
                    <!--MENU LEVELS-->
                    <li class="has-child-item close-item" ng-class="{'has-child-item open-item':managecompanyOpen==true}">
                        <a>
                            <i class="fa fa-briefcase company-color" aria-hidden="true"></i>
                            <span>Manage Company</span>
                        </a>
                        <ul class="nav child-nav level-1">
                            <li ui-sref-active="active-item"><a ui-sref="onlyregisteredcompany">Only Reg. Company (s)</a></li>
                            <li ui-sref-active="active-item"><a ui-sref="subscribedcompany">Subscribed Company (s)</a></li>
                        </ul>
                    </li>
                    <!--Manage DB-->
                    <li ui-sref-active="active-item" ng-if="UserCanAccessManageDatabaseMenu() == true">
                        <a ui-sref="database"><i class="fa fa-database tracking-color" aria-hidden="true"></i><span>Manage Database</span></a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>
<!--TEMPLATE scripts-->
<!-- ========================================================= -->
<script src="Assets_SProvider/javascripts/template-script.min.js"></script>
<script src="Assets_SProvider/javascripts/template-init.min.js"></script>
