﻿!function(t){var e=t.module("blockUI",[]);e.config(["$provide","$httpProvider",function(t,e){t.decorator("$exceptionHandler",["$delegate","$injector",function(t,e){var o,n;return function(r,c){if((n=n||e.get("blockUIConfig")).resetOnException)try{(o=o||e.get("blockUI")).instances.reset()}catch(t){console.log("$exceptionHandler",r)}t(r,c)}}]),e.interceptors.push("blockUIHttpInterceptor")}]),e.run(["$document","blockUIConfig","$templateCache",function(t,e,o){e.autoInjectBodyBlock&&t.find("body").attr("block-ui","main"),e.template&&(e.templateUrl="$$block-ui-template$$",o.put(e.templateUrl,e.template))}]),e.config(["$provide",function(t){t.decorator("$location",o)}]);var o=["$delegate","blockUI","blockUIConfig",function(e,o,n){if(n.blockBrowserNavigation){o.$_blockLocationChange=!0;t.forEach(["url","path","search","hash","state"],function(t){var n=e[t];e[t]=function(){var t=n.apply(e,arguments);return t===e&&(o.$_blockLocationChange=!1),t}})}return e}];function n(e,o,n){if(n.blockBrowserNavigation){function r(){e.$on("$locationChangeStart",function(t){o.$_blockLocationChange&&o.state().blockCount>0&&t.preventDefault()}),e.$on("$locationChangeSuccess",function(){o.$_blockLocationChange=n.blockBrowserNavigation})}if(function(e){try{t.module(e)}catch(t){return!1}return!0}("ngRoute"))var c=e.$on("$viewContentLoaded",function(){c(),r()});else r()}}e.directive("blockUiContainer",["blockUIConfig","blockUiContainerLinkFn",function(t,e){return{scope:!0,restrict:"A",templateUrl:t.templateUrl,compile:function(t){return e}}}]).factory("blockUiContainerLinkFn",["blockUI","blockUIUtils",function(t,e){return function(t,e,o){var n=e.inheritedData("block-ui");if(!n)throw new Error("No parent block-ui service instance located.");t.state=n.state()}}]),e.directive("blockUi",["blockUiCompileFn",function(t){return{scope:!0,restrict:"A",compile:t}}]).factory("blockUiCompileFn",["blockUiPreLinkFn",function(t){return function(e,o){return e.append('<div block-ui-container class="block-ui-container"></div>'),{pre:t}}}]).factory("blockUiPreLinkFn",["blockUI","blockUIUtils","blockUIConfig",function(t,e,o){return function(r,c,i){c.hasClass("block-ui")||c.addClass(o.cssClass),i.$observe("blockUiMessageClass",function(t){r.$_blockUiMessageClass=t});var a=i.blockUi||"_"+r.$id,l=t.instances.get(a);if("main"===a)n(r,l,o);else{var s=c.inheritedData("block-ui");s&&(l._parent=s)}r.$on("$destroy",function(){l.release()}),l.addRef(),r.$_blockUiState=l.state(),r.$watch("$_blockUiState.blocking",function(t){c.attr("aria-busy",!!t),c.toggleClass("block-ui-visible",!!t)}),r.$watch("$_blockUiState.blockCount > 0",function(t){c.toggleClass("block-ui-active",!!t)});var u=i.blockUiPattern;if(u){var f=e.buildRegExp(u);l.pattern(f)}c.data("block-ui",l)}}]),e.constant("blockUIConfig",{templateUrl:"angular-block-ui/angular-block-ui.ng.html",delay:250,message:"Please Wait ...",autoBlock:!0,resetOnException:!0,requestFilter:t.noop,autoInjectBodyBlock:!0,cssClass:"block-ui block-ui-anim-fade",blockBrowserNavigation:!1}),e.factory("blockUIHttpInterceptor",["$q","$injector","blockUIConfig","$templateCache",function(t,e,o,n){var r;function c(){r=r||e.get("blockUI")}function i(t){o.autoBlock&&t&&!t.$_noBlock&&t.$_blocks&&(c(),t.$_blocks.stop())}function a(e){try{i(e.config)}catch(t){console.log("httpRequestError",t)}return t.reject(e)}return{request:function(t){if(o.autoBlock&&("GET"!=t.method||!n.get(t.url))){var e=o.requestFilter(t);!1===e?t.$_noBlock=!0:(c(),t.$_blocks=r.instances.locate(t),t.$_blocks.start(e))}return t},requestError:a,response:function(t){return t&&i(t.config),t},responseError:a}}]),e.factory("blockUI",["blockUIConfig","$timeout","blockUIUtils","$document",function(e,o,n,r){var c=r.find("body"),i=["id","blockCount","blocking"];var a=[];a.get=function(s){if(!isNaN(s))throw new Error("BlockUI id cannot be a number");var u=a[s];return u||(u=a[s]=new function(a){var s,u=this,f={id:a,blockCount:0,message:e.message,blocking:!1},k=[];this._id=a,this._refs=0,this.start=function(a){a=a||{},t.isString(a)?a={message:a}:t.forEach(i,function(t){if(a[t])throw new Error("The property "+t+" is reserved for the block state.")}),t.extend(f,a),f.blockCount>0?f.message=a.message||f.message||e.message:f.message=a.message||e.message,f.blockCount++;var l=t.element(r[0].activeElement);function k(){s=null,f.blocking=!0}l.length&&n.isElementInBlockScope(l,u)&&(u._restoreFocus=l[0],o(function(){u._restoreFocus&&u._restoreFocus!==c[0]&&u._restoreFocus.blur()})),s||0===e.delay?0===e.delay&&k():s=o(k,e.delay)},this._cancelStartTimeout=function(){s&&(o.cancel(s),s=null)},this.stop=function(){f.blockCount=Math.max(0,--f.blockCount),0===f.blockCount&&u.reset(!0)},this.isBlocking=function(){return f.blocking},this.message=function(t){f.message=t},this.pattern=function(t){return void 0!==t&&(u._pattern=t),u._pattern},this.reset=function(e){if(u._cancelStartTimeout(),f.blockCount=0,f.blocking=!1,u._restoreFocus&&(!r[0].activeElement||r[0].activeElement===c[0])){try{u._restoreFocus.focus()}catch(t){n=u._restoreFocus,o(function(){if(n)try{n.focus()}catch(t){}},100)}u._restoreFocus=null}var n;try{e&&t.forEach(k,function(t){t()})}finally{k.length=0}},this.done=function(t){k.push(t)},this.state=function(){return f},this.addRef=function(){u._refs+=1},this.release=function(){--u._refs<=0&&l.instances._destroy(u)}}(s),a.push(u)),u},a._destroy=function(e){if(t.isString(e)&&(e=a[e]),e){e.reset();var o=n.indexOf(a,e);a.splice(o,1),delete a[e.state().id]}},a.locate=function(t){var e=[];n.forEachFnHook(e,"start"),n.forEachFnHook(e,"stop");for(var o=a.length;o--;){var r=a[o],c=r._pattern;c&&c.test(t.url)&&e.push(r)}return 0===e.length&&e.push(l),e},n.forEachFnHook(a,"reset");var l=a.get("main");return l.addRef(),l.instances=a,l}]),e.factory("blockUIUtils",function(){var e=t.element,o={buildRegExp:function(t){var e=t.match(/^\/(.*)\/([gim]*)$/);if(!e)throw Error("Incorrect regular expression format: "+t);return new RegExp(e[1],e[2])},forEachFn:function(t,e,o){for(var n=t.length;n--;){var r=t[n];r[e].apply(r,o)}},forEachFnHook:function(t,e){t[e]=function(){o.forEachFn(this,e,arguments)}},isElementInBlockScope:function(t,e){for(var o=t.inheritedData("block-ui");o;){if(o===e)return!0;o=o._parent}return!1},findElement:function(t,n,r){var c=null;if(n(t))c=t;else for(var i,a=(i=r?t.parent():t.children()).length;!c&&a--;)c=o.findElement(e(i[a]),n,r);return c},indexOf:function(t,e,o){for(var n=o||0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1}};return o}),t.module("blockUI").run(["$templateCache",function(t){t.put("angular-block-ui/angular-block-ui.ng.html",'<div class="block-ui-overlay"></div><div class="block-ui-message-container" aria-live="assertive" aria-atomic="true"><div class="block-ui-message" ng-class="$_blockUiMessageClass">{{ state.message }}</div></div>')}])}(angular);