﻿/// <reference path="../../app.js" />
PCSMSApp.controller('profileController', function ($scope, $rootScope, appServices, blockUI, ProfileServices, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state, $http) {
    $rootScope.CurrentState = $state.current.name;

    //Funtion used/called later:
    function updateCookieWithCompanyDetails(name, value) {
        var extendedTime = new Date();
        var universalTime = new Date(extendedTime.getTime() + extendedTime.getTimezoneOffset() * 60000);
        var expireTime = universalTime.getTime() + 10800000; // 3 hours
        //var expireTime = universalTime.getTime() + 10000; // 10 seconds
        extendedTime.setTime(expireTime);
        document.cookie = name + '=' + value + '; Path=/; Expires=' + extendedTime + ';';
    };

    //appServices.CheckCompanyHasToPayForNextSession().then(function (response) {
    //    $rootScope.PckgRenewWarning = response.data;

    //})
    //.then(function () {
    //    if ($rootScope.PckgRenewWarning.Result == true && $rootScope.PckgRenewWarning.AuthStatus != "Suspended") {
    //        //Prepare the duration
    //        if ($rootScope.PckgRenewWarning.PayingDuration.DurationInDay != 0) {
    //            $scope.Duration = $rootScope.PckgRenewWarning.PayingDuration.DurationInDay + ' day(s)';
    //        }
    //        else if ($rootScope.PckgRenewWarning.PayingDuration.DurationInHour != 0) {
    //            $scope.Duration = $rootScope.PckgRenewWarning.PayingDuration.DurationInHour + ' hour(s)';
    //        }
    //        else if ($rootScope.PckgRenewWarning.PayingDuration.DurationInMinute != 0) {
    //            $scope.Duration = $rootScope.PckgRenewWarning.PayingDuration.DurationInMinute + ' minute(s)';
    //        }
    //        else if ($rootScope.PckgRenewWarning.PayingDuration.DurationInSecond != 0) {
    //            $scope.Duration = $rootScope.PckgRenewWarning.PayingDuration.DurationInSecond + ' second(s)';
    //        }
    //        toastr.warning("Please pay within " + $scope.Duration + " to continue to avail current service. Failure to do so will result in package suspension.", "Caution!", {
    //            autoDismiss: false,
    //            containerId: 'toast-container',
    //            maxOpened: 0,
    //            newestOnTop: true,
    //            positionClass: 'toast-top-left',
    //            preventDuplicates: false,
    //            preventOpenDuplicates: false,
    //            target: 'body',
    //            progressBar: true,
    //            timeOut: 600000,
    //            closeButton: true,
    //            tapToDismiss: false,
    //        })

    //    }
    //});

    //console.log("--Executed After Controller fired--");
    $scope.pro = "Organization's Profile";
    $scope.isInEditModeCI = false;
    $scope.isInEditModeCPI = false;
    $scope.isInEditModeCLC = false;
    $scope.isInEditModeCPPP = false;
    $scope.PristinesCI = true;
    $scope.EnableEditCI = false;
    $scope.EnableEditCPI = false;
    $scope.EnableEditCLC = false;
    $scope.EnableEditCPPP = false;
    $scope.AutoCompleteOptions = {};
    $scope.AutoCompleteOptions.country = 'bd';
    $scope.Pdffilename = null;
    $('#btnPdfPreview').hide();
    $scope.HasImage = false;
    $scope.HasPDF = false;
    //$('#FirstCollapser').trigger('click');
    //$('#FirstCollapserIcon').removeClass("glyphicon-plus").addClass("glyphicon-minus");


    //--------------------------------------------Prevent Default Event--------------------------------------

    $scope.PreventDefaultEvent = function (e) {
        e.preventDefault();
    };


    //-----------------------------Retrieving data from db:------------------------------
    ProfileServices.GetCompanyTypeList().then(function (response) {
        $scope.CompanyTypeList = response.data;
    });

    //ProfileServices.GetCountryList().then(function (response) {
    //    $scope.CountryList = response.data;
    //    //console.log("---Country List---");
    //    //console.log(response.data);
    //});

    //------------------------------Reverse Geo Coding FUNC--------------------------------
    function geocodeLatLng(geocoder) {
        $scope.inputModel = $scope.Profile.CompanyLat + "," + $scope.Profile.CompanyLong;
        $scope.latlngStr = $scope.inputModel.split(',', 2);
        $scope.latlng = { lat: parseFloat($scope.latlngStr[0]), lng: parseFloat($scope.latlngStr[1]) };
        geocoder.geocode({ 'location': $scope.latlng }, function (results, status) {
            if (status === 'OK') {
                if (results[0]) {
                    $scope.SearchedPlace = results[0].formatted_address;
                    if (true) {
                        $scope.infowindow.setContent("<span class='badge x-darker-1'>" + $scope.SearchedPlace + "</span>");
                        $scope.infowindow.open($scope.map, $scope.marker);
                        $scope.PristinesCI = false;
                    }

                } else {
                    toastr.error('No result found', 'Error!');
                    $scope.PristinesCI = true;
                }
            } else {
                toastr.error('Geocoder failed due to: ' + status);
                $scope.PristinesCI = true;
            }
        });
    }
    //-------------------Function to set the CompanyInformation Form to be able to submitted-------------------
    $scope.ChangeAll = function () {
        $scope.PristinesCI = false;
        //if ($scope.Profile.CompanyMobile == "" && $scope.Profile.CompanyPhone == "") {
        //    $scope.CompanyPhoneMobileRequired = true;
        //}
        //else if ($scope.Profile.CompanyMobile != "" && $scope.Profile.CompanyPhone == "") {
        //    $scope.CompanyPhoneMobileRequired = false;
        //}
        //else if ($scope.Profile.CompanyMobile == "" && $scope.Profile.CompanyPhone != "") {
        //    $scope.CompanyPhoneMobileRequired = false;
        //}
        //else if ($scope.Profile.CompanyMobile != "" && $scope.Profile.CompanyPhone != "") {
        //    $scope.CompanyPhoneMobileRequired = false;
        //}
    };

    //---------------------------------------Function to prevent change event of Search Location or autocomplete textbox-----------------
    $scope.SearchLocationChange = function (e) {
        console.log(e);
        if ($scope.isInEditModeCI == false) {
            e.preventDefault();
            $('#SearchLocation').attr("disabled", "disabled");
        }
    }

    //---------------------------------------Load the map--------------------------------------
    $scope.LoadMap = function () {
        $timeout(function () {
            if (($scope.Profile.CompanyLat > 0 || $scope.Profile.CompanyLong > 0) && $scope.isInEditModeCI == false) {
                $scope.map = new google.maps.Map(document.getElementById('map-canvas'), {
                    center: { lat: $scope.Profile.CompanyLat, lng: $scope.Profile.CompanyLong },
                    zoom: 17,
                    draggable: false,
                    gestureHandling: 'greedy'
                });

                $scope.marker = new google.maps.Marker({
                    map: $scope.map,
                    position: {
                        lat: $scope.Profile.CompanyLat,
                        lng: $scope.Profile.CompanyLong
                    },
                    draggable: false
                });
            } else if (($scope.Profile.CompanyLat <= 0 || $scope.Profile.CompanyLong <= 0) && ($scope.isInEditModeCI == false || $scope.isInEditModeCI == true)) {
                $scope.map = new google.maps.Map(document.getElementById('map-canvas'), {
                    center: { lat: 23.915416, lng: 89.9129994 },
                    zoom: 6,
                    draggable: false,
                    gestureHandling: 'greedy'
                });
                $scope.infowindow = new google.maps.InfoWindow();
                $scope.marker = new google.maps.Marker({ map: $scope.map });
            }
            else {
                $scope.map = new google.maps.Map(document.getElementById('map-canvas'), {
                    center: { lat: 23.915416, lng: 89.9129994 },
                    zoom: 6,
                    draggable: false,
                    gestureHandling: 'greedy'
                });
                $scope.infowindow = new google.maps.InfoWindow();
                $scope.marker = new google.maps.Marker({ map: $scope.map });
            }
            $scope.geocoder = new google.maps.Geocoder(); //Geocoder Defination
            $scope.infowindow = new google.maps.InfoWindow(); //InfoWindow Defination
            var input = document.getElementById('SearchLocation');
            var autocomplete = new google.maps.places.Autocomplete(input);

            // Bind the map's bounds (viewport) property to the autocomplete object,
            // so that the autocomplete requests use the current map bounds for the
            // bounds option in the request.
            autocomplete.bindTo('bounds', $scope.map);
            var contentString = "<span class='badge x-darker-1'>" + $scope.Profile.CompanyName + "</span>";

            //Creating infoWindow
            $scope.infowindow.setContent(contentString);

            //Set Map and Marker to Dragable position while editing


            autocomplete.addListener('place_changed', function () {
                $scope.infowindow.close();
                $scope.marker.setVisible(false);
                var place = autocomplete.getPlace();
                if (!place.geometry) {
                    // User entered the name of a Place that was not suggested and
                    // pressed the Enter key, or the Place Details request failed.
                    window.alert("No details available for input: '" + place.name + "'");
                    return;
                }

                // If the place has a geometry, then present it on a map.
                if (place.geometry.viewport) {
                    $scope.map.fitBounds(place.geometry.viewport);
                    $scope.Profile.CompanyLat = place.geometry.location.lat();
                    $scope.Profile.CompanyLong = place.geometry.location.lng();
                    toastr.info("Now you can drag the marker to get precious location", "Caution!");
                    //console.log("--After Binding New Location--");
                    //console.log($scope.Profile.CompanyLat);
                    //console.log($scope.Profile.CompanyLong);
                    //console.log(place.adr_address);
                } else {
                    $scope.map.setCenter(place.geometry.location);
                    $scope.map.setZoom(17);  // Why 17? Because it looks good.
                }
                $scope.marker.setPosition(place.geometry.location);
                $scope.marker.setVisible(true);
                $scope.marker.setOptions({ draggable: true });
                $scope.infowindow.setContent("<span class='badge x-darker-1'>" + place.adr_address + "</span>");
                $scope.infowindow.open($scope.map, $scope.marker);
                geocodeLatLng($scope.geocoder);
            });


            //Click On Empty Map

            //Map CLICK Event
            $scope.map.addListener('click', function (e) {
                if ($scope.isInEditModeCI == true) {
                    if (($scope.Profile.CompanyLat == undefined || $scope.Profile.CompanyLat == "") && ($scope.Profile.CompanyLong == undefined || $scope.Profile.CompanyLong == "")) {
                        $scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$setDirty();
                        $scope.myLatLng = new google.maps.LatLng(e.latLng.lat(), e.latLng.lng());
                        $scope.Profile.CompanyLat = e.latLng.lat();
                        $scope.Profile.CompanyLong = e.latLng.lng();
                        $scope.marker.setVisible(true);
                        $scope.marker.setPosition($scope.myLatLng);
                        geocodeLatLng($scope.geocoder);
                    }
                    else {
                        toastr.warning('There is already a marker on this map available. Please drag position or search for precious location on search box', 'Warning!');
                        $scope.map.setCenter({ lat: $scope.Profile.CompanyLat, lng: $scope.Profile.CompanyLong });
                        $scope.map.setZoom(17);  // Why 17? Because it looks good.
                    }

                }
                else {
                    toastr.error('Edit to pin or move marker on map', 'Error!');
                }

                //console.log(e.latLng.lat());
                //console.log(e.latLng.lng());
            });

            //Marker DRAG Event
            $scope.marker.addListener('drag', function () {
                //console.log('Dragging...');
                $scope.infowindow.close();
                $scope.infowindow.setContent(null);
            });

            //Marker CLICK Event
            $scope.marker.addListener('click', function () {
                $scope.infowindow.open($scope.map, $scope.marker);
            });


            //Marker DRAGEND Event
            $scope.marker.addListener('dragend', function (e) {
                $scope.PristinesCI = false;
                $scope.Profile.CompanyLat = e.latLng.lat();
                $scope.Profile.CompanyLong = e.latLng.lng();
                //console.log("--Only Draggin LAT")
                //console.log($scope.Profile.CompanyLat);
                //console.log("--Only Draggin LNG")
                //console.log($scope.Profile.CompanyLong);
                geocodeLatLng($scope.geocoder);
            });
        }, 3000)
            .then(function () {

                $scope.EnableEditCI = true;
                $scope.EnableEditCPI = true;
                $scope.EnableEditCLC = true;
                $scope.EnableEditCPPP = true;
            });

    };



    //----------------------------Promise Function To Load Profile After click on edit button----------------------------

    function PromiseOfLoadProfile() {
        return new Promise(function (resolve) {
            ProfileServices.GetCompanyProfileDetails()
                .then(function (response) {
                    //console.log('--Promise Function Hit--');
                    if (response.data != null) {
                        $scope.Profile = response.data;
                        if ($scope.isInEditModeCI == true) {
                            $scope.map.setOptions({ draggable: true });
                            $scope.marker.setOptions({ draggable: true });

                        }
                        else if ($scope.isInEditModeCI == false) {
                            $scope.map.setOptions({ draggable: false });
                            $scope.marker.setOptions({ draggable: false });
                            $scope.infowindow.close();
                            $scope.infowindow.setContent(null);
                        }
                        $scope.CurrentEmail = response.data.CompanyEmail;
                        if (($scope.Profile.CompanyBillingAddress == $scope.Profile.CompanyAddress) && $scope.Profile.CompanyAddress != null) {
                            $scope.CheckSameAsAddress = 1;
                        }
                    }
                    else {
                        //console.log('$scope.Profile is not inserted with this null object')
                    }
                });
            resolve();
            //console.log('--Promise Function Resolved Successfully--');
        });
    };


    //---------------------------------Db Operations------------------------------------
    $scope.LoadProfile = ProfileServices.GetCompanyProfileDetails()
        .then(function (response) {
            if (response.data != null) {
                $scope.Profile = response.data;
                $scope.CurrentEmail = response.data.CompanyEmail;
                if (($scope.Profile.CompanyBillingAddress == $scope.Profile.CompanyAddress) && $scope.Profile.CompanyAddress != null) {
                    $scope.CheckSameAsAddress = 1;
                }
                if ($scope.Profile.CompanyLogo != null) {
                    $('#imageHolder').prepend('<img id="imageTag" class="animated fadeIn" src="" />');
                    $('#imageTag').attr("src", "../Company_Images/Profile_Images/" + $scope.Profile.CompanyLogo);
                    $scope.src = "/Company_Images/Profile_Images/" + $scope.Profile.CompanyLogo;
                    //imageName = response.data.CompanyLogo ;
                }

                if ($scope.Profile.PrivacyPolicy != null) {
                    $scope.source = '../Company_PrivacyPolicy/' + $scope.Profile.PrivacyPolicy;
                    showPDF($scope.source);
                    //var __PDF_DOC,
                    //    __CURRENT_PAGE,
                    //    __TOTAL_PAGES,
                    //    __PAGE_RENDERING_IN_PROGRESS = 0,
                    //    __CANVAS = $('#pdf-canvasDB').get(0),
                    //    __CANVAS_CTX = __CANVAS.getContext('2d');

                    //function showPDF(pdf_url) {
                    //    $("#pdf-loaderDB").show();

                    //    PDFJS.getDocument({ url: pdf_url }).then(function (pdf_doc) {
                    //        __PDF_DOC = pdf_doc;
                    //        __TOTAL_PAGES = __PDF_DOC.numPages;
                    //        $scope.__TOTAL_PAGES = __TOTAL_PAGES;

                    //        // Hide the pdf loader and show pdf container in HTML
                    //        $("#pdf-loaderDB").hide();
                    //        $("#pdf-contentsDB").show();
                    //        $("#pdf-total-pagesDB").text(__TOTAL_PAGES);

                    //        // Show the first page
                    //        showPage(1);
                    //    }).catch(function (error) {
                    //        // If error re-show the upload button
                    //        $("#pdf-loaderDB").hide();

                    //        alert(error.message);
                    //    });;
                    //}

                    //function showPage(page_no) {
                    //    __PAGE_RENDERING_IN_PROGRESS = 1;
                    //    __CURRENT_PAGE = page_no;
                    //    $scope.__CURRENT_PAGE = __CURRENT_PAGE;

                    //    // Disable Prev & Next buttons while page is being loaded
                    //    $("#pdf-nextDB, #pdf-prevDB").attr('disabled', 'disabled');

                    //    // While page is being rendered hide the canvas and show a loading message
                    //    //$("#pdf-canvas").hide();
                    //    $("#page-loaderDB").show();

                    //    // Update current page in HTML
                    //    $("#pdf-current-pageDB").text(page_no);

                    //    // Fetch the page
                    //    __PDF_DOC.getPage(page_no).then(function (page) {
                    //        // As the canvas is of a fixed width we need to set the scale of the viewport accordingly
                    //        var scale_required = __CANVAS.width / page.getViewport(1).width;

                    //        // Get viewport of the page at required scale
                    //        var viewport = page.getViewport(scale_required);

                    //        // Set canvas height
                    //        __CANVAS.height = viewport.height;

                    //        var renderContext = {
                    //            canvasContext: __CANVAS_CTX,
                    //            viewport: viewport
                    //        };

                    //        // Render the page contents in the canvas
                    //        page.render(renderContext).then(function () {
                    //            __PAGE_RENDERING_IN_PROGRESS = 0;

                    //            // Re-enable Prev & Next buttons
                    //            $("#pdf-nextDB, #pdf-prevDB").removeAttr('disabled');

                    //            // Show the canvas and hide the page loader
                    //            $("#pdf-canvasDB").show();
                    //            $("#page-loaderDB").hide();
                    //        });
                    //    });
                    //}

                    //// Previous page of the PDF
                    //$("#pdf-prevDB").on('click', function () {
                    //    if (__CURRENT_PAGE != 1)
                    //        showPage(--__CURRENT_PAGE);
                    //});

                    //// Next page of the PDF
                    //$("#pdf-nextDB").on('click', function () {
                    //    if (__CURRENT_PAGE != __TOTAL_PAGES)
                    //        showPage(++__CURRENT_PAGE);

                    //    if ($scope.__TOTAL_PAGES == $scope.__CURRENT_PAGE) {
                    //        $("#pdf-nextDB").attr('disabled', 'disabled');
                    //    }
                    //});
                }



                //console.log('--Empty Profile Object Checking---')
                //console.log($scope.Profile);
                //$scope.LoadMap();
            }
            else {
                //console.log('$scope.Profile is not inserted with this null object')
            }
        });

    //console.log("This is the Complete Profile Object");
    //console.log($scope.Profile);
    $scope.position = {}


    $scope.MatchWithExistingPassword = function (form) {
        $scope.ChangeAll();
        form.CompanyPassword.$setValidity("password", true);
        if ($scope.Profile.ExistingPassword == $scope.Profile.CompanyPassword) {
            //console.log(form);
            form.CompanyPassword.$setValidity("password", false);
            form.CompanyPassword.errorMessage = "New Password & Existing Password Can't Be Same.";
        }
        else {
            form.CompanyPassword.$setValidity("password", true);
            form.CompanyPassword.errorMessage = "";
        }
    };

    //Add SameAsAddress
    $scope.SameAsAddress = function () {
        if ($scope.Profile.CompanyBillingAddress == undefined || $scope.Profile.CompanyBillingAddress == "") {
            $scope.Profile.CompanyBillingAddress = $scope.Profile.CompanyAddress;
            $scope.ChangeAll();
        }
        else if ($scope.Profile.CompanyBillingAddress != null) {
            $scope.Profile.CompanyBillingAddress = $scope.Profile.CompanyAddress;
        }
        else if ($scope.Profile.CompanyBillingAddress != undefined) {
            $scope.Profile.CompanyBillingAddress = undefined;
            //console.log($scope.CheckSameAsAddress);
            $scope.ChangeAll();
        }

    };
    $scope.BindBillingAddress = function () {
        if ($scope.CheckSameAsAddress == 1) {
            $scope.Profile.CompanyBillingAddress = $scope.Profile.CompanyAddress;
            $scope.ChangeAll();
        }
        if ($scope.Profile.CompanyAddress == "") {
            $scope.Profile.CompanyAddress = undefined;
            $scope.CheckSameAsAddress = 0;
            $scope.ChangeAll();
        }
        else if ($scope.Profile.CompanyAddress == undefined) {
            $scope.CheckSameAsAddress = 0;
            $scope.ChangeAll();
        }
        //console.log("--Current Status of CompanyAddress--");
        //console.log($scope.Profile.CompanyAddress);
        $scope.ChangeAll();
    };


    //$(document).ready(function () {
    //    $(window).keydown(function (event) {
    //        if (event.keyCode == 13) {
    //            event.preventDefault();
    //            return false;
    //        }
    //    });
    //});
    $('#SearchLocation').keypress(function (e) {
        if (e.which == 13) {
            e.preventDefault();
            return false;
        }
    });



    //Add plus minus sign to Pannel Heading of Accordion:
    $('.collapse').on('shown.bs.collapse', function () {
        $(this).parent().find(".glyphicon-plus").removeClass("glyphicon-plus").addClass("glyphicon-minus");
    }).on('hidden.bs.collapse', function () {
        $(this).parent().find(".glyphicon-minus").removeClass("glyphicon-minus").addClass("glyphicon-plus");
    });

    $('#a_ProfileFormSaveCompanySecondaryInfo_CompanyInformation').on('shown.bs.collapse', function () {
        $(this).parent().find(".glyphicon-plus").removeClass("glyphicon-plus").addClass("glyphicon-minus");
    }).on('hidden.bs.collapse', function () {
        $(this).parent().find(".glyphicon-minus").removeClass("glyphicon-minus").addClass("glyphicon-plus");
    });

    //Trigger First Accordion
    $('#FirstCollapser').on('click', function () {
        if ($('#FirstCollapserIcon').hasClass("glyphicon-plus") == true) {
            $('#FirstCollapserIcon').removeClass("glyphicon-plus").addClass("glyphicon-minus");
        }
        else if ($('#FirstCollapserIcon').hasClass("glyphicon-minus") == true) {
            $('#FirstCollapserIcon').removeClass("glyphicon-minus").addClass("glyphicon-plus");
        }

    });
    $('#FirstCollapserText').on('click', function () {
        if ($('#FirstCollapserIcon').hasClass("glyphicon-plus") == true) {
            $('#FirstCollapserIcon').removeClass("glyphicon-plus").addClass("glyphicon-minus");
        }
        else if ($('#FirstCollapserIcon').hasClass("glyphicon-minus") == true) {
            $('#FirstCollapserIcon').removeClass("glyphicon-minus").addClass("glyphicon-plus");
        }

    });


    //Trigger Second Accordion
    $('#SecondCollapser').on('click', function () {
        if ($('#SecondCollapserIcon').hasClass("glyphicon-plus") == true) {
            $('#SecondCollapserIcon').removeClass("glyphicon-plus").addClass("glyphicon-minus");
        }
        else if ($('#SecondCollapserIcon').hasClass("glyphicon-minus") == true) {
            $('#SecondCollapserIcon').removeClass("glyphicon-minus").addClass("glyphicon-plus");
        }

    });
    $('#SecondCollapserText').on('click', function () {
        if ($('#SecondCollapserIcon').hasClass("glyphicon-plus") == true) {
            $('#SecondCollapserIcon').removeClass("glyphicon-plus").addClass("glyphicon-minus");
        }
        else if ($('#SecondCollapserIcon').hasClass("glyphicon-minus") == true) {
            $('#SecondCollapserIcon').removeClass("glyphicon-minus").addClass("glyphicon-plus");
        }

    });

    //Trigger Third Accordion
    $('#ThirdCollapser').on('click', function () {
        if ($('#ThirdCollapserIcon').hasClass("glyphicon-plus") == true) {
            $('#ThirdCollapserIcon').removeClass("glyphicon-plus").addClass("glyphicon-minus");
        }
        else if ($('#ThirdCollapserIcon').hasClass("glyphicon-minus") == true) {
            $('#ThirdCollapserIcon').removeClass("glyphicon-minus").addClass("glyphicon-plus");
        }

    });
    $('#ThirdCollapserText').on('click', function () {
        if ($('#ThirdCollapserIcon').hasClass("glyphicon-plus") == true) {
            $('#ThirdCollapserIcon').removeClass("glyphicon-plus").addClass("glyphicon-minus");
        }
        else if ($('#ThirdCollapserIcon').hasClass("glyphicon-minus") == true) {
            $('#ThirdCollapserIcon').removeClass("glyphicon-minus").addClass("glyphicon-plus");
        }

    });


    ////Trigger Fourth Accordion
    //$('#FourthCollapser').on('click', function () {
    //    if ($('#FourthCollapserIcon').hasClass("glyphicon-plus") == true) {
    //        $('#FourthCollapserIcon').removeClass("glyphicon-plus").addClass("glyphicon-minus");
    //    }
    //    else if ($('#FourthCollapserIcon').hasClass("glyphicon-minus") == true) {
    //        $('#FourthCollapserIcon').removeClass("glyphicon-minus").addClass("glyphicon-plus");
    //    }

    //});
    //$('#FourthCollapserText').on('click', function () {
    //    if ($('#FourthCollapserIcon').hasClass("glyphicon-plus") == true) {
    //        $('#FourthCollapserIcon').removeClass("glyphicon-plus").addClass("glyphicon-minus");
    //    }
    //    else if ($('#FourthCollapserIcon').hasClass("glyphicon-minus") == true) {
    //        $('#FourthCollapserIcon').removeClass("glyphicon-minus").addClass("glyphicon-plus");
    //    }

    //});

    //-----------------------------------------Set To Edit Mode-------------------------------------------------------

    //-----------------------------------------FIRST COLLAPSER CLICK EVENT--------------------------------------------
    //First Collapser EDIT---------------------COMPANY INFORMATION
    $scope.UpdateCompanyInfoEdit = function () {
        if (($scope.isInEditModeCPI == true || $scope.isInEditModeCLC == true) && (!$scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$pristine || !$scope.ProfileFormChangeCompanyCredentials.$pristine)) {
            var conf = confirm("All of your changes will be reverted. Do you want to continue?");
            if (conf == true) {
                PromiseOfLoadProfile()
                    .then(function () {
                        $scope.isInEditModeCI = true;

                        if ($('#FirstCollapserIcon').hasClass("glyphicon-minus") == false) {
                            $('#FirstCollapserText').trigger('click');
                            $scope.isInEditModeCPI = false;
                            $scope.isInEditModeCLC = false;
                            $scope.isInEditModeCPPP = false;
                        }
                        //Set another form to $pristine which are in Edit Mode as user revert all the changes 
                        $scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$setPristine();
                        $scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$setUntouched();
                        $scope.ProfileFormChangeCompanyCredentials.$setPristine();
                        $scope.ProfileFormChangeCompanyCredentials.$setUntouched();
                    });
            }
        }
        else {
            PromiseOfLoadProfile()
                .then(function () {
                    $scope.isInEditModeCI = true;
                    if ($('#FirstCollapserIcon').hasClass("glyphicon-minus") == false) {
                        $('#FirstCollapserText').trigger('click');
                        $scope.isInEditModeCPI = false;
                        $scope.isInEditModeCLC = false;
                    }
                });
        }
    }

    //First Collapser CANCEL EDIT
    $scope.UpdateCompanyInfoCancelEdit = function () {
        if ($scope.isInEditModeCI == true && !$scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$pristine) {
            var conf = confirm("All of your changes will be reverted. Do you want to continue?");
            if (conf == true) {
                PromiseOfLoadProfile()
                    .then(function () {
                        $scope.isInEditModeCI = false;
                        if ($('#FirstCollapserIcon').hasClass("glyphicon-minus") == true) {
                            $('#FirstCollapserText').trigger('click');
                        }
                        $scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$setPristine();
                        $scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$setUntouched();
                    })
                    .then(function () {
                        blockUI.start();
                        $timeout(function () {
                            $state.reload();
                            $rootScope.divId = 1;
                            blockUI.stop();
                        }, 1)
                    });
            }
        }
        else {
            PromiseOfLoadProfile()
                .then(function () {
                    $scope.isInEditModeCI = false;
                    if ($('#FirstCollapserIcon').hasClass("glyphicon-minus") == true) {
                        $('#FirstCollapserText').trigger('click');
                    }
                })
                .then(function () {
                    blockUI.start();
                    $timeout(function () {
                        $state.reload();
                        $rootScope.divId = 1;
                        blockUI.stop();
                    }, 1)
                });
        }

    }

    //-----------------------------------------SECOND COLLAPSER CLICK EVENT--------------------------------------------           
    //Second Collapser EDIT--------------------CONTACT PERSON INFO
    $scope.UpdateContactPersonInfoEdit = function () {
        if (($scope.isInEditModeCI == true || $scope.isInEditModeCLC == true) && (!$scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$pristine || !$scope.ProfileFormChangeCompanyCredentials.$pristine)) {
            var conf = confirm("All of your changes will be reverted. Do you want to continue?");
            if (conf == true) {
                PromiseOfLoadProfile()
                    .then(function () {
                        $scope.isInEditModeCPI = true;
                        if ($('#SecondCollapserIcon').hasClass("glyphicon-minus") == false) {
                            $('#SecondCollapserText').trigger('click');
                            $scope.isInEditModeCI = false;
                            $scope.isInEditModeCLC = false;
                            $scope.isInEditModeCPPP = false;
                        }
                        //Set another form to $pristine which are in Edit Mode as user revert all the changes
                        $scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$setPristine();
                        $scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$setUntouched();
                        $scope.ProfileFormChangeCompanyCredentials.$setPristine();
                        $scope.ProfileFormChangeCompanyCredentials.$setUntouched();
                    });
            }
        }
        else {
            PromiseOfLoadProfile()
                .then(function () {
                    $scope.isInEditModeCPI = true;
                    if ($('#SecondCollapserIcon').hasClass("glyphicon-minus") == false) {
                        $('#SecondCollapserText').trigger('click');
                        $scope.isInEditModeCI = false;
                        $scope.isInEditModeCLC = false;
                    }
                });
        }
    }

    //Secnd Collapser CANCEL EDIT
    $scope.UpdateContactPersonInfoCancelEdit = function () {
        if ($scope.isInEditModeCPI == true && !$scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$pristine) {
            var conf = confirm("All of your changes will be reverted. Do you want to continue?");
            if (conf == true) {
                PromiseOfLoadProfile()
                    .then(function () {
                        $scope.isInEditModeCPI = false;
                        if ($('#SecondCollapserIcon').hasClass("glyphicon-minus") == true) {
                            $('#SecondCollapserText').trigger('click');
                        }
                        $scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$setPristine();
                        $scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.setUntouched();
                    });
            }
        }
        else {
            PromiseOfLoadProfile()
                .then(function () {
                    $scope.isInEditModeCPI = false;
                    if ($('#SecondCollapserIcon').hasClass("glyphicon-minus") == true) {
                        $('#SecondCollapserText').trigger('click');
                    }
                });
        }
    }

    //-----------------------------------------THIRD COLLAPSER CLICK EVENT--------------------------------------------
    //Third Collapser EDIT---------------------CHANGE CREDENTIALS
    $scope.UpdateLoginCredentialsEdit = function () {
        if (($scope.isInEditModeCPI == true || $scope.isInEditModeCI == true) && (!$scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$pristine || !$scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$pristine)) {
            var conf = confirm("All of your changes will be reverted. Do you want to continue?");
            if (conf == true) {
                PromiseOfLoadProfile()
                    .then(function () {
                        $scope.isInEditModeCLC = true;
                        if ($('#ThirdCollapserIcon').hasClass("glyphicon-minus") == false) {
                            $('#ThirdCollapserText').trigger('click');
                            $scope.isInEditModeCI = false;
                            $scope.isInEditModeCPI = false;
                            $scope.isInEditModeCPPP = false;
                        }
                        //Set another form to $pristine which are in Edit Mode as user revert all the changes
                        $scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$setPristine();
                        $scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$setUntouched();
                        $scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$setPristine();
                        $scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$setUntouched();
                    });
            }
        }
        else {
            PromiseOfLoadProfile()
                .then(function () {
                    $scope.isInEditModeCLC = true;
                    if ($('#ThirdCollapserIcon').hasClass("glyphicon-minus") == false) {
                        $('#ThirdCollapserText').trigger('click');
                        $scope.isInEditModeCI = false;
                        $scope.isInEditModeCPI = false;
                    }
                });
        }

    }

    //Third Collapser CANCEL EDIT
    $scope.UpdateLoginCredentialsCancelEdit = function () {
        if ($scope.isInEditModeCLC == true && !$scope.ProfileFormChangeCompanyCredentials.$pristine) {
            var conf = confirm("All of your changes will be reverted. Do you want to continue?");
            if (conf == true) {
                PromiseOfLoadProfile()
                    .then(function () {
                        $scope.isInEditModeCLC = false;
                        if ($('#ThirdCollapserIcon').hasClass("glyphicon-minus") == true) {
                            $('#ThirdCollapserText').trigger('click');
                        }
                        $scope.ProfileFormChangeCompanyCredentials.$setPristine();
                        $scope.ProfileFormChangeCompanyCredentials.setUntouched();
                    });
            }
        }
        else {
            PromiseOfLoadProfile()
                .then(function () {
                    $scope.isInEditModeCLC = false;
                    if ($('#ThirdCollapserIcon').hasClass("glyphicon-minus") == true) {
                        $('#ThirdCollapserText').trigger('click');
                    }
                });
        }
    }


    ////-----------------------------------------FOURTH COLLAPSER CLICK EVENT--------------------------------------------
    ////Fourth Collapser EDIT---------------------UPDATE COMPANY PRIVACY POLICY
    //$scope.UpdateCompanyPrivacyPolicyEdit = function () {
    //    if (($scope.isInEditModeCPI == true || $scope.isInEditModeCI == true || $scope.isInEditModeCLC == true) && (!$scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$pristine || !$scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$pristine || !$scope.ProfileFormChangeCompanyCredentials.$pristine)) {
    //        var conf = confirm("All of your changes will be reverted. Do you want to continue?");
    //        if (conf == true) {
    //            PromiseOfLoadProfile()
    //            .then(function () {
    //                $scope.isInEditModeCPPP = true;
    //                if ($('#FourthCollapserIcon').hasClass("glyphicon-minus") == false) {
    //                    $('#FourthCollapserText').trigger('click');
    //                    $scope.isInEditModeCI = false;
    //                    $scope.isInEditModeCPI = false;
    //                    $scope.isInEditModeCLC = false;
    //                }
    //                //Set another form to $pristine which are in Edit Mode as user revert all the changes
    //                $scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$setPristine();
    //                $scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$setUntouched();
    //                $scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$setPristine();
    //                $scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$setUntouched();
    //                $scope.ProfileFormChangeCompanyCredentials.$setPristine();
    //                $scope.ProfileFormChangeCompanyCredentials.$setUntouched();
    //            });
    //        }
    //    }
    //    else {
    //        PromiseOfLoadProfile()
    //        .then(function () {
    //            $scope.isInEditModeCPPP = true;
    //            if ($('#FourthCollapserIcon').hasClass("glyphicon-minus") == false) {
    //                $('#FourthCollapserText').trigger('click');
    //                $scope.isInEditModeCI = false;
    //                $scope.isInEditModeCPI = false;
    //                $scope.isInEditModeCLC = false;
    //            }
    //        });
    //    }

    //}

    ////Fourth Collapser CANCEL EDIT
    //$scope.UpdateCompanyPrivacyPolicyCancelEdit = function () {
    //    if ($scope.isInEditModeCPPP == true && $scope.HasPDF == true) {
    //        var conf = confirm("All of your changes will be reverted. Do you want to continue?");
    //        if (conf == true) {
    //            PromiseOfLoadProfile()
    //            .then(function () {
    //                $scope.isInEditModeCPPP = false;
    //                $("#file-to-upload").val(null);
    //                $('#btnPdfPreview').hide();
    //                $("#btnPdfPreview").html("Preview");
    //                if ($('#FourthCollapserIcon').hasClass("glyphicon-minus") == true) {
    //                    $('#FourthCollapserText').trigger('click');
    //                }
    //                if ($scope.Profile.PrivacyPolicy != null) {
    //                    $scope.source = '../Company_PrivacyPolicy/' + $scope.Profile.PrivacyPolicy;
    //                    showPDF($scope.source);
    //                }
    //                //$scope.ProfileFormChangeCompanyCredentials.$setPristine();
    //                //$scope.ProfileFormChangeCompanyCredentials.setUntouched();
    //            })
    //            .then(function () {
    //                blockUI.start();
    //                $timeout(function () {
    //                    $state.reload();
    //                    $rootScope.divId = 4;
    //                    blockUI.stop();
    //                }, 1)
    //            });
    //        }
    //    }
    //    else {
    //        PromiseOfLoadProfile()
    //         .then(function () {
    //             $scope.isInEditModeCPPP = false;
    //             if ($('#FourthCollapserIcon').hasClass("glyphicon-minus") == true) {
    //                 $('#FourthCollapserText').trigger('click');
    //             }
    //         })
    //        .then(function () {
    //            blockUI.start();
    //            $timeout(function () {
    //                $state.reload();
    //                $rootScope.divId = 4;
    //                blockUI.stop();
    //            }, 1)
    //        });
    //    }
    //}

    //--------------------------------------Saving or updating:-------------------------------------
    $scope.UpdateCompanyInfo = function () {
        if ($scope.ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$invalid == false) {
            $scope.CompInfo = {
                Id: $scope.Profile.Id,
                CompanyTypeId: $scope.Profile.CompanyTypeId,
                CompanyPhone: $scope.Profile.CompanyPhone,
                CompanyMobile: $scope.Profile.CompanyMobile,
                CompanyWebsite: $scope.Profile.CompanyWebsite,
                CompanyAddress: $scope.Profile.CompanyAddress,
                CompanyBillingAddress: $scope.Profile.CompanyBillingAddress,
                CompanyLat: $scope.Profile.CompanyLat,
                CompanyLong: $scope.Profile.CompanyLong
            };
            //console.log("===Outside===");
            //console.log($scope.CompInfo);
            if ($scope.Profile.Id != null) {
                //console.log("===Inside===");
                //console.log($scope.CompInfo);
                ProfileServices.UpdateCompanyInfo($scope.CompInfo).then(function (response) {
                    //console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                        $rootScope.divId = "1";
                        //console.log($rootScope.divId);

                        ProfileServices.GetCompanyDetailsForCookies($rootScope.CompanyId).then(function (response) {
                            if (response.data) {
                                //updating Cookie With CompanyPckgPrivileges
                                updateCookieWithCompanyDetails('Company_BANKDEMO_Details', angular.toJson(response.data));
                                //console.log(angular.toJson(response.data));
                            }
                        });
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                        $rootScope.divId = "1";
                        //console.log($rootScope.divId);
                    }
                })
                    .then(function () {
                        //Image Upload
                        if ($scope.HasImage == true) {
                            ProfileServices.UploadCP_Logo($scope.Profile.Id, formdata).then(function (response) {
                                if (response.data.IsReport == "NotOk") {
                                    toastr.error(response.data.Message, "Error!");
                                }
                            })
                        }
                    })
                    .then(function () {
                        blockUI.start();
                        $timeout(function () {
                            $state.reload();
                            $rootScope.divId = "1";
                            //console.log($rootScope.divId);
                            blockUI.stop();
                        }, 200)

                    });
            } else {
                toastr.error('Please Make Sure That You have singed up', 'Updating Failed')
                    .then(function () {
                        blockUI.start();
                        $timeout(function () {
                            $state.reload();
                            blockUI.stop();
                        }, 200)

                    });
            }
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }

    };

    $scope.UpdateContactPersonInfo = function () {
        if ($scope.ProfileFormSaveCompanySecondaryInfo_ContactInformation.$invalid == false) {
            $scope.ContactPersonInfo = {
                Id: $scope.Profile.Id,
                ContactPerson: $scope.Profile.ContactPerson,
                ContactPersonPhone: $scope.Profile.ContactPersonPhone,
                ContactPersonMobile: $scope.Profile.ContactPersonMobile,
                ContactPersonEmail: $scope.Profile.ContactPersonEmail,
                ContactPersonDesignation: $scope.Profile.ContactPersonDesignation
            };
            //console.log("===Outside===");
            //console.log($scope.ContactPersonInfo);
            if ($scope.Profile.Id != null) {
                //console.log("===Inside===");
                //console.log($scope.ContactPersonInfo);
                ProfileServices.UpdateContactPersonInfo($scope.ContactPersonInfo).then(function (response) {
                    //console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                        $rootScope.divId = "2";
                        //console.log($rootScope.divId);

                        ProfileServices.GetCompanyDetailsForCookies($rootScope.CompanyId).then(function (response) {
                            if (response.data) {
                                //updating Cookie With CompanyPckgPrivileges
                                updateCookieWithCompanyDetails('Company_BANKDEMO_Details', angular.toJson(response.data));
                                //console.log(angular.toJson(response.data));
                            }
                        });
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                        $rootScope.divId = "2";
                        //console.log($rootScope.divId);
                    }
                })
                    .then(function () {
                        blockUI.start();
                        $timeout(function () {
                            $state.reload();
                            $rootScope.divId = "2";
                            blockUI.stop();
                            //console.log($rootScope.divId);
                        }, 200)

                    });
            } else {
                toastr.error('Please Make Sure Tha You have singed up', 'Updating Failed')
                    .then(function () {
                        blockUI.start();
                        $timeout(function () {
                            $state.reload();
                            blockUI.stop();
                        }, 200)

                    });
            }
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }

    };

    $scope.ChangeCompanyEmailForLogin = function () {
        if ($scope.ProfileFormChangeCompanyCredentials.CompanyEmail.$invalid == false) {
            if ($scope.CurrentEmail != $scope.Profile.CompanyEmail) {
                if ($scope.Profile.Id != null) {
                    ProfileServices.ChangeCompanyEmailForLogin($scope.Profile.Id, $scope.Profile.CompanyEmail).then(function (response) {
                        //console.log(response.data);
                        if (response.data.IsReport === "Ok") {
                            toastr.success(response.data.Message, 'Successful');
                            $scope.CLC = $rootScope.divId;

                            $rootScope.divId = "3";
                            //console.log($rootScope.divId);
                        }
                        else if (response.data.IsReport === "NotOk") {
                            toastr.error(response.data.Message, 'Failed');
                            $rootScope.divId = "3";
                            //console.log($rootScope.divId);
                        }
                        else if (response.data.IsReport === "CompanyEmailExists") {
                            toastr.warning(response.data.Message, 'Warning!');
                            $rootScope.divId = "3";
                            //console.log($rootScope.divId);
                        }
                    })
                        .then(function () {
                            blockUI.start();
                            $timeout(function () {
                                $state.reload();
                                $rootScope.divId = "3";
                                //console.log($rootScope.divId);
                                blockUI.stop();
                            }, 200)

                        });
                }
            }
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }

    };

    $scope.ChangeCompanyPasswordForLogin = function () {
        if ($scope.ProfileFormChangeCompanyCredentials.$invalid == false) {
            if ($scope.Profile.Id != null) {
                ProfileServices.ChangeCompanyPasswordForLogin($scope.Profile.Id, $scope.Profile.ExistingPassword, $scope.Profile.CompanyPassword).then(function (response) {
                    //console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                        $scope.CLC = $rootScope.divId;

                        $rootScope.divId = "3";
                        //console.log($rootScope.divId);
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                        $rootScope.divId = "3";
                        //console.log($rootScope.divId);
                    }
                    else if (response.data.IsReport === "ExistingAndNewPasswordSame") {
                        toastr.warning(response.data.Message, 'Warning!');
                        $rootScope.divId = "3";
                        //console.log($rootScope.divId);
                    }
                    else if (response.data.IsReport === "ExistingPasswordDoesNotMatch") {
                        toastr.error(response.data.Message, 'Failed!');
                        $rootScope.divId = "3";
                        //console.log($rootScope.divId);
                    }
                })
                    .then(function () {
                        blockUI.start();
                        $timeout(function () {
                            $state.reload();
                            $rootScope.divId = "3";
                            //console.log($rootScope.divId);
                            blockUI.stop();
                        }, 200)

                    });
            }
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }

    };

    //$scope.UpdateCompanyPrivacyPolicy = function () {
    //    if ($scope.HasPDF == true) {
    //        if (['application/pdf'].indexOf($("#file-to-upload").get(0).files[0].type) == -1) {
    //            toastr.error('Not a PDF', 'Error!');
    //        }
    //        else {
    //            if ($scope.Profile.Id != null) {
    //                ProfileServices.UploadCP_PrivacyPolicy($scope.Profile.Id, formdata).then(function (response) {
    //                    if (response.data.IsReport == "Ok") {
    //                        toastr.success(response.data.Message, "Success!");
    //                    }
    //                    else if (response.data.IsReport == "NotOk") {
    //                        toastr.error(response.data.Message, "Error!");
    //                    }
    //                })
    //                .then(function () {
    //                    blockUI.start();
    //                    $timeout(function () {
    //                        $state.reload();
    //                        $rootScope.divId = "4";
    //                        //console.log($rootScope.divId);
    //                        blockUI.stop();
    //                    }, 200)

    //                });
    //            }
    //        }

    //    }
    //    else {
    //        toastr.error("Please select a .pdf file to upload", 'Error!');
    //    }
    //}

    //====================================================Image Upload JOB====================================================


    var companyLogo;


    var formdata = new FormData();
    $scope.getTheFiles = function ($files) {
        formdata.append(0, $files[0]);
        //console.log($files[0]);
        $scope.HasImage = true;
        //window.angular.forEach($files, function (value, key) {
        //    formdata.append(key, value);
        //    //console.log("$files");
        //    //console.log($files);
        //    //console.log("Key");
        //    //console.log(key);
        //    //console.log(value);
        //});
    };


    //   //Getting image name through jquery:
    //   $("#fileCompanyLogo").change(function () {
    //       var removeFakePath = this.value.split("\\");
    //       var getFileWithExt = removeFakePath[removeFakePath.length - 1];
    //       var splitExtension = getFileWithExt.split(".");
    //       var filename = splitExtension[0];
    //       var extension = splitExtension[1];

    //       companyLogo = getFileWithExt;
    //       //console.log(companyLogo);
    //       $scope.PristinesCI = false;
    //   });

    //   //If user do not choose any image at all, then companyLogo set to null;
    //   $("#btnRemove").click(function () {
    //       companyLogo = null;
    //       //Uncomment to make Image mandatory field and use 'NoImageFound' in ng-disabled of save/update buttons:
    //       $scope.NoImageFound = true;
    //   });

    //   //================================================PDF Operation============================================
    //   var __PDF_DOC,
    //__CURRENT_PAGE,
    //__TOTAL_PAGES,
    //__PAGE_RENDERING_IN_PROGRESS = 0,
    //__CANVAS = $('#pdf-canvas').get(0),
    //__CANVAS_CTX = __CANVAS.getContext('2d');

    //   function showPDF(pdf_url) {
    //       $("#pdf-loader").show();

    //       PDFJS.getDocument({ url: pdf_url }).then(function (pdf_doc) {
    //           __PDF_DOC = pdf_doc;
    //           __TOTAL_PAGES = __PDF_DOC.numPages;
    //           $scope.__TOTAL_PAGES = __TOTAL_PAGES;

    //           // Hide the pdf loader and show pdf container in HTML
    //           $("#pdf-loader").hide();
    //           $("#pdf-contents").show();
    //           $("#pdf-total-pages").text(__TOTAL_PAGES);

    //           // Show the first page
    //           showPage(1);
    //       }).catch(function (error) {
    //           // If error re-show the upload button
    //           $("#pdf-loader").hide();
    //           $("#upload-button").show();

    //           alert(error.message);
    //       });;
    //   }

    //   function showPage(page_no) {
    //       __PAGE_RENDERING_IN_PROGRESS = 1;
    //       __CURRENT_PAGE = page_no;
    //       $scope.__CURRENT_PAGE = __CURRENT_PAGE;

    //       // Disable Prev & Next buttons while page is being loaded
    //       $("#pdf-next, #pdf-prev").attr('disabled', 'disabled');

    //       // While page is being rendered hide the canvas and show a loading message
    //       //$("#pdf-canvas").hide();
    //       $("#page-loader").show();

    //       // Update current page in HTML
    //       $("#pdf-current-page").text(page_no);

    //       // Fetch the page
    //       __PDF_DOC.getPage(page_no).then(function (page) {
    //           // As the canvas is of a fixed width we need to set the scale of the viewport accordingly
    //           var scale_required = __CANVAS.width / page.getViewport(1).width;

    //           // Get viewport of the page at required scale
    //           var viewport = page.getViewport(scale_required);

    //           // Set canvas height
    //           __CANVAS.height = viewport.height;

    //           var renderContext = {
    //               canvasContext: __CANVAS_CTX,
    //               viewport: viewport
    //           };

    //           // Render the page contents in the canvas
    //           page.render(renderContext).then(function () {
    //               __PAGE_RENDERING_IN_PROGRESS = 0;

    //               // Re-enable Prev & Next buttons
    //               $("#pdf-next, #pdf-prev").removeAttr('disabled');

    //               // Show the canvas and hide the page loader
    //               $("#pdf-canvas").show();
    //               $("#page-loader").hide();
    //           });
    //       });
    //   }

    //   // Upon click this should should trigger click on the #file-to-upload file input element
    //   // This is better than showing the not-good-looking file input element
    //   $("#upload-button").on('click', function () {
    //       $("#file-to-upload").trigger('click');
    //   });

    //   // When user chooses a PDF file
    //   $("#file-to-upload").on('change', function () {
    //       // Validate whether PDF        
    //       if (['application/pdf'].indexOf($("#file-to-upload").get(0).files[0].type) == -1) {
    //           toastr.error('Not a PDF', 'Error!');
    //           return;
    //       }

    //       //$("#upload-button").hide();

    //       // Send the object url of the pdf
    //       var PdffileInput = document.getElementById('file-to-upload');
    //       $scope.Pdffilename = PdffileInput.files[0].name;
    //       $('#btnPdfPreview').show();
    //       $("#btnPdfPreview").html(PdffileInput.files[0].name);
    //       $scope.HasPDF = true;
    //       showPDF(URL.createObjectURL($("#file-to-upload").get(0).files[0]));
    //   });

    //   // Previous page of the PDF
    //   $("#pdf-prev").on('click', function () {
    //       if (__CURRENT_PAGE != 1)
    //           showPage(--__CURRENT_PAGE);
    //   });

    //   // Next page of the PDF
    //   $("#pdf-next").on('click', function () {
    //       if (__CURRENT_PAGE != __TOTAL_PAGES)
    //           showPage(++__CURRENT_PAGE);

    //       if ($scope.__TOTAL_PAGES == $scope.__CURRENT_PAGE) {
    //           $("#pdf-next").attr('disabled', 'disabled');
    //       }
    //   });

    //   $scope.OpenPDFModal = function () {
    //       $('#PDFModal').modal('show');
    //   }
    //   $scope.ShowThePDFFRomDB = function () {
    //       $scope.source = '../Company_PrivacyPolicy/' + $scope.Profile.PrivacyPolicy;
    //       showPDF($scope.source);
    //       $('#PDFModal').modal('show');
    //   }

    //   //======================================================PDF Upload Job====================================================
    //   var formdata = new FormData();
    //   $scope.getTheFilesPdf = function ($files) {
    //       formdata.append(0, $files[0]);
    //       console.log($files[0]);
    //       $scope.HasPDF = true;
    //       //window.angular.forEach($files, function (value, key) {
    //       //    formdata.append(key, value);
    //       //    //console.log("$files");
    //       //    //console.log($files);
    //       //    //console.log("Key");
    //       //    //console.log(key);
    //       //    //console.log(value);
    //       //});
    //   };
})