﻿/// <reference path="../../../Assets_SProvider/angularjs/angular.js" />
/// <reference path="../../../app.js" />
PCSMSApp.factory("ProfileServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        GetCompanyDetailsForCookies: function (param) {
                return $http({
                    url: "/Api/CP_Profile/GetCompanyDetailsForCookies/" + param,
                    method: "POST",
                    headers: {
                        "content-type": "application/json",
                        "cache-control": "no-cache"
                    },
                    data: param,
                    async: false
                });
            },
        UpdateCompanyInfo: function (param) {
            return $http({
                url: "/Api/CP_Profile/UpdateCompanyInfo",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        UpdateContactPersonInfo: function (param) {
            return $http({
                url: "/Api/CP_Profile/UpdateContactPersonInfo",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        ChangeCompanyEmailForLogin: function (param1, param2) {
            return $http({
                url: "/Api/CP_Profile/ChangeCompanyEmailForLogin/" + param1 + "/" + param2,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        ChangeCompanyPasswordForLogin: function (param1, param2, param3) {
            return $http({
                url: "/Api/CP_Profile/ChangeCompanyPasswordForLogin/" + param1 + "/" + param2 + "/" + param3,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetCompanyProfileDetails: function () {
            return $http({
                url: "/Api/CP_Profile/GetCompanyProfileDetails/" + $rootScope.CompanyId,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        UploadCP_PrivacyPolicy: function (id, formdata) {
            return $http({
                url: "/Api/CP_Profile/UploadCP_PrivacyPolicy/" + id,
                method: "POST",
                data: formdata,
                headers: {
                    "content-type": undefined,
                    "cache-control": "no-cache"
                }
            });
        },

        UploadCP_Logo: function (id, formdata) {
            return $http({
                url: "/Api/CP_Profile/UploadCP_Logo/" + id,
                method: "POST",
                data: formdata,
                headers: {
                    "content-type": undefined,
                    "cache-control": "no-cache"
                }
            });
        },

        /*Enternal API*/
        GetCompanyTypeList: function () {
            return $http({
                url: "/api/CompanyType/GetCompanyTypeList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        //GetCountryList: function () {
        //    return $http({
        //        url: "/api/Country/GetCountryList",
        //        method: "GET",
        //        headers: {
        //            "content-type": "application/json",
        //            "cache-control": "no-cache"
        //        },
        //        async: false
        //    });
        //},
        //GetCountryDetails: function (id) {
        //    return $http({
        //        url: "/api/Country/GetCountryDetails/"+id,
        //        method: "GET",
        //        headers: {
        //            "content-type": "application/json",
        //            "cache-control": "no-cache"
        //        },
        //        async: false
        //    });
        //},
        //GetDivisionList: function () {
        //    return $http({
        //        url: "/api/Division/GetDivisionList",
        //        method: "GET",
        //        headers: {
        //            "content-type": "application/json",
        //            "cache-control": "no-cache"
        //        },
        //        async: false
        //    });
        //},
        //GetZoneList: function () {
        //    return $http({
        //        url: "/api/Zone/GetZoneList",
        //        method: "GET",
        //        headers: {
        //            "content-type": "application/json",
        //            "cache-control": "no-cache"
        //        },
        //        async: false
        //    });
        //},
        //GetAreaList: function () {
        //    return $http({
        //        url: "/api/Area/GetAreaList",
        //        method: "GET",
        //        headers: {
        //            "content-type": "application/json",
        //            "cache-control": "no-cache"
        //        },
        //        async: false
        //    });
        //},
        //GetDivisionListByCountryId: function (CountryId) {
        //    return $http({
        //        url: "/api/Division/GetDivisionListByCountryId/" + CountryId,
        //        method: "GET",
        //        headers: {
        //            "content-type": "application/json",
        //            "cache-control": "no-cache"
        //        },
        //        async: false
        //    });
        //},
        //GetZoneListByDivisionId: function (DivisionId) {
        //    return $http({
        //        url: "/api/Zone/GetZoneListByDivisionId/" + DivisionId,
        //        method: "GET",
        //        headers: {
        //            "content-type": "application/json",
        //            "cache-control": "no-cache"
        //        },
        //        async: false
        //    });
        //},
        //GetAreaListByZoneId: function (ZoneId) {
        //    return $http({
        //        url: "/api/Area/GetAreaListByZoneId/" + ZoneId,
        //        method: "GET",
        //        headers: {
        //            "content-type": "application/json",
        //            "cache-control": "no-cache"
        //        },
        //        async: false
        //    });
        //},
        //GetDivisionDetails: function (DivisionId) {
        //    return $http({
        //        url: "/api/division/GetDivisionDetails/" + DivisionId,
        //        method: "GET",
        //        headers: {
        //            "content-type": "application/json",
        //            "cache-control": "no-cache"
        //        },
        //        async: false
        //    });
        //},
    };
}]);