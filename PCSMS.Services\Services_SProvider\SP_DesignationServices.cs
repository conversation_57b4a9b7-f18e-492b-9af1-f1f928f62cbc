﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web.Mvc;
using PCSMS.Common;
using PCSMS.Models;
using PCSMS.Models.Models_Shared;
using PCSMS.Models.Models_SProvider;

namespace PCSMS.Services.Services_SProvider
{
    public class SP_DesignationServices: ISP_DesignationServices
    {
        private readonly PCSMSDbContext _context;
        private readonly IEntityService<SP_Designation> _services;
        public SP_DesignationServices()
        {
            _context = new PCSMSDbContext();
            _services = new EntityService<SP_Designation>(_context);
        }

        //Private Services :
        private bool DesignationExist(SP_Designation obj)
        {
            var result = _context.SP_Designation.FirstOrDefault(x => x.DesignationName == obj.DesignationName && x.Id != obj.Id);

            if (result == null)
            {
                return false;
            }
            return true;
        }


        //Public Services :
        public JsonResult SaveSP_Designation(SP_Designation obj)
        {
            string message;
            try
            {
                if (_services.DoesExist(x => x.DesignationName == obj.DesignationName))
                {
                    Generator.IsReport = "DesignationExists";
                    message = "This Designation already exists !";
                }
                else
                {
                    _services.Save(obj);
                    _services.SaveChanges();

                    Generator.IsReport = "Ok";
                    message = "Designation created successfully !";
                }
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult UpdateSP_Designation(SP_Designation obj)
        {
            List<object> avoidProperties = new List<object>();
            string message;

            if (DesignationExist(obj))
            {
                Generator.IsReport = "DesignationExists";
                message = "Designation : " + "\"" + obj.DesignationName + "\"" + " already exists !";

            }
            else
            {
                try
                {
                    _services.Update(obj, avoidProperties);
                    _services.SaveChanges();
                    Generator.IsReport = "Ok";
                    message = "Designation updated successfully !";
                }
                catch (Exception ex)
                {
                    message = ex.Message;
                    Generator.IsReport = "NotOk";
                }
            }

            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult DeleteSP_Designation(long designationId)
        {
            string message;
            try
            {
                _context.Database.ExecuteSqlCommand($"Delete [SP_Designation] WHERE Id = {designationId}");
                _context.SaveChanges();
                Generator.IsReport = "Ok";
                message = "Designation deleted successfully !";
            }
            catch (Exception exp)
            {
                Generator.IsReport = "NotOk";
                message = exp.Message;
            }
            return new JsonResult
            {
                Data = new
                {
                    Generator.IsReport,
                    Message = message
                },
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult GetSP_DesignationList()
        {
            return new JsonResult
            {
                Data = _context.SP_Designation.Select(x => new
                {
                    x.Id,
                    x.DesignationName,
                }).ToList(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }

        public JsonResult GetSP_DesignationDetails(Expression<Func<SP_Designation, bool>> expression)
        {
            return new JsonResult
            {
                Data = _context.SP_Designation.Where(expression)
                .Select(x => new
                {
                    x.Id,
                    x.DesignationName,
                }).FirstOrDefault(),
                JsonRequestBehavior = JsonRequestBehavior.AllowGet
            };
        }
    }

    public interface ISP_DesignationServices
    {
        JsonResult DeleteSP_Designation(long designationId);
        JsonResult GetSP_DesignationDetails(Expression<Func<SP_Designation, bool>> expression);
        JsonResult SaveSP_Designation(SP_Designation obj);
        JsonResult UpdateSP_Designation(SP_Designation obj);
        JsonResult GetSP_DesignationList();
    }
}
