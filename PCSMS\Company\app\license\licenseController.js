﻿/// <reference path="app.js" />
PCSMSApp.controller('licenseController', function ($scope, licenseServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    //====================================================================Declaration=================================================================================
    $rootScope.LicenseList = [];
    $scope.License = {};




    //====================================================================Element Processing===================================
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [DTColumnDefBuilder.newColumnDef(5).notSortable()
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
        .withOption('paging', true)
        .withOption('searching', true)
        .withOption('info', true)
    .withOption('order', [2, 'desc']);





    $rootScope.HasActiveTrialLicense = false;
    $rootScope.CurrentlyALicenseBeingRequested = false;
    $rootScope.IsLicenseAboutToExpire = false;

    //====================================================================DB Operation==================================================
    licenseServices.GetLicenseListByCompanyId($rootScope.CompanyId).then(function (response) {
       
        $rootScope.LicenseList = response.data;
        angular.forEach(response.data, function (value, key) {
            
            if (value.LicenseType == "Trial" && value.Status == "Active") {
                $rootScope.HasActiveTrialLicense = true;
            } 
            else if (value.LicenseType == "Chargeable" && value.Status == "Requested") 
            {
                $rootScope.CurrentlyALicenseBeingRequested = true;
            }
            if (value.LicenseKey == null) {
                value.LicenseKey = "---------------------------------------";
            }
            if (value.RequestedOn != null) {
                var reqDateUtc = moment.utc(value.RequestedOn); 
                value.RequestedOn = reqDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
            }
            if (value.ExpiryDate != null) {
                var expDateUtc = moment.utc(value.ExpiryDate);
                value.ExpiryDate = expDateUtc.local().format("YYYY-MM-DD").toString();
            }
        });
    });

    $scope.Requestlicense = function () {
        swal({
            title: "Are You Sure?",
            text: "You are going to request for a new license",
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn btn-success",
            confirmButtonText: "Yes",
            cancelButtonClass: "btn btn-default",
            cancelButtonText: "No",
            closeOnConfirm: true
        },
        function (isConfirm) {
            if (isConfirm) {
                licenseServices.Requestlicense($rootScope.CompanyId).then(function (response) {
                    if (response.data.IsReport == "Ok") {
                        toastr.success(response.data.Message, "Success");
                    }
                    else if (response.data.IsReport == "NotOk") {
                        toastr.error(response.data.Message, "Error!");
                    }
                })
                .then(function () {
                    $state.reload();
                })
            } else {
                $state.reload();
            }
        });
    };

    $scope.RequestForLicenseRenewal = function (licenseId, status, secondaryStatus) {

        swal({
            title: "Are You Sure?",
            text: "You are going to request for a renewal of this existing license",
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn btn-success",
            confirmButtonText: "Yes",
            cancelButtonClass: "btn btn-default",
            cancelButtonText: "No",
            closeOnConfirm: true
        },
        function (isConfirm) {
            if (isConfirm) {
                if (status != "Requested" && secondaryStatus != "Renewal Requested") {
                    licenseServices.RequestForLicenseRenewal(licenseId).then(function (response) {
                        if (response.data.IsReport == "Ok") {
                            toastr.success(response.data.Message, "Success");
                        }
                        else if (response.data.IsReport == "NotOk") {
                            toastr.error(response.data.Message, "Error!");
                        }
                    })
                .then(function () {
                    $state.reload();
                })
                }
                else {
                    if (status == "Requested") {
                        toastr.error("License can not be renewed because it is in 'Requested' status.", "Error!");
                    }
                    else if (secondaryStatus == "Renewal Requested") {
                        toastr.error("License can not be renewed because it is in 'Renewal Requested' status.", "Error!");
                    }
                }
            } else {
                $state.reload();
            }
        });
    };

    $scope.GetLicenseDetailsByLicenseId = function (Id) {
        licenseServices.GetLicenseDetailsByLicenseId(Id).then(function (response) {
            $scope.License = response.data;
            
                if ($scope.License.ActivatedOn != null) {
                    var actDateUtc = moment.utc($scope.License.ActivatedOn);
                    $scope.License.ActivatedOn = actDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
                }
                if ($scope.License.RequestedOn != null) {
                    var reqDateUtc = moment.utc($scope.License.RequestedOn);
                    $scope.License.RequestedOn = reqDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
                }
                if ($scope.License.ExpiryDate != null) {
                    var expDateUtc = moment.utc($scope.License.ExpiryDate);
                    $scope.License.ExpiryDate = expDateUtc.local().format("YYYY-MM-DD").toString();
                }
        })
        .then(function () {
            $('#LicenseDetailsModal').modal('show');
        })
    }


    
    //====================================================================Modal Operation=============================================================================
    $scope.cancelLicenseDetailsModal = function () {
        $('#LicenseDetailsModal').modal("hide");
        $timeout(function () {
            $scope.License = {};
        }, 200);
    }



});