﻿/// <reference path="../../../app.js" />
PCSMSApp.controller('branchController', function ($scope, $rootScope, blockUI, BranchServices, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    $scope.pro = "Organization's Branch";
    $rootScope.settingsOpen = true;

    //Retrieving data from db:
    $scope.BranchList = BranchServices.GetSP_BranchList().then(function (response) {
        $scope.BranchList = response.data;
        return response.data;
    });

    //Datatable
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [
        DTColumnDefBuilder.newColumnDef(1).withOption('width', '1%').notSortable()
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
        .withOption('paging', true)
        .withOption('searching', true)
        .withOption('info', true);

    

    //Creating an empty object to be sent:
    $scope.Branch = {};



    //Opening a modal:
    $scope.openBranchModal = function (id) {

        if (id != null) {
            //blockUI.start();
            //blockUI.message("Loading Branch Details ...")
            BranchServices.GetSP_BranchDetails(id).then(function (response) {
                $scope.Branch = response.data;
                $scope.TempBranchName = response.data.BranchName;

            })
                .then(function () {
                    //blockUI.stop();
                    $('#BranchModal').modal('show');
                });
        }
    };


    //Saving or updating:
    $scope.SaveOrUpdateBranch = function () {

        if ($scope.BranchForm.$invalid == false && $scope.LiveValidation($scope.Branch.BranchName) == true) {
            console.log("===Outside===");
            console.log($scope.Branch);
            if ($scope.Branch.Id == null) {
                console.log("===Inside===");
                console.log($scope.Branch);
                //blockUI.start();
                //blockUI.message("Saving Branch ...");
                BranchServices.SaveSP_Branch($scope.Branch).then(function (response) {
                    console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "BranchExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                    else if (response.data.IsReport === "ProfileNotCreated") {
                        toastr.warning(response.data.Message, 'Warning');
                    }
                })
                    .then(function () {
                        $scope.cancelBranchListModal();
                        $scope.BranchForm.$setPristine();
                        $scope.BranchForm.$setUntouched();
                    })
                    .then(function () {
                        $timeout(function () {
                            BranchServices.GetSP_BranchList().then(function (response) {
                                $scope.BranchList = response.data;
                                return response.data;
                            })
                        }, 100)
                    })
            } else {
                //blockUI.start();
                //blockUI.message("Updating Branch ...");
                BranchServices.UpdateSP_Branch($scope.Branch).then(function (response) {
                    console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                    else if (response.data.IsReport === "BranchExists") {
                        toastr.warning(response.data.Message, 'Already Exist');
                    }
                })
                    .then(function () {
                        $scope.cancelBranchListModal();
                        $scope.BranchForm.$setPristine();
                        $scope.BranchForm.$setUntouched();
                    })
                    .then(function () {
                        $timeout(function () {
                            BranchServices.GetSP_BranchList().then(function (response) {
                                $scope.BranchList = response.data;
                                return response.data;
                            })
                        }, 100)
                    });
            }
        }
        else {
            toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
        }

    };

    //Deleting:
    $scope.deleteBranchAlert = function (BranchId, BranchName) {
        swal({
            title: "Are you sure?",
            text: "You are going to delete the branch " + BranchName,
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn-danger",
            confirmButtonText: "Yes, delete it!",
            closeOnConfirm: true
        },
            function () {
                console.log(BranchId + ' ' + BranchName);
                //blockUI.start();
                //blockUI.message("Deleting Branch ...");
                BranchServices.DeleteSP_Branch(BranchId).then(function (response) {
                    console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                })
                    .then(function () {
                        //blockUI.message("Loading Branch List ...");
                        BranchServices.GetSP_BranchList().then(function(response) {
                                $scope.BranchList = response.data;
                                return response.data;
                            })
                            .then(function() {
                                //blockUI.stop();
                            });
                    });

            });
    };

    //Closing a modal
    $scope.cancelBranchListModal = function () {
        $('#BranchModal').modal('hide');
        $timeout(function () {
            //$scope.Branch = {};
            //$scope.TempBranchName = null;
            //blockUI.stop();
            $state.reload();
        }, 300);
        //console.log($scope.Branch);
        //$scope.BranchForm.$setPristine();
        //$scope.BranchForm.$setUntouched();
    };

    //Live Validation
    $scope.LiveValidation = function (x) {        
        var valid = false;
        if ($scope.BranchList.length > 0) {
            for (var i = 0; i < $scope.BranchList.length; i++) {
                if ($scope.BranchList[i].BranchName == x) {
                    if ($scope.BranchList[i].BranchName == $scope.TempBranchName) {
                        valid = true;
                    }
                    else {
                        valid = false;
                        $scope.LiveValidationError = "Branch already exists";
                        break;
                    }
                }
                else {
                    valid = true;
                }
            }
        }
        else if ($scope.BranchList.length == 0) {
            valid = true;
        }
        return valid;
    };
})