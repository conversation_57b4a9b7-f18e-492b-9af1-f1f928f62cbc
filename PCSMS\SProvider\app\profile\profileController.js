﻿/// <reference path="../../../app.js" />
PCSMSApp.controller('profileController', function ($scope, $rootScope, ProfileServices, blockUI, $q, toastr, $compile, $http, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {


    $scope.pro = "Organization's Profile";
    $scope.IsInCreateMode = false;
    $scope.IsInDisplayMode = false;
    $scope.isInEditMode = false;
    $scope.EnableEdit = false;
    var currentDate = new Date();
    $scope.MaxDate = currentDate.setDate(currentDate.getDate() - 1);
    $scope.MinDate = currentDate.setYear(currentDate.getFullYear() - 90);
    //$('#btnPdfPreview').hide();
    $scope.HasImage = false;
    $scope.HasPdf = false;


    ProfileServices.GetCompanyTypeList().then(function(response) {
            $scope.CompanyTypeList = response.data;
        })
        .then(function() {
            ProfileServices.GetSP_ProfileDetails().then(function(response) {
                $scope.Profile = response.data;
                if ($scope.Profile != null) {
                    if ($scope.Profile.OrganizationLogo != null) {
                        $('#imageHolder').prepend('<img id="imageTag" src="" />');
                        $('#imageTag').attr("src", "../SProvider_Images/Profile_Images/" + $scope.Profile.OrganizationLogo);
                        //imageName = response.data.CompanyLogo ;
                        $scope.HasImage = true;
                    } else {
                        $('#imageHolder').prepend('<img id="imageTag" src="" />');
                        $('#imageTag').attr("src", "../SProvider_Images/Default_Images/Image-not-found.png");
                        $scope.HasImage = false;
                    }
                    if ($scope.Profile.OrganizationLogo != null) {
                        $('#imageHolderEdit').prepend('<img id="imageTagEdit" src="" />');
                        $('#imageTagEdit').attr("src", "../SProvider_Images/Profile_Images/" + $scope.Profile.OrganizationLogo);
                        //imageName = response.data.CompanyLogo ;
                        $scope.HasImage = true;
                    } else {
                        $('#imageHolderEdit').prepend('<img id="imageTagEdit" src="" />');
                        $('#imageTagEdit').attr("src", "../SProvider_Images/Default_Images/Image-not-found.png");
                        $scope.HasImage = false;
                    }
                    if ($scope.Profile.OrganizationLogo != null) {
                        $('#imageHolderView').prepend('<img id="imageTagView" src="" />');
                        $('#imageTagView').attr("src", "../SProvider_Images/Profile_Images/" + $scope.Profile.OrganizationLogo);
                        //imageName = response.data.CompanyLogo ;
                        $scope.HasImage = true;
                    } else {
                        $('#imageHolderView').prepend('<img id="imageTagView" src="" />');
                        $('#imageTagView').attr("src", "../SProvider_Images/Default_Images/Image-not-found.png");
                        $scope.HasImage = false;
                    }
                }
                //console.log(response.data);
                if ($scope.Profile == null) {
                    $scope.IsInCreateMode = true;
                    $scope.IsInDisplayMode = false;
                    $scope.isInEditMode = false;
                    $('#tab1').trigger('click');
                } else {
                    $scope.IsInDisplayMode = true;
                }
            });
        });
    



    //------------------------------------------------------------------Reverse Geo Coding FUNC-----------------------------------------------------------------------
    function geocodeLatLng(geocoder) {
        $scope.inputModel = $scope.Profile.OrganizationLat + "," + $scope.Profile.OrganizationLong;
        $scope.latlngStr = $scope.inputModel.split(',', 2);
        $scope.latlng = { lat: parseFloat($scope.latlngStr[0]), lng: parseFloat($scope.latlngStr[1]) };
        geocoder.geocode({ 'location': $scope.latlng }, function (results, status) {
            if (status === 'OK') {
                if (results[0]) {
                    $scope.SearchedPlace = results[0].formatted_address;
                    $scope.infowindow.setContent("<span class='badge x-darker-1'>" + $scope.SearchedPlace + "</span>");
                    $scope.infowindow.open($scope.map, $scope.marker);
                    $scope.PristinesCI = false;

                } else {
                    toastr.error('No result found', 'Error!');
                }
            } else {
                toastr.error('Geocoder failed due to: ' + status);
            }
        });
    }

    //Load Map

    $scope.LoadMap = function () {
        $timeout(function () {
            //console.log("Map Load Hitted");
            //Display Mode (Has Data)
            $scope.map = new google.maps.Map(document.getElementById('map-canvasC'), {
                center: { lat: 23.915416, lng: 89.9129994 },
                zoom: 6,
                draggable: true,
                gestureHandling: 'greedy'
            });

            $scope.marker = new google.maps.Marker({
                map: $scope.map,
                draggable: true
            });
            //console.log("Map Loaded Else C1");


            $scope.geocoder = new google.maps.Geocoder(); //Geocoder Defination
            $scope.infowindow = new google.maps.InfoWindow(); //InfoWindow Defination
            var input = document.getElementById('SearchLocation');
            var autocomplete = new google.maps.places.Autocomplete(input);

            var inputEdit = document.getElementById('SearchLocationEdit');
            var autocompleteEdit = new google.maps.places.Autocomplete(inputEdit);

            // Bind the map's bounds (viewport) property to the autocomplete object,
            // so that the autocomplete requests use the current map bounds for the
            // bounds option in the request.
            autocomplete.bindTo('bounds', $scope.map);
            autocompleteEdit.bindTo('bounds', $scope.map);

            //Set Map and Marker to Dragable position while editing


            //console.log($scope.Profile);
            if ($scope.Profile != null) {
                if (($scope.Profile.OrganizationLat > 0 || $scope.Profile.OrganizationLong > 0) && $scope.IsInDisplayMode == true) {
                    $scope.map = new google.maps.Map(document.getElementById('map-canvasD'), {
                        center: { lat: $scope.Profile.OrganizationLat, lng: $scope.Profile.OrganizationLong },
                        zoom: 17,
                        draggable: false,
                        gestureHandling: 'greedy'
                    });

                    $scope.marker = new google.maps.Marker({
                        map: $scope.map,
                        position: {
                            lat: $scope.Profile.OrganizationLat,
                            lng: $scope.Profile.OrganizationLong
                        },
                        draggable: false
                    });
                    console.log("Map Loaded D1");
                    var contentString = "<span class='badge x-darker-1'>" + $scope.Profile.OrganizationName + "</span>";

                    //Creating infoWindow
                    $scope.infowindow.setContent(contentString);
                }
                    //Display Mode !(Has Data)
                else if (($scope.Profile.OrganizationLat <= 0 || $scope.Profile.OrganizationLong <= 0) && $scope.IsInDisplayMode == true) {
                    $scope.map = new google.maps.Map(document.getElementById('map-canvasD'), {
                        center: { lat: 23.915416, lng: 89.9129994 },
                        zoom: 6,
                        draggable: false,
                        gestureHandling: 'greedy'
                    });

                    $scope.marker = new google.maps.Marker({
                        map: $scope.map,
                        draggable: false
                    });
                    console.log("Map Loaded D2");
                    var contentString = "<span class='badge x-darker-1'>" + $scope.Profile.OrganizationName + "</span>";

                    //Creating infoWindow
                    $scope.infowindow.setContent(contentString);
                }
                    //Edit Mode !(Has Data)
                else if (($scope.Profile.OrganizationLat <= 0 || $scope.Profile.OrganizationLong <= 0) && $scope.isInEditMode == true) {
                    $scope.map = new google.maps.Map(document.getElementById('map-canvasE'), {
                        center: { lat: 23.915416, lng: 89.9129994 },
                        zoom: 6,
                        draggable: true,
                        gestureHandling: 'greedy'
                    });

                    $scope.marker = new google.maps.Marker({ map: $scope.map });
                    console.log("Map Loaded E1");
                    var contentString = "<span class='badge x-darker-1'>" + $scope.Profile.OrganizationName + "</span>";

                    //Creating infoWindow
                    $scope.infowindow.setContent(contentString);
                }
                    //Edit Mode (Has Data)
                else if (($scope.Profile.OrganizationLat > 0 || $scope.Profile.OrganizationLong > 0) && $scope.isInEditMode == true) {
                    $scope.map = new google.maps.Map(document.getElementById('map-canvasE'), {
                        center: { lat: $scope.Profile.OrganizationLat, lng: $scope.Profile.OrganizationLong },
                        zoom: 17,
                        draggable: true,
                        gestureHandling: 'greedy'
                    });

                    $scope.marker = new google.maps.Marker({
                        map: $scope.map,
                        position: {
                            lat: $scope.Profile.OrganizationLat,
                            lng: $scope.Profile.OrganizationLong
                        },
                        draggable: true
                    });
                    console.log("Map Loaded E2");
                    var contentString = "<span class='badge x-darker-1'>" + $scope.Profile.OrganizationName + "</span>";

                    //Creating infoWindow
                    $scope.infowindow.setContent(contentString);
                }
                else if ($scope.IsInCreateMode == true) {
                    $scope.map = new google.maps.Map(document.getElementById('map-canvasC'), {
                        center: { lat: 23.915416, lng: 89.9129994 },
                        zoom: 6,
                        draggable: false,
                        gestureHandling: 'greedy'
                    });
                    $scope.infowindow = new google.maps.InfoWindow();
                    $scope.marker = new google.maps.Marker({ map: $scope.map });
                    console.log("Map Loaded C");
                    var contentString = "<span class='badge x-darker-1'>" + $scope.Profile.OrganizationName + "</span>";

                    //Creating infoWindow
                    $scope.infowindow.setContent(contentString);
                }
            }


            autocomplete.addListener('place_changed', function () {
                $scope.infowindow.close();
                $scope.marker.setVisible(false);
                var place = autocomplete.getPlace();
                if (!place.geometry) {
                    // User entered the name of a Place that was not suggested and
                    // pressed the Enter key, or the Place Details request failed.
                    window.alert("No details available for input: '" + place.name + "'");
                    return;
                }

                // If the place has a geometry, then present it on a map.
                if (place.geometry.viewport) {
                    $scope.map.fitBounds(place.geometry.viewport);
                    $scope.Profile.OrganizationLat = place.geometry.location.lat();
                    $scope.Profile.OrganizationLong = place.geometry.location.lng();
                    console.log("--After Binding New Location--");
                    console.log($scope.Profile.OrganizationLat);
                    console.log($scope.Profile.OrganizationLong);
                    console.log(place.adr_address);
                } else {
                    $scope.map.setCenter(place.geometry.location);
                    $scope.map.setZoom(17);  // Why 17? Because it looks good.
                }
                $scope.marker.setPosition(place.geometry.location);
                $scope.marker.setVisible(true);
                $scope.marker.setOptions({ draggable: true });
                $scope.infowindow.setContent("<span class='badge x-darker-1'>" + place.adr_address + "</span>");
                $scope.infowindow.open($scope.map, $scope.marker);
                geocodeLatLng($scope.geocoder);
            });


            autocompleteEdit.addListener('place_changed', function () {
                $scope.infowindow.close();
                $scope.marker.setVisible(false);
                var place = autocompleteEdit.getPlace();
                if (!place.geometry) {
                    // User entered the name of a Place that was not suggested and
                    // pressed the Enter key, or the Place Details request failed.
                    window.alert("No details available for input: '" + place.name + "'");
                    return;
                }

                // If the place has a geometry, then present it on a map.
                if (place.geometry.viewport) {
                    $scope.map.fitBounds(place.geometry.viewport);
                    $scope.Profile.OrganizationLat = place.geometry.location.lat();
                    $scope.Profile.OrganizationLong = place.geometry.location.lng();
                    console.log("--After Binding New Location--");
                    console.log($scope.Profile.OrganizationLat);
                    console.log($scope.Profile.OrganizationLong);
                    console.log(place.adr_address);
                } else {
                    $scope.map.setCenter(place.geometry.location);
                    $scope.map.setZoom(17);  // Why 17? Because it looks good.
                }
                $scope.marker.setPosition(place.geometry.location);
                $scope.marker.setVisible(true);
                $scope.marker.setOptions({ draggable: true });
                $scope.infowindow.setContent("<span class='badge x-darker-1'>" + place.adr_address + "</span>");
                $scope.infowindow.open($scope.map, $scope.marker);
                geocodeLatLng($scope.geocoder);
            });


            //Click On Empty Map

            //Map CLICK Event
            $scope.map.addListener('click', function (e) {
                if ($scope.isInEditMode == true) {
                    if (($scope.Profile.OrganizationLat == undefined || $scope.Profile.OrganizationLat == "") && ($scope.Profile.OrganizationLong == undefined || $scope.Profile.OrganizationLong == "")) {
                        $scope.myLatLng = new google.maps.LatLng(e.latLng.lat(), e.latLng.lng());
                        $scope.Profile.OrganizationLat = e.latLng.lat();
                        $scope.Profile.OrganizationLong = e.latLng.lng();
                        $scope.marker.setVisible(true);
                        $scope.marker.setPosition($scope.myLatLng);
                        $scope.marker.setOptions({ draggable: true });
                        geocodeLatLng($scope.geocoder);
                    }
                    else {
                        toastr.warning('There is already a marker on this map available. Please drag position or search for precious location on search box', 'Warning!');
                        $scope.map.setCenter({ lat: $scope.Profile.OrganizationLat, lng: $scope.Profile.OrganizationLong });
                        $scope.map.setZoom(17);  // Why 17? Because it looks good.
                    }
                }
                else {
                    if ($scope.isInEditMode == true) {
                        toastr.error('Edit to pin or move pin on map', 'Error!');
                    }
                    else if ($scope.IsInCreateMode == true) {
                        toastr.info('Please search for location to pin on map', 'Info!');
                    }
                }

                console.log(e.latLng.lat());
                console.log(e.latLng.lng());
            });

            //Marker DRAG Event
            $scope.marker.addListener('drag', function () {
                console.log('Dragging...');
                $scope.infowindow.close();
                $scope.infowindow.setContent(null);
            });

            //Marker CLICK Event
            $scope.marker.addListener('click', function () {
                $scope.infowindow.open($scope.map, $scope.marker);
            });


            //Marker DRAGEND Event
            $scope.marker.addListener('dragend', function (e) {
                $scope.PristinesCI = false;
                $scope.Profile.OrganizationLat = e.latLng.lat();
                $scope.Profile.OrganizationLong = e.latLng.lng();
                console.log("--Only Draggin LAT")
                console.log($scope.Profile.OrganizationLat);
                console.log("--Only Draggin LNG")
                console.log($scope.Profile.OrganizationLong);
                geocodeLatLng($scope.geocoder);
            });



        }, 3000)
        .then(function () {

            $scope.EnableEdit = true;
        });

    };

    //Creating an empty object to be sent:
    $scope.Profile = {};


    //Switch to edit/default mode
    $scope.SwitchToEditMode = function () {
        $scope.EnableEdit = false;
        $scope.isInEditMode = true;
        $scope.IsInDisplayMode = false;
        $('#tab3').trigger('click');
        $scope.LoadMap();
    }
    $scope.SwitchToDisplayMode = function () {
        ProfileServices.GetSP_ProfileDetails().then(function (response) {
            $scope.Profile = response.data;
        })
        .then(function () {
            //blockUI.start();
            $timeout(function () {
                $state.reload();
                //blockUI.stop();
            }, 1)
        });
        $scope.EnableEdit = false;
        $('#tab2').trigger('click');
        $scope.IsInDisplayMode = true;
        $scope.isInEditMode = false;
        $scope.LoadMap();
        //$("#file-to-upload").val(null);
        ////$('#btnPdfPreview').hide();
        //$("#btnPdfPreview").html("Preview");
    }

    //Saving or updating:
    $scope.SaveOrUpdateProfile = function () {
        //SAVING DATA
        if ($scope.Profile.Id == null) {
            if ($scope.ProfileCreateForm.$invalid == false) {
                //blockUI.start();
                //blockUI.message("Saving Profile Information ...");
                ProfileServices.SaveSP_Profile($scope.Profile).then(function (response) {
                    console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                })
                .then(function () {
                    //Photo Upload
                    //blockUI.message("Uploading Logo ...");
                    if ($scope.HasImage==true) {
                        ProfileServices.UploadSP_Logo(formdata).then(function (response) {
                            if (response.data.IsReport == "NotOk") {
                                if (response.data.IsReport == "NotOk") {
                                    toastr.error(response.data.Message, "Error!");
                                }
                            }
                        })
                    }
                })
                .then(function () {
                    //blockUI.start();
                    $timeout(function () {
                        $state.reload();
                        //blockUI.stop();
                    })
                });
            }
            else {
                toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
            }

        } else {
            //UPDATING DATA
            if ($scope.ProfileEditForm.$invalid == false) {
                //blockUI.start();
                //blockUI.message("Updating Profile Information ...");
                ProfileServices.UpdateSP_Profile($scope.Profile).then(function (response) {
                    console.log(response.data);
                    if (response.data.IsReport === "Ok") {
                        toastr.success(response.data.Message, 'Successful');
                    }
                    else if (response.data.IsReport === "NotOk") {
                        toastr.error(response.data.Message, 'Failed');
                    }
                })
                .then(function () {
                    //Photo Upload
                    
                    if ($scope.HasImage == true) {
                        //blockUI.message("Uploading Logo ...");
                        ProfileServices.UploadSP_Logo(formdata).then(function (response) {
                            console.log(response.data.message);
                            if ($scope.HasImage == true) {

                            }
                            else if ($scope.HasImage == false) {
                                toastr.error(response.data.Message, "Error!");
                            }
                        })
                    }
                })
                .then(function () {
                    //blockUI.start();
                    $timeout(function () {
                        $state.reload();
                        //blockUI.stop();
                    })
                });
            }
            else {
                toastr.error("This form contains invalid data. Can not be submitted", 'Error!');
            }
        }
    };

    //====================================================Image Upload JOB====================================================


    var companyLogo;


    var formdata = new FormData();
    $scope.getTheFiles = function ($files) {
        formdata.append(0, $files[0]);
        console.log($files[0]);
        $scope.HasImage = true;
    };


    //Getting image name through jquery:
    $("#fileCompanyLogo").change(function () {
        var removeFakePath = this.value.split("\\");
        var getFileWithExt = removeFakePath[removeFakePath.length - 1];
        var splitExtension = getFileWithExt.split(".");
        var filename = splitExtension[0];
        var extension = splitExtension[1];

        companyLogo = getFileWithExt;
        console.log(companyLogo);
        $scope.PristinesCI = false;
    });

    //If user do not choose any image at all, then companyLogo set to null;
    $("#btnRemove").click(function () {
        companyLogo = null;
        //Uncomment to make Image mandatory field and use 'NoImageFound' in ng-disabled of save/update buttons:
        $scope.NoImageFound = true;
    });

})
