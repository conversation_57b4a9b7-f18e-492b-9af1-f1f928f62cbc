﻿


==========Step 0:=============Create a column inside Model (code first method) as follows:
[DatabaseGenerated(DatabaseGeneratedOption.Computed)]
[MaxLength(70)]
public string PackageCode { get; set; }

 
==========Step 1:=============Change DB name as needed, Change FUNCTION name as needed. 
USE [ThirdEye]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE FUNCTION [dbo].[MyAutoPackageCode](
    @Value int
) 
RETURNS varchar(10) 
WITH SCHEMABINDING 
AS 
BEGIN
    DECLARE @ReturnValue varchar(8);

    SET @ReturnValue = CONVERT(varchar(8), @Value);
    SET @ReturnValue = REPLICATE('0', 8 - DATALENGTH(@ReturnValue)) + @ReturnValue;

    RETURN (@ReturnValue);
END;



==========Step 2:=============Run, It will generate a function inside Programability>Functions>scalar-valued-functions. 
==========Step 3:=============then, packagemanagerconsole> update-database, Go to Sql design mode for the table that going to have this auto generated code; select that column 
==========Step 4:=============Go down, Open 'Computed Column Specification>Formula
==========Step 5:=============write : (isnull('PCKG-'+[dbo].[MyAutoPackageCode]([Id]),'')) [as you want]
==========Step 6:=============save
==========Step 6:=============visual studio>tools>console package manager> update-database
