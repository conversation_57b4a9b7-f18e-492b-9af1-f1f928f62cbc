﻿/// <reference path="app.js" />
PCSMSApp.controller('databaseController', function ($scope, databaseServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {
    $scope.Database = {};

    //Getting list
    databaseServices.GetCompanyListWhoHasScreenshots().then(function (response) {
        $scope.CompanyList = response.data;
    });

    //Deleting:
    $scope.DeleteScreenshots = function () {

        // scope.AllCompaniesChkBoxSelected() == false means single company's screenshots to be deleted
        if ($scope.AllCompaniesChkBoxSelected() == false){
            databaseServices.DeleteSingularCompanyScreenshotsFromFolderByDateRange($scope.Database.CompanyId,moment($scope.Database.DateUpTo).format("YYYY-MM-DD HH:mm:ss.sss")).then(function(response) {
                    if (response.data.IsReport == "Ok") {
                        toastr.success(response.data.Message, "Success");
                    } 
                    else if (response.data.IsReport == "NoImage") {
                        toastr.warning(response.data.Message, "Warning!");
                    }
                    else if (response.data.IsReport == "NotOk") {
                        toastr.error(response.data.Message, "Error!");
                    }
                })
                .then(function() {
                    $timeout(function() {
                        $state.reload();
                    },300);
                });
        }
        else {
            databaseServices.DeleteAllCompanyScreenshotsFromFolderByDateRange(moment($scope.Database.DateUpTo).format("YYYY-MM-DD HH:mm:ss.sss")).then(function (response) {
                    if (response.data.IsReport == "Ok") {
                        toastr.success(response.data.Message, "Success");
                    }
                    else if (response.data.IsReport == "NoImage") {
                        toastr.warning(response.data.Message, "Warning!");
                    }
                    else if (response.data.IsReport == "NotOk") {
                        toastr.error(response.data.Message, "Error!");
                    }
                })
                .then(function () {
                    $timeout(function () {
                        $state.reload();
                    }, 300);
                });
        }
        
    }


    

    //==============Additional Methods:

    //setting date-max-limit for disabling
    var date = new Date();
    $scope.DisableUpTo = date.setDate(date.getDate() - 14);
    //$scope.DisableUpTo = date;


    //checking form validation 
    $scope.IsFormValid = function () {
        if (($scope.Database.CompanyId == null && $scope.Database.AllCompanies == "Y" && $scope.Database.DateUpTo != null)
            || ($scope.Database.CompanyId > 0 && ($scope.Database.AllCompanies == "N"
            || $scope.Database.AllCompanies == undefined) && $scope.Database.DateUpTo != null)) {
            return true;
        } else {
            return false;
        }
    }

    $scope.AllCompaniesChkBoxSelected = function () {
        if ($scope.Database.AllCompanies == "Y") {
            $scope.Database.CompanyId = null;
            $scope.DatabaseForm.Company.$setPristine(true);
            return true;
        } else {
            return false;
        }
    }

});