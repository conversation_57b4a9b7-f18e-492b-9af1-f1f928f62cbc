﻿/// <reference path="app.js" />
PCSMSApp.controller('appController', function ($scope, $rootScope, appServices, $cookies, licenseServices, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {

    //====================================================================Declaration=================================================================================

    
    $scope.UserCanAccessManageDatabaseMenu = function() {
        if ($rootScope.UserAccessTypeName == "Super Admin" || $rootScope.UserAccessTypeName == "Admin") {
            return true;
        }
        return false;
    }

    $scope.DownloadDesktopApp = function () {
        window.location = '/Download-app/PCSMS-App.exe';
    }


    //====================================================================Element Processing==========================================================================

    $scope.HTMLCollapser = function () {
        if ($rootScope.HTMLCollapseStatus == "fixed left-sidebar-top") {
            updateCookie('HTMLCollapseStatus', "fixed left-sidebar-top left-sidebar-collapsed");
            $rootScope.HTMLCollapseStatus = "fixed left-sidebar-top left-sidebar-collapsed";
        }
        else if ($rootScope.HTMLCollapseStatus == "fixed left-sidebar-top left-sidebar-collapsed") {
            updateCookie('HTMLCollapseStatus', "fixed left-sidebar-top");
            $rootScope.HTMLCollapseStatus = "fixed left-sidebar-top";
        }
    }




    //====================================================================Miscellaneous Function======================================================================

    $scope.LogOut = function () {
        appServices.Logout($rootScope.UserId).then(function (response) {
            $rootScope.SProvider_Token_Codeaura_Demo = null;
            if (response.data == true) {

                $cookies.remove('SProvider_Token_Codeaura_Demo', { path: '/' });
                $cookies.remove('SProvider_Details_Codeaura_Demo', { path: '/' });
                $window.location.href = "/SProvider/#/logIn";

                toastr.info("You have logged out !", {
                    timeOut: 2000
                });
            }
        });
    };


    //For HTML Collapsing
    function updateCookie(name, value) {
        document.cookie = name + '=' + value + '; Path=/; Expires=' + new Date() + ';';;

    };


    
    //signalR:
    var audio = new Audio('/Notification_Audio/light.mp3');
    var notificationHub = $.connection.notificationHub;

    //Event 1:
    notificationHub.client.onLicenseRequested = function () {
        audio.play();
        toastr.success("A new license has been requested", 'Success !');
        licenseServices.GetAllLicenseList().then(function (response) {
            $rootScope.LicenseList = response.data;

            angular.forEach(response.data, function (value, key) {
                if (value.RequestedOn != null) {
                    var reqDateUtc = moment.utc(value.RequestedOn);
                    value.RequestedOn = reqDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
                }
                if (value.ExpiryDate != null) {
                    var expDateUtc = moment.utc(value.ExpiryDate);
                    value.ExpiryDate = expDateUtc.local().format("YYYY-MM-DD").toString();
                }
            });
        });


    };
    //Event 2:
    notificationHub.client.onLicenseRenewalRequested = function (licenseCode) {
        audio.play();
        toastr.success("A renewal requested for License Code : " + licenseCode, 'Success !');
        licenseServices.GetAllLicenseList().then(function (response) {
            $rootScope.LicenseList = response.data;

            angular.forEach(response.data, function (value, key) {
                if (value.RequestedOn != null) {
                    var reqDateUtc = moment.utc(value.RequestedOn);
                    value.RequestedOn = reqDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
                }
                if (value.ExpiryDate != null) {
                    var expDateUtc = moment.utc(value.ExpiryDate);
                    value.ExpiryDate = expDateUtc.local().format("YYYY-MM-DD").toString();
                }
            });
        });


    };
    //Event 3:
    notificationHub.client.broadcastSProdiverThatCompanyLicenseExpired = function (companyName, licenseCode) {
        //audio.play();
        //toastr.info("License Code : " + licenseCode + " expired for " + companyName, 'Info !');
        licenseServices.GetAllLicenseList().then(function (response) {
            $rootScope.LicenseList = response.data;
            angular.forEach(response.data, function (value, key) {
                if (value.RequestedOn != null) {
                    var reqDateUtc = moment.utc(value.RequestedOn);
                    value.RequestedOn = reqDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
                }
                if (value.ExpiryDate != null) {
                    var expDateUtc = moment.utc(value.ExpiryDate);
                    value.ExpiryDate = expDateUtc.local().format("YYYY-MM-DD").toString();
                }
            });
        });


    };


    $.connection.hub.start().done(function () {
        console.log("Hub started from sprovider");
    });
});