﻿/// <reference path="app.js" />
PCSMSApp.controller('screenshotsController', function($scope,
    deviceServices,
    screenshotsServices,
    screenshotsServices,
    $rootScope,
    appServices,
    $cookies,
    $http,
    blockUI,
    $window,
    $q,
    toastr,
    $compile,
    $timeout,
    DTOptionsBuilder,
    DTColumnBuilder,
    DTColumnDefBuilder,
    $state) {
    //====================================================================Declaration=================================================================================
    $scope.DeviceList = [];
    $scope.Loader = false;

    $scope.ScreenshotDownloadable = false;

    function isAnyScreenshotDownloadable(obj) {
        var keepGoing = true;
        var result = false;
        var count = 0;
        angular.forEach(obj,
            function(value, key) {
                if (keepGoing) {
                    if (value.Status == null) {
                        count = count + 1;
                        if (count > 1) {

                            keepGoing = false;
                            result = true;
                        }
                    }
                }
            });
        return result;
    }

    function convertDatabaseUtcDatetimeToLocalPcTime(obj) {
        angular.forEach(obj,
            function (value, key) {
                if (value.CapturedOn != null) {
                    var capDateUtc = moment.utc(value.CapturedOn);
                    value.CapturedOn = capDateUtc.local().format("DD-MM-YYYY hh:mm:ss a").toString();
                }
                
            });
    }


    //Sorting datatable by date:
    $scope.vm = {};
    $scope.vm.dtInstance = {};
    $scope.vm.dtColumnDefs = [
        DTColumnDefBuilder.newColumnDef(0).notSortable() //note : this zero to adjust for avoiding error
    ];
    $scope.vm.dtOptions = DTOptionsBuilder.newOptions()
        .withOption('paging', true).withDisplayLength(5)
        .withOption('searching', true)
        .withOption('info', true)
        .withOption('order', [0, 'desc']); //note : this zero to adjust for avoiding error

    


  

    //Modal for each screenshot
    $scope.openFilePreviewModal = function(obj) {

        //console.log(obj.Url);
        $timeout(function() {
                //if (obj.Photo != null) {
                //    $scope.imgSrc = "../Company_Images/User_Images/" + obj.Photo;
                //} else {
                //    if (obj.Gender == 'Male') {
                //        $scope.imgSrc = "../Company_Images/Default_Images/male.png";
                //    }
                //    else if (obj.Gender == 'Female') {
                //        $scope.imgSrc = "../Company_Images/Default_Images/female.jpg";
                //    }
                //}


                $scope.file = {
                    Url: obj.Url,
                    CapturedOn: obj.CapturedOn,
                    MouseClick: obj.MouseClick,
                    KeyStroke: obj.KeyStroke,
                    ScreenShot: obj.ScreenShot,
                    UserFullName: obj.FirstName + " " + obj.LastName,
                    Designation: obj.Designation,
                    UserPhotoUrl: obj.UserPhotoUrl,
                    Gender: obj.Gender,
                    DeviceUniqueId: obj.DeviceUniqueId,
                    DeviceName: obj.DeviceName,
                    Status: obj.Status,
                }
            },
            200);

        $('#FilePreviewModal').modal('show');
    };
    $scope.cancelFilePreviewModal = function() {
        $('#FilePreviewModal').modal('hide');
        $timeout(function() {
                $scope.file = null;
            },
            200);
    };

    //Downloading Screenshots:
    $scope.ScreenshotsDownloadable = false;
    $scope.DownloadScreenshot = function(obj) {
        if (obj) {
            var link = document.createElement('a');
            link.href = obj.Url; // use realtive Url 
            link.download = obj.ScreenShot;
            document.body.appendChild(link);
            link.click();
        } else {
            toastr.error("No screenshot found for download !", "Error!");
        }

    }
   
    //Deleting Screenshot:
    $scope.DeleteScreenshot = function(obj) {
        if (obj) {
            swal({
                    title: "Are You Sure ?",
                    text: "You can not undo this action once the screenshot gets deleted !",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn btn-success",
                    confirmButtonText: "Yes",
                    cancelButtonClass: "btn btn-default",
                    cancelButtonText: "No",
                    closeOnConfirm: true
                },
                function(isConfirm) {
                    if (isConfirm) {
                        screenshotsServices.DeleteScreenshot(obj.Id).then(function(response) {
                                if (response.data.IsReport == "Ok") {
                                    toastr.success(response.data.Message, "Success");
                                    $scope.RefreshSS(obj.DeviceUniqueId);

                                } else if (response.data.IsReport == "NotOk") {
                                    toastr.error(response.data.Message, "Error!");
                                }
                            })
                            .then(function() {
                                var result = $scope.GetScreenShotsForDevice();
                                $scope.ScreenshotDownloadable = isAnyScreenshotDownloadable(result);
                            });
                    }
                });

        } else {
            toastr.error("No screenshot found for deletation !", "Error!");
        }
    }


    

    //=========================

    screenshotsServices.GetScreenshotsListByCompanyId($rootScope.CompanyId).then(function(response) {
        $scope.MyList = response.data;
        console.log(response.data);
        //now converting datetime to local:
        angular.forEach($scope.MyList, function (obj, key) {
            convertDatabaseUtcDatetimeToLocalPcTime(obj.Screenshots);
        });
    });
    
    $scope.GetPrevious = function (deviceUniqueId, lastId) {
        screenshotsServices.GetScreenshotsListByPrevButton(deviceUniqueId, $rootScope.CompanyId, lastId).then(function(response) {
            $scope.NewSShots = response.data;
            console.log($scope.NewSShots);
            if ($scope.NewSShots == null) {
                toastr.info("No old screenshot found !", "Info !");
            } else {
                //now converting datetime to local:
                convertDatabaseUtcDatetimeToLocalPcTime(response.data);

                angular.forEach($scope.MyList, function (obj, key) {
                    if (obj.DeviceUniqueId == deviceUniqueId) {
                        obj.Screenshots = [];

                        angular.forEach($scope.NewSShots, function (val, key) {
                            //pushing into array
                            obj.Screenshots.push(val);
                        });
                    }


                });
            }

            
            });

    }
    
    $scope.GetNew = function (deviceUniqueId, lastId) {

        $scope.PrevBtnClicked = false;
        console.log(deviceUniqueId);
        console.log(lastId);
        screenshotsServices.GetScreenshotsListByNewButton(deviceUniqueId, $rootScope.CompanyId, lastId).then(function (response) {

            $scope.NewSShots = response.data;
            console.log($scope.NewSShots);

            if ($scope.NewSShots == null) {
                toastr.info("No new screenshot found !", "Info !");
            } else {
                //now converting datetime to local:
            convertDatabaseUtcDatetimeToLocalPcTime(response.data);

            angular.forEach($scope.MyList, function (obj, key) {
                if (obj.DeviceUniqueId == deviceUniqueId && $scope.NewSShots != null) {
                    obj.Screenshots = [];

                    angular.forEach($scope.NewSShots, function (val, key) {
                        //pushing into array 
                        obj.Screenshots.push(val);
                    });
                }


            });
            }
            
        });

    }


});