﻿/// <reference path="app.js" />
PCSMSApp.controller('defaultScheduleController', function ($scope, deviceServices, defaultScheduleServices, $rootScope, appServices, $cookies, blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {

    $scope.ScheduleMenuOpen = true;
    
    
    $scope.Schedule = {};
    
    $scope.AtleastOneDateIsSelected = false; //This is needed for validation
    $scope.Loading = false;//For Capturing and identyfying loader;
    
    $scope.TimingList = [
        {"Timing": 1, "Id":1},
        {"Timing": 5, "Id":5},
        {"Timing": 10, "Id":10},
        {"Timing": 15, "Id":15},
        {"Timing": 20, "Id":20},
        {"Timing": 30, "Id":30},
        {"Timing": 60, "Id":60},
        {"Timing": "Random", "Id":"Random"},
        ]

    $scope.Schedule.Interval = 15;

    //$scope.Schedule.StartTime = moment("09:00:00", "HH:mm:ss")._d;
    //$scope.Schedule.EndTime = moment("22:00:00", "HH:mm:ss")._d;

    $scope.hstep = 1;
    $scope.mstep = 1;
    $scope.ismeridian = true;
    $scope.Schedule.Sat = "N";
    $scope.Schedule.Sun = "N";
    $scope.Schedule.Mon = "N";
    $scope.Schedule.Tues = "N";
    $scope.Schedule.Wed = "N";
    $scope.Schedule.Thurs = "N";
    $scope.Schedule.Fri = "N";
    
    $scope.IsAtLeastOneDaySelected = function() {
        if ($scope.Schedule.Sat == "N" &&
            $scope.Schedule.Sun == "N" &&
            $scope.Schedule.Mon == "N" &&
            $scope.Schedule.Tues == "N" &&
            $scope.Schedule.Wed == "N" &&
            $scope.Schedule.Thurs == "N" &&
            $scope.Schedule.Fri == "N") {
            return false;
        }
        return true;
    }


    defaultScheduleServices.GetDeviceDefaultScheduleByCompanyId($rootScope.CompanyId).then(function (response) {
        $scope.Schedule = response.data;
        
        if ($scope.Schedule != null) {
            $scope.Schedule.StartTime = moment($scope.Schedule.StartTime, "HH:mm:ss")._d;
            $scope.Schedule.EndTime = moment($scope.Schedule.EndTime, "HH:mm:ss")._d;
            if ($scope.Schedule.IsRandom == "Y") {
                $scope.Schedule.Interval = "Random";
            }
        }
        
    });
    $scope.CreateOrUpdateDefaultSchedule = function () {
        console.log($scope.Schedule.Id);

        if ($scope.Schedule.Interval == "Random") {
            $scope.Schedule.Interval = null;
            $scope.Schedule.IsRandom = "Y";

        } else {
            $scope.Schedule.IsRandom = "N";
        }
        console.log($scope.Schedule);

        if ($scope.Schedule.Id == undefined) {
            console.log("Create mode");
            $scope.Schedule.StartTime = moment($scope.Schedule.StartTime).format("HH:mm:ss");
            $scope.Schedule.EndTime = moment($scope.Schedule.EndTime).format("HH:mm:ss");
            $scope.Schedule.CompanyId = $rootScope.CompanyId;
            defaultScheduleServices.CreateDeviceDefaultScheduleByCompany($scope).then(function (response) {
                    if (response.data.IsReport == "Ok") {
                        toastr.success(response.data.Message, "Success!");
                    } else if (response.data.IsReport == "NotOk") {
                        toastr.error(response.data.Message, "Error!");
                    }
                })
                .then(function () {
                    $timeout(function () {
                        $state.reload();
                    }, 300);
                });
        }
        else
        {

            console.log("Update mode");
            $scope.Schedule.StartTime = moment($scope.Schedule.StartTime).format("HH:mm:ss");
            $scope.Schedule.EndTime = moment($scope.Schedule.EndTime).format("HH:mm:ss");
            console.log($scope.Schedule);
            defaultScheduleServices.UpdateDeviceDefaultScheduleByCompany($scope).then(function (response) {
                    if (response.data.IsReport == "Ok") {
                        toastr.success(response.data.Message, "Success!");
                    } else if (response.data.IsReport == "NotOk") {
                        toastr.error(response.data.Message, "Error!");
                    }
                })
                .then(function () {
                    $timeout(function () {
                        $state.reload();
                    }, 300);
                });
        }


    }
    
});