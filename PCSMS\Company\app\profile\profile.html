﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div><!-- CONTENT -->
<!-- ========================================================= -->
<div class="content">
    <!-- content HEADER -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li>
                    <i class="fa fa-home" aria-hidden="true"></i>
                    <a ui-sref="profile">My Account</a>
                </li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="panel-group" id="accordion">
            <div class="panel panel-default">
                <div class="panel panel-default">
                    <div class="panel-header">
                        <h3 class="panel-title"><a id="FirstCollapserText" style="cursor:pointer" data-parent="#accordion" data-toggle="collapse" data-target="#a_ProfileFormSaveCompanySecondaryInfo_CompanyInformation">Company Information</a></h3>
                        <div class="panel-actions">
                            <ul>
                                <li class="action" ng-show="isInEditModeCI==false">
                                    <a tooltip-placement="left" uib-tooltip="Edit" ng-disabled="EnableEditCI==false" ng-class="{disabled: EnableEditCI==false}" ng-click="UpdateCompanyInfoEdit()"><span class="glyphicon glyphicon-edit"></span></a>
                                </li>
                                <li class="action" ng-show="isInEditModeCI==true">
                                    <a tooltip-placement="left" uib-tooltip="Cancel Edit" ng-click="UpdateCompanyInfoCancelEdit()"><span class="glyphicon glyphicon-remove" style="color:red"></span></a>
                                </li>
                                <li class="action">
                                    <a id="FirstCollapser" style="cursor:pointer" data-parent="#accordion" data-toggle="collapse" data-target="#a_ProfileFormSaveCompanySecondaryInfo_CompanyInformation"><span id="FirstCollapserIcon" class="glyphicon glyphicon-plus" ng-class="{'glyphicon glyphicon-minus':{{divId}}==1}"></span></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="panel-collapse collapse" ng-class="{'panel-collapse collapse in':{{divId}}==1}" id="a_ProfileFormSaveCompanySecondaryInfo_CompanyInformation">
                        <div class="panel-content">
                            <form class="form-horizontal" name="ProfileFormSaveCompanySecondaryInfo_CompanyInformation" novalidate>
                                <div class="row">
                                    <div class="col-md-6 separator">
                                        <div class="form-group">
                                            <label for="CompanyName" class="col-md-4 control-label">Company Name</label>
                                            <div class="col-md-7">
                                                <input type="text" class="form-control" autocomplete="off" placeholder="Your Company" name="CompanyName" id="CompanyName" onkeypress="return false" onkeyup="return false" onkeydown="return false"
                                                       ng-model="Profile.CompanyName"
                                                       ng-disabled="true"
                                                       ng-keydown="PreventDefaultEvent($event)"
                                                       ng-keypress="PreventDefaultEvent($event)"
                                                       ng-keyup="PreventDefaultEvent($event)" />
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="CompanyType" class="col-md-4 control-label">Company Type</label>
                                            <div class="col-md-7" ng-disabled="isInEditModeCI==false">
                                                <select class="form-control" name="CompanyTypeId"
                                                        ng-model="Profile.CompanyTypeId"
                                                        ng-required="true"
                                                        ng-options="CompanyType.Id as CompanyType.CompanyTypeName for CompanyType in CompanyTypeList"
                                                        ng-change="ChangeAll()"
                                                        ng-disabled="isInEditModeCI==false">
                                                    <option value="">Select</option>
                                                </select>
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyTypeId.$dirty && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyTypeId.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyTypeId.$error.required">*Required</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="CompanyPhone" class="col-md-4 control-label">Company Phone</label>
                                            <div class="col-md-7">
                                                <input type="text" class="form-control" placeholder="Enter Phone Number" name="CompanyPhone"
                                                       ng-model="Profile.CompanyPhone"
                                                       ng-minlength="5"
                                                       ng-maxlength="20"
                                                       ng-required="Profile.CompanyMobile==''"
                                                       ng-change="ChangeAll()"
                                                       ng-disabled="isInEditModeCI==false"
                                                       ng-keydown="isInEditModeCI==false?PreventDefaultEvent($event):ChangeAll()"
                                                       ng-keypress="isInEditModeCI==false?PreventDefaultEvent($event):ChangeAll()"
                                                       ng-keyup="isInEditModeCI==false?PreventDefaultEvent($event):ChangeAll()" />
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyPhone.$dirty && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyPhone.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyPhone.$error.required">*Either Phone or Mobile Number is Required</span></li>
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyPhone.$error.pattern">Not a valid Phone Number</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyPhone.$error.pattern && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyPhone.$error.minlength">Minimum length is 5</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyPhone.$error.pattern && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyPhone.$error.maxlength">Maximum length is 20</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="CompanyMobile" class="col-md-4 control-label">Company Mobile</label>
                                            <div class="col-md-7">
                                                <input type="text" class="form-control" placeholder="Enter Mobile Number" name="CompanyMobile"
                                                       ng-required="Profile.CompanyPhone==''"
                                                       ng-model="Profile.CompanyMobile"
                                                       ng-minlength="11"
                                                       ng-maxlength="20"
                                                       ng-disabled="isInEditModeCI==false"
                                                       ng-change="ChangeAll()"
                                                       ng-keydown="isInEditModeCI==false?PreventDefaultEvent($event):ChangeAll()"
                                                       ng-keypress="isInEditModeCI==false?PreventDefaultEvent($event):ChangeAll()"
                                                       ng-keyup="isInEditModeCI==false?PreventDefaultEvent($event):ChangeAll()">
                                                <ul class="list-unstyled errormessage pull-left"
                                                    ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyMobile.$dirty && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyMobile.$invalid">
                                                    <li class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyMobile.$error.required">*Either Phone or Mobile Number is Required</li>
                                                    <li class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyMobile.$error.pattern">Not a valid Mobile Number</li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyMobile.$error.pattern && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyMobile.$error.minlength">Minimum length is 11</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyMobile.$error.pattern && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyMobile.$error.maxlength">Maximum length is 20</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="CompanyAddress" class="col-md-4 control-label">Address</label>
                                            <div class="col-md-7">
                                                <textarea class="form-control" placeholder="Enter Address" name="CompanyAddress" style="resize:none; max-height:74px; height:74px;"
                                                          ng-minlength="6"
                                                          ng-maxlength="200"
                                                          ng-model="Profile.CompanyAddress"
                                                          ng-change="BindBillingAddress()"
                                                          ng-disabled="isInEditModeCI==false"
                                                          ng-keydown="isInEditModeCI==false?PreventDefaultEvent($event):BindBillingAddress()"
                                                          ng-keypress="isInEditModeCI==false?PreventDefaultEvent($event):BindBillingAddress()"
                                                          ng-keyup="isInEditModeCI==false?PreventDefaultEvent($event):BindBillingAddress()"></textarea>
                                                <ul class="list-unstyled errormessage pull-left"
                                                    ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyAddress.$dirty && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyAddress.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyAddress.$error.pattern">Not a valid Address</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyAddress.$error.pattern && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyAddress.$error.minlength">Minimum length is 6</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyAddress.$error.pattern && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyAddress.$error.maxlength">Maximum length is 200</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-md-4">
                                            </div>
                                            <div class="col-md-7">
                                                <div class="checkbox-custom checkbox-success">
                                                    <input type="checkbox" id="checkboxCustom3" ng-click="SameAsAddress()" ng-model="CheckSameAsAddress" ng-true-value="1" ng-false-value="0" ng-disabled="(isInEditModeCI==false) || (Profile.CompanyAddress==undefined)">
                                                    <label class="check" for="checkboxCustom3">Same as Address</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="CompanyBillingAddress" class="col-md-4 control-label">Billing Address</label>
                                            <div class="col-md-7">
                                                <textarea class="form-control" placeholder="Enter Billing Address" name="CompanyBillingAddress" style="resize:none; max-height:74px; height:74px;"
                                                          ng-minlength="6"
                                                          ng-maxlength="200"
                                                          ng-model="Profile.CompanyBillingAddress"
                                                          ng-change="ChangeAll()"
                                                          ng-disabled="isInEditModeCI==false || CheckSameAsAddress==1"
                                                          ng-keydown="isInEditModeCI==false || CheckSameAsAddress==1?PreventDefaultEvent($event):ChangeAll()"
                                                          ng-keypress="isInEditModeCI==false || CheckSameAsAddress==1?PreventDefaultEvent($event):ChangeAll()"
                                                          ng-keyup="isInEditModeCI==false || CheckSameAsAddress==1?PreventDefaultEvent($event):ChangeAll()"></textarea>
                                                <ul class="list-unstyled errormessage pull-left"
                                                    ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyAddress.$dirty && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyBillingAddress.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyBillingAddress.$error.pattern">Not a valid Address</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyBillingAddress.$error.pattern && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyBillingAddress.$error.minlength">Minimum length is 6</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyBillingAddress.$error.pattern && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyBillingAddress.$error.maxlength">Maximum length is 200</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="CompanyWebsite" class="col-md-4 control-label">Website</label>
                                            <div class="col-md-7">
                                                <input type="text" class="form-control" placeholder="Enter Company Website" name="CompanyWebsite"
                                                       ng-model="Profile.CompanyWebsite"
                                                       ng-minlength="8"
                                                       ng-maxlength="40"
                                                       ng-change="ChangeAll()"
                                                       ng-disabled="isInEditModeCI==false"
                                                       ng-keydown="isInEditModeCI==false?PreventDefaultEvent($event):ChangeAll()"
                                                       ng-keypress="isInEditModeCI==false?PreventDefaultEvent($event):ChangeAll()"
                                                       ng-keyup="isInEditModeCI==false?PreventDefaultEvent($event):ChangeAll()"
                                                       ng-pattern="/^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?(([www]){3}|[^w]{1}[a-zA-Z0-9]{1,64})([\.]{1}[a-zA-Z0-9-]{1,253})+\.[a-z]{2,6}(:[0-9]{1,5})?(\/.*)?$/" />
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyWebsite.$dirty && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyWebsite.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyWebsite.$error.pattern">Not a valid Website</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyWebsite.$error.pattern && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyWebsite.$error.minlength">Minimum length is 8</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyWebsite.$error.pattern && ProfileFormSaveCompanySecondaryInfo_CompanyInformation.CompanyWebsite.$error.maxlength">Maximum length is 40</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="row">
                                            <div class="form-group">
                                                <label class="col-lg-4 control-label">Photo</label>
                                                <div class="col-lg-7">
                                                    <div style="{{isInEditModeCI==false?'pointer-events:none; float:left':'float:left'}}">
                                                        <div class="fileinput fileinput-new" data-provides="fileinput" style="margin-left:0%; width:160px; min-width:100%; max-width:160%">
                                                            <div id="imageHolder" class="fileinput-preview thumbnail" data-trigger="fileinput" style="min-width: 160px; max-width:100%; height: 114px;"></div>
                                                            <div ng-show="isInEditModeCI==true">
                                                                <span class="btn btn-default btn-file btn-block">
                                                                    <span class="fileinput-new">Select Compmay Logo</span>
                                                                    <span class="fileinput-exists">Change Photo</span>
                                                                    <input type="file" id="fileCompanyLogo" ng-files="getTheFiles($files)" name="fileCompanyLogo" bind-file ng-model="theFile">
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group" style="margin-right:0px; margin-left:0px">
                                                <label class="col-md-4 control-label" style="margin-top:110px">Pin on map</label>
                                                <div class="col-md-7">
                                                    <div class="text-center" style="border-style:solid; border-width:1px; border-color:#ccc #ccc #ccc #ccc">
                                                        <div ng-init="LoadMap()" style="height:240px; width:100%" id="map-canvas">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group" style="margin-right:0px; margin-left:0px">
                                                <label for="SearchLocation" class="col-md-4 control-label">Search Location</label>
                                                <div class="col-md-7">
                                                    <input type="text" class="form-control" placeholder="Search Location" name="SearchLocation" id="SearchLocation"
                                                           ng-model="SearchLocation"
                                                           ng-disabled="isInEditModeCI==false"
                                                           ng-keydown="SearchLocationChange($event)"
                                                           ng-keypress="SearchLocationChange($event)"
                                                           ng-keyup="SearchLocationChange($event)" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <div class="panel-footer" style="background-color:transparent" ng-if="isInEditModeCI==true">
                            <div class="row">
                                <div class="col-lg-12">
                                    <button type="button"
                                            ng-disabled="ProfileFormSaveCompanySecondaryInfo_CompanyInformation.$invalid"
                                            ng-click="UpdateCompanyInfo()"class="btn btn-md btn-primary pull-right">
                                        Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>





            <div class="panel panel-default">
                <div class="panel panel-default">
                    <div class="panel-header">
                        <h3 class="panel-title"><a id="SecondCollapserText" style="cursor:pointer" data-parent="#accordion" data-toggle="collapse" data-target="#a_ProfileFormSaveCompanySecondaryInfo_ContactInformation">Contact Person Information</a></h3>
                        <div class="panel-actions">
                            <ul>
                                <li class="action" ng-show="isInEditModeCPI==false">
                                    <a tooltip-placement="left" uib-tooltip="Edit"><span class="glyphicon glyphicon-edit" ng-class="{disabled: EnableEditCPI==false}" ng-click="UpdateContactPersonInfoEdit()"></span></a>
                                </li>
                                <li class="action" ng-show="isInEditModeCPI==true">
                                    <a tooltip-placement="left" uib-tooltip="Cancel Edit" ng-click="UpdateContactPersonInfoCancelEdit()"><span class="glyphicon glyphicon-remove" style="color:red"></span></a>
                                </li>
                                <li class="action">
                                    <a id="SecondCollapser" style="cursor:pointer" data-toggle="collapse" data-parent="#accordion" data-target="#a_ProfileFormSaveCompanySecondaryInfo_ContactInformation"><span id="SecondCollapserIcon" class="glyphicon glyphicon-plus" ng-class="{'glyphicon glyphicon-minus':{{divId}}==2}"></span></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="panel-collapse collapse" ng-class="{'panel-collapse collapse in':{{divId}}==2}" id="a_ProfileFormSaveCompanySecondaryInfo_ContactInformation">
                        <div class="panel-content" style="padding-bottom:26px;">
                            <form class="form-horizontal" name="ProfileFormSaveCompanySecondaryInfo_ContactInformation" novalidate>
                                <div class="row">
                                    <div class="col-md-6 separator">
                                        <div class="form-group">
                                            <label for="ContactPerson" class="col-md-4 control-label">Contact Person</label>
                                            <div class="col-md-7">
                                                <input type="text" class="form-control" placeholder="Contact Person Name" name="ContactPerson"
                                                       ng-model="Profile.ContactPerson"
                                                       ng-minlength="4"
                                                       ng-maxlength="50"
                                                       ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" 
                                                       ng-required="true"
                                                       ng-disabled="isInEditModeCPI==false"
                                                       ng-keydown="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-keypress="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-keyup="isInEditModeCPI==false?PreventDefaultEvent($event):Function()" />
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPerson.$dirty && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPerson.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPerson.$error.required">*Required</span></li>
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPerson.$error.pattern">Start with capital letter, don't use special character.</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPerson.$error.pattern && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPerson.$error.minlength">Minimum length is 4</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPerson.$error.pattern && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPerson.$error.maxlength">Maximum length is 50</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="ContactPersonEmail" class="col-md-4 control-label">Email</label>
                                            <div class="col-md-7">
                                                <input type="email" class="form-control" placeholder="Contact Person Email" name="ContactPersonEmail"
                                                       ng-maxlength="30"
                                                       ng-minlength="11"
                                                       ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'
                                                       ng-model="Profile.ContactPersonEmail"
                                                       ng-disabled="isInEditModeCPI==false"
                                                       ng-keydown="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-keypress="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-keyup="isInEditModeCPI==false?PreventDefaultEvent($event):Function()" />
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonEmail.$dirty && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonEmail.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonEmail.$error.pattern">Not a valid Email</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonEmail.$error.pattern && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonEmail.$error.minlength">Minimum length is 11</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonEmail.$error.pattern && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonEmail.$error.maxlength">Maximum length is 30</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="ContactPersonPhone" class="col-md-4 control-label">Phone</label>
                                            <div class="col-md-7">
                                                <input type="text" class="form-control" placeholder="e.g (+8802xxxxx)" name="ContactPersonPhone"
                                                       ng-model="Profile.ContactPersonPhone"
                                                       ng-minlength="5"
                                                       ng-maxlength="20"
                                                       ng-required="Profile.ContactPersonMobile==''"
                                                       ng-disabled="isInEditModeCPI==false"
                                                       ng-keydown="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-keypress="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-keyup="isInEditModeCPI==false?PreventDefaultEvent($event):Function()" />
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonPhone.$dirty && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonPhone.$invalid">
                                                    <li class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonPhone.$error.required">*Either Phone or Mobile Number is Required</li>
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonPhone.$error.pattern">Not a valid Phone Number</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonPhone.$error.pattern && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonPhone.$error.minlength">Minimum length is 5</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonPhone.$error.pattern && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonPhone.$error.maxlength">Maximum length is 20</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="ContactPersonMobile" class="col-md-4 control-label">Mobile</label>
                                            <div class="col-md-7">
                                                <input type="text" class="form-control" placeholder="e.g (+8801xxxxxxxxx)" name="ContactPersonMobile"
                                                       ng-model="Profile.ContactPersonMobile"
                                                       ng-minlength="11"
                                                       ng-maxlength="20"
                                                       ng-required="Profile.ContactPersonPhone==''"
                                                       ng-keydown="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-keypress="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-keyup="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-disabled="isInEditModeCPI==false" />
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonMobile.$dirty && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonMobile.$invalid">
                                                    <li class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonMobile.$error.required">*Either Phone or Mobile Number is Required</li>
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonMobile.$error.pattern">Not a valid Mobile Number</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonMobile.$error.pattern && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonMobile.$error.minlength">Minimum length is 11</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonMobile.$error.pattern && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonMobile.$error.maxlength">Maximum length is 20</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="ContactPersonDesignation" class="col-md-4 control-label">Designation</label>
                                            <div class="col-md-7">
                                                <input type="text" class="form-control" placeholder="Enter Designation" name="ContactPersonDesignation"
                                                       ng-model="Profile.ContactPersonDesignation"
                                                       ng-minlength="4"
                                                       ng-maxlength="30"
                                                       ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/" 
                                                       ng-required="true"
                                                       ng-keydown="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-keypress="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-keyup="isInEditModeCPI==false?PreventDefaultEvent($event):Function()"
                                                       ng-disabled="isInEditModeCPI==false" />
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonDesignation.$dirty && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonDesignation.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonDesignation.$error.required">*Required</span></li>
                                                    <li><span class="pull-left" ng-show="ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonDesignation.$error.pattern">Start with capital letter, don't use special character.</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonDesignation.$error.pattern && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonDesignation.$error.minlength">Minimum length is 4</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonDesignation.$error.pattern && ProfileFormSaveCompanySecondaryInfo_ContactInformation.ContactPersonDesignation.$error.maxlength">Maximum length is 30</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>


                        <div class="panel-footer" style="background-color:transparent" ng-if="isInEditModeCPI==true">
                            <div class="row">
                                <div class="col-lg-12">
                                    <button type="button"
                                            ng-disabled="ProfileFormSaveCompanySecondaryInfo_ContactInformation.$invalid || ProfileFormSaveCompanySecondaryInfo_ContactInformation.$pristine"
                                            ng-click="UpdateContactPersonInfo()"
                                            class="btn btn-md btn-primary pull-right">
                                        Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel panel-default">
                    <div class="panel-header">
                        <h3 class="panel-title"><a id="ThirdCollapserText" style="cursor:pointer" data-parent="#accordion" data-toggle="collapse" data-target="#a_ProfileFormChangeCompanyCredentials">Login Credentials</a></h3>
                        <div class="panel-actions">
                            <ul>
                                <li class="action" ng-show="isInEditModeCLC==false">
                                    <a tooltip-placement="left" uib-tooltip="Edit" ng-click="UpdateLoginCredentialsEdit()" ng-class="{disabled: EnableEditCLC==false}"><span class="glyphicon glyphicon-edit"></span></a>
                                </li>
                                <li class="action" ng-show="isInEditModeCLC==true">
                                    <a tooltip-placement="left" uib-tooltip="Cancel Edit" ng-click="UpdateLoginCredentialsCancelEdit()"><span class="glyphicon glyphicon-remove" style="color:red"></span></a>
                                </li>
                                <li class="action">
                                    <a id="ThirdCollapser" style="cursor:pointer" data-toggle="collapse" data-parent="#accordion" data-target="#a_ProfileFormChangeCompanyCredentials"><span id="ThirdCollapserIcon" class="glyphicon glyphicon-plus" ng-class="{'glyphicon glyphicon-minus':{{divId}}==3}"></span></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="panel-collapse collapse" id="a_ProfileFormChangeCompanyCredentials" ng-class="{'panel-collapse collapse in':{{divId}}==3}">
                        <div class="panel-content" style="padding-bottom:26px;">
                            <form class="form-horizontal" name="ProfileFormChangeCompanyCredentials" novalidate>
                                <div class="row">
                                    <div class="col-md-6 separator">
                                        <div class="form-group">
                                            <label for="Email" class="col-md-4 control-label">Email</label>
                                            <div class="col-md-7">
                                                <input type="email" class="form-control" placeholder="Enter Email" name="CompanyEmail"
                                                       ng-required="true"
                                                       ng-model="Profile.CompanyEmail"
                                                       ng-minlength="11"
                                                       ng-maxlength="30"
                                                       ng-disabled="isInEditModeCLC==false"
                                                       ng-keydown="isInEditModeCLC==false?PreventDefaultEvent($event):Function()"
                                                       ng-keypress="isInEditModeCLC==false?PreventDefaultEvent($event):Function()"
                                                       ng-keyup="isInEditModeCLC==false?PreventDefaultEvent($event):Function()"
                                                       ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/' />
                                                <ul class="list-unstyled errormessage pull-left"
                                                    ng-show="ProfileFormChangeCompanyCredentials.CompanyEmail.$dirty && ProfileFormChangeCompanyCredentials.CompanyEmail.$invalid">
                                                    <li class="pull-left" ng-show="ProfileFormChangeCompanyCredentials.CompanyEmail.$error.pattern">Not a valid Email Address</li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormChangeCompanyCredentials.CompanyEmail.$error.pattern && ProfileFormChangeCompanyCredentials.CompanyEmail.$error.minlength">Minimum length is 11</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormChangeCompanyCredentials.CompanyEmail.$error.pattern && ProfileFormChangeCompanyCredentials.CompanyEmail.$error.maxlength">Maximum length is 30</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-md-4"></div>
                                            <div class="col-md-7"></div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-md-4"></div>
                                            <div class="col-md-7"></div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-md-4"></div>
                                            <div class="col-md-7"></div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-md-4"></div>
                                            <div class="col-md-7"></div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-md-4"></div>
                                            <div class="col-md-7"></div>
                                        </div>
                                        <div class="form-group">
                                            <div class="col-md-4"></div>
                                            <div class="col-md-7"></div>
                                        </div>
                                        <div class="form-group" ng-if="isInEditModeCLC==true">
                                            <div class="col-md-4"></div>
                                            <div class="col-md-7">
                                                <button type="button"
                                                        ng-disabled="ProfileFormChangeCompanyCredentials.CompanyEmail.$invalid || CurrentEmail==Profile.CompanyEmail"
                                                        ng-click="ChangeCompanyEmailForLogin()"
                                                        class="btn btn-md btn-primary pull-right">
                                                    Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                                </button>                                                
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="Password" class="col-md-4 control-label">Existing Password</label>
                                            <div class="col-md-7">
                                                <input type="password" class="form-control" placeholder="Enter Your Existing Passowrd" name="ExistingPassword"
                                                       ng-required="true"
                                                       ng-model="Profile.ExistingPassword"
                                                       ng-minlength="6"
                                                       ng-maxlength="50"
                                                       ng-disabled="isInEditModeCLC==false"
                                                       ng-change="MatchWithExistingPassword(ProfileFormChangeCompanyCredentials)"
                                                       ng-keydown="isInEditModeCLC==false?PreventDefaultEvent($event):MatchWithExistingPassword(ProfileFormChangeCompanyCredentials)"
                                                       ng-keypress="isInEditModeCLC==false?PreventDefaultEvent($event):MatchWithExistingPassword(ProfileFormChangeCompanyCredentials)"
                                                       ng-keyup="isInEditModeCLC==false?PreventDefaultEvent($event):MatchWithExistingPassword(ProfileFormChangeCompanyCredentials)"
                                                       ng-pattern="/([a-zA-Z0-9 ])\w+/" />
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormChangeCompanyCredentials.ExistingPassword.$dirty && ProfileFormChangeCompanyCredentials.ExistingPassword.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormChangeCompanyCredentials.ExistingPassword.$error.required">*Required</span></li>
                                                    <li><span class="pull-left" ng-show="ProfileFormChangeCompanyCredentials.ExistingPassword.$error.pattern">Not a valid Password</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormChangeCompanyCredentials.ExistingPassword.$error.pattern && ProfileFormChangeCompanyCredentials.ExistingPassword.$error.minlength">Minimum length is 6</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormChangeCompanyCredentials.ExistingPassword.$error.pattern && ProfileFormChangeCompanyCredentials.ExistingPassword.$error.maxlength">Maximum length is 50</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="NewPassword" class="col-md-4 control-label">New Password</label>
                                            <div class="col-md-7">
                                                <input type="password" class="form-control" placeholder="Enter Your New Password" name="CompanyPassword"
                                                       ng-required="true"
                                                       ng-model="Profile.CompanyPassword"
                                                       ng-minlength="6"
                                                       ng-maxlength="50"
                                                       ng-pattern="/([a-zA-Z0-9 ])\w+/"
                                                       ng-disabled="isInEditModeCLC==false"
                                                       ng-keydown="isInEditModeCLC==false?PreventDefaultEvent($event):MatchWithExistingPassword(ProfileFormChangeCompanyCredentials)"
                                                       ng-keypress="isInEditModeCLC==false?PreventDefaultEvent($event):MatchWithExistingPassword(ProfileFormChangeCompanyCredentials)"
                                                       ng-keyup="isInEditModeCLC==false?PreventDefaultEvent($event):MatchWithExistingPassword(ProfileFormChangeCompanyCredentials)"
                                                       ng-change="MatchWithExistingPassword(ProfileFormChangeCompanyCredentials)" />
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormChangeCompanyCredentials.CompanyPassword.$dirty && ProfileFormChangeCompanyCredentials.CompanyPassword.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormChangeCompanyCredentials.CompanyPassword.$error.required">*Required</span></li>
                                                    <li><span class="pull-left" ng-show="ProfileFormChangeCompanyCredentials.CompanyPassword.$invalid">{{ProfileFormChangeCompanyCredentials.CompanyPassword.errorMessage}}</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormChangeCompanyCredentials.CompanyPassword.$error.pattern && ProfileFormChangeCompanyCredentials.CompanyPassword.$error.minlength">Minimum length is 6</span></li>
                                                    <li><span class="pull-left" ng-show="!ProfileFormChangeCompanyCredentials.CompanyPassword.$error.pattern && ProfileFormChangeCompanyCredentials.CompanyPassword.$error.maxlength">Maximum length is 50</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="ConfirmPassword" class="col-md-4 control-label">Confirm Password</label>
                                            <div class="col-md-7">
                                                <input type="password" class="form-control" placeholder="Re-type New Password" name="ConfirmPassword"
                                                       ng-required="true"
                                                       ng-model="Profile.ConfirmPassword"
                                                       ng-minlength="6"
                                                       ng-maxlength="50"
                                                       ng-pattern="Profile.CompanyPassword"
                                                       ng-keydown="isInEditModeCLC==false?PreventDefaultEvent($event):Function()"
                                                       ng-keypress="isInEditModeCLC==false?PreventDefaultEvent($event):Function()"
                                                       ng-keyup="isInEditModeCLC==false?PreventDefaultEvent($event):Function()"
                                                       ng-disabled="isInEditModeCLC==false" />
                                                <ul class="list-unstyled errormessage pull-left" ng-show="ProfileFormChangeCompanyCredentials.ConfirmPassword.$dirty && ProfileFormChangeCompanyCredentials.ConfirmPassword.$invalid">
                                                    <li><span class="pull-left" ng-show="ProfileFormChangeCompanyCredentials.ConfirmPassword.$error.required">*Required</span></li>
                                                    <li><span class="pull-left" ng-show="ProfileFormChangeCompanyCredentials.ConfirmPassword.$error.pattern">Password is not matched</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="form-group" ng-if="isInEditModeCLC==true">
                                            <div class="col-md-4"></div>
                                            ="
                                            <div class="col-md-7">
                                                <button type="button"
                                                        ng-disabled="ProfileFormChangeCompanyCredentials.$invalid"
                                                        ng-click="ChangeCompanyPasswordForLogin()"
                                                        class="btn btn-md btn-primary pull-right">
                                                    Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                                </button>                                                
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<style>
    .label-Italic {
        font-style: italic;
        font-size: 15px;
    }

    #description {
        font-family: Roboto;
        font-size: 15px;
        font-weight: 300;
    }

    #infowindow-content .title {
        font-weight: bold;
    }

    #infowindow-content {
        display: none;
    }

    #map #infowindow-content {
        display: inline;
    }

    .pac-card {
        margin: 10px 10px 0 0;
        border-radius: 2px 0 0 2px;
        box-sizing: border-box;
        -moz-box-sizing: border-box;
        outline: none;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        background-color: #fff;
        font-family: Roboto;
    }

    #pac-container {
        padding-bottom: 12px;
        margin-right: 12px;
    }

    .pac-controls {
        display: inline-block;
        padding: 5px 11px;
    }

        .pac-controls label {
            font-family: Roboto;
            font-size: 13px;
            font-weight: 300;
        }

    #pac-input {
        background-color: #fff;
        font-family: Roboto;
        font-size: 15px;
        font-weight: 300;
        margin-left: 12px;
        padding: 0 11px 0 13px;
        text-overflow: ellipsis;
        width: 400px;
    }

        #pac-input:focus {
            border-color: #4d90fe;
        }

    #title {
        color: #fff;
        background-color: #4d90fe;
        font-size: 25px;
        font-weight: 500;
        padding: 6px 12px;
    }

    .disabled {
        pointer-events: none;
    }
</style>







