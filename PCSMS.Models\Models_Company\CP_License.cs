﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PCSMS.Models.Common;
using System.ComponentModel.DataAnnotations.Schema;

namespace PCSMS.Models.Models_Company
{
    public class CP_License : Entity<int>
    {
        [ForeignKey("CompanyId")]
        public virtual CP_Profile CP_Profile { get; set; }
        public int? CompanyId { get; set; }

        public string LCode { get; set; }
        public string LicenseKey { get; set; }
        
        public int DeviceLimit { get; set; }
        public string LicenseType { get; set; }
        public DateTime? ActivatedOn { get; set; }
        public DateTime? RequestedOn { get; set; }
        [MaxLength(20)]
        public string Status { get; set; }
        [MaxLength(20)]
        public string SecondaryStatus { get; set; }

        //STATUS 
        //Requested
        //Active
        //In-Active
        //Expired

        //SecondaryStatus
        //Renewal Requested   (n.b. space available)
    }
}
