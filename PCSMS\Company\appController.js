﻿/// <reference path="app.js" />
PCSMSApp.controller('appController', function ($scope, $rootScope, appServices, $cookies, licenseServices,blockUI, $window, $q, toastr, $compile, $timeout, DTOptionsBuilder, DTColumnBuilder, DTColumnDefBuilder, $state) {

    //====================================================================Declaration=================================================================================

    //====================================================================Element Processing==========================================================================

    $scope.HTMLCollapser = function () {
        if ($rootScope.HTMLCollapseStatus == "fixed left-sidebar-top") {
            //updateCookie('HTMLCollapseStatus', "fixed left-sidebar-top left-sidebar-collapsed");
            $rootScope.HTMLCollapseStatus = "fixed left-sidebar-top left-sidebar-collapsed";
        }
        else if ($rootScope.HTMLCollapseStatus == "fixed left-sidebar-top left-sidebar-collapsed") {
            //updateCookie('HTMLCollapseStatus', "fixed left-sidebar-top");
            $rootScope.HTMLCollapseStatus = "fixed left-sidebar-top";
        }
    }

   




    //====================================================================Miscellaneous Function======================================================================
    $scope.LogOut = function () {
        //console.log($rootScope.CompanyId);
        appServices.Logout($rootScope.CompanyId).then(function (response) {
            $rootScope.CompanyToken = null;
            if (response.data == true) {

                $cookies.remove('Company_Token_Codeaura_Demo', { path: '/' });
                $cookies.remove('Company_Details_Codeaura_Demo', { path: '/' });

                //Reset the HTML collapser;
                $cookies.remove('HTMLCollapseStatus', { path: '/' });
                //$window.location.href = "/Company/#/logIn";
                $state.go("logIn");

                toastr.info("You have logged out !", {
                    timeOut: 2000
                });
            }
        });
    }

    //==================
    //// SignalR: 
    //var notificationHub = $.connection.notificationHub;
    //notificationHub.client.onLicenseStatusTransition = function (licenseId) {

    //    alert(licenseId);
    //};




    //signalR:
    var audio = new Audio('/Notification_Audio/open-ended.mp3');
    var notificationHub = $.connection.notificationHub;


    //Event 1:
    notificationHub.client.onLicenseRenewal = function (companyId, licenseCode) {

        if ($rootScope.CompanyId == companyId) {
            audio.play();
            toastr.success("License Code: " + licenseCode + ' has been renewed !', 'Success !');
            licenseServices.GetLicenseListByCompanyId($rootScope.CompanyId).then(function (response) {

                $rootScope.LicenseList = response.data;
                angular.forEach(response.data, function (value, key) {

                    if (value.LicenseType == "Trial" && value.Status == "Active") {
                        $scope.HasActiveTrialLicense = true;
                    }
                    else if (value.LicenseType == "Chargeable" && value.Status == "Requested") {
                        $rootScope.CurrentlyALicenseBeingRequested = true;
                    }
                    if (value.LicenseKey == null) {
                        value.LicenseKey = "---------------------------------------";
                    }
                    if (value.RequestedOn != null) {
                        var reqDateUtc = moment.utc(value.RequestedOn);
                        value.RequestedOn = reqDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
                    }
                    if (value.ExpiryDate != null) {
                        var expDateUtc = moment.utc(value.ExpiryDate);
                        value.ExpiryDate = expDateUtc.local().format("YYYY-MM-DD").toString();
                    }
                });
            });
        }


    };
    //Event 2:
    notificationHub.client.onLicenseApproval = function (companyId, licenseCode) {

        if ($rootScope.CompanyId == companyId) {
            audio.play();
            toastr.success("License Code : " + licenseCode + ' has been approved !', 'Success !');
            licenseServices.GetLicenseListByCompanyId($rootScope.CompanyId).then(function (response) {

                $rootScope.LicenseList = response.data;
                $rootScope.CurrentlyALicenseBeingRequested = false;
                angular.forEach(response.data, function (value, key) {
                    if (value.LicenseKey == null) {
                        value.LicenseKey = "---------------------------------------";
                    }
                    if (value.RequestedOn != null) {
                        var reqDateUtc = moment.utc(value.RequestedOn);
                        value.RequestedOn = reqDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
                    }
                    if (value.ExpiryDate != null) {
                        var expDateUtc = moment.utc(value.ExpiryDate);
                        value.ExpiryDate = expDateUtc.local().format("YYYY-MM-DD").toString();
                    }
                    console.log(value.LicenseType + " , " + value.Status);
                });
            });

            console.log($rootScope.CurrentlyALicenseBeingRequested);
        }


    };
    //Event 3:
    notificationHub.client.onLicenseExpiration = function (companyId, licenseCode) {

        if ($rootScope.CompanyId == companyId) {
            audio.play();
            toastr.warning("License Code : " + licenseCode + ' has expired !', 'Warning !');
            licenseServices.GetLicenseListByCompanyId($rootScope.CompanyId).then(function (response) {

                $rootScope.LicenseList = response.data;
                $rootScope.CurrentlyALicenseBeingRequested = false;
                angular.forEach(response.data, function (value, key) {
                    if (value.LicenseKey == null) {
                        value.LicenseKey = "---------------------------------------";
                    }
                    if (value.RequestedOn != null) {
                        var reqDateUtc = moment.utc(value.RequestedOn);
                        value.RequestedOn = reqDateUtc.local().format("YYYY-MM-DD hh:mm:ss a").toString();
                    }
                    if (value.ExpiryDate != null) {
                        var expDateUtc = moment.utc(value.ExpiryDate);
                        value.ExpiryDate = expDateUtc.local().format("YYYY-MM-DD").toString();
                    }
                });
            });
        }


    };



    $.connection.hub.start().done(function () {
        console.log("Hub started from company");
    });



});