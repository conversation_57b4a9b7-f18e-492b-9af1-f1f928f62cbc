﻿PCSMSApp.factory("AccessTypeServices", ["$http", "$rootScope", "$window", function ($http, $rootScope, $window) {
    return {
        SaveAccessType: function (param) {
            return $http({
                url: "/api/AccessType/SaveAccessType",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        UpdateAccessType: function (param) {
            return $http({
                url: "/api/AccessType/UpdateAccessType",
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                data: param,
                async: false
            });
        },
        GetAccessTypeList: function () {
            return $http({
                url: "/api/AccessType/GetAccessTypeList",
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        GetAccessTypeDetails: function (id) {
            return $http({
                url: "/api/AccessType/GetAccessTypeDetails/" + id,
                method: "GET",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        },
        DeleteAccessType: function (id) {
            return $http({
                url: "/api/AccessType/DeleteAccessType/" + id,
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "cache-control": "no-cache"
                },
                async: false
            });
        }
    };
}]);