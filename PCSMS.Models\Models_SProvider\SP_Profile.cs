﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PCSMS.Models.Common;
using PCSMS.Models.Models_Shared;

namespace PCSMS.Models.Models_SProvider
{
    public class SP_Profile : Entity<int>
    {
        [MaxLength(70)]
        [Required]
        public string OrganizationName { get; set; }
        [MaxLength(150)]
        public string OrganizationAddress { get; set; }
        [MaxLength(50)]
        public string OrganizationPhone { get; set; }
        [MaxLength(50)]
        public string OrganizationMobile { get; set; }
        [MaxLength(70)]
        public string OrganizationWebsite { get; set; }
        [MaxLength(70)]
        public string OrganizationEmail { get; set; }
        public double? OrganizationLat { get; set; }
        public double? OrganizationLong { get; set; }
        [MaxLength(150)]
        public string OrganizationLogo { get; set; }

        [ForeignKey("CompanyTypeId")]
        public virtual CompanyType CompanyType { get; set; }
        public int? CompanyTypeId { get; set; }

    }
}
