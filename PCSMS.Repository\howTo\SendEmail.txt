﻿

//Just Go here : Less secure apps , Log on using your Email and Password which use for sending mail in your c# code , and choose Turn On. : https://myaccount.google.com/lesssecureapps

//Also please go to this link and click on Continue Allow access to your Google account : https://accounts.google.com/DisplayUnlockCaptcha

			MailMessage msg = new MailMessage();

            msg.From = new MailAddress("<EMAIL>");
            msg.To.Add("<EMAIL>");
            msg.Subject = "Hello world 4! " ;
            msg.Body = "hi to you 4... :)";
            SmtpClient client = new SmtpClient();
            client.UseDefaultCredentials = true;
            client.Host = "smtp.gmail.com";
            client.Port = 587;
            client.EnableSsl = true;
            client.DeliveryMethod = SmtpDeliveryMethod.Network;
            client.Credentials = new NetworkCredential("<EMAIL>", "vgnnr32s");
            client.Timeout = 20000;
            var message = "";

            try
            {
                client.Send(msg);
                message = "Ok, mail sent !";
            }
            catch (Exception ex)
            {
                message = ex.Message;
            }