﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!-- main content -->

<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-dashboard" aria-hidden="true"></i><a ui-sref="userprofile">My Profile</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div class="panel-content">
                        <div class="row">
                            <div class="col-lg-12">
                                <!--Login Credentials-->
                                <div class="col-lg-4 animated fadeInUpBig">
                                    <form name="UserFormCredentials">
                                        <div class="panel panel-default panel-bordered">
                                            <div class="panel-content">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <!--Existing Password-->
                                                        <div class="form-group" style="line-height:2">
                                                            <label class="req">Existing Password</label>
                                                            <input type="password" name="ExistingPassword" class="form-control" placeholder="Enter Existing Password"
                                                                   ng-required="true"
                                                                   ng-model="User.ExistingPassword"
                                                                   ng-minlength="6"
                                                                   ng-maxlength="50"
                                                                   ng-pattern="/([a-zA-Z0-9 ])\w+/" />
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="UserFormCredentials.ExistingPassword.$dirty && UserFormCredentials.ExistingPassword.$invalid">
                                                                    <li><span class="pull-left" ng-show="UserFormCredentials.ExistingPassword.$error.required">*Required</span></li>
                                                                    <li><span class="pull-left" ng-show="UserFormCredentials.ExistingPassword.$error.pattern">Not a valid ExistingPassword</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormCredentials.ExistingPassword.$error.pattern && UserFormCredentials.ExistingPassword.$error.minlength">Minimum length is 6</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormCredentials.ExistingPassword.$error.pattern && UserFormCredentials.ExistingPassword.$error.maxlength">Maximum length is 50</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <!--New Password-->
                                                        <div class="form-group" style="line-height:2">
                                                            <label class="req">New Password</label>
                                                            <input type="password" name="NewPassword" class="form-control" placeholder="Enter New Password"
                                                                   ng-required="true"
                                                                   ng-model="User.NewPassword"
                                                                   ng-change="LiveValidationPassword(User.ExistingPassword,User.NewPassword)"
                                                                   ng-minlength="6"
                                                                   ng-maxlength="50"
                                                                   ng-pattern="/([a-zA-Z0-9 ])\w+/" />
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="UserFormCredentials.NewPassword.$dirty && UserFormCredentials.NewPassword.$invalid || LiveValidationPassword(User.ExistingPassword,User.NewPassword)==false">
                                                                    <li><span class="pull-left" ng-show="LiveValidationPassword(User.ExistingPassword,User.NewPassword)==false">{{LiveValidationPasswordError}}</span></li>
                                                                    <li><span class="pull-left" ng-show="UserFormCredentials.NewPassword.$error.required">*Required</span></li>
                                                                    <li><span class="pull-left" ng-show="UserFormCredentials.NewPassword.$error.pattern">Not a valid NewPassword</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormCredentials.NewPassword.$error.pattern && UserFormCredentials.NewPassword.$error.minlength">Minimum length is 6</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormCredentials.NewPassword.$error.pattern && UserFormCredentials.NewPassword.$error.maxlength">Maximum length is 50</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <!--Re-Type New Password-->
                                                        <!--ng-disabled="User.NewPassword==undefined || LiveValidationPassword(User.ExistingPassword,User.NewPassword)==false"-->
                                                        <div class="form-group">
                                                            <label class="req">Confirm Password</label>
                                                            <input type="password" name="ConfirmPassword" class="form-control" placeholder="Re-Type New Passsword"
                                                                   ng-required="true"
                                                                   ng-model="User.ConfirmPassword"
                                                                   ng-pattern="User.NewPassword" />
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="UserFormCredentials.ConfirmPassword.$dirty && UserFormCredentials.ConfirmPassword.$invalid">
                                                                    <li><span class="pull-left" ng-show="UserFormCredentials.ConfirmPassword.$error.required">*Required</span></li>
                                                                    <li><span class="pull-left" ng-show="UserFormCredentials.ConfirmPassword.$error.pattern">Password is not matched</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div style="height:4px"></div>
                                            </div>
                                            <div class="panel-footer" style="background-color:transparent">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <button ng-disabled="UserFormCredentials.$invalid ||  LiveValidationPassword(User.ExistingPassword,User.NewPassword)==false"
                                                                type="button"
                                                                ng-click="UpdateUserFormCredentials()" class="btn btn-md btn-primary pull-right">
                                                            Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <!--Profile Picture-->
                                <div class="col-lg-4 animated fadeInUpBig">
                                    <form name="UserFormProfilePicture">
                                        <div class="panel panel-default panel-bordered">
                                            <div class="panel-content">
                                                <div class="text-center">
                                                    <div class="fileinput fileinput-new" data-provides="fileinput" style="margin-left:0%; width:100%; height:216px;">
                                                        <div id="imageHolder" class="fileinput-preview thumbnail" data-trigger="fileinput" style="min-width: 137px; max-width:100%; height: 180px;"></div>
                                                        <div>
                                                            <span class="btn btn-default btn-file">
                                                                <span class="fileinput-new">Select Photo</span>
                                                                <span class="fileinput-exists">Change Photo</span>
                                                                <input type="file" id="fileProfilePicture" ng-files="getTheFiles($files)" name="fileProfilePicture" bind-file ng-model="theFile">
                                                            </span>
                                                            <a class="btn btn-default fileinput-exists" ng-show="false" id="a_fileProfilePicture" data-dismiss="fileinput">Remove</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel-footer" style="background-color:transparent">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <button ng-disabled="HasImage==false  || ChangedImage==false"
                                                                type="button"
                                                                ng-click="UpdateUserFormProfilePicture()" class="btn btn-md btn-primary pull-right">
                                                            Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <!--Administrative Info-->
                                <div class="col-lg-4 animated fadeInUpBig">
                                    <form name="UserFormAdministrativeInfo" novalidate>
                                        <!---->
                                        <div class="panel panel-default panel-bordered" ng-if="$root.UserAccessTypeId<3">
                                            <div class="panel-content" style="height:auto">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div>
                                                            <!--Select Branch-->
                                                            <div class="form-group" style="line-height:2">
                                                                <label class="req">Select Branch</label>
                                                                <select class="form-control" name="SP_BranchId"
                                                                        ng-model="User.SP_BranchId"
                                                                        ng-required="true"
                                                                        ng-options="Branch.Id as Branch.BranchName for Branch in BranchList">
                                                                    <option value="">Select</option>
                                                                </select>
                                                                <div class="row custom-row">
                                                                    <span class="text-danger pull-left"
                                                                          ng-show="UserFormAdministrativeInfo.SP_BranchId.$dirty && UserFormAdministrativeInfo.SP_BranchId.$invalid">
                                                                        <span ng-show="UserFormAdministrativeInfo.SP_BranchId.$error.required">*Required</span>
                                                                    </span>
                                                                </div>
                                                            </div>

                                                            <!--Select AccessType 1-->
                                                            <!--This is for Super Admin who can't change his access type-->
                                                            <!--<pre></pre>
                                        {{User.Id}}-->
                                                            <div class="form-group" style="line-height:2">
                                                                <label class="req">Select Access Type</label>
                                                                <select class="form-control" name="AccessTypeId"
                                                                        ng-model="User.AccessTypeId"
                                                                        ng-required="true"
                                                                        ng-options="AccessType.Id as AccessType.AccessTypeName disable when AccessType.Id>$root.UserAccessTypeId for AccessType in AccessTypeList | filter:FilterAccessType()">
                                                                    <option value="">Select</option>
                                                                </select>
                                                                <div class="row custom-row">
                                                                    <span class="text-danger pull-left"
                                                                          ng-show="UserFormAdministrativeInfo.AccessTypeId.$dirty && UserFormAdministrativeInfo.AccessTypeId.$invalid">
                                                                        <span ng-show="UserFormAdministrativeInfo.AccessTypeId.$error.required">*Required</span>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <!--Select AccessType 3-->
                                                            <div class="form-group" style="line-height:2" ng-if="User.Id == $root.UserId && User.AccessTypeId==$root.UserAccessTypeId && $root.UserAccessTypeId<1">
                                                                <label class="req">Select Access Type</label>
                                                                <select class="form-control" name="AccessTypeId"
                                                                        ng-model="User.AccessTypeId"
                                                                        ng-required="true"
                                                                        ng-options="AccessType.Id as AccessType.AccessTypeName disable when AccessType.Id>$root.UserAccessTypeId for AccessType in AccessTypeList">
                                                                    <option value="">Select</option>
                                                                </select>
                                                                <div class="row custom-row">
                                                                    <span class="text-danger pull-left"
                                                                          ng-show="UserFormAdministrativeInfo.AccessTypeId.$dirty && UserFormAdministrativeInfo.AccessTypeId.$invalid">
                                                                        <span ng-show="UserFormAdministrativeInfo.AccessTypeId.$error.required">*Required</span>
                                                                    </span>
                                                                </div>
                                                            </div>

                                                            <!--Select Designation-->
                                                            <div class="form-group" style="line-height:2">
                                                                <label class="req">Select Designation</label>
                                                                <select class="form-control" name="SP_DesignationId"
                                                                        ng-model="User.SP_DesignationId"
                                                                        ng-required="true"
                                                                        ng-options="Designation.Id as Designation.DesignationName for Designation in DesignationList">
                                                                    <option value="">Select</option>
                                                                    <option ng-repeat="e in DesignationList" ng-selected="User.SP_DesignationId==e.Id" value="{{e.Id}}">{{e.DesignationName}}</option>
                                                                </select>
                                                                <div class="row custom-row">
                                                                    <span class="text-danger pull-left"
                                                                          ng-show="UserFormAdministrativeInfo.SP_DesignationId.$dirty && UserFormAdministrativeInfo.SP_DesignationId.$invalid">
                                                                        <span ng-show="UserFormAdministrativeInfo.SP_DesignationId.$error.required">*Required</span>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel-footer" style="background-color:transparent">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <button ng-disabled="UserFormAdministrativeInfo.$invalid"
                                                                type="button"
                                                                ng-click="UpdateUserFormAdministrativeInfo()" class="btn btn-md btn-primary pull-right">
                                                            Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12">
                                <!--User Personal Info-->
                                <div class="col-lg-12 animated fadeInDownBig">
                                    <form name="UserFormPersonalInfo">
                                        <div class="panel panel-default panel-bordered">
                                            <div class="panel-content">
                                                <div class="row">
                                                    <div class="col-lg-4 separator">
                                                        <!--First Name-->
                                                        <div class="form-group">
                                                            <label class="req control-label">First Name</label>
                                                            <input type="text" name="FirstName" class="form-control" placeholder="Enter First Name"
                                                                   ng-model="User.FirstName"
                                                                   ng-required="true"
                                                                   ng-minlength="4"
                                                                   ng-maxlength="50"
                                                                   ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/"/>
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="UserFormPersonalInfo.FirstName.$dirty && UserFormPersonalInfo.FirstName.$invalid">
                                                                    <li><span class="pull-left" ng-show="UserFormPersonalInfo.FirstName.$error.required">*Required</span></li>
                                                                    <li><span class="pull-left" ng-show="UserFormPersonalInfo.FirstName.$error.pattern">Start with capital letter, don't use special character.</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormPersonalInfo.FirstName.$error.pattern && UserFormPersonalInfo.FirstName.$error.minlength">Minimum length is 4</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormPersonalInfo.FirstName.$error.pattern && UserFormPersonalInfo.FirstName.$error.maxlength">Maximum length is 50</span></li>
                                                                </ul>
                                                            </div>

                                                        </div>
                                                        <!--Last Name-->
                                                        <div class="form-group">
                                                            <label class="req control-label">Last Name</label>
                                                            <input type="text" name="LastName" class="form-control" placeholder="Enter Last Name"
                                                                   ng-model="User.LastName"
                                                                   ng-required="true"
                                                                   ng-minlength="4"
                                                                   ng-maxlength="50"
                                                                   ng-pattern="/^[A-Z][a-zA-Z0-9\.\-_ ]*$/"/>
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="UserFormPersonalInfo.LastName.$dirty && UserFormPersonalInfo.LastName.$invalid">
                                                                    <li><span class="pull-left" ng-show="UserFormPersonalInfo.LastName.$error.required">*Required</span></li>
                                                                    <li><span class="pull-left" ng-show="UserFormPersonalInfo.LastName.$error.pattern">Start with capital letter, don't use special character.</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormPersonalInfo.LastName.$error.pattern && UserFormPersonalInfo.LastName.$error.minlength">Minimum length is 4</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormPersonalInfo.LastName.$error.pattern && UserFormPersonalInfo.LastName.$error.maxlength">Maximum length is 50</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <!--Gender-->
                                                        <div class="form-group">
                                                            <label class="req">Gender</label>
                                                            <select class="form-control" name="Gender"
                                                                    ng-required="true"
                                                                    ng-model="User.Gender">
                                                                <option selected="selected" value="">Select</option>
                                                                <option value="Male">Male</option>
                                                                <option value="Female">Female</option>
                                                            </select>
                                                            <div class="row custom-row">
                                                                <span class="text-danger pull-left"
                                                                      ng-show="UserFormPersonalInfo.Gender.$dirty && UserFormPersonalInfo.Gender.$invalid">
                                                                    <span ng-show="UserFormPersonalInfo.Gender.$error.required">*Required</span>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-4 separator">

                                                        <!--Birth Date-->
                                                        <div class="form-group">
                                                            <label class="control-label">Birth Date</label>
                                                            <datepicker date-format="dd/MM/yyyy" date-max-limit="{{MaxDate | date}}" date-min-limit="{{MinDate | date}}">
                                                                <input class="form-control custom-form-control" style="cursor: pointer" placeholder="Select Birth Date" name="BirthDate" type="text"
                                                                       ng-model="User.BirthDate"
                                                                       ng-readonly="true"/>
                                                            </datepicker>
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="UserFormPersonalInfo.BirthDate.$dirty && UserFormPersonalInfo.BirthDate.$invalid">
                                                                    <li><span class="pull-left" ng-show="UserFormPersonalInfo.BirthDate.$error.pattern">This is not a valid date. Use datepicker to pick a date</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <!--Mobile-->
                                                        <div class="form-group">
                                                            <label class="control-label">Mobile</label>
                                                            <input type="text" name="Mobile" class="form-control" placeholder="(e.g. 01xxxxxxxxx)"
                                                                   ng-required="true"
                                                                   ng-model="User.Mobile"
                                                                   ng-minlength="8"
                                                                   ng-maxlength="20"/>
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left"
                                                                    ng-show="UserFormPersonalInfo.Mobile.$dirty && UserFormPersonalInfo.Mobile.$invalid">
                                                                    <li><span class="pull-left" ng-show="UserFormPersonalInfo.Mobile.$error.minlength">Minimum length is 8</span></li>
                                                                    <li><span class="pull-left" ng-show="UserFormPersonalInfo.Mobile.$error.maxlength">Maximum length is 20</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <!--Email-->
                                                        <div class="form-group">
                                                            <label class="control-label">Username / Email</label>
                                                            <input type="email" name="Email" class="form-control" placeholder="Enter Email"
                                                                   ng-model="User.Email"
                                                                   ng-change="LiveValidation(User.Email)"
                                                                   ng-minlength="11"
                                                                   ng-maxlength="30"
                                                                   ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/'/>
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left"
                                                                    ng-show="UserFormPersonalInfo.Email.$dirty && UserFormPersonalInfo.Email.$invalid">
                                                                    <li class="pull-left" ng-show="UserFormPersonalInfo.Email.$error.pattern">Not a valid email address</li>
                                                                    <li><span class="pull-left" ng-show="!UserFormPersonalInfo.Email.$error.pattern && UserFormPersonalInfo.Email.$error.minlength">Minimum length is 11</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormPersonalInfo.Email.$error.pattern && UserFormPersonalInfo.Email.$error.maxlength">Maximum length is 30</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-lg-4">
                                                        <!--Username-->
                                                        <!--<div class="form-group" style="line-height: 2">
                                                            <label class="req">Username</label>
                                                            <input type="text" name="Username" class="form-control" placeholder="Enter Username"
                                                                   ng-model="User.Username"
                                                                   ng-required="true"
                                                                   ng-minlength="11"
                                                                   ng-maxlength="40"
                                                                   ng-pattern='/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/' />
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left" ng-show="(UserFormPersonalInfo.Username.$dirty && UserFormPersonalInfo.Username.$invalid) || LiveValidation(User.Email)==false">
                                                                    <li><span class="pull-left" ng-show="UserFormPersonalInfo.Username.$error.required">*Required</span></li>
                                                                    <li><span class="pull-left" ng-show="UserFormPersonalInfo.Username.$error.pattern">Not a valid Email.</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormPersonalInfo.Username.$error.pattern && UserFormPersonalInfo.Username.$error.minlength">Minimum length is 11</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormPersonalInfo.Username.$error.pattern && UserFormPersonalInfo.Username.$error.maxlength">Maximum length is 40</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>-->
                                                        <!--Address-->
                                                        <div class="form-group">
                                                            <label class="control-label">Address</label>
                                                            <textarea class="form-control no-resize" name="Address" placeholder="Enter Address" style="height: 106px; resize: none" ng-required="true"
                                                                      ng-model="User.Address"
                                                                      ng-maxlength="200"
                                                                      ng-minlength="3"></textarea>
                                                            <div class="row custom-row">
                                                                <ul class="list-unstyled text-danger pull-left"
                                                                    ng-show="UserFormPersonalInfo.Address.$dirty && UserFormPersonalInfo.Address.$invalid">
                                                                    <li class="pull-left" ng-show="UserFormPersonalInfo.Address.$error.pattern">Not a valid address</li>
                                                                    <li class="pull-left" ng-show="UserFormPersonalInfo.Address.$error.min">Not a valid address</li>
                                                                    <li class="pull-left" ng-show="UserFormPersonalInfo.Address.$error.max">Not a valid address</li>
                                                                    <li><span class="pull-left" ng-show="!UserFormPersonalInfo.Address.$error.pattern && UserFormPersonalInfo.Address.$error.minlength">Minimum length is 3</span></li>
                                                                    <li><span class="pull-left" ng-show="!UserFormPersonalInfo.Address.$error.pattern && UserFormPersonalInfo.Address.$error.maxlength">Maximum length is 200</span></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="panel-footer" style="background-color: transparent">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <button ng-disabled="UserFormPersonalInfo.$invalid || LiveValidation(User.Email)==false"
                                                                type="button"
                                                                ng-click="UpdateUserFormPersonalInfo()" class="btn btn-md btn-primary pull-right">
                                                            Update &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
</div>


<div id="main_wrapper" class="animated fadeInUp">
    <div class="page_content">
        <div class="container-fluid">
            <div class="panel panel-default" style="transition:all 0.5s ease !important">
                <div class="panel-heading">
                    <div class="row">
                        <h4 class="pull-left panel-heading-Modified">My Profile</h4>
                    </div>
                </div>
                <div class="panel-body">

                </div>
            </div>
        </div>
    </div>
</div>