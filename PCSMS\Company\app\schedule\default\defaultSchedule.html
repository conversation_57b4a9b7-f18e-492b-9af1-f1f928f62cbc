﻿<!-- left-sidebar -->
<div ng-include="'appTemplate/left-sidebar.html'"></div>
<!-- page-header -->
<div ng-include="'appTemplate/page-header.html'"></div>
<!-- CONTENT -->
<!-- ========================================================= -->

<div class="content">
    <!-- content HEADER -->
    <!-- ========================================================= -->
    <div class="content-header">
        <!-- leftside content header -->
        <div class="leftside-content-header">
            <ul class="breadcrumbs">
                <li><i class="fa fa-calendar-times-o" aria-hidden="true"></i><a ui-sref="device">Default Schedule Settings</a></li>
            </ul>
        </div>
    </div>
    <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    <div class="animated fadeInUp">
        <div class="row">

            <!--LEFT SIDE-->
            <div class="col-sm-12 col-md-9 col-lg-9">
                <div class="panel b-primary bt-md" style="min-height: 600px;">
                    <div class="panel-content">
                       <div>
                           <span class="text-bold text-lg"><u>SET DEFAULT SCHEDULE FOR NEW DEVICE / PC</u></span>
                       </div><br />
                       <div class="panel-content">
                                <form name="ScheduleForm" class="form-horizontal" novalidate>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <!--Mandatory Fields Related to Capturing (01)-->
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <div class="panel panel-primary panel-bordered">
                                                        <div class="panel-body">
                                                            <div class="row">
                                                                <!--CapturingStartTime-->
                                                                <div class="col-lg-3">
                                                                    <div class="form-group-sm">
                                                                        <label class="control-label">Capture Start Time<span class="required">*</span></label>
                                                                        <div uib-timepicker="" ng-model="Schedule.StartTime" max="Schedule.EndTime" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ng-required="true" class="ng-pristine ng-untouched ng-valid ng-isolate-scope ng-not-empty ng-valid-time ng-valid-required" required="required">
                                                                            <table class="uib-timepicker">
                                                                                <tbody>
                                                                                    <tr class="text-center" ng-show="::showSpinners">
                                                                                        <td class="uib-increment hours"><button class="btn-link" ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}" ng-disabled="noIncrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                        <td>&nbsp;</td>
                                                                                        <td class="uib-increment minutes"><button class="btn-link" ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}" ng-disabled="noIncrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                        <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                        <td ng-show="showSeconds" class="uib-increment seconds ng-hide"><button class="btn-link" ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}" ng-disabled="noIncrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                        <td ng-show="showMeridian" class=""></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td class="form-group uib-time hours" ng-class="{'has-error': invalidHours}">
                                                                                            <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-hours ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementHours()" ng-blur="blur()">
                                                                                        </td>
                                                                                        <td class="uib-separator" style="font-weight:bold">:</td>
                                                                                        <td class="form-group uib-time minutes" ng-class="{'has-error': invalidMinutes}">
                                                                                            <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-minutes ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementMinutes()" ng-blur="blur()">
                                                                                        </td>
                                                                                        <td ng-show="showSeconds" class="uib-separator ng-hide" style="font-weight:bold">:</td>
                                                                                        <td class="form-group uib-time seconds ng-hide" ng-class="{'has-error': invalidSeconds}" ng-show="showSeconds">
                                                                                            <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-seconds ng-not-empty ng-valid-maxlength" ng-readonly="readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementSeconds()" ng-blur="blur()">
                                                                                        </td>
                                                                                        <td ng-show="showMeridian" class="uib-time am-pm"><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-date text-center ng-binding disabled" style="height:30px; padding-top:4px; padding-bottom:4px" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="0" disabled="disabled">AM</button></td>
                                                                                    </tr>
                                                                                    <tr class="text-center" ng-show="::showSpinners">
                                                                                        <td class="uib-decrement hours" style="padding-top:3px"><button class="btn-link" ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}" ng-disabled="noDecrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                        <td>&nbsp;</td>
                                                                                        <td class="uib-decrement minutes" style="padding-top:3px"><button class="btn-link" ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}" ng-disabled="noDecrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                        <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                        <td ng-show="showSeconds" class="uib-decrement seconds ng-hide" style="padding-top:3px"><button class="btn-link" ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}" ng-disabled="noDecrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                        <td ng-show="showMeridian" class=""></td>
                                                                                    </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </div>
                                                                    </div>
                                                                    <br>
                                                                </div>

                                                                <!--CapturingEndTime-->
                                                                <div class="col-lg-3 separator">
                                                                    <div class="form-group-sm">
                                                                        <label class="control-label">Capture End Time<span class="required">*</span></label>
                                                                        <div uib-timepicker="" ng-model="Schedule.EndTime" min="Schedule.StartTime" hour-step="hstep" minute-step="mstep" show-meridian="ismeridian" ng-required="true" class="ng-pristine ng-untouched ng-valid ng-isolate-scope ng-not-empty ng-valid-time ng-valid-required" required="required">
                                                                            <table class="uib-timepicker">
                                                                                <tbody>
                                                                                    <tr class="text-center" ng-show="::showSpinners">
                                                                                        <td class="uib-increment hours"><button class="btn-link" ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}" ng-disabled="noIncrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                        <td>&nbsp;</td>
                                                                                        <td class="uib-increment minutes"><button class="btn-link" ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}" ng-disabled="noIncrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                        <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                        <td ng-show="showSeconds" class="uib-increment seconds ng-hide"><button class="btn-link" ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}" ng-disabled="noIncrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></button></td>
                                                                                        <td ng-show="showMeridian" class=""></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td class="form-group uib-time hours" ng-class="{'has-error': invalidHours}">
                                                                                            <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-hours ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementHours()" ng-blur="blur()">
                                                                                        </td>
                                                                                        <td class="uib-separator" style="font-weight:bold">:</td>
                                                                                        <td class="form-group uib-time minutes" ng-class="{'has-error': invalidMinutes}">
                                                                                            <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-minutes ng-not-empty ng-valid-maxlength" ng-readonly="::readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementMinutes()" ng-blur="blur()">
                                                                                        </td>
                                                                                        <td ng-show="showSeconds" class="uib-separator ng-hide" style="font-weight:bold">:</td>
                                                                                        <td class="form-group uib-time seconds ng-hide" ng-class="{'has-error': invalidSeconds}" ng-show="showSeconds">
                                                                                            <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center ng-pristine ng-untouched ng-valid ng-valid-seconds ng-not-empty ng-valid-maxlength" ng-readonly="readonlyInput" maxlength="2" tabindex="0" ng-disabled="noIncrementSeconds()" ng-blur="blur()">
                                                                                        </td>
                                                                                        <td ng-show="showMeridian" class="uib-time am-pm"><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-date text-center ng-binding disabled" style="height:30px; padding-top:4px; padding-bottom:4px" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="0" disabled="disabled">PM</button></td>
                                                                                    </tr>
                                                                                    <tr class="text-center" ng-show="::showSpinners">
                                                                                        <td class="uib-decrement hours" style="padding-top:3px"><button class="btn-link" ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}" ng-disabled="noDecrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                        <td>&nbsp;</td>
                                                                                        <td class="uib-decrement minutes" style="padding-top:3px"><button class="btn-link" ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}" ng-disabled="noDecrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                        <td ng-show="showSeconds" class="ng-hide">&nbsp;</td>
                                                                                        <td ng-show="showSeconds" class="uib-decrement seconds ng-hide" style="padding-top:3px"><button class="btn-link" ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}" ng-disabled="noDecrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></button></td>
                                                                                        <td ng-show="showMeridian" class=""></td>
                                                                                    </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </div>
                                                                    </div>
                                                                    <br>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <!--Capturing Interval-->
                                                                    <div class="form-group-sm">
                                                                        <div class="row">
                                                                            <div class="col-lg-5">
                                                                                <label class="control-label">Capture Interval<span class="required">*</span></label>
                                                                            </div>
                                                                            <div class="col-lg-7">
                                                                                <select class="form-control" name="Timing"
                                                                                        ng-model="Schedule.Interval"
                                                                                        ng-required="true"
                                                                                        
                                                                                        ng-options="Timing.Id as Timing.Timing for Timing in TimingList">
                                                                                    <option value="">Select</option>
                                                                                    <option ng-repeat="e in TimingList" ng-selected="Schedule.Interval==e.Id" value="{{e.Id}}">{{e.Timing}}</option>
                                                                                </select>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <br>
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <div class="panel panel-primary panel-bordered">
                                                        <div class="panel-body">
                                                            <!--ng-WeekDays-->
                                                            <div class="form-group-sm">
                                                                <div class="row">
                                                                    <div class="col-lg-3"><label class="control-label" style="margin-top:16px">Day (s) of the week<span class="required">*</span></label></div>
                                                                    <div class="col-lg-9" style="width:68.333333%">
                                                                        <!--<ng-weekday-selector ng-model="$parent.CapturingDaysPreProcessed" ng-change="WeekDayChange()" ng-disabled="Loading==true" ng-required="true"></ng-weekday-selector>
                                                                        <div class="row" style="margin-left:150px">
                                                                            <ul class="list-unstyled errormessage pull-left"
                                                                                ng-show="AtleastOneDateIsSelected==false">
                                                                                <li><span class="pull-left" ng-show="AtleastOneDateIsSelected==false">At least one capture day required !</span></li>
                                                                            </ul>
                                                                        </div>-->
                                                                        <div class="col-sm-4">
                                                                            <div class="checkbox">
                                                                                <label>
                                                                                    <input type="checkbox" value="Saturday" ng-model="Schedule.Sat" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                    <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                    Saturday
                                                                                </label>
                                                                            </div>
                                                                            <div class="checkbox">
                                                                                <label>
                                                                                    <input type="checkbox" value="" ng-model="Schedule.Sun" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                    <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                    Sunday
                                                                                </label>
                                                                            </div>
                                                                            <div class="checkbox">
                                                                                <label>
                                                                                    <input type="checkbox" value="" ng-model="Schedule.Mon" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                    <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                    Monday
                                                                                </label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-sm-4">
                                                                            <div class="checkbox">
                                                                                <label>
                                                                                    <input type="checkbox" value="" ng-model="Schedule.Tues" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                    <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                    Tuesday
                                                                                </label>
                                                                            </div>
                                                                            <div class="checkbox">
                                                                                <label>
                                                                                    <input type="checkbox" value="" ng-model="Schedule.Wed" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                    <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                    Wednesday
                                                                                </label>
                                                                            </div>
                                                                            <div class="checkbox">
                                                                                <label>
                                                                                    <input type="checkbox" value="" ng-model="Schedule.Thurs" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                    <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                    Thursday
                                                                                </label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-sm-4">
                                                                            <div class="checkbox">
                                                                                <label>
                                                                                    <input type="checkbox" value="" ng-model="Schedule.Fri" ng-true-value="'Y'" ng-false-value="'N'">
                                                                                    <span class="cr"><i class="cr-icon glyphicon glyphicon-ok"></i></span>
                                                                                    Friday
                                                                                </label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div><br /><br /><br /><br /><br />
                                    <div class="row row-modal">
                                        <div>
                                            <span class="pull-left color-primary">
                                                This default-schedule settings will be applied to new device / pc.{{Schedule.length}}
                                            </span>
                                        </div>
                                        <div>
                                            <button type="button"
                                                    class="btn btn-wide btn-primary pull-right btn-right-border-radius"
                                                    ng-disabled="ScheduleForm.$invalid || Loading==true || IsAtLeastOneDaySelected()==false"
                                                    ng-click="CreateOrUpdateDefaultSchedule()">
                                                <span ng-if="Loading==false">
                                                    Set &nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-check fa-1x"></i>
                                                </span>
                                                <i ng-if="Loading==true" class="fa fa-spinner fa-spin"></i>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                    </div>
                </div>
            </div>
            <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->

            <!--RIGHT SIDE-->
            <div class="col-sm-12 col-md-3 col-lg-3">
                <div class="panel b-primary bt-md" style="min-height: 600px;">
                    <div class="panel-content" style="overflow: auto">
                        <br/>
                        <div class="panel panel-default panel-bordered">
                            <div class="panel-content">
                                <img class="img-responsive" src="Assets_Company/images/datatables/calendar.png" style="width: 40%"/>
                            </div><br/>
                            <div class="panel-content">
                                <p class="text-justify">
                                    You can set screen capture starting-time and end-time here for <span class="text-bold color-primary">any new device or pc</span> that gets registered under any license.
                                    Interval time indicates how often a screenshot should get taken and saved to server. 
                                    <br/><br/><br/>
                                    Here, you can easily set which days a pc user activities should be monitored. For example, if you check Monday to
                                    Friday and leave Saturday and Sunday unchecked, it means device / pc screen will only be captured Monday to Friday when a user logs in.

                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- =-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-= -->
    </div>
</div>

    <style>
        .checkbox label:after,
        .radio label:after {
            content: '';
            display: table;
            clear: both;
        }

        .checkbox .cr,
        .radio .cr {
            position: relative;
            display: inline-block;
            border: 1px solid #a9a9a9;
            border-radius: .25em;
            width: 1.3em;
            height: 1.3em;
            float: left;
            margin-right: .5em;
        }

        .radio .cr {
            border-radius: 50%;
        }

            .checkbox .cr .cr-icon,
            .radio .cr .cr-icon {
                position: absolute;
                font-size: .8em;
                line-height: 0;
                top: 50%;
                left: 20%;
            }

            .radio .cr .cr-icon {
                margin-left: 0.04em;
            }

        .checkbox label input[type="checkbox"],
        .radio label input[type="radio"] {
            display: none;
        }

            .checkbox label input[type="checkbox"] + .cr > .cr-icon,
            .radio label input[type="radio"] + .cr > .cr-icon {
                transform: scale(3) rotateZ(-20deg);
                opacity: 0;
                transition: all .3s ease-in;
            }

            .checkbox label input[type="checkbox"]:checked + .cr > .cr-icon,
            .radio label input[type="radio"]:checked + .cr > .cr-icon {
                transform: scale(1) rotateZ(0deg);
                opacity: 1;
            }

            .checkbox label input[type="checkbox"]:disabled + .cr,
            .radio label input[type="radio"]:disabled + .cr {
                opacity: .5;
            }
    </style>
